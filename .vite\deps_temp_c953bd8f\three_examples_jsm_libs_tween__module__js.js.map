{"version": 3, "sources": ["../../node_modules/three/examples/jsm/libs/tween.module.js"], "sourcesContent": ["/**\n * The Ease class provides a collection of easing functions for use with tween.js.\n */\nvar Easing = Object.freeze({\n    Linear: Object.freeze({\n        None: function (amount) {\n            return amount;\n        },\n        In: function (amount) {\n            return this.None(amount);\n        },\n        Out: function (amount) {\n            return this.None(amount);\n        },\n        InOut: function (amount) {\n            return this.None(amount);\n        },\n    }),\n    Quadratic: Object.freeze({\n        In: function (amount) {\n            return amount * amount;\n        },\n        Out: function (amount) {\n            return amount * (2 - amount);\n        },\n        InOut: function (amount) {\n            if ((amount *= 2) < 1) {\n                return 0.5 * amount * amount;\n            }\n            return -0.5 * (--amount * (amount - 2) - 1);\n        },\n    }),\n    Cubic: Object.freeze({\n        In: function (amount) {\n            return amount * amount * amount;\n        },\n        Out: function (amount) {\n            return --amount * amount * amount + 1;\n        },\n        InOut: function (amount) {\n            if ((amount *= 2) < 1) {\n                return 0.5 * amount * amount * amount;\n            }\n            return 0.5 * ((amount -= 2) * amount * amount + 2);\n        },\n    }),\n    Quartic: Object.freeze({\n        In: function (amount) {\n            return amount * amount * amount * amount;\n        },\n        Out: function (amount) {\n            return 1 - --amount * amount * amount * amount;\n        },\n        InOut: function (amount) {\n            if ((amount *= 2) < 1) {\n                return 0.5 * amount * amount * amount * amount;\n            }\n            return -0.5 * ((amount -= 2) * amount * amount * amount - 2);\n        },\n    }),\n    Quintic: Object.freeze({\n        In: function (amount) {\n            return amount * amount * amount * amount * amount;\n        },\n        Out: function (amount) {\n            return --amount * amount * amount * amount * amount + 1;\n        },\n        InOut: function (amount) {\n            if ((amount *= 2) < 1) {\n                return 0.5 * amount * amount * amount * amount * amount;\n            }\n            return 0.5 * ((amount -= 2) * amount * amount * amount * amount + 2);\n        },\n    }),\n    Sinusoidal: Object.freeze({\n        In: function (amount) {\n            return 1 - Math.sin(((1.0 - amount) * Math.PI) / 2);\n        },\n        Out: function (amount) {\n            return Math.sin((amount * Math.PI) / 2);\n        },\n        InOut: function (amount) {\n            return 0.5 * (1 - Math.sin(Math.PI * (0.5 - amount)));\n        },\n    }),\n    Exponential: Object.freeze({\n        In: function (amount) {\n            return amount === 0 ? 0 : Math.pow(1024, amount - 1);\n        },\n        Out: function (amount) {\n            return amount === 1 ? 1 : 1 - Math.pow(2, -10 * amount);\n        },\n        InOut: function (amount) {\n            if (amount === 0) {\n                return 0;\n            }\n            if (amount === 1) {\n                return 1;\n            }\n            if ((amount *= 2) < 1) {\n                return 0.5 * Math.pow(1024, amount - 1);\n            }\n            return 0.5 * (-Math.pow(2, -10 * (amount - 1)) + 2);\n        },\n    }),\n    Circular: Object.freeze({\n        In: function (amount) {\n            return 1 - Math.sqrt(1 - amount * amount);\n        },\n        Out: function (amount) {\n            return Math.sqrt(1 - --amount * amount);\n        },\n        InOut: function (amount) {\n            if ((amount *= 2) < 1) {\n                return -0.5 * (Math.sqrt(1 - amount * amount) - 1);\n            }\n            return 0.5 * (Math.sqrt(1 - (amount -= 2) * amount) + 1);\n        },\n    }),\n    Elastic: Object.freeze({\n        In: function (amount) {\n            if (amount === 0) {\n                return 0;\n            }\n            if (amount === 1) {\n                return 1;\n            }\n            return -Math.pow(2, 10 * (amount - 1)) * Math.sin((amount - 1.1) * 5 * Math.PI);\n        },\n        Out: function (amount) {\n            if (amount === 0) {\n                return 0;\n            }\n            if (amount === 1) {\n                return 1;\n            }\n            return Math.pow(2, -10 * amount) * Math.sin((amount - 0.1) * 5 * Math.PI) + 1;\n        },\n        InOut: function (amount) {\n            if (amount === 0) {\n                return 0;\n            }\n            if (amount === 1) {\n                return 1;\n            }\n            amount *= 2;\n            if (amount < 1) {\n                return -0.5 * Math.pow(2, 10 * (amount - 1)) * Math.sin((amount - 1.1) * 5 * Math.PI);\n            }\n            return 0.5 * Math.pow(2, -10 * (amount - 1)) * Math.sin((amount - 1.1) * 5 * Math.PI) + 1;\n        },\n    }),\n    Back: Object.freeze({\n        In: function (amount) {\n            var s = 1.70158;\n            return amount === 1 ? 1 : amount * amount * ((s + 1) * amount - s);\n        },\n        Out: function (amount) {\n            var s = 1.70158;\n            return amount === 0 ? 0 : --amount * amount * ((s + 1) * amount + s) + 1;\n        },\n        InOut: function (amount) {\n            var s = 1.70158 * 1.525;\n            if ((amount *= 2) < 1) {\n                return 0.5 * (amount * amount * ((s + 1) * amount - s));\n            }\n            return 0.5 * ((amount -= 2) * amount * ((s + 1) * amount + s) + 2);\n        },\n    }),\n    Bounce: Object.freeze({\n        In: function (amount) {\n            return 1 - Easing.Bounce.Out(1 - amount);\n        },\n        Out: function (amount) {\n            if (amount < 1 / 2.75) {\n                return 7.5625 * amount * amount;\n            }\n            else if (amount < 2 / 2.75) {\n                return 7.5625 * (amount -= 1.5 / 2.75) * amount + 0.75;\n            }\n            else if (amount < 2.5 / 2.75) {\n                return 7.5625 * (amount -= 2.25 / 2.75) * amount + 0.9375;\n            }\n            else {\n                return 7.5625 * (amount -= 2.625 / 2.75) * amount + 0.984375;\n            }\n        },\n        InOut: function (amount) {\n            if (amount < 0.5) {\n                return Easing.Bounce.In(amount * 2) * 0.5;\n            }\n            return Easing.Bounce.Out(amount * 2 - 1) * 0.5 + 0.5;\n        },\n    }),\n    generatePow: function (power) {\n        if (power === void 0) { power = 4; }\n        power = power < Number.EPSILON ? Number.EPSILON : power;\n        power = power > 10000 ? 10000 : power;\n        return {\n            In: function (amount) {\n                return Math.pow(amount, power);\n            },\n            Out: function (amount) {\n                return 1 - Math.pow((1 - amount), power);\n            },\n            InOut: function (amount) {\n                if (amount < 0.5) {\n                    return Math.pow((amount * 2), power) / 2;\n                }\n                return (1 - Math.pow((2 - amount * 2), power)) / 2 + 0.5;\n            },\n        };\n    },\n});\n\nvar now = function () { return performance.now(); };\n\n/**\n * Controlling groups of tweens\n *\n * Using the TWEEN singleton to manage your tweens can cause issues in large apps with many components.\n * In these cases, you may want to create your own smaller groups of tween\n */\nvar Group = /** @class */ (function () {\n    function Group() {\n        this._tweens = {};\n        this._tweensAddedDuringUpdate = {};\n    }\n    Group.prototype.getAll = function () {\n        var _this = this;\n        return Object.keys(this._tweens).map(function (tweenId) {\n            return _this._tweens[tweenId];\n        });\n    };\n    Group.prototype.removeAll = function () {\n        this._tweens = {};\n    };\n    Group.prototype.add = function (tween) {\n        this._tweens[tween.getId()] = tween;\n        this._tweensAddedDuringUpdate[tween.getId()] = tween;\n    };\n    Group.prototype.remove = function (tween) {\n        delete this._tweens[tween.getId()];\n        delete this._tweensAddedDuringUpdate[tween.getId()];\n    };\n    Group.prototype.update = function (time, preserve) {\n        if (time === void 0) { time = now(); }\n        if (preserve === void 0) { preserve = false; }\n        var tweenIds = Object.keys(this._tweens);\n        if (tweenIds.length === 0) {\n            return false;\n        }\n        // Tweens are updated in \"batches\". If you add a new tween during an\n        // update, then the new tween will be updated in the next batch.\n        // If you remove a tween during an update, it may or may not be updated.\n        // However, if the removed tween was added during the current batch,\n        // then it will not be updated.\n        while (tweenIds.length > 0) {\n            this._tweensAddedDuringUpdate = {};\n            for (var i = 0; i < tweenIds.length; i++) {\n                var tween = this._tweens[tweenIds[i]];\n                var autoStart = !preserve;\n                if (tween && tween.update(time, autoStart) === false && !preserve) {\n                    delete this._tweens[tweenIds[i]];\n                }\n            }\n            tweenIds = Object.keys(this._tweensAddedDuringUpdate);\n        }\n        return true;\n    };\n    return Group;\n}());\n\n/**\n *\n */\nvar Interpolation = {\n    Linear: function (v, k) {\n        var m = v.length - 1;\n        var f = m * k;\n        var i = Math.floor(f);\n        var fn = Interpolation.Utils.Linear;\n        if (k < 0) {\n            return fn(v[0], v[1], f);\n        }\n        if (k > 1) {\n            return fn(v[m], v[m - 1], m - f);\n        }\n        return fn(v[i], v[i + 1 > m ? m : i + 1], f - i);\n    },\n    Bezier: function (v, k) {\n        var b = 0;\n        var n = v.length - 1;\n        var pw = Math.pow;\n        var bn = Interpolation.Utils.Bernstein;\n        for (var i = 0; i <= n; i++) {\n            b += pw(1 - k, n - i) * pw(k, i) * v[i] * bn(n, i);\n        }\n        return b;\n    },\n    CatmullRom: function (v, k) {\n        var m = v.length - 1;\n        var f = m * k;\n        var i = Math.floor(f);\n        var fn = Interpolation.Utils.CatmullRom;\n        if (v[0] === v[m]) {\n            if (k < 0) {\n                i = Math.floor((f = m * (1 + k)));\n            }\n            return fn(v[(i - 1 + m) % m], v[i], v[(i + 1) % m], v[(i + 2) % m], f - i);\n        }\n        else {\n            if (k < 0) {\n                return v[0] - (fn(v[0], v[0], v[1], v[1], -f) - v[0]);\n            }\n            if (k > 1) {\n                return v[m] - (fn(v[m], v[m], v[m - 1], v[m - 1], f - m) - v[m]);\n            }\n            return fn(v[i ? i - 1 : 0], v[i], v[m < i + 1 ? m : i + 1], v[m < i + 2 ? m : i + 2], f - i);\n        }\n    },\n    Utils: {\n        Linear: function (p0, p1, t) {\n            return (p1 - p0) * t + p0;\n        },\n        Bernstein: function (n, i) {\n            var fc = Interpolation.Utils.Factorial;\n            return fc(n) / fc(i) / fc(n - i);\n        },\n        Factorial: (function () {\n            var a = [1];\n            return function (n) {\n                var s = 1;\n                if (a[n]) {\n                    return a[n];\n                }\n                for (var i = n; i > 1; i--) {\n                    s *= i;\n                }\n                a[n] = s;\n                return s;\n            };\n        })(),\n        CatmullRom: function (p0, p1, p2, p3, t) {\n            var v0 = (p2 - p0) * 0.5;\n            var v1 = (p3 - p1) * 0.5;\n            var t2 = t * t;\n            var t3 = t * t2;\n            return (2 * p1 - 2 * p2 + v0 + v1) * t3 + (-3 * p1 + 3 * p2 - 2 * v0 - v1) * t2 + v0 * t + p1;\n        },\n    },\n};\n\n/**\n * Utils\n */\nvar Sequence = /** @class */ (function () {\n    function Sequence() {\n    }\n    Sequence.nextId = function () {\n        return Sequence._nextId++;\n    };\n    Sequence._nextId = 0;\n    return Sequence;\n}());\n\nvar mainGroup = new Group();\n\n/**\n * Tween.js - Licensed under the MIT license\n * https://github.com/tweenjs/tween.js\n * ----------------------------------------------\n *\n * See https://github.com/tweenjs/tween.js/graphs/contributors for the full list of contributors.\n * Thank you all, you're awesome!\n */\nvar Tween = /** @class */ (function () {\n    function Tween(_object, _group) {\n        if (_group === void 0) { _group = mainGroup; }\n        this._object = _object;\n        this._group = _group;\n        this._isPaused = false;\n        this._pauseStart = 0;\n        this._valuesStart = {};\n        this._valuesEnd = {};\n        this._valuesStartRepeat = {};\n        this._duration = 1000;\n        this._isDynamic = false;\n        this._initialRepeat = 0;\n        this._repeat = 0;\n        this._yoyo = false;\n        this._isPlaying = false;\n        this._reversed = false;\n        this._delayTime = 0;\n        this._startTime = 0;\n        this._easingFunction = Easing.Linear.None;\n        this._interpolationFunction = Interpolation.Linear;\n        // eslint-disable-next-line\n        this._chainedTweens = [];\n        this._onStartCallbackFired = false;\n        this._onEveryStartCallbackFired = false;\n        this._id = Sequence.nextId();\n        this._isChainStopped = false;\n        this._propertiesAreSetUp = false;\n        this._goToEnd = false;\n    }\n    Tween.prototype.getId = function () {\n        return this._id;\n    };\n    Tween.prototype.isPlaying = function () {\n        return this._isPlaying;\n    };\n    Tween.prototype.isPaused = function () {\n        return this._isPaused;\n    };\n    Tween.prototype.to = function (target, duration) {\n        if (duration === void 0) { duration = 1000; }\n        if (this._isPlaying)\n            throw new Error('Can not call Tween.to() while Tween is already started or paused. Stop the Tween first.');\n        this._valuesEnd = target;\n        this._propertiesAreSetUp = false;\n        this._duration = duration;\n        return this;\n    };\n    Tween.prototype.duration = function (duration) {\n        if (duration === void 0) { duration = 1000; }\n        this._duration = duration;\n        return this;\n    };\n    Tween.prototype.dynamic = function (dynamic) {\n        if (dynamic === void 0) { dynamic = false; }\n        this._isDynamic = dynamic;\n        return this;\n    };\n    Tween.prototype.start = function (time, overrideStartingValues) {\n        if (time === void 0) { time = now(); }\n        if (overrideStartingValues === void 0) { overrideStartingValues = false; }\n        if (this._isPlaying) {\n            return this;\n        }\n        // eslint-disable-next-line\n        this._group && this._group.add(this);\n        this._repeat = this._initialRepeat;\n        if (this._reversed) {\n            // If we were reversed (f.e. using the yoyo feature) then we need to\n            // flip the tween direction back to forward.\n            this._reversed = false;\n            for (var property in this._valuesStartRepeat) {\n                this._swapEndStartRepeatValues(property);\n                this._valuesStart[property] = this._valuesStartRepeat[property];\n            }\n        }\n        this._isPlaying = true;\n        this._isPaused = false;\n        this._onStartCallbackFired = false;\n        this._onEveryStartCallbackFired = false;\n        this._isChainStopped = false;\n        this._startTime = time;\n        this._startTime += this._delayTime;\n        if (!this._propertiesAreSetUp || overrideStartingValues) {\n            this._propertiesAreSetUp = true;\n            // If dynamic is not enabled, clone the end values instead of using the passed-in end values.\n            if (!this._isDynamic) {\n                var tmp = {};\n                for (var prop in this._valuesEnd)\n                    tmp[prop] = this._valuesEnd[prop];\n                this._valuesEnd = tmp;\n            }\n            this._setupProperties(this._object, this._valuesStart, this._valuesEnd, this._valuesStartRepeat, overrideStartingValues);\n        }\n        return this;\n    };\n    Tween.prototype.startFromCurrentValues = function (time) {\n        return this.start(time, true);\n    };\n    Tween.prototype._setupProperties = function (_object, _valuesStart, _valuesEnd, _valuesStartRepeat, overrideStartingValues) {\n        for (var property in _valuesEnd) {\n            var startValue = _object[property];\n            var startValueIsArray = Array.isArray(startValue);\n            var propType = startValueIsArray ? 'array' : typeof startValue;\n            var isInterpolationList = !startValueIsArray && Array.isArray(_valuesEnd[property]);\n            // If `to()` specifies a property that doesn't exist in the source object,\n            // we should not set that property in the object\n            if (propType === 'undefined' || propType === 'function') {\n                continue;\n            }\n            // Check if an Array was provided as property value\n            if (isInterpolationList) {\n                var endValues = _valuesEnd[property];\n                if (endValues.length === 0) {\n                    continue;\n                }\n                // Handle an array of relative values.\n                // Creates a local copy of the Array with the start value at the front\n                var temp = [startValue];\n                for (var i = 0, l = endValues.length; i < l; i += 1) {\n                    var value = this._handleRelativeValue(startValue, endValues[i]);\n                    if (isNaN(value)) {\n                        isInterpolationList = false;\n                        console.warn('Found invalid interpolation list. Skipping.');\n                        break;\n                    }\n                    temp.push(value);\n                }\n                if (isInterpolationList) {\n                    // if (_valuesStart[property] === undefined) { // handle end values only the first time. NOT NEEDED? setupProperties is now guarded by _propertiesAreSetUp.\n                    _valuesEnd[property] = temp;\n                    // }\n                }\n            }\n            // handle the deepness of the values\n            if ((propType === 'object' || startValueIsArray) && startValue && !isInterpolationList) {\n                _valuesStart[property] = startValueIsArray ? [] : {};\n                var nestedObject = startValue;\n                for (var prop in nestedObject) {\n                    _valuesStart[property][prop] = nestedObject[prop];\n                }\n                // TODO? repeat nested values? And yoyo? And array values?\n                _valuesStartRepeat[property] = startValueIsArray ? [] : {};\n                var endValues = _valuesEnd[property];\n                // If dynamic is not enabled, clone the end values instead of using the passed-in end values.\n                if (!this._isDynamic) {\n                    var tmp = {};\n                    for (var prop in endValues)\n                        tmp[prop] = endValues[prop];\n                    _valuesEnd[property] = endValues = tmp;\n                }\n                this._setupProperties(nestedObject, _valuesStart[property], endValues, _valuesStartRepeat[property], overrideStartingValues);\n            }\n            else {\n                // Save the starting value, but only once unless override is requested.\n                if (typeof _valuesStart[property] === 'undefined' || overrideStartingValues) {\n                    _valuesStart[property] = startValue;\n                }\n                if (!startValueIsArray) {\n                    // eslint-disable-next-line\n                    // @ts-ignore FIXME?\n                    _valuesStart[property] *= 1.0; // Ensures we're using numbers, not strings\n                }\n                if (isInterpolationList) {\n                    // eslint-disable-next-line\n                    // @ts-ignore FIXME?\n                    _valuesStartRepeat[property] = _valuesEnd[property].slice().reverse();\n                }\n                else {\n                    _valuesStartRepeat[property] = _valuesStart[property] || 0;\n                }\n            }\n        }\n    };\n    Tween.prototype.stop = function () {\n        if (!this._isChainStopped) {\n            this._isChainStopped = true;\n            this.stopChainedTweens();\n        }\n        if (!this._isPlaying) {\n            return this;\n        }\n        // eslint-disable-next-line\n        this._group && this._group.remove(this);\n        this._isPlaying = false;\n        this._isPaused = false;\n        if (this._onStopCallback) {\n            this._onStopCallback(this._object);\n        }\n        return this;\n    };\n    Tween.prototype.end = function () {\n        this._goToEnd = true;\n        this.update(Infinity);\n        return this;\n    };\n    Tween.prototype.pause = function (time) {\n        if (time === void 0) { time = now(); }\n        if (this._isPaused || !this._isPlaying) {\n            return this;\n        }\n        this._isPaused = true;\n        this._pauseStart = time;\n        // eslint-disable-next-line\n        this._group && this._group.remove(this);\n        return this;\n    };\n    Tween.prototype.resume = function (time) {\n        if (time === void 0) { time = now(); }\n        if (!this._isPaused || !this._isPlaying) {\n            return this;\n        }\n        this._isPaused = false;\n        this._startTime += time - this._pauseStart;\n        this._pauseStart = 0;\n        // eslint-disable-next-line\n        this._group && this._group.add(this);\n        return this;\n    };\n    Tween.prototype.stopChainedTweens = function () {\n        for (var i = 0, numChainedTweens = this._chainedTweens.length; i < numChainedTweens; i++) {\n            this._chainedTweens[i].stop();\n        }\n        return this;\n    };\n    Tween.prototype.group = function (group) {\n        if (group === void 0) { group = mainGroup; }\n        this._group = group;\n        return this;\n    };\n    Tween.prototype.delay = function (amount) {\n        if (amount === void 0) { amount = 0; }\n        this._delayTime = amount;\n        return this;\n    };\n    Tween.prototype.repeat = function (times) {\n        if (times === void 0) { times = 0; }\n        this._initialRepeat = times;\n        this._repeat = times;\n        return this;\n    };\n    Tween.prototype.repeatDelay = function (amount) {\n        this._repeatDelayTime = amount;\n        return this;\n    };\n    Tween.prototype.yoyo = function (yoyo) {\n        if (yoyo === void 0) { yoyo = false; }\n        this._yoyo = yoyo;\n        return this;\n    };\n    Tween.prototype.easing = function (easingFunction) {\n        if (easingFunction === void 0) { easingFunction = Easing.Linear.None; }\n        this._easingFunction = easingFunction;\n        return this;\n    };\n    Tween.prototype.interpolation = function (interpolationFunction) {\n        if (interpolationFunction === void 0) { interpolationFunction = Interpolation.Linear; }\n        this._interpolationFunction = interpolationFunction;\n        return this;\n    };\n    // eslint-disable-next-line\n    Tween.prototype.chain = function () {\n        var tweens = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            tweens[_i] = arguments[_i];\n        }\n        this._chainedTweens = tweens;\n        return this;\n    };\n    Tween.prototype.onStart = function (callback) {\n        this._onStartCallback = callback;\n        return this;\n    };\n    Tween.prototype.onEveryStart = function (callback) {\n        this._onEveryStartCallback = callback;\n        return this;\n    };\n    Tween.prototype.onUpdate = function (callback) {\n        this._onUpdateCallback = callback;\n        return this;\n    };\n    Tween.prototype.onRepeat = function (callback) {\n        this._onRepeatCallback = callback;\n        return this;\n    };\n    Tween.prototype.onComplete = function (callback) {\n        this._onCompleteCallback = callback;\n        return this;\n    };\n    Tween.prototype.onStop = function (callback) {\n        this._onStopCallback = callback;\n        return this;\n    };\n    /**\n     * @returns true if the tween is still playing after the update, false\n     * otherwise (calling update on a paused tween still returns true because\n     * it is still playing, just paused).\n     */\n    Tween.prototype.update = function (time, autoStart) {\n        if (time === void 0) { time = now(); }\n        if (autoStart === void 0) { autoStart = true; }\n        if (this._isPaused)\n            return true;\n        var property;\n        var elapsed;\n        var endTime = this._startTime + this._duration;\n        if (!this._goToEnd && !this._isPlaying) {\n            if (time > endTime)\n                return false;\n            if (autoStart)\n                this.start(time, true);\n        }\n        this._goToEnd = false;\n        if (time < this._startTime) {\n            return true;\n        }\n        if (this._onStartCallbackFired === false) {\n            if (this._onStartCallback) {\n                this._onStartCallback(this._object);\n            }\n            this._onStartCallbackFired = true;\n        }\n        if (this._onEveryStartCallbackFired === false) {\n            if (this._onEveryStartCallback) {\n                this._onEveryStartCallback(this._object);\n            }\n            this._onEveryStartCallbackFired = true;\n        }\n        elapsed = (time - this._startTime) / this._duration;\n        elapsed = this._duration === 0 || elapsed > 1 ? 1 : elapsed;\n        var value = this._easingFunction(elapsed);\n        // properties transformations\n        this._updateProperties(this._object, this._valuesStart, this._valuesEnd, value);\n        if (this._onUpdateCallback) {\n            this._onUpdateCallback(this._object, elapsed);\n        }\n        if (elapsed === 1) {\n            if (this._repeat > 0) {\n                if (isFinite(this._repeat)) {\n                    this._repeat--;\n                }\n                // Reassign starting values, restart by making startTime = now\n                for (property in this._valuesStartRepeat) {\n                    if (!this._yoyo && typeof this._valuesEnd[property] === 'string') {\n                        this._valuesStartRepeat[property] =\n                            // eslint-disable-next-line\n                            // @ts-ignore FIXME?\n                            this._valuesStartRepeat[property] + parseFloat(this._valuesEnd[property]);\n                    }\n                    if (this._yoyo) {\n                        this._swapEndStartRepeatValues(property);\n                    }\n                    this._valuesStart[property] = this._valuesStartRepeat[property];\n                }\n                if (this._yoyo) {\n                    this._reversed = !this._reversed;\n                }\n                if (this._repeatDelayTime !== undefined) {\n                    this._startTime = time + this._repeatDelayTime;\n                }\n                else {\n                    this._startTime = time + this._delayTime;\n                }\n                if (this._onRepeatCallback) {\n                    this._onRepeatCallback(this._object);\n                }\n                this._onEveryStartCallbackFired = false;\n                return true;\n            }\n            else {\n                if (this._onCompleteCallback) {\n                    this._onCompleteCallback(this._object);\n                }\n                for (var i = 0, numChainedTweens = this._chainedTweens.length; i < numChainedTweens; i++) {\n                    // Make the chained tweens start exactly at the time they should,\n                    // even if the `update()` method was called way past the duration of the tween\n                    this._chainedTweens[i].start(this._startTime + this._duration, false);\n                }\n                this._isPlaying = false;\n                return false;\n            }\n        }\n        return true;\n    };\n    Tween.prototype._updateProperties = function (_object, _valuesStart, _valuesEnd, value) {\n        for (var property in _valuesEnd) {\n            // Don't update properties that do not exist in the source object\n            if (_valuesStart[property] === undefined) {\n                continue;\n            }\n            var start = _valuesStart[property] || 0;\n            var end = _valuesEnd[property];\n            var startIsArray = Array.isArray(_object[property]);\n            var endIsArray = Array.isArray(end);\n            var isInterpolationList = !startIsArray && endIsArray;\n            if (isInterpolationList) {\n                _object[property] = this._interpolationFunction(end, value);\n            }\n            else if (typeof end === 'object' && end) {\n                // eslint-disable-next-line\n                // @ts-ignore FIXME?\n                this._updateProperties(_object[property], start, end, value);\n            }\n            else {\n                // Parses relative end values with start as base (e.g.: +10, -3)\n                end = this._handleRelativeValue(start, end);\n                // Protect against non numeric properties.\n                if (typeof end === 'number') {\n                    // eslint-disable-next-line\n                    // @ts-ignore FIXME?\n                    _object[property] = start + (end - start) * value;\n                }\n            }\n        }\n    };\n    Tween.prototype._handleRelativeValue = function (start, end) {\n        if (typeof end !== 'string') {\n            return end;\n        }\n        if (end.charAt(0) === '+' || end.charAt(0) === '-') {\n            return start + parseFloat(end);\n        }\n        return parseFloat(end);\n    };\n    Tween.prototype._swapEndStartRepeatValues = function (property) {\n        var tmp = this._valuesStartRepeat[property];\n        var endValue = this._valuesEnd[property];\n        if (typeof endValue === 'string') {\n            this._valuesStartRepeat[property] = this._valuesStartRepeat[property] + parseFloat(endValue);\n        }\n        else {\n            this._valuesStartRepeat[property] = this._valuesEnd[property];\n        }\n        this._valuesEnd[property] = tmp;\n    };\n    return Tween;\n}());\n\nvar VERSION = '21.0.0';\n\n/**\n * Tween.js - Licensed under the MIT license\n * https://github.com/tweenjs/tween.js\n * ----------------------------------------------\n *\n * See https://github.com/tweenjs/tween.js/graphs/contributors for the full list of contributors.\n * Thank you all, you're awesome!\n */\nvar nextId = Sequence.nextId;\n/**\n * Controlling groups of tweens\n *\n * Using the TWEEN singleton to manage your tweens can cause issues in large apps with many components.\n * In these cases, you may want to create your own smaller groups of tweens.\n */\nvar TWEEN = mainGroup;\n// This is the best way to export things in a way that's compatible with both ES\n// Modules and CommonJS, without build hacks, and so as not to break the\n// existing API.\n// https://github.com/rollup/rollup/issues/1961#issuecomment-423037881\nvar getAll = TWEEN.getAll.bind(TWEEN);\nvar removeAll = TWEEN.removeAll.bind(TWEEN);\nvar add = TWEEN.add.bind(TWEEN);\nvar remove = TWEEN.remove.bind(TWEEN);\nvar update = TWEEN.update.bind(TWEEN);\nvar exports = {\n    Easing: Easing,\n    Group: Group,\n    Interpolation: Interpolation,\n    now: now,\n    Sequence: Sequence,\n    nextId: nextId,\n    Tween: Tween,\n    VERSION: VERSION,\n    getAll: getAll,\n    removeAll: removeAll,\n    add: add,\n    remove: remove,\n    update: update,\n};\n\nexport { Easing, Group, Interpolation, Sequence, Tween, VERSION, add, exports as default, getAll, nextId, now, remove, removeAll, update };\n"], "mappings": ";;;AAGA,IAAI,SAAS,OAAO,OAAO;AAAA,EACvB,QAAQ,OAAO,OAAO;AAAA,IAClB,MAAM,SAAU,QAAQ;AACpB,aAAO;AAAA,IACX;AAAA,IACA,IAAI,SAAU,QAAQ;AAClB,aAAO,KAAK,KAAK,MAAM;AAAA,IAC3B;AAAA,IACA,KAAK,SAAU,QAAQ;AACnB,aAAO,KAAK,KAAK,MAAM;AAAA,IAC3B;AAAA,IACA,OAAO,SAAU,QAAQ;AACrB,aAAO,KAAK,KAAK,MAAM;AAAA,IAC3B;AAAA,EACJ,CAAC;AAAA,EACD,WAAW,OAAO,OAAO;AAAA,IACrB,IAAI,SAAU,QAAQ;AAClB,aAAO,SAAS;AAAA,IACpB;AAAA,IACA,KAAK,SAAU,QAAQ;AACnB,aAAO,UAAU,IAAI;AAAA,IACzB;AAAA,IACA,OAAO,SAAU,QAAQ;AACrB,WAAK,UAAU,KAAK,GAAG;AACnB,eAAO,MAAM,SAAS;AAAA,MAC1B;AACA,aAAO,QAAQ,EAAE,UAAU,SAAS,KAAK;AAAA,IAC7C;AAAA,EACJ,CAAC;AAAA,EACD,OAAO,OAAO,OAAO;AAAA,IACjB,IAAI,SAAU,QAAQ;AAClB,aAAO,SAAS,SAAS;AAAA,IAC7B;AAAA,IACA,KAAK,SAAU,QAAQ;AACnB,aAAO,EAAE,SAAS,SAAS,SAAS;AAAA,IACxC;AAAA,IACA,OAAO,SAAU,QAAQ;AACrB,WAAK,UAAU,KAAK,GAAG;AACnB,eAAO,MAAM,SAAS,SAAS;AAAA,MACnC;AACA,aAAO,QAAQ,UAAU,KAAK,SAAS,SAAS;AAAA,IACpD;AAAA,EACJ,CAAC;AAAA,EACD,SAAS,OAAO,OAAO;AAAA,IACnB,IAAI,SAAU,QAAQ;AAClB,aAAO,SAAS,SAAS,SAAS;AAAA,IACtC;AAAA,IACA,KAAK,SAAU,QAAQ;AACnB,aAAO,IAAI,EAAE,SAAS,SAAS,SAAS;AAAA,IAC5C;AAAA,IACA,OAAO,SAAU,QAAQ;AACrB,WAAK,UAAU,KAAK,GAAG;AACnB,eAAO,MAAM,SAAS,SAAS,SAAS;AAAA,MAC5C;AACA,aAAO,SAAS,UAAU,KAAK,SAAS,SAAS,SAAS;AAAA,IAC9D;AAAA,EACJ,CAAC;AAAA,EACD,SAAS,OAAO,OAAO;AAAA,IACnB,IAAI,SAAU,QAAQ;AAClB,aAAO,SAAS,SAAS,SAAS,SAAS;AAAA,IAC/C;AAAA,IACA,KAAK,SAAU,QAAQ;AACnB,aAAO,EAAE,SAAS,SAAS,SAAS,SAAS,SAAS;AAAA,IAC1D;AAAA,IACA,OAAO,SAAU,QAAQ;AACrB,WAAK,UAAU,KAAK,GAAG;AACnB,eAAO,MAAM,SAAS,SAAS,SAAS,SAAS;AAAA,MACrD;AACA,aAAO,QAAQ,UAAU,KAAK,SAAS,SAAS,SAAS,SAAS;AAAA,IACtE;AAAA,EACJ,CAAC;AAAA,EACD,YAAY,OAAO,OAAO;AAAA,IACtB,IAAI,SAAU,QAAQ;AAClB,aAAO,IAAI,KAAK,KAAM,IAAM,UAAU,KAAK,KAAM,CAAC;AAAA,IACtD;AAAA,IACA,KAAK,SAAU,QAAQ;AACnB,aAAO,KAAK,IAAK,SAAS,KAAK,KAAM,CAAC;AAAA,IAC1C;AAAA,IACA,OAAO,SAAU,QAAQ;AACrB,aAAO,OAAO,IAAI,KAAK,IAAI,KAAK,MAAM,MAAM,OAAO;AAAA,IACvD;AAAA,EACJ,CAAC;AAAA,EACD,aAAa,OAAO,OAAO;AAAA,IACvB,IAAI,SAAU,QAAQ;AAClB,aAAO,WAAW,IAAI,IAAI,KAAK,IAAI,MAAM,SAAS,CAAC;AAAA,IACvD;AAAA,IACA,KAAK,SAAU,QAAQ;AACnB,aAAO,WAAW,IAAI,IAAI,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM;AAAA,IAC1D;AAAA,IACA,OAAO,SAAU,QAAQ;AACrB,UAAI,WAAW,GAAG;AACd,eAAO;AAAA,MACX;AACA,UAAI,WAAW,GAAG;AACd,eAAO;AAAA,MACX;AACA,WAAK,UAAU,KAAK,GAAG;AACnB,eAAO,MAAM,KAAK,IAAI,MAAM,SAAS,CAAC;AAAA,MAC1C;AACA,aAAO,OAAO,CAAC,KAAK,IAAI,GAAG,OAAO,SAAS,EAAE,IAAI;AAAA,IACrD;AAAA,EACJ,CAAC;AAAA,EACD,UAAU,OAAO,OAAO;AAAA,IACpB,IAAI,SAAU,QAAQ;AAClB,aAAO,IAAI,KAAK,KAAK,IAAI,SAAS,MAAM;AAAA,IAC5C;AAAA,IACA,KAAK,SAAU,QAAQ;AACnB,aAAO,KAAK,KAAK,IAAI,EAAE,SAAS,MAAM;AAAA,IAC1C;AAAA,IACA,OAAO,SAAU,QAAQ;AACrB,WAAK,UAAU,KAAK,GAAG;AACnB,eAAO,QAAQ,KAAK,KAAK,IAAI,SAAS,MAAM,IAAI;AAAA,MACpD;AACA,aAAO,OAAO,KAAK,KAAK,KAAK,UAAU,KAAK,MAAM,IAAI;AAAA,IAC1D;AAAA,EACJ,CAAC;AAAA,EACD,SAAS,OAAO,OAAO;AAAA,IACnB,IAAI,SAAU,QAAQ;AAClB,UAAI,WAAW,GAAG;AACd,eAAO;AAAA,MACX;AACA,UAAI,WAAW,GAAG;AACd,eAAO;AAAA,MACX;AACA,aAAO,CAAC,KAAK,IAAI,GAAG,MAAM,SAAS,EAAE,IAAI,KAAK,KAAK,SAAS,OAAO,IAAI,KAAK,EAAE;AAAA,IAClF;AAAA,IACA,KAAK,SAAU,QAAQ;AACnB,UAAI,WAAW,GAAG;AACd,eAAO;AAAA,MACX;AACA,UAAI,WAAW,GAAG;AACd,eAAO;AAAA,MACX;AACA,aAAO,KAAK,IAAI,GAAG,MAAM,MAAM,IAAI,KAAK,KAAK,SAAS,OAAO,IAAI,KAAK,EAAE,IAAI;AAAA,IAChF;AAAA,IACA,OAAO,SAAU,QAAQ;AACrB,UAAI,WAAW,GAAG;AACd,eAAO;AAAA,MACX;AACA,UAAI,WAAW,GAAG;AACd,eAAO;AAAA,MACX;AACA,gBAAU;AACV,UAAI,SAAS,GAAG;AACZ,eAAO,OAAO,KAAK,IAAI,GAAG,MAAM,SAAS,EAAE,IAAI,KAAK,KAAK,SAAS,OAAO,IAAI,KAAK,EAAE;AAAA,MACxF;AACA,aAAO,MAAM,KAAK,IAAI,GAAG,OAAO,SAAS,EAAE,IAAI,KAAK,KAAK,SAAS,OAAO,IAAI,KAAK,EAAE,IAAI;AAAA,IAC5F;AAAA,EACJ,CAAC;AAAA,EACD,MAAM,OAAO,OAAO;AAAA,IAChB,IAAI,SAAU,QAAQ;AAClB,UAAI,IAAI;AACR,aAAO,WAAW,IAAI,IAAI,SAAS,WAAW,IAAI,KAAK,SAAS;AAAA,IACpE;AAAA,IACA,KAAK,SAAU,QAAQ;AACnB,UAAI,IAAI;AACR,aAAO,WAAW,IAAI,IAAI,EAAE,SAAS,WAAW,IAAI,KAAK,SAAS,KAAK;AAAA,IAC3E;AAAA,IACA,OAAO,SAAU,QAAQ;AACrB,UAAI,IAAI,UAAU;AAClB,WAAK,UAAU,KAAK,GAAG;AACnB,eAAO,OAAO,SAAS,WAAW,IAAI,KAAK,SAAS;AAAA,MACxD;AACA,aAAO,QAAQ,UAAU,KAAK,WAAW,IAAI,KAAK,SAAS,KAAK;AAAA,IACpE;AAAA,EACJ,CAAC;AAAA,EACD,QAAQ,OAAO,OAAO;AAAA,IAClB,IAAI,SAAU,QAAQ;AAClB,aAAO,IAAI,OAAO,OAAO,IAAI,IAAI,MAAM;AAAA,IAC3C;AAAA,IACA,KAAK,SAAU,QAAQ;AACnB,UAAI,SAAS,IAAI,MAAM;AACnB,eAAO,SAAS,SAAS;AAAA,MAC7B,WACS,SAAS,IAAI,MAAM;AACxB,eAAO,UAAU,UAAU,MAAM,QAAQ,SAAS;AAAA,MACtD,WACS,SAAS,MAAM,MAAM;AAC1B,eAAO,UAAU,UAAU,OAAO,QAAQ,SAAS;AAAA,MACvD,OACK;AACD,eAAO,UAAU,UAAU,QAAQ,QAAQ,SAAS;AAAA,MACxD;AAAA,IACJ;AAAA,IACA,OAAO,SAAU,QAAQ;AACrB,UAAI,SAAS,KAAK;AACd,eAAO,OAAO,OAAO,GAAG,SAAS,CAAC,IAAI;AAAA,MAC1C;AACA,aAAO,OAAO,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,MAAM;AAAA,IACrD;AAAA,EACJ,CAAC;AAAA,EACD,aAAa,SAAU,OAAO;AAC1B,QAAI,UAAU,QAAQ;AAAE,cAAQ;AAAA,IAAG;AACnC,YAAQ,QAAQ,OAAO,UAAU,OAAO,UAAU;AAClD,YAAQ,QAAQ,MAAQ,MAAQ;AAChC,WAAO;AAAA,MACH,IAAI,SAAU,QAAQ;AAClB,eAAO,KAAK,IAAI,QAAQ,KAAK;AAAA,MACjC;AAAA,MACA,KAAK,SAAU,QAAQ;AACnB,eAAO,IAAI,KAAK,IAAK,IAAI,QAAS,KAAK;AAAA,MAC3C;AAAA,MACA,OAAO,SAAU,QAAQ;AACrB,YAAI,SAAS,KAAK;AACd,iBAAO,KAAK,IAAK,SAAS,GAAI,KAAK,IAAI;AAAA,QAC3C;AACA,gBAAQ,IAAI,KAAK,IAAK,IAAI,SAAS,GAAI,KAAK,KAAK,IAAI;AAAA,MACzD;AAAA,IACJ;AAAA,EACJ;AACJ,CAAC;AAED,IAAI,MAAM,WAAY;AAAE,SAAO,YAAY,IAAI;AAAG;AAQlD,IAAI;AAAA;AAAA,EAAuB,WAAY;AACnC,aAASA,SAAQ;AACb,WAAK,UAAU,CAAC;AAChB,WAAK,2BAA2B,CAAC;AAAA,IACrC;AACA,IAAAA,OAAM,UAAU,SAAS,WAAY;AACjC,UAAI,QAAQ;AACZ,aAAO,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,SAAU,SAAS;AACpD,eAAO,MAAM,QAAQ,OAAO;AAAA,MAChC,CAAC;AAAA,IACL;AACA,IAAAA,OAAM,UAAU,YAAY,WAAY;AACpC,WAAK,UAAU,CAAC;AAAA,IACpB;AACA,IAAAA,OAAM,UAAU,MAAM,SAAU,OAAO;AACnC,WAAK,QAAQ,MAAM,MAAM,CAAC,IAAI;AAC9B,WAAK,yBAAyB,MAAM,MAAM,CAAC,IAAI;AAAA,IACnD;AACA,IAAAA,OAAM,UAAU,SAAS,SAAU,OAAO;AACtC,aAAO,KAAK,QAAQ,MAAM,MAAM,CAAC;AACjC,aAAO,KAAK,yBAAyB,MAAM,MAAM,CAAC;AAAA,IACtD;AACA,IAAAA,OAAM,UAAU,SAAS,SAAU,MAAM,UAAU;AAC/C,UAAI,SAAS,QAAQ;AAAE,eAAO,IAAI;AAAA,MAAG;AACrC,UAAI,aAAa,QAAQ;AAAE,mBAAW;AAAA,MAAO;AAC7C,UAAI,WAAW,OAAO,KAAK,KAAK,OAAO;AACvC,UAAI,SAAS,WAAW,GAAG;AACvB,eAAO;AAAA,MACX;AAMA,aAAO,SAAS,SAAS,GAAG;AACxB,aAAK,2BAA2B,CAAC;AACjC,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,cAAI,QAAQ,KAAK,QAAQ,SAAS,CAAC,CAAC;AACpC,cAAI,YAAY,CAAC;AACjB,cAAI,SAAS,MAAM,OAAO,MAAM,SAAS,MAAM,SAAS,CAAC,UAAU;AAC/D,mBAAO,KAAK,QAAQ,SAAS,CAAC,CAAC;AAAA,UACnC;AAAA,QACJ;AACA,mBAAW,OAAO,KAAK,KAAK,wBAAwB;AAAA,MACxD;AACA,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAKF,IAAI,gBAAgB;AAAA,EAChB,QAAQ,SAAU,GAAG,GAAG;AACpB,QAAI,IAAI,EAAE,SAAS;AACnB,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,KAAK,MAAM,CAAC;AACpB,QAAI,KAAK,cAAc,MAAM;AAC7B,QAAI,IAAI,GAAG;AACP,aAAO,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC;AAAA,IAC3B;AACA,QAAI,IAAI,GAAG;AACP,aAAO,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;AAAA,IACnC;AACA,WAAO,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;AAAA,EACnD;AAAA,EACA,QAAQ,SAAU,GAAG,GAAG;AACpB,QAAI,IAAI;AACR,QAAI,IAAI,EAAE,SAAS;AACnB,QAAI,KAAK,KAAK;AACd,QAAI,KAAK,cAAc,MAAM;AAC7B,aAAS,IAAI,GAAG,KAAK,GAAG,KAAK;AACzB,WAAK,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC;AAAA,IACrD;AACA,WAAO;AAAA,EACX;AAAA,EACA,YAAY,SAAU,GAAG,GAAG;AACxB,QAAI,IAAI,EAAE,SAAS;AACnB,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,KAAK,MAAM,CAAC;AACpB,QAAI,KAAK,cAAc,MAAM;AAC7B,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACf,UAAI,IAAI,GAAG;AACP,YAAI,KAAK,MAAO,IAAI,KAAK,IAAI,EAAG;AAAA,MACpC;AACA,aAAO,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC;AAAA,IAC7E,OACK;AACD,UAAI,IAAI,GAAG;AACP,eAAO,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,MACvD;AACA,UAAI,IAAI,GAAG;AACP,eAAO,EAAE,CAAC,KAAK,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AAAA,MAClE;AACA,aAAO,GAAG,EAAE,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;AAAA,IAC/F;AAAA,EACJ;AAAA,EACA,OAAO;AAAA,IACH,QAAQ,SAAU,IAAI,IAAI,GAAG;AACzB,cAAQ,KAAK,MAAM,IAAI;AAAA,IAC3B;AAAA,IACA,WAAW,SAAU,GAAG,GAAG;AACvB,UAAI,KAAK,cAAc,MAAM;AAC7B,aAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;AAAA,IACnC;AAAA,IACA,WAAY,2BAAY;AACpB,UAAI,IAAI,CAAC,CAAC;AACV,aAAO,SAAU,GAAG;AAChB,YAAI,IAAI;AACR,YAAI,EAAE,CAAC,GAAG;AACN,iBAAO,EAAE,CAAC;AAAA,QACd;AACA,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AACxB,eAAK;AAAA,QACT;AACA,UAAE,CAAC,IAAI;AACP,eAAO;AAAA,MACX;AAAA,IACJ,EAAG;AAAA,IACH,YAAY,SAAU,IAAI,IAAI,IAAI,IAAI,GAAG;AACrC,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,MAAM,KAAK,MAAM;AACrB,UAAI,KAAK,IAAI;AACb,UAAI,KAAK,IAAI;AACb,cAAQ,IAAI,KAAK,IAAI,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,KAAK,IAAI;AAAA,IAC/F;AAAA,EACJ;AACJ;AAKA,IAAI;AAAA;AAAA,EAA0B,WAAY;AACtC,aAASC,YAAW;AAAA,IACpB;AACA,IAAAA,UAAS,SAAS,WAAY;AAC1B,aAAOA,UAAS;AAAA,IACpB;AACA,IAAAA,UAAS,UAAU;AACnB,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI,YAAY,IAAI,MAAM;AAU1B,IAAI;AAAA;AAAA,EAAuB,WAAY;AACnC,aAASC,OAAM,SAAS,QAAQ;AAC5B,UAAI,WAAW,QAAQ;AAAE,iBAAS;AAAA,MAAW;AAC7C,WAAK,UAAU;AACf,WAAK,SAAS;AACd,WAAK,YAAY;AACjB,WAAK,cAAc;AACnB,WAAK,eAAe,CAAC;AACrB,WAAK,aAAa,CAAC;AACnB,WAAK,qBAAqB,CAAC;AAC3B,WAAK,YAAY;AACjB,WAAK,aAAa;AAClB,WAAK,iBAAiB;AACtB,WAAK,UAAU;AACf,WAAK,QAAQ;AACb,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,aAAa;AAClB,WAAK,aAAa;AAClB,WAAK,kBAAkB,OAAO,OAAO;AACrC,WAAK,yBAAyB,cAAc;AAE5C,WAAK,iBAAiB,CAAC;AACvB,WAAK,wBAAwB;AAC7B,WAAK,6BAA6B;AAClC,WAAK,MAAM,SAAS,OAAO;AAC3B,WAAK,kBAAkB;AACvB,WAAK,sBAAsB;AAC3B,WAAK,WAAW;AAAA,IACpB;AACA,IAAAA,OAAM,UAAU,QAAQ,WAAY;AAChC,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,OAAM,UAAU,YAAY,WAAY;AACpC,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,OAAM,UAAU,WAAW,WAAY;AACnC,aAAO,KAAK;AAAA,IAChB;AACA,IAAAA,OAAM,UAAU,KAAK,SAAU,QAAQ,UAAU;AAC7C,UAAI,aAAa,QAAQ;AAAE,mBAAW;AAAA,MAAM;AAC5C,UAAI,KAAK;AACL,cAAM,IAAI,MAAM,yFAAyF;AAC7G,WAAK,aAAa;AAClB,WAAK,sBAAsB;AAC3B,WAAK,YAAY;AACjB,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,WAAW,SAAU,UAAU;AAC3C,UAAI,aAAa,QAAQ;AAAE,mBAAW;AAAA,MAAM;AAC5C,WAAK,YAAY;AACjB,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,UAAU,SAAU,SAAS;AACzC,UAAI,YAAY,QAAQ;AAAE,kBAAU;AAAA,MAAO;AAC3C,WAAK,aAAa;AAClB,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,QAAQ,SAAU,MAAM,wBAAwB;AAC5D,UAAI,SAAS,QAAQ;AAAE,eAAO,IAAI;AAAA,MAAG;AACrC,UAAI,2BAA2B,QAAQ;AAAE,iCAAyB;AAAA,MAAO;AACzE,UAAI,KAAK,YAAY;AACjB,eAAO;AAAA,MACX;AAEA,WAAK,UAAU,KAAK,OAAO,IAAI,IAAI;AACnC,WAAK,UAAU,KAAK;AACpB,UAAI,KAAK,WAAW;AAGhB,aAAK,YAAY;AACjB,iBAAS,YAAY,KAAK,oBAAoB;AAC1C,eAAK,0BAA0B,QAAQ;AACvC,eAAK,aAAa,QAAQ,IAAI,KAAK,mBAAmB,QAAQ;AAAA,QAClE;AAAA,MACJ;AACA,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,WAAK,wBAAwB;AAC7B,WAAK,6BAA6B;AAClC,WAAK,kBAAkB;AACvB,WAAK,aAAa;AAClB,WAAK,cAAc,KAAK;AACxB,UAAI,CAAC,KAAK,uBAAuB,wBAAwB;AACrD,aAAK,sBAAsB;AAE3B,YAAI,CAAC,KAAK,YAAY;AAClB,cAAI,MAAM,CAAC;AACX,mBAAS,QAAQ,KAAK;AAClB,gBAAI,IAAI,IAAI,KAAK,WAAW,IAAI;AACpC,eAAK,aAAa;AAAA,QACtB;AACA,aAAK,iBAAiB,KAAK,SAAS,KAAK,cAAc,KAAK,YAAY,KAAK,oBAAoB,sBAAsB;AAAA,MAC3H;AACA,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,yBAAyB,SAAU,MAAM;AACrD,aAAO,KAAK,MAAM,MAAM,IAAI;AAAA,IAChC;AACA,IAAAA,OAAM,UAAU,mBAAmB,SAAU,SAAS,cAAc,YAAY,oBAAoB,wBAAwB;AACxH,eAAS,YAAY,YAAY;AAC7B,YAAI,aAAa,QAAQ,QAAQ;AACjC,YAAI,oBAAoB,MAAM,QAAQ,UAAU;AAChD,YAAI,WAAW,oBAAoB,UAAU,OAAO;AACpD,YAAI,sBAAsB,CAAC,qBAAqB,MAAM,QAAQ,WAAW,QAAQ,CAAC;AAGlF,YAAI,aAAa,eAAe,aAAa,YAAY;AACrD;AAAA,QACJ;AAEA,YAAI,qBAAqB;AACrB,cAAI,YAAY,WAAW,QAAQ;AACnC,cAAI,UAAU,WAAW,GAAG;AACxB;AAAA,UACJ;AAGA,cAAI,OAAO,CAAC,UAAU;AACtB,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK,GAAG;AACjD,gBAAI,QAAQ,KAAK,qBAAqB,YAAY,UAAU,CAAC,CAAC;AAC9D,gBAAI,MAAM,KAAK,GAAG;AACd,oCAAsB;AACtB,sBAAQ,KAAK,6CAA6C;AAC1D;AAAA,YACJ;AACA,iBAAK,KAAK,KAAK;AAAA,UACnB;AACA,cAAI,qBAAqB;AAErB,uBAAW,QAAQ,IAAI;AAAA,UAE3B;AAAA,QACJ;AAEA,aAAK,aAAa,YAAY,sBAAsB,cAAc,CAAC,qBAAqB;AACpF,uBAAa,QAAQ,IAAI,oBAAoB,CAAC,IAAI,CAAC;AACnD,cAAI,eAAe;AACnB,mBAAS,QAAQ,cAAc;AAC3B,yBAAa,QAAQ,EAAE,IAAI,IAAI,aAAa,IAAI;AAAA,UACpD;AAEA,6BAAmB,QAAQ,IAAI,oBAAoB,CAAC,IAAI,CAAC;AACzD,cAAI,YAAY,WAAW,QAAQ;AAEnC,cAAI,CAAC,KAAK,YAAY;AAClB,gBAAI,MAAM,CAAC;AACX,qBAAS,QAAQ;AACb,kBAAI,IAAI,IAAI,UAAU,IAAI;AAC9B,uBAAW,QAAQ,IAAI,YAAY;AAAA,UACvC;AACA,eAAK,iBAAiB,cAAc,aAAa,QAAQ,GAAG,WAAW,mBAAmB,QAAQ,GAAG,sBAAsB;AAAA,QAC/H,OACK;AAED,cAAI,OAAO,aAAa,QAAQ,MAAM,eAAe,wBAAwB;AACzE,yBAAa,QAAQ,IAAI;AAAA,UAC7B;AACA,cAAI,CAAC,mBAAmB;AAGpB,yBAAa,QAAQ,KAAK;AAAA,UAC9B;AACA,cAAI,qBAAqB;AAGrB,+BAAmB,QAAQ,IAAI,WAAW,QAAQ,EAAE,MAAM,EAAE,QAAQ;AAAA,UACxE,OACK;AACD,+BAAmB,QAAQ,IAAI,aAAa,QAAQ,KAAK;AAAA,UAC7D;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,OAAM,UAAU,OAAO,WAAY;AAC/B,UAAI,CAAC,KAAK,iBAAiB;AACvB,aAAK,kBAAkB;AACvB,aAAK,kBAAkB;AAAA,MAC3B;AACA,UAAI,CAAC,KAAK,YAAY;AAClB,eAAO;AAAA,MACX;AAEA,WAAK,UAAU,KAAK,OAAO,OAAO,IAAI;AACtC,WAAK,aAAa;AAClB,WAAK,YAAY;AACjB,UAAI,KAAK,iBAAiB;AACtB,aAAK,gBAAgB,KAAK,OAAO;AAAA,MACrC;AACA,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,MAAM,WAAY;AAC9B,WAAK,WAAW;AAChB,WAAK,OAAO,QAAQ;AACpB,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,QAAQ,SAAU,MAAM;AACpC,UAAI,SAAS,QAAQ;AAAE,eAAO,IAAI;AAAA,MAAG;AACrC,UAAI,KAAK,aAAa,CAAC,KAAK,YAAY;AACpC,eAAO;AAAA,MACX;AACA,WAAK,YAAY;AACjB,WAAK,cAAc;AAEnB,WAAK,UAAU,KAAK,OAAO,OAAO,IAAI;AACtC,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,SAAS,SAAU,MAAM;AACrC,UAAI,SAAS,QAAQ;AAAE,eAAO,IAAI;AAAA,MAAG;AACrC,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,YAAY;AACrC,eAAO;AAAA,MACX;AACA,WAAK,YAAY;AACjB,WAAK,cAAc,OAAO,KAAK;AAC/B,WAAK,cAAc;AAEnB,WAAK,UAAU,KAAK,OAAO,IAAI,IAAI;AACnC,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,oBAAoB,WAAY;AAC5C,eAAS,IAAI,GAAG,mBAAmB,KAAK,eAAe,QAAQ,IAAI,kBAAkB,KAAK;AACtF,aAAK,eAAe,CAAC,EAAE,KAAK;AAAA,MAChC;AACA,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,QAAQ,SAAU,OAAO;AACrC,UAAI,UAAU,QAAQ;AAAE,gBAAQ;AAAA,MAAW;AAC3C,WAAK,SAAS;AACd,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,QAAQ,SAAU,QAAQ;AACtC,UAAI,WAAW,QAAQ;AAAE,iBAAS;AAAA,MAAG;AACrC,WAAK,aAAa;AAClB,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,SAAS,SAAU,OAAO;AACtC,UAAI,UAAU,QAAQ;AAAE,gBAAQ;AAAA,MAAG;AACnC,WAAK,iBAAiB;AACtB,WAAK,UAAU;AACf,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,cAAc,SAAU,QAAQ;AAC5C,WAAK,mBAAmB;AACxB,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,OAAO,SAAU,MAAM;AACnC,UAAI,SAAS,QAAQ;AAAE,eAAO;AAAA,MAAO;AACrC,WAAK,QAAQ;AACb,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,SAAS,SAAU,gBAAgB;AAC/C,UAAI,mBAAmB,QAAQ;AAAE,yBAAiB,OAAO,OAAO;AAAA,MAAM;AACtE,WAAK,kBAAkB;AACvB,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,gBAAgB,SAAU,uBAAuB;AAC7D,UAAI,0BAA0B,QAAQ;AAAE,gCAAwB,cAAc;AAAA,MAAQ;AACtF,WAAK,yBAAyB;AAC9B,aAAO;AAAA,IACX;AAEA,IAAAA,OAAM,UAAU,QAAQ,WAAY;AAChC,UAAI,SAAS,CAAC;AACd,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,eAAO,EAAE,IAAI,UAAU,EAAE;AAAA,MAC7B;AACA,WAAK,iBAAiB;AACtB,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,UAAU,SAAU,UAAU;AAC1C,WAAK,mBAAmB;AACxB,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,eAAe,SAAU,UAAU;AAC/C,WAAK,wBAAwB;AAC7B,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,WAAW,SAAU,UAAU;AAC3C,WAAK,oBAAoB;AACzB,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,WAAW,SAAU,UAAU;AAC3C,WAAK,oBAAoB;AACzB,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,aAAa,SAAU,UAAU;AAC7C,WAAK,sBAAsB;AAC3B,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,SAAS,SAAU,UAAU;AACzC,WAAK,kBAAkB;AACvB,aAAO;AAAA,IACX;AAMA,IAAAA,OAAM,UAAU,SAAS,SAAU,MAAM,WAAW;AAChD,UAAI,SAAS,QAAQ;AAAE,eAAO,IAAI;AAAA,MAAG;AACrC,UAAI,cAAc,QAAQ;AAAE,oBAAY;AAAA,MAAM;AAC9C,UAAI,KAAK;AACL,eAAO;AACX,UAAI;AACJ,UAAI;AACJ,UAAI,UAAU,KAAK,aAAa,KAAK;AACrC,UAAI,CAAC,KAAK,YAAY,CAAC,KAAK,YAAY;AACpC,YAAI,OAAO;AACP,iBAAO;AACX,YAAI;AACA,eAAK,MAAM,MAAM,IAAI;AAAA,MAC7B;AACA,WAAK,WAAW;AAChB,UAAI,OAAO,KAAK,YAAY;AACxB,eAAO;AAAA,MACX;AACA,UAAI,KAAK,0BAA0B,OAAO;AACtC,YAAI,KAAK,kBAAkB;AACvB,eAAK,iBAAiB,KAAK,OAAO;AAAA,QACtC;AACA,aAAK,wBAAwB;AAAA,MACjC;AACA,UAAI,KAAK,+BAA+B,OAAO;AAC3C,YAAI,KAAK,uBAAuB;AAC5B,eAAK,sBAAsB,KAAK,OAAO;AAAA,QAC3C;AACA,aAAK,6BAA6B;AAAA,MACtC;AACA,iBAAW,OAAO,KAAK,cAAc,KAAK;AAC1C,gBAAU,KAAK,cAAc,KAAK,UAAU,IAAI,IAAI;AACpD,UAAI,QAAQ,KAAK,gBAAgB,OAAO;AAExC,WAAK,kBAAkB,KAAK,SAAS,KAAK,cAAc,KAAK,YAAY,KAAK;AAC9E,UAAI,KAAK,mBAAmB;AACxB,aAAK,kBAAkB,KAAK,SAAS,OAAO;AAAA,MAChD;AACA,UAAI,YAAY,GAAG;AACf,YAAI,KAAK,UAAU,GAAG;AAClB,cAAI,SAAS,KAAK,OAAO,GAAG;AACxB,iBAAK;AAAA,UACT;AAEA,eAAK,YAAY,KAAK,oBAAoB;AACtC,gBAAI,CAAC,KAAK,SAAS,OAAO,KAAK,WAAW,QAAQ,MAAM,UAAU;AAC9D,mBAAK,mBAAmB,QAAQ;AAAA;AAAA,cAG5B,KAAK,mBAAmB,QAAQ,IAAI,WAAW,KAAK,WAAW,QAAQ,CAAC;AAAA,YAChF;AACA,gBAAI,KAAK,OAAO;AACZ,mBAAK,0BAA0B,QAAQ;AAAA,YAC3C;AACA,iBAAK,aAAa,QAAQ,IAAI,KAAK,mBAAmB,QAAQ;AAAA,UAClE;AACA,cAAI,KAAK,OAAO;AACZ,iBAAK,YAAY,CAAC,KAAK;AAAA,UAC3B;AACA,cAAI,KAAK,qBAAqB,QAAW;AACrC,iBAAK,aAAa,OAAO,KAAK;AAAA,UAClC,OACK;AACD,iBAAK,aAAa,OAAO,KAAK;AAAA,UAClC;AACA,cAAI,KAAK,mBAAmB;AACxB,iBAAK,kBAAkB,KAAK,OAAO;AAAA,UACvC;AACA,eAAK,6BAA6B;AAClC,iBAAO;AAAA,QACX,OACK;AACD,cAAI,KAAK,qBAAqB;AAC1B,iBAAK,oBAAoB,KAAK,OAAO;AAAA,UACzC;AACA,mBAAS,IAAI,GAAG,mBAAmB,KAAK,eAAe,QAAQ,IAAI,kBAAkB,KAAK;AAGtF,iBAAK,eAAe,CAAC,EAAE,MAAM,KAAK,aAAa,KAAK,WAAW,KAAK;AAAA,UACxE;AACA,eAAK,aAAa;AAClB,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAAA,OAAM,UAAU,oBAAoB,SAAU,SAAS,cAAc,YAAY,OAAO;AACpF,eAAS,YAAY,YAAY;AAE7B,YAAI,aAAa,QAAQ,MAAM,QAAW;AACtC;AAAA,QACJ;AACA,YAAI,QAAQ,aAAa,QAAQ,KAAK;AACtC,YAAI,MAAM,WAAW,QAAQ;AAC7B,YAAI,eAAe,MAAM,QAAQ,QAAQ,QAAQ,CAAC;AAClD,YAAI,aAAa,MAAM,QAAQ,GAAG;AAClC,YAAI,sBAAsB,CAAC,gBAAgB;AAC3C,YAAI,qBAAqB;AACrB,kBAAQ,QAAQ,IAAI,KAAK,uBAAuB,KAAK,KAAK;AAAA,QAC9D,WACS,OAAO,QAAQ,YAAY,KAAK;AAGrC,eAAK,kBAAkB,QAAQ,QAAQ,GAAG,OAAO,KAAK,KAAK;AAAA,QAC/D,OACK;AAED,gBAAM,KAAK,qBAAqB,OAAO,GAAG;AAE1C,cAAI,OAAO,QAAQ,UAAU;AAGzB,oBAAQ,QAAQ,IAAI,SAAS,MAAM,SAAS;AAAA,UAChD;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,OAAM,UAAU,uBAAuB,SAAU,OAAO,KAAK;AACzD,UAAI,OAAO,QAAQ,UAAU;AACzB,eAAO;AAAA,MACX;AACA,UAAI,IAAI,OAAO,CAAC,MAAM,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK;AAChD,eAAO,QAAQ,WAAW,GAAG;AAAA,MACjC;AACA,aAAO,WAAW,GAAG;AAAA,IACzB;AACA,IAAAA,OAAM,UAAU,4BAA4B,SAAU,UAAU;AAC5D,UAAI,MAAM,KAAK,mBAAmB,QAAQ;AAC1C,UAAI,WAAW,KAAK,WAAW,QAAQ;AACvC,UAAI,OAAO,aAAa,UAAU;AAC9B,aAAK,mBAAmB,QAAQ,IAAI,KAAK,mBAAmB,QAAQ,IAAI,WAAW,QAAQ;AAAA,MAC/F,OACK;AACD,aAAK,mBAAmB,QAAQ,IAAI,KAAK,WAAW,QAAQ;AAAA,MAChE;AACA,WAAK,WAAW,QAAQ,IAAI;AAAA,IAChC;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAEF,IAAI,UAAU;AAUd,IAAI,SAAS,SAAS;AAOtB,IAAI,QAAQ;AAKZ,IAAI,SAAS,MAAM,OAAO,KAAK,KAAK;AACpC,IAAI,YAAY,MAAM,UAAU,KAAK,KAAK;AAC1C,IAAI,MAAM,MAAM,IAAI,KAAK,KAAK;AAC9B,IAAI,SAAS,MAAM,OAAO,KAAK,KAAK;AACpC,IAAI,SAAS,MAAM,OAAO,KAAK,KAAK;AACpC,IAAI,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;", "names": ["Group", "Sequence", "Tween"]}