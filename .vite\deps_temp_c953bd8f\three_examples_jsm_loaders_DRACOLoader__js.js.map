{"version": 3, "sources": ["../../node_modules/three/examples/jsm/loaders/DRACOLoader.js"], "sourcesContent": ["import {\n\tBufferAttribute,\n\tBufferGeometry,\n\tColor,\n\tFileLoader,\n\tLoader,\n\tLinearSRGBColorSpace,\n\tSRGBColorSpace\n} from 'three';\n\nconst _taskCache = new WeakMap();\n\nclass DRACOLoader extends Loader {\n\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t\tthis.decoderPath = '';\n\t\tthis.decoderConfig = {};\n\t\tthis.decoderBinary = null;\n\t\tthis.decoderPending = null;\n\n\t\tthis.workerLimit = 4;\n\t\tthis.workerPool = [];\n\t\tthis.workerNextTaskID = 1;\n\t\tthis.workerSourceURL = '';\n\n\t\tthis.defaultAttributeIDs = {\n\t\t\tposition: 'POSITION',\n\t\t\tnormal: 'NORMAL',\n\t\t\tcolor: 'COLOR',\n\t\t\tuv: 'TEX_COORD'\n\t\t};\n\t\tthis.defaultAttributeTypes = {\n\t\t\tposition: 'Float32Array',\n\t\t\tnormal: 'Float32Array',\n\t\t\tcolor: 'Float32Array',\n\t\t\tuv: 'Float32Array'\n\t\t};\n\n\t}\n\n\tsetDecoderPath( path ) {\n\n\t\tthis.decoderPath = path;\n\n\t\treturn this;\n\n\t}\n\n\tsetDecoderConfig( config ) {\n\n\t\tthis.decoderConfig = config;\n\n\t\treturn this;\n\n\t}\n\n\tsetWorkerLimit( workerLimit ) {\n\n\t\tthis.workerLimit = workerLimit;\n\n\t\treturn this;\n\n\t}\n\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tconst loader = new FileLoader( this.manager );\n\n\t\tloader.setPath( this.path );\n\t\tloader.setResponseType( 'arraybuffer' );\n\t\tloader.setRequestHeader( this.requestHeader );\n\t\tloader.setWithCredentials( this.withCredentials );\n\n\t\tloader.load( url, ( buffer ) => {\n\n\t\t\tthis.parse( buffer, onLoad, onError );\n\n\t\t}, onProgress, onError );\n\n\t}\n\n\tparse( buffer, onLoad, onError ) {\n\n\t\tthis.decodeDracoFile( buffer, onLoad, null, null, SRGBColorSpace ).catch( onError );\n\n\t}\n\n\tdecodeDracoFile( buffer, callback, attributeIDs, attributeTypes, vertexColorSpace = LinearSRGBColorSpace ) {\n\n\t\tconst taskConfig = {\n\t\t\tattributeIDs: attributeIDs || this.defaultAttributeIDs,\n\t\t\tattributeTypes: attributeTypes || this.defaultAttributeTypes,\n\t\t\tuseUniqueIDs: !! attributeIDs,\n\t\t\tvertexColorSpace: vertexColorSpace,\n\t\t};\n\n\t\treturn this.decodeGeometry( buffer, taskConfig ).then( callback );\n\n\t}\n\n\tdecodeGeometry( buffer, taskConfig ) {\n\n\t\tconst taskKey = JSON.stringify( taskConfig );\n\n\t\t// Check for an existing task using this buffer. A transferred buffer cannot be transferred\n\t\t// again from this thread.\n\t\tif ( _taskCache.has( buffer ) ) {\n\n\t\t\tconst cachedTask = _taskCache.get( buffer );\n\n\t\t\tif ( cachedTask.key === taskKey ) {\n\n\t\t\t\treturn cachedTask.promise;\n\n\t\t\t} else if ( buffer.byteLength === 0 ) {\n\n\t\t\t\t// Technically, it would be possible to wait for the previous task to complete,\n\t\t\t\t// transfer the buffer back, and decode again with the second configuration. That\n\t\t\t\t// is complex, and I don't know of any reason to decode a Draco buffer twice in\n\t\t\t\t// different ways, so this is left unimplemented.\n\t\t\t\tthrow new Error(\n\n\t\t\t\t\t'THREE.DRACOLoader: Unable to re-decode a buffer with different ' +\n\t\t\t\t\t'settings. Buffer has already been transferred.'\n\n\t\t\t\t);\n\n\t\t\t}\n\n\t\t}\n\n\t\t//\n\n\t\tlet worker;\n\t\tconst taskID = this.workerNextTaskID ++;\n\t\tconst taskCost = buffer.byteLength;\n\n\t\t// Obtain a worker and assign a task, and construct a geometry instance\n\t\t// when the task completes.\n\t\tconst geometryPending = this._getWorker( taskID, taskCost )\n\t\t\t.then( ( _worker ) => {\n\n\t\t\t\tworker = _worker;\n\n\t\t\t\treturn new Promise( ( resolve, reject ) => {\n\n\t\t\t\t\tworker._callbacks[ taskID ] = { resolve, reject };\n\n\t\t\t\t\tworker.postMessage( { type: 'decode', id: taskID, taskConfig, buffer }, [ buffer ] );\n\n\t\t\t\t\t// this.debug();\n\n\t\t\t\t} );\n\n\t\t\t} )\n\t\t\t.then( ( message ) => this._createGeometry( message.geometry ) );\n\n\t\t// Remove task from the task list.\n\t\t// Note: replaced '.finally()' with '.catch().then()' block - iOS 11 support (#19416)\n\t\tgeometryPending\n\t\t\t.catch( () => true )\n\t\t\t.then( () => {\n\n\t\t\t\tif ( worker && taskID ) {\n\n\t\t\t\t\tthis._releaseTask( worker, taskID );\n\n\t\t\t\t\t// this.debug();\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t// Cache the task result.\n\t\t_taskCache.set( buffer, {\n\n\t\t\tkey: taskKey,\n\t\t\tpromise: geometryPending\n\n\t\t} );\n\n\t\treturn geometryPending;\n\n\t}\n\n\t_createGeometry( geometryData ) {\n\n\t\tconst geometry = new BufferGeometry();\n\n\t\tif ( geometryData.index ) {\n\n\t\t\tgeometry.setIndex( new BufferAttribute( geometryData.index.array, 1 ) );\n\n\t\t}\n\n\t\tfor ( let i = 0; i < geometryData.attributes.length; i ++ ) {\n\n\t\t\tconst result = geometryData.attributes[ i ];\n\t\t\tconst name = result.name;\n\t\t\tconst array = result.array;\n\t\t\tconst itemSize = result.itemSize;\n\n\t\t\tconst attribute = new BufferAttribute( array, itemSize );\n\n\t\t\tif ( name === 'color' ) {\n\n\t\t\t\tthis._assignVertexColorSpace( attribute, result.vertexColorSpace );\n\n\t\t\t\tattribute.normalized = ( array instanceof Float32Array ) === false;\n\n\t\t\t}\n\n\t\t\tgeometry.setAttribute( name, attribute );\n\n\t\t}\n\n\t\treturn geometry;\n\n\t}\n\n\t_assignVertexColorSpace( attribute, inputColorSpace ) {\n\n\t\t// While .drc files do not specify colorspace, the only 'official' tooling\n\t\t// is PLY and OBJ converters, which use sRGB. We'll assume sRGB when a .drc\n\t\t// file is passed into .load() or .parse(). GLTFLoader uses internal APIs\n\t\t// to decode geometry, and vertex colors are already Linear-sRGB in there.\n\n\t\tif ( inputColorSpace !== SRGBColorSpace ) return;\n\n\t\tconst _color = new Color();\n\n\t\tfor ( let i = 0, il = attribute.count; i < il; i ++ ) {\n\n\t\t\t_color.fromBufferAttribute( attribute, i ).convertSRGBToLinear();\n\t\t\tattribute.setXYZ( i, _color.r, _color.g, _color.b );\n\n\t\t}\n\n\t}\n\n\t_loadLibrary( url, responseType ) {\n\n\t\tconst loader = new FileLoader( this.manager );\n\t\tloader.setPath( this.decoderPath );\n\t\tloader.setResponseType( responseType );\n\t\tloader.setWithCredentials( this.withCredentials );\n\n\t\treturn new Promise( ( resolve, reject ) => {\n\n\t\t\tloader.load( url, resolve, undefined, reject );\n\n\t\t} );\n\n\t}\n\n\tpreload() {\n\n\t\tthis._initDecoder();\n\n\t\treturn this;\n\n\t}\n\n\t_initDecoder() {\n\n\t\tif ( this.decoderPending ) return this.decoderPending;\n\n\t\tconst useJS = typeof WebAssembly !== 'object' || this.decoderConfig.type === 'js';\n\t\tconst librariesPending = [];\n\n\t\tif ( useJS ) {\n\n\t\t\tlibrariesPending.push( this._loadLibrary( 'draco_decoder.js', 'text' ) );\n\n\t\t} else {\n\n\t\t\tlibrariesPending.push( this._loadLibrary( 'draco_wasm_wrapper.js', 'text' ) );\n\t\t\tlibrariesPending.push( this._loadLibrary( 'draco_decoder.wasm', 'arraybuffer' ) );\n\n\t\t}\n\n\t\tthis.decoderPending = Promise.all( librariesPending )\n\t\t\t.then( ( libraries ) => {\n\n\t\t\t\tconst jsContent = libraries[ 0 ];\n\n\t\t\t\tif ( ! useJS ) {\n\n\t\t\t\t\tthis.decoderConfig.wasmBinary = libraries[ 1 ];\n\n\t\t\t\t}\n\n\t\t\t\tconst fn = DRACOWorker.toString();\n\n\t\t\t\tconst body = [\n\t\t\t\t\t'/* draco decoder */',\n\t\t\t\t\tjsContent,\n\t\t\t\t\t'',\n\t\t\t\t\t'/* worker */',\n\t\t\t\t\tfn.substring( fn.indexOf( '{' ) + 1, fn.lastIndexOf( '}' ) )\n\t\t\t\t].join( '\\n' );\n\n\t\t\t\tthis.workerSourceURL = URL.createObjectURL( new Blob( [ body ] ) );\n\n\t\t\t} );\n\n\t\treturn this.decoderPending;\n\n\t}\n\n\t_getWorker( taskID, taskCost ) {\n\n\t\treturn this._initDecoder().then( () => {\n\n\t\t\tif ( this.workerPool.length < this.workerLimit ) {\n\n\t\t\t\tconst worker = new Worker( this.workerSourceURL );\n\n\t\t\t\tworker._callbacks = {};\n\t\t\t\tworker._taskCosts = {};\n\t\t\t\tworker._taskLoad = 0;\n\n\t\t\t\tworker.postMessage( { type: 'init', decoderConfig: this.decoderConfig } );\n\n\t\t\t\tworker.onmessage = function ( e ) {\n\n\t\t\t\t\tconst message = e.data;\n\n\t\t\t\t\tswitch ( message.type ) {\n\n\t\t\t\t\t\tcase 'decode':\n\t\t\t\t\t\t\tworker._callbacks[ message.id ].resolve( message );\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tcase 'error':\n\t\t\t\t\t\t\tworker._callbacks[ message.id ].reject( message );\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tconsole.error( 'THREE.DRACOLoader: Unexpected message, \"' + message.type + '\"' );\n\n\t\t\t\t\t}\n\n\t\t\t\t};\n\n\t\t\t\tthis.workerPool.push( worker );\n\n\t\t\t} else {\n\n\t\t\t\tthis.workerPool.sort( function ( a, b ) {\n\n\t\t\t\t\treturn a._taskLoad > b._taskLoad ? - 1 : 1;\n\n\t\t\t\t} );\n\n\t\t\t}\n\n\t\t\tconst worker = this.workerPool[ this.workerPool.length - 1 ];\n\t\t\tworker._taskCosts[ taskID ] = taskCost;\n\t\t\tworker._taskLoad += taskCost;\n\t\t\treturn worker;\n\n\t\t} );\n\n\t}\n\n\t_releaseTask( worker, taskID ) {\n\n\t\tworker._taskLoad -= worker._taskCosts[ taskID ];\n\t\tdelete worker._callbacks[ taskID ];\n\t\tdelete worker._taskCosts[ taskID ];\n\n\t}\n\n\tdebug() {\n\n\t\tconsole.log( 'Task load: ', this.workerPool.map( ( worker ) => worker._taskLoad ) );\n\n\t}\n\n\tdispose() {\n\n\t\tfor ( let i = 0; i < this.workerPool.length; ++ i ) {\n\n\t\t\tthis.workerPool[ i ].terminate();\n\n\t\t}\n\n\t\tthis.workerPool.length = 0;\n\n\t\tif ( this.workerSourceURL !== '' ) {\n\n\t\t\tURL.revokeObjectURL( this.workerSourceURL );\n\n\t\t}\n\n\t\treturn this;\n\n\t}\n\n}\n\n/* WEB WORKER */\n\nfunction DRACOWorker() {\n\n\tlet decoderConfig;\n\tlet decoderPending;\n\n\tonmessage = function ( e ) {\n\n\t\tconst message = e.data;\n\n\t\tswitch ( message.type ) {\n\n\t\t\tcase 'init':\n\t\t\t\tdecoderConfig = message.decoderConfig;\n\t\t\t\tdecoderPending = new Promise( function ( resolve/*, reject*/ ) {\n\n\t\t\t\t\tdecoderConfig.onModuleLoaded = function ( draco ) {\n\n\t\t\t\t\t\t// Module is Promise-like. Wrap before resolving to avoid loop.\n\t\t\t\t\t\tresolve( { draco: draco } );\n\n\t\t\t\t\t};\n\n\t\t\t\t\tDracoDecoderModule( decoderConfig ); // eslint-disable-line no-undef\n\n\t\t\t\t} );\n\t\t\t\tbreak;\n\n\t\t\tcase 'decode':\n\t\t\t\tconst buffer = message.buffer;\n\t\t\t\tconst taskConfig = message.taskConfig;\n\t\t\t\tdecoderPending.then( ( module ) => {\n\n\t\t\t\t\tconst draco = module.draco;\n\t\t\t\t\tconst decoder = new draco.Decoder();\n\n\t\t\t\t\ttry {\n\n\t\t\t\t\t\tconst geometry = decodeGeometry( draco, decoder, new Int8Array( buffer ), taskConfig );\n\n\t\t\t\t\t\tconst buffers = geometry.attributes.map( ( attr ) => attr.array.buffer );\n\n\t\t\t\t\t\tif ( geometry.index ) buffers.push( geometry.index.array.buffer );\n\n\t\t\t\t\t\tself.postMessage( { type: 'decode', id: message.id, geometry }, buffers );\n\n\t\t\t\t\t} catch ( error ) {\n\n\t\t\t\t\t\tconsole.error( error );\n\n\t\t\t\t\t\tself.postMessage( { type: 'error', id: message.id, error: error.message } );\n\n\t\t\t\t\t} finally {\n\n\t\t\t\t\t\tdraco.destroy( decoder );\n\n\t\t\t\t\t}\n\n\t\t\t\t} );\n\t\t\t\tbreak;\n\n\t\t}\n\n\t};\n\n\tfunction decodeGeometry( draco, decoder, array, taskConfig ) {\n\n\t\tconst attributeIDs = taskConfig.attributeIDs;\n\t\tconst attributeTypes = taskConfig.attributeTypes;\n\n\t\tlet dracoGeometry;\n\t\tlet decodingStatus;\n\n\t\tconst geometryType = decoder.GetEncodedGeometryType( array );\n\n\t\tif ( geometryType === draco.TRIANGULAR_MESH ) {\n\n\t\t\tdracoGeometry = new draco.Mesh();\n\t\t\tdecodingStatus = decoder.DecodeArrayToMesh( array, array.byteLength, dracoGeometry );\n\n\t\t} else if ( geometryType === draco.POINT_CLOUD ) {\n\n\t\t\tdracoGeometry = new draco.PointCloud();\n\t\t\tdecodingStatus = decoder.DecodeArrayToPointCloud( array, array.byteLength, dracoGeometry );\n\n\t\t} else {\n\n\t\t\tthrow new Error( 'THREE.DRACOLoader: Unexpected geometry type.' );\n\n\t\t}\n\n\t\tif ( ! decodingStatus.ok() || dracoGeometry.ptr === 0 ) {\n\n\t\t\tthrow new Error( 'THREE.DRACOLoader: Decoding failed: ' + decodingStatus.error_msg() );\n\n\t\t}\n\n\t\tconst geometry = { index: null, attributes: [] };\n\n\t\t// Gather all vertex attributes.\n\t\tfor ( const attributeName in attributeIDs ) {\n\n\t\t\tconst attributeType = self[ attributeTypes[ attributeName ] ];\n\n\t\t\tlet attribute;\n\t\t\tlet attributeID;\n\n\t\t\t// A Draco file may be created with default vertex attributes, whose attribute IDs\n\t\t\t// are mapped 1:1 from their semantic name (POSITION, NORMAL, ...). Alternatively,\n\t\t\t// a Draco file may contain a custom set of attributes, identified by known unique\n\t\t\t// IDs. glTF files always do the latter, and `.drc` files typically do the former.\n\t\t\tif ( taskConfig.useUniqueIDs ) {\n\n\t\t\t\tattributeID = attributeIDs[ attributeName ];\n\t\t\t\tattribute = decoder.GetAttributeByUniqueId( dracoGeometry, attributeID );\n\n\t\t\t} else {\n\n\t\t\t\tattributeID = decoder.GetAttributeId( dracoGeometry, draco[ attributeIDs[ attributeName ] ] );\n\n\t\t\t\tif ( attributeID === - 1 ) continue;\n\n\t\t\t\tattribute = decoder.GetAttribute( dracoGeometry, attributeID );\n\n\t\t\t}\n\n\t\t\tconst attributeResult = decodeAttribute( draco, decoder, dracoGeometry, attributeName, attributeType, attribute );\n\n\t\t\tif ( attributeName === 'color' ) {\n\n\t\t\t\tattributeResult.vertexColorSpace = taskConfig.vertexColorSpace;\n\n\t\t\t}\n\n\t\t\tgeometry.attributes.push( attributeResult );\n\n\t\t}\n\n\t\t// Add index.\n\t\tif ( geometryType === draco.TRIANGULAR_MESH ) {\n\n\t\t\tgeometry.index = decodeIndex( draco, decoder, dracoGeometry );\n\n\t\t}\n\n\t\tdraco.destroy( dracoGeometry );\n\n\t\treturn geometry;\n\n\t}\n\n\tfunction decodeIndex( draco, decoder, dracoGeometry ) {\n\n\t\tconst numFaces = dracoGeometry.num_faces();\n\t\tconst numIndices = numFaces * 3;\n\t\tconst byteLength = numIndices * 4;\n\n\t\tconst ptr = draco._malloc( byteLength );\n\t\tdecoder.GetTrianglesUInt32Array( dracoGeometry, byteLength, ptr );\n\t\tconst index = new Uint32Array( draco.HEAPF32.buffer, ptr, numIndices ).slice();\n\t\tdraco._free( ptr );\n\n\t\treturn { array: index, itemSize: 1 };\n\n\t}\n\n\tfunction decodeAttribute( draco, decoder, dracoGeometry, attributeName, attributeType, attribute ) {\n\n\t\tconst numComponents = attribute.num_components();\n\t\tconst numPoints = dracoGeometry.num_points();\n\t\tconst numValues = numPoints * numComponents;\n\t\tconst byteLength = numValues * attributeType.BYTES_PER_ELEMENT;\n\t\tconst dataType = getDracoDataType( draco, attributeType );\n\n\t\tconst ptr = draco._malloc( byteLength );\n\t\tdecoder.GetAttributeDataArrayForAllPoints( dracoGeometry, attribute, dataType, byteLength, ptr );\n\t\tconst array = new attributeType( draco.HEAPF32.buffer, ptr, numValues ).slice();\n\t\tdraco._free( ptr );\n\n\t\treturn {\n\t\t\tname: attributeName,\n\t\t\tarray: array,\n\t\t\titemSize: numComponents\n\t\t};\n\n\t}\n\n\tfunction getDracoDataType( draco, attributeType ) {\n\n\t\tswitch ( attributeType ) {\n\n\t\t\tcase Float32Array: return draco.DT_FLOAT32;\n\t\t\tcase Int8Array: return draco.DT_INT8;\n\t\t\tcase Int16Array: return draco.DT_INT16;\n\t\t\tcase Int32Array: return draco.DT_INT32;\n\t\t\tcase Uint8Array: return draco.DT_UINT8;\n\t\t\tcase Uint16Array: return draco.DT_UINT16;\n\t\t\tcase Uint32Array: return draco.DT_UINT32;\n\n\t\t}\n\n\t}\n\n}\n\nexport { DRACOLoader };\n"], "mappings": ";;;;;;;;;;;;AAUA,IAAM,aAAa,oBAAI,QAAQ;AAE/B,IAAM,cAAN,cAA0B,OAAO;AAAA,EAEhC,YAAa,SAAU;AAEtB,UAAO,OAAQ;AAEf,SAAK,cAAc;AACnB,SAAK,gBAAgB,CAAC;AACtB,SAAK,gBAAgB;AACrB,SAAK,iBAAiB;AAEtB,SAAK,cAAc;AACnB,SAAK,aAAa,CAAC;AACnB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AAEvB,SAAK,sBAAsB;AAAA,MAC1B,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,IAAI;AAAA,IACL;AACA,SAAK,wBAAwB;AAAA,MAC5B,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,IAAI;AAAA,IACL;AAAA,EAED;AAAA,EAEA,eAAgB,MAAO;AAEtB,SAAK,cAAc;AAEnB,WAAO;AAAA,EAER;AAAA,EAEA,iBAAkB,QAAS;AAE1B,SAAK,gBAAgB;AAErB,WAAO;AAAA,EAER;AAAA,EAEA,eAAgB,aAAc;AAE7B,SAAK,cAAc;AAEnB,WAAO;AAAA,EAER;AAAA,EAEA,KAAM,KAAK,QAAQ,YAAY,SAAU;AAExC,UAAM,SAAS,IAAI,WAAY,KAAK,OAAQ;AAE5C,WAAO,QAAS,KAAK,IAAK;AAC1B,WAAO,gBAAiB,aAAc;AACtC,WAAO,iBAAkB,KAAK,aAAc;AAC5C,WAAO,mBAAoB,KAAK,eAAgB;AAEhD,WAAO,KAAM,KAAK,CAAE,WAAY;AAE/B,WAAK,MAAO,QAAQ,QAAQ,OAAQ;AAAA,IAErC,GAAG,YAAY,OAAQ;AAAA,EAExB;AAAA,EAEA,MAAO,QAAQ,QAAQ,SAAU;AAEhC,SAAK,gBAAiB,QAAQ,QAAQ,MAAM,MAAM,cAAe,EAAE,MAAO,OAAQ;AAAA,EAEnF;AAAA,EAEA,gBAAiB,QAAQ,UAAU,cAAc,gBAAgB,mBAAmB,sBAAuB;AAE1G,UAAM,aAAa;AAAA,MAClB,cAAc,gBAAgB,KAAK;AAAA,MACnC,gBAAgB,kBAAkB,KAAK;AAAA,MACvC,cAAc,CAAC,CAAE;AAAA,MACjB;AAAA,IACD;AAEA,WAAO,KAAK,eAAgB,QAAQ,UAAW,EAAE,KAAM,QAAS;AAAA,EAEjE;AAAA,EAEA,eAAgB,QAAQ,YAAa;AAEpC,UAAM,UAAU,KAAK,UAAW,UAAW;AAI3C,QAAK,WAAW,IAAK,MAAO,GAAI;AAE/B,YAAM,aAAa,WAAW,IAAK,MAAO;AAE1C,UAAK,WAAW,QAAQ,SAAU;AAEjC,eAAO,WAAW;AAAA,MAEnB,WAAY,OAAO,eAAe,GAAI;AAMrC,cAAM,IAAI;AAAA,UAET;AAAA,QAGD;AAAA,MAED;AAAA,IAED;AAIA,QAAI;AACJ,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,OAAO;AAIxB,UAAM,kBAAkB,KAAK,WAAY,QAAQ,QAAS,EACxD,KAAM,CAAE,YAAa;AAErB,eAAS;AAET,aAAO,IAAI,QAAS,CAAE,SAAS,WAAY;AAE1C,eAAO,WAAY,MAAO,IAAI,EAAE,SAAS,OAAO;AAEhD,eAAO,YAAa,EAAE,MAAM,UAAU,IAAI,QAAQ,YAAY,OAAO,GAAG,CAAE,MAAO,CAAE;AAAA,MAIpF,CAAE;AAAA,IAEH,CAAE,EACD,KAAM,CAAE,YAAa,KAAK,gBAAiB,QAAQ,QAAS,CAAE;AAIhE,oBACE,MAAO,MAAM,IAAK,EAClB,KAAM,MAAM;AAEZ,UAAK,UAAU,QAAS;AAEvB,aAAK,aAAc,QAAQ,MAAO;AAAA,MAInC;AAAA,IAED,CAAE;AAGH,eAAW,IAAK,QAAQ;AAAA,MAEvB,KAAK;AAAA,MACL,SAAS;AAAA,IAEV,CAAE;AAEF,WAAO;AAAA,EAER;AAAA,EAEA,gBAAiB,cAAe;AAE/B,UAAM,WAAW,IAAI,eAAe;AAEpC,QAAK,aAAa,OAAQ;AAEzB,eAAS,SAAU,IAAI,gBAAiB,aAAa,MAAM,OAAO,CAAE,CAAE;AAAA,IAEvE;AAEA,aAAU,IAAI,GAAG,IAAI,aAAa,WAAW,QAAQ,KAAO;AAE3D,YAAM,SAAS,aAAa,WAAY,CAAE;AAC1C,YAAM,OAAO,OAAO;AACpB,YAAM,QAAQ,OAAO;AACrB,YAAM,WAAW,OAAO;AAExB,YAAM,YAAY,IAAI,gBAAiB,OAAO,QAAS;AAEvD,UAAK,SAAS,SAAU;AAEvB,aAAK,wBAAyB,WAAW,OAAO,gBAAiB;AAEjE,kBAAU,aAAe,iBAAiB,iBAAmB;AAAA,MAE9D;AAEA,eAAS,aAAc,MAAM,SAAU;AAAA,IAExC;AAEA,WAAO;AAAA,EAER;AAAA,EAEA,wBAAyB,WAAW,iBAAkB;AAOrD,QAAK,oBAAoB,eAAiB;AAE1C,UAAM,SAAS,IAAI,MAAM;AAEzB,aAAU,IAAI,GAAG,KAAK,UAAU,OAAO,IAAI,IAAI,KAAO;AAErD,aAAO,oBAAqB,WAAW,CAAE,EAAE,oBAAoB;AAC/D,gBAAU,OAAQ,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,CAAE;AAAA,IAEnD;AAAA,EAED;AAAA,EAEA,aAAc,KAAK,cAAe;AAEjC,UAAM,SAAS,IAAI,WAAY,KAAK,OAAQ;AAC5C,WAAO,QAAS,KAAK,WAAY;AACjC,WAAO,gBAAiB,YAAa;AACrC,WAAO,mBAAoB,KAAK,eAAgB;AAEhD,WAAO,IAAI,QAAS,CAAE,SAAS,WAAY;AAE1C,aAAO,KAAM,KAAK,SAAS,QAAW,MAAO;AAAA,IAE9C,CAAE;AAAA,EAEH;AAAA,EAEA,UAAU;AAET,SAAK,aAAa;AAElB,WAAO;AAAA,EAER;AAAA,EAEA,eAAe;AAEd,QAAK,KAAK,eAAiB,QAAO,KAAK;AAEvC,UAAM,QAAQ,OAAO,gBAAgB,YAAY,KAAK,cAAc,SAAS;AAC7E,UAAM,mBAAmB,CAAC;AAE1B,QAAK,OAAQ;AAEZ,uBAAiB,KAAM,KAAK,aAAc,oBAAoB,MAAO,CAAE;AAAA,IAExE,OAAO;AAEN,uBAAiB,KAAM,KAAK,aAAc,yBAAyB,MAAO,CAAE;AAC5E,uBAAiB,KAAM,KAAK,aAAc,sBAAsB,aAAc,CAAE;AAAA,IAEjF;AAEA,SAAK,iBAAiB,QAAQ,IAAK,gBAAiB,EAClD,KAAM,CAAE,cAAe;AAEvB,YAAM,YAAY,UAAW,CAAE;AAE/B,UAAK,CAAE,OAAQ;AAEd,aAAK,cAAc,aAAa,UAAW,CAAE;AAAA,MAE9C;AAEA,YAAM,KAAK,YAAY,SAAS;AAEhC,YAAM,OAAO;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,GAAG,UAAW,GAAG,QAAS,GAAI,IAAI,GAAG,GAAG,YAAa,GAAI,CAAE;AAAA,MAC5D,EAAE,KAAM,IAAK;AAEb,WAAK,kBAAkB,IAAI,gBAAiB,IAAI,KAAM,CAAE,IAAK,CAAE,CAAE;AAAA,IAElE,CAAE;AAEH,WAAO,KAAK;AAAA,EAEb;AAAA,EAEA,WAAY,QAAQ,UAAW;AAE9B,WAAO,KAAK,aAAa,EAAE,KAAM,MAAM;AAEtC,UAAK,KAAK,WAAW,SAAS,KAAK,aAAc;AAEhD,cAAMA,UAAS,IAAI,OAAQ,KAAK,eAAgB;AAEhD,QAAAA,QAAO,aAAa,CAAC;AACrB,QAAAA,QAAO,aAAa,CAAC;AACrB,QAAAA,QAAO,YAAY;AAEnB,QAAAA,QAAO,YAAa,EAAE,MAAM,QAAQ,eAAe,KAAK,cAAc,CAAE;AAExE,QAAAA,QAAO,YAAY,SAAW,GAAI;AAEjC,gBAAM,UAAU,EAAE;AAElB,kBAAS,QAAQ,MAAO;AAAA,YAEvB,KAAK;AACJ,cAAAA,QAAO,WAAY,QAAQ,EAAG,EAAE,QAAS,OAAQ;AACjD;AAAA,YAED,KAAK;AACJ,cAAAA,QAAO,WAAY,QAAQ,EAAG,EAAE,OAAQ,OAAQ;AAChD;AAAA,YAED;AACC,sBAAQ,MAAO,6CAA6C,QAAQ,OAAO,GAAI;AAAA,UAEjF;AAAA,QAED;AAEA,aAAK,WAAW,KAAMA,OAAO;AAAA,MAE9B,OAAO;AAEN,aAAK,WAAW,KAAM,SAAW,GAAG,GAAI;AAEvC,iBAAO,EAAE,YAAY,EAAE,YAAY,KAAM;AAAA,QAE1C,CAAE;AAAA,MAEH;AAEA,YAAM,SAAS,KAAK,WAAY,KAAK,WAAW,SAAS,CAAE;AAC3D,aAAO,WAAY,MAAO,IAAI;AAC9B,aAAO,aAAa;AACpB,aAAO;AAAA,IAER,CAAE;AAAA,EAEH;AAAA,EAEA,aAAc,QAAQ,QAAS;AAE9B,WAAO,aAAa,OAAO,WAAY,MAAO;AAC9C,WAAO,OAAO,WAAY,MAAO;AACjC,WAAO,OAAO,WAAY,MAAO;AAAA,EAElC;AAAA,EAEA,QAAQ;AAEP,YAAQ,IAAK,eAAe,KAAK,WAAW,IAAK,CAAE,WAAY,OAAO,SAAU,CAAE;AAAA,EAEnF;AAAA,EAEA,UAAU;AAET,aAAU,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,EAAG,GAAI;AAEnD,WAAK,WAAY,CAAE,EAAE,UAAU;AAAA,IAEhC;AAEA,SAAK,WAAW,SAAS;AAEzB,QAAK,KAAK,oBAAoB,IAAK;AAElC,UAAI,gBAAiB,KAAK,eAAgB;AAAA,IAE3C;AAEA,WAAO;AAAA,EAER;AAED;AAIA,SAAS,cAAc;AAEtB,MAAI;AACJ,MAAI;AAEJ,cAAY,SAAW,GAAI;AAE1B,UAAM,UAAU,EAAE;AAElB,YAAS,QAAQ,MAAO;AAAA,MAEvB,KAAK;AACJ,wBAAgB,QAAQ;AACxB,yBAAiB,IAAI,QAAS,SAAW,SAAsB;AAE9D,wBAAc,iBAAiB,SAAW,OAAQ;AAGjD,oBAAS,EAAE,MAAa,CAAE;AAAA,UAE3B;AAEA,6BAAoB,aAAc;AAAA,QAEnC,CAAE;AACF;AAAA,MAED,KAAK;AACJ,cAAM,SAAS,QAAQ;AACvB,cAAM,aAAa,QAAQ;AAC3B,uBAAe,KAAM,CAAE,WAAY;AAElC,gBAAM,QAAQ,OAAO;AACrB,gBAAM,UAAU,IAAI,MAAM,QAAQ;AAElC,cAAI;AAEH,kBAAM,WAAW,eAAgB,OAAO,SAAS,IAAI,UAAW,MAAO,GAAG,UAAW;AAErF,kBAAM,UAAU,SAAS,WAAW,IAAK,CAAE,SAAU,KAAK,MAAM,MAAO;AAEvE,gBAAK,SAAS,MAAQ,SAAQ,KAAM,SAAS,MAAM,MAAM,MAAO;AAEhE,iBAAK,YAAa,EAAE,MAAM,UAAU,IAAI,QAAQ,IAAI,SAAS,GAAG,OAAQ;AAAA,UAEzE,SAAU,OAAQ;AAEjB,oBAAQ,MAAO,KAAM;AAErB,iBAAK,YAAa,EAAE,MAAM,SAAS,IAAI,QAAQ,IAAI,OAAO,MAAM,QAAQ,CAAE;AAAA,UAE3E,UAAE;AAED,kBAAM,QAAS,OAAQ;AAAA,UAExB;AAAA,QAED,CAAE;AACF;AAAA,IAEF;AAAA,EAED;AAEA,WAAS,eAAgB,OAAO,SAAS,OAAO,YAAa;AAE5D,UAAM,eAAe,WAAW;AAChC,UAAM,iBAAiB,WAAW;AAElC,QAAI;AACJ,QAAI;AAEJ,UAAM,eAAe,QAAQ,uBAAwB,KAAM;AAE3D,QAAK,iBAAiB,MAAM,iBAAkB;AAE7C,sBAAgB,IAAI,MAAM,KAAK;AAC/B,uBAAiB,QAAQ,kBAAmB,OAAO,MAAM,YAAY,aAAc;AAAA,IAEpF,WAAY,iBAAiB,MAAM,aAAc;AAEhD,sBAAgB,IAAI,MAAM,WAAW;AACrC,uBAAiB,QAAQ,wBAAyB,OAAO,MAAM,YAAY,aAAc;AAAA,IAE1F,OAAO;AAEN,YAAM,IAAI,MAAO,8CAA+C;AAAA,IAEjE;AAEA,QAAK,CAAE,eAAe,GAAG,KAAK,cAAc,QAAQ,GAAI;AAEvD,YAAM,IAAI,MAAO,yCAAyC,eAAe,UAAU,CAAE;AAAA,IAEtF;AAEA,UAAM,WAAW,EAAE,OAAO,MAAM,YAAY,CAAC,EAAE;AAG/C,eAAY,iBAAiB,cAAe;AAE3C,YAAM,gBAAgB,KAAM,eAAgB,aAAc,CAAE;AAE5D,UAAI;AACJ,UAAI;AAMJ,UAAK,WAAW,cAAe;AAE9B,sBAAc,aAAc,aAAc;AAC1C,oBAAY,QAAQ,uBAAwB,eAAe,WAAY;AAAA,MAExE,OAAO;AAEN,sBAAc,QAAQ,eAAgB,eAAe,MAAO,aAAc,aAAc,CAAE,CAAE;AAE5F,YAAK,gBAAgB,GAAM;AAE3B,oBAAY,QAAQ,aAAc,eAAe,WAAY;AAAA,MAE9D;AAEA,YAAM,kBAAkB,gBAAiB,OAAO,SAAS,eAAe,eAAe,eAAe,SAAU;AAEhH,UAAK,kBAAkB,SAAU;AAEhC,wBAAgB,mBAAmB,WAAW;AAAA,MAE/C;AAEA,eAAS,WAAW,KAAM,eAAgB;AAAA,IAE3C;AAGA,QAAK,iBAAiB,MAAM,iBAAkB;AAE7C,eAAS,QAAQ,YAAa,OAAO,SAAS,aAAc;AAAA,IAE7D;AAEA,UAAM,QAAS,aAAc;AAE7B,WAAO;AAAA,EAER;AAEA,WAAS,YAAa,OAAO,SAAS,eAAgB;AAErD,UAAM,WAAW,cAAc,UAAU;AACzC,UAAM,aAAa,WAAW;AAC9B,UAAM,aAAa,aAAa;AAEhC,UAAM,MAAM,MAAM,QAAS,UAAW;AACtC,YAAQ,wBAAyB,eAAe,YAAY,GAAI;AAChE,UAAM,QAAQ,IAAI,YAAa,MAAM,QAAQ,QAAQ,KAAK,UAAW,EAAE,MAAM;AAC7E,UAAM,MAAO,GAAI;AAEjB,WAAO,EAAE,OAAO,OAAO,UAAU,EAAE;AAAA,EAEpC;AAEA,WAAS,gBAAiB,OAAO,SAAS,eAAe,eAAe,eAAe,WAAY;AAElG,UAAM,gBAAgB,UAAU,eAAe;AAC/C,UAAM,YAAY,cAAc,WAAW;AAC3C,UAAM,YAAY,YAAY;AAC9B,UAAM,aAAa,YAAY,cAAc;AAC7C,UAAM,WAAW,iBAAkB,OAAO,aAAc;AAExD,UAAM,MAAM,MAAM,QAAS,UAAW;AACtC,YAAQ,kCAAmC,eAAe,WAAW,UAAU,YAAY,GAAI;AAC/F,UAAM,QAAQ,IAAI,cAAe,MAAM,QAAQ,QAAQ,KAAK,SAAU,EAAE,MAAM;AAC9E,UAAM,MAAO,GAAI;AAEjB,WAAO;AAAA,MACN,MAAM;AAAA,MACN;AAAA,MACA,UAAU;AAAA,IACX;AAAA,EAED;AAEA,WAAS,iBAAkB,OAAO,eAAgB;AAEjD,YAAS,eAAgB;AAAA,MAExB,KAAK;AAAc,eAAO,MAAM;AAAA,MAChC,KAAK;AAAW,eAAO,MAAM;AAAA,MAC7B,KAAK;AAAY,eAAO,MAAM;AAAA,MAC9B,KAAK;AAAY,eAAO,MAAM;AAAA,MAC9B,KAAK;AAAY,eAAO,MAAM;AAAA,MAC9B,KAAK;AAAa,eAAO,MAAM;AAAA,MAC/B,KAAK;AAAa,eAAO,MAAM;AAAA,IAEhC;AAAA,EAED;AAED;", "names": ["worker"]}