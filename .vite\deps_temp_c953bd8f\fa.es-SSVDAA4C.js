import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/fa.es.js
var weekDays = [
  "دوشنبه",
  "سه شنبه",
  "چهار شنبه",
  "پنج شنبه",
  "جمعه",
  "شنبه",
  "یک شنبه"
];
var months = [
  "ژانویه",
  "فوریه",
  "مارس",
  "آوریل",
  "می",
  "ژوئن",
  "ژوئیه",
  "اوت",
  "سپتامبر",
  "اکتبر",
  "نوامبر",
  "دسامبر"
];
var years = "سالها";
var year = "سال";
var month = "ماه";
var week = "هفته";
var day = "روز";
var today = "امروز";
var noEvent = "رویدادی نیست";
var allDay = "تمام روز";
var deleteEvent = "حذف";
var createEvent = "ایجاد یک رویداد";
var dateFormat = "dddd D MMMM YYYY";
var fa = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  fa as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/fa.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=fa.es-SSVDAA4C.js.map
