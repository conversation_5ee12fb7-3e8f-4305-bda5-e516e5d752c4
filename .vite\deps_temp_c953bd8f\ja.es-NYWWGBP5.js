import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/ja.es.js
var weekDays = [
  "月",
  "火",
  "水",
  "木",
  "金",
  "土",
  "日"
];
var months = [
  "1月",
  "2月",
  "3月",
  "4月",
  "5月",
  "6月",
  "7月",
  "8月",
  "9月",
  "10月",
  "11月",
  "12月"
];
var years = "年";
var year = "今年";
var month = "月";
var week = "週";
var day = "日";
var today = "今日";
var noEvent = "イベントなし";
var allDay = "終日";
var deleteEvent = "削除";
var createEvent = "イベント作成";
var dateFormat = "YYYY年 MMMM D日 (dddd)";
var ja = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  ja as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/ja.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=ja.es-NYWWGBP5.js.map
