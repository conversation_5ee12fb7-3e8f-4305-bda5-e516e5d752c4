import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/ca.es.js
var weekDays = [
  "Dill<PERSON>",
  "<PERSON>marts",
  "<PERSON>me<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "Divendres",
  "<PERSON><PERSON>bt<PERSON>",
  "<PERSON><PERSON><PERSON>"
];
var weekDaysShort = [
  "Dl",
  "Dt",
  "Dc",
  "Dj",
  "Dv",
  "Ds",
  "Dg"
];
var months = [
  "Gener",
  "Febrer",
  "Març",
  "Abril",
  "Maig",
  "<PERSON>y",
  "<PERSON>l",
  "Agost",
  "Setembre",
  "Octubre",
  "Novembre",
  "Desembre"
];
var years = "Anys";
var year = "Any";
var month = "Mes";
var week = "Setmana";
var day = "Dia";
var today = "Avui";
var noEvent = "No hi ha esdeveniments";
var allDay = "Tot el dia";
var deleteEvent = "Eliminar";
var createEvent = "Crear un esdeveniment";
var dateFormat = "dddd D MMMM YYYY";
var ca = {
  weekDays,
  weekDaysShort,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  ca as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  weekDaysShort,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/ca.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=ca.es-IQVCWLQM.js.map
