<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Signup Prefill</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-step {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 200px;
        }
    </style>
</head>
<body>
    <h1>Signup Form Prefill Test</h1>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <p>To test the signup form prefill functionality:</p>
        <ol>
            <li>Open the application at <a href="http://localhost:4005" target="_blank">http://localhost:4005</a></li>
            <li>Click on "Sign Up" to access the signup form</li>
            <li>Fill out the first step (First Name and Last Name) and click "Next"</li>
            <li>Fill out the second step (Email) and click "Next"</li>
            <li>Fill out the third step (Organization Role) and click "Next"</li>
            <li>Now click the "Back" button multiple times</li>
            <li>Verify that previously entered values are prefilled in each step</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Expected Behavior</h2>
        <div class="test-step">
            <strong>Step 1 → Step 2 → Back to Step 1:</strong>
            <p>First Name and Last Name fields should be prefilled with previously entered values</p>
        </div>
        <div class="test-step">
            <strong>Step 2 → Step 3 → Back to Step 2:</strong>
            <p>Email field should be prefilled with previously entered value</p>
        </div>
        <div class="test-step">
            <strong>Step 3 → Step 4 → Back to Step 3:</strong>
            <p>Organization Role field should be prefilled with previously entered value</p>
        </div>
        <div class="test-step">
            <strong>Step 4 (Password) → Back to Step 3:</strong>
            <p>Organization Role field should be prefilled, and when going forward again, password fields should be prefilled</p>
        </div>
    </div>

    <div class="test-section">
        <h2>Debug Information</h2>
        <p>Check the browser console for debug logs that show:</p>
        <ul>
            <li><code>getCurrentInitialValues</code> - Shows what initial values are being set</li>
            <li><code>handleBackButton called</code> - Shows when back button is pressed</li>
            <li><code>handleBackButton after</code> - Shows the new step/substep after navigation</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Implementation Details</h2>
        <p>The fix includes:</p>
        <ul>
            <li>Added <code>getCurrentInitialValues()</code> helper function that returns appropriate initial values based on current step</li>
            <li>Added <code>:initial-values="getCurrentInitialValues()"</code> to both Form components</li>
            <li>Added <code>:key</code> attributes to force form re-rendering when navigating</li>
            <li>Form data is stored in <code>formData</code> reactive object and used to populate initial values</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Quick Test</h2>
        <p>Use this form to simulate the behavior:</p>
        <div id="test-form">
            <div id="step1" style="display: block;">
                <h3>Step 1: Personal Info</h3>
                <input type="text" id="firstName" placeholder="First Name" />
                <input type="text" id="lastName" placeholder="Last Name" />
                <button onclick="nextStep(1)">Next</button>
            </div>
            <div id="step2" style="display: none;">
                <h3>Step 2: Email</h3>
                <input type="email" id="email" placeholder="Email" />
                <button onclick="prevStep(2)">Back</button>
                <button onclick="nextStep(2)">Next</button>
            </div>
            <div id="step3" style="display: none;">
                <h3>Step 3: Organization Role</h3>
                <input type="text" id="role" placeholder="Organization Role" />
                <button onclick="prevStep(3)">Back</button>
                <button onclick="nextStep(3)">Next</button>
            </div>
            <div id="step4" style="display: none;">
                <h3>Step 4: Password</h3>
                <input type="password" id="password" placeholder="Password" />
                <input type="password" id="confirmPassword" placeholder="Confirm Password" />
                <button onclick="prevStep(4)">Back</button>
                <button onclick="submitForm()">Submit</button>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        const formData = {
            firstName: '',
            lastName: '',
            email: '',
            role: '',
            password: '',
            confirmPassword: ''
        };

        function showStep(step) {
            for (let i = 1; i <= 4; i++) {
                document.getElementById(`step${i}`).style.display = i === step ? 'block' : 'none';
            }
        }

        function saveCurrentStepData() {
            switch (currentStep) {
                case 1:
                    formData.firstName = document.getElementById('firstName').value;
                    formData.lastName = document.getElementById('lastName').value;
                    break;
                case 2:
                    formData.email = document.getElementById('email').value;
                    break;
                case 3:
                    formData.role = document.getElementById('role').value;
                    break;
                case 4:
                    formData.password = document.getElementById('password').value;
                    formData.confirmPassword = document.getElementById('confirmPassword').value;
                    break;
            }
        }

        function loadStepData(step) {
            switch (step) {
                case 1:
                    document.getElementById('firstName').value = formData.firstName;
                    document.getElementById('lastName').value = formData.lastName;
                    break;
                case 2:
                    document.getElementById('email').value = formData.email;
                    break;
                case 3:
                    document.getElementById('role').value = formData.role;
                    break;
                case 4:
                    document.getElementById('password').value = formData.password;
                    document.getElementById('confirmPassword').value = formData.confirmPassword;
                    break;
            }
        }

        function nextStep(step) {
            saveCurrentStepData();
            currentStep = step + 1;
            showStep(currentStep);
            loadStepData(currentStep);
            console.log('Next step:', currentStep, 'Form data:', formData);
        }

        function prevStep(step) {
            saveCurrentStepData();
            currentStep = step - 1;
            showStep(currentStep);
            loadStepData(currentStep);
            console.log('Previous step:', currentStep, 'Form data:', formData);
        }

        function submitForm() {
            saveCurrentStepData();
            alert('Form submitted! Check console for data.');
            console.log('Final form data:', formData);
        }
    </script>
</body>
</html>
