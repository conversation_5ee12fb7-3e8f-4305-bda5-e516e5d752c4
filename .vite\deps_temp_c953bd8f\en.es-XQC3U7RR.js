import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/en.es.js
var weekDays = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday"
];
var months = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December"
];
var years = "Years";
var year = "Year";
var month = "Month";
var week = "Week";
var day = "Day";
var today = "Today";
var noEvent = "No Event";
var allDay = "All day";
var deleteEvent = "Delete";
var createEvent = "Create an event";
var dateFormat = "dddd MMMM D{S}, YYYY";
var en = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  en as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/en.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=en.es-XQC3U7RR.js.map
