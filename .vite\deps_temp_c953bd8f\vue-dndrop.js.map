{"version": 3, "sources": ["../../node_modules/vue-dndrop/dist/vue-dndrop.esm.js"], "sourcesContent": ["/**\n * Bundle of: vue-dndrop\n * Generated: 2023-02-10\n * Version: 1.3.1\n */\n\nimport { h } from 'vue';\n\nvar containerInstance = 'dndrop-container-instance';\nvar wrapperClass = 'dndrop-draggable-wrapper';\nvar animationClass = 'animated';\nvar translationValue = '__dndrop_draggable_translation_value';\nvar visibilityValue = '__dndrop_draggable_visibility_value';\nvar ghostClass = 'dndrop-ghost';\n\nvar containerClass = 'dndrop-container';\n\nvar extraSizeForInsertion = 'dndrop-extra-size-for-insertion';\nvar stretcherElementClass = 'dndrop-stretcher-element';\nvar stretcherElementInstance = 'dndrop-stretcher-instance';\n\nvar disableTouchActions = 'dndrop-disable-touch-action';\nvar noUserSelectClass = 'dndrop-no-user-select';\n\nvar preventAutoScrollClass = 'dndrop-prevent-auto-scroll-class';\n\nvar dropPlaceholderDefaultClass = 'dndrop-drop-preview-default-class';\nvar dropPlaceholderInnerClass = 'dndrop-drop-preview-inner-class';\nvar dropPlaceholderWrapperClass = 'dndrop-drop-preview-constant-class';\nvar dropPlaceholderFlexContainerClass = 'dndrop-drop-preview-flex-container-class';\n\nvar defaultOptions = {\n  groupName: undefined,\n  behaviour: 'move', // move | copy\n  orientation: 'vertical', // vertical | horizontal\n  getChildPayload: undefined,\n  animationDuration: 250,\n  autoScrollEnabled: true,\n  shouldAcceptDrop: undefined,\n  shouldAnimateDrop: undefined,\n};\n\nvar removeChildAt = function (parent, index) {\n  return parent.removeChild(parent.children[index]);\n};\n\nvar addChildAt = function (parent, child, index) {\n  if (index >= parent.children.length) {\n    parent.appendChild(child);\n  } else {\n    parent.insertBefore(child, parent.children[index]);\n  }\n};\n\nfunction domDropHandler (ref) {\n  ref.element;\n  var draggables = ref.draggables;\n\n  return function (dropResult, onDrop) {\n    var removedIndex = dropResult.removedIndex;\n    var addedIndex = dropResult.addedIndex;\n    var element = dropResult.element;\n    var removedWrapper = null;\n    if (removedIndex !== null) {\n      removedWrapper = removeChildAt(element, removedIndex);\n      draggables.splice(removedIndex, 1);\n    }\n\n    if (addedIndex !== null) {\n      var wrapper = window.document.createElement('div');\n      wrapper.className = 'dndrop-draggable-wrapper';\n      wrapper.appendChild(\n        removedWrapper && removedWrapper.firstElementChild\n          ? removedWrapper.firstElementChild\n          : element\n      );\n      addChildAt(element, wrapper, addedIndex);\n      if (addedIndex >= draggables.length) {\n        draggables.push(wrapper);\n      } else {\n        draggables.splice(addedIndex, 0, wrapper);\n      }\n    }\n\n    if (onDrop) {\n      onDrop(dropResult);\n    }\n  };\n}\n\nfunction reactDropHandler () {\n  var handler = function () {\n    return function (dropResult, onDrop) {\n      if (onDrop) {\n        onDrop(dropResult);\n      }\n    };\n  };\n\n  return {\n    handler: handler,\n  };\n}\n\n/* eslint-disable no-useless-call */\nvar getIntersection = function (rect1, rect2) {\n  return {\n    left: Math.max(rect1.left, rect2.left),\n    top: Math.max(rect1.top, rect2.top),\n    right: Math.min(rect1.right, rect2.right),\n    bottom: Math.min(rect1.bottom, rect2.bottom),\n  };\n};\n\nvar ScrollAxis$1 = {\n  x: 'x',\n  y: 'y',\n  xy: 'xy'\n};\n\nvar getIntersectionOnAxis = function (rect1, rect2, axis) {\n  if (axis === 'x') {\n    return {\n      left: Math.max(rect1.left, rect2.left),\n      top: rect1.top,\n      right: Math.min(rect1.right, rect2.right),\n      bottom: rect1.bottom,\n    };\n  } else {\n    return {\n      left: rect1.left,\n      top: Math.max(rect1.top, rect2.top),\n      right: rect1.right,\n      bottom: Math.min(rect1.bottom, rect2.bottom),\n    };\n  }\n};\nvar getContainerRect = function (element) {\n  var _rect = element.getBoundingClientRect();\n  var rect = {\n    left: _rect.left,\n    right: _rect.right,\n    top: _rect.top,\n    bottom: _rect.bottom,\n  };\n  if (hasBiggerChild(element, 'x') && !isScrollingOrHidden(element, 'x')) {\n    var width = rect.right - rect.left;\n    rect.right = rect.right + element.scrollWidth - width;\n  }\n  if (hasBiggerChild(element, 'y') && !isScrollingOrHidden(element, 'y')) {\n    var height = rect.bottom - rect.top;\n    rect.bottom = rect.bottom + element.scrollHeight - height;\n  }\n  return rect;\n};\nvar getScrollingAxis = function (element) {\n  var style = window.getComputedStyle(element);\n  var overflow = style.overflow;\n  var general = overflow === 'auto' || overflow === 'scroll';\n  if (general) { return ScrollAxis$1.xy; }\n  var overFlowX = style['overflow-x'];\n  var xScroll = overFlowX === 'auto' || overFlowX === 'scroll';\n  var overFlowY = style['overflow-y'];\n  var yScroll = overFlowY === 'auto' || overFlowY === 'scroll';\n  if (xScroll && yScroll) { return ScrollAxis$1.xy; }\n  if (xScroll) { return ScrollAxis$1.x; }\n  if (yScroll) { return ScrollAxis$1.y; }\n  return null;\n};\nvar isScrolling = function (element, axis) {\n  var style = window.getComputedStyle(element);\n  var overflow = style.overflow;\n  var overFlowAxis = style[(\"overflow-\" + axis)];\n  var general = overflow === 'auto' || overflow === 'scroll';\n  var dimensionScroll = overFlowAxis === 'auto' || overFlowAxis === 'scroll';\n  return general || dimensionScroll;\n};\nvar isScrollingOrHidden = function (element, axis) {\n  var style = window.getComputedStyle(element);\n  var overflow = style.overflow;\n  var overFlowAxis = style[(\"overflow-\" + axis)];\n  var general = overflow === 'auto' || overflow === 'scroll' || overflow === 'hidden';\n  var dimensionScroll = overFlowAxis === 'auto' || overFlowAxis === 'scroll' || overFlowAxis === 'hidden';\n  return general || dimensionScroll;\n};\nvar hasBiggerChild = function (element, axis) {\n  if (axis === 'x') {\n    return element.scrollWidth > element.clientWidth;\n  } else {\n    return element.scrollHeight > element.clientHeight;\n  }\n};\nvar getVisibleRect = function (element, elementRect) {\n  var currentElement = element;\n  var rect = elementRect || getContainerRect(element);\n  currentElement = element.parentElement;\n  while (currentElement) {\n    if (hasBiggerChild(currentElement, 'x') && isScrollingOrHidden(currentElement, 'x')) {\n      rect = getIntersectionOnAxis(rect, currentElement.getBoundingClientRect(), 'x');\n    }\n    if (hasBiggerChild(currentElement, 'y') && isScrollingOrHidden(currentElement, 'y')) {\n      rect = getIntersectionOnAxis(rect, currentElement.getBoundingClientRect(), 'y');\n    }\n    currentElement = currentElement.parentElement;\n  }\n  return rect;\n};\nvar getParentRelevantContainerElement = function (element, relevantContainers) {\n  var current = element;\n  while (current) {\n    if (current[containerInstance]) {\n      var container = current[containerInstance];\n      if (relevantContainers.some(function (p) { return p === container; })) {\n        return container;\n      }\n    }\n    current = current.parentElement;\n  }\n  return null;\n};\nvar listenScrollParent = function (element, clb) {\n  var scrollers = [];\n  setScrollers();\n  function setScrollers () {\n    var currentElement = element;\n    while (currentElement) {\n      if (isScrolling(currentElement, 'x') || isScrolling(currentElement, 'y')) {\n        scrollers.push(currentElement);\n      }\n      currentElement = currentElement.parentElement;\n    }\n  }\n  function dispose () {\n    stop();\n    scrollers = null;\n  }\n  function start () {\n    if (scrollers) {\n      scrollers.forEach(function (p) { return p.addEventListener('scroll', clb); });\n      window.addEventListener('scroll', clb);\n    }\n  }\n  function stop () {\n    if (scrollers) {\n      scrollers.forEach(function (p) { return p.removeEventListener('scroll', clb); });\n      window.removeEventListener('scroll', clb);\n    }\n  }\n  return {\n    dispose: dispose,\n    start: start,\n    stop: stop\n  };\n};\nvar getParent = function (element, selector) {\n  var current = element;\n  while (current) {\n    if (current.matches(selector)) {\n      return current;\n    }\n    current = current.parentElement;\n  }\n  return null;\n};\nvar hasClass = function (element, cls) {\n  return (element.className\n    .split(' ')\n    .map(function (p) { return p; })\n    .indexOf(cls) > -1);\n};\nvar addClass = function (element, cls) {\n  if (element) {\n    var classes = element.className.split(' ').filter(function (p) { return p; });\n    if (classes.indexOf(cls) === -1) {\n      classes.unshift(cls);\n      element.className = classes.join(' ');\n    }\n  }\n};\nvar removeClass = function (element, cls) {\n  if (element) {\n    var classes = element.className.split(' ').filter(function (p) { return p && p !== cls; });\n    element.className = classes.join(' ');\n  }\n};\nvar debounce = function (fn, delay, immediate) {\n  var timer = null;\n  return function () {\n    var params = [], len = arguments.length;\n    while ( len-- ) params[ len ] = arguments[ len ];\n\n    if (timer) {\n      clearTimeout(timer);\n    }\n    if (immediate && !timer) {\n      fn.call.apply(fn, [ null ].concat( params ));\n    } else {\n      timer = setTimeout(function () {\n        timer = null;\n        fn.call.apply(fn, [ null ].concat( params ));\n      }, delay);\n    }\n  };\n};\nvar isMobile$1 = function () {\n  if (typeof window !== 'undefined') {\n    if (window.navigator.userAgent.match(/Android/i) ||\n            window.navigator.userAgent.match(/webOS/i) ||\n            window.navigator.userAgent.match(/iPhone/i) ||\n            window.navigator.userAgent.match(/iPad/i) ||\n            window.navigator.userAgent.match(/iPod/i) ||\n            window.navigator.userAgent.match(/BlackBerry/i) ||\n            window.navigator.userAgent.match(/Windows Phone/i)) {\n      return true;\n    } else {\n      return false;\n    }\n  }\n  return false;\n};\nvar clearSelection = function () {\n  if (window.getSelection) {\n    // @ts-ignore: Object is possibly 'null'.\n    if (window.getSelection().empty) {\n      // Chrome\n      // @ts-ignore: Object is possibly 'null'.\n      window.getSelection().empty();\n      // @ts-ignore: Object is possibly 'null'.\n    } else if (window.getSelection().removeAllRanges) {\n      // Firefox\n      // @ts-ignore: Object is possibly 'null'.\n      window.getSelection().removeAllRanges();\n    }\n  } else if (window.document.selection) {\n    // IE?\n    window.document.selection.empty();\n  }\n};\nvar getElementCursor = function (element) {\n  if (element) {\n    var style = window.getComputedStyle(element);\n    if (style) {\n      return style.cursor;\n    }\n  }\n  return null;\n};\nfunction isVisible (rect) {\n  return !(rect.bottom <= rect.top || rect.right <= rect.left);\n}\n\n/* eslint-disable no-undef */\nvar horizontalMap = {\n  size: 'offsetWidth',\n  distanceToParent: 'offsetLeft',\n  translate: 'transform',\n  begin: 'left',\n  end: 'right',\n  dragPosition: 'x',\n  scrollSize: 'scrollWidth',\n  offsetSize: 'offsetWidth',\n  scrollValue: 'scrollLeft',\n  scale: 'scaleX',\n  setSize: 'width',\n  setters: {\n    translate: function (val) { return (\"translate3d(\" + val + \"px, 0, 0)\"); }\n  }\n};\nvar verticalMap = {\n  size: 'offsetHeight',\n  distanceToParent: 'offsetTop',\n  translate: 'transform',\n  begin: 'top',\n  end: 'bottom',\n  dragPosition: 'y',\n  scrollSize: 'scrollHeight',\n  offsetSize: 'offsetHeight',\n  scrollValue: 'scrollTop',\n  scale: 'scaleY',\n  setSize: 'height',\n  setters: {\n    translate: function (val) { return (\"translate3d(0,\" + val + \"px, 0)\"); }\n  }\n};\nfunction orientationDependentProps (map) {\n  function get (obj, prop) {\n    var mappedProp = map[prop];\n    return obj[mappedProp || prop];\n  }\n  function set (obj, prop, value) {\n    obj[map[prop]] = map.setters[prop] ? map.setters[prop](value) : value;\n  }\n  return { get: get, set: set };\n}\nfunction layoutManager (containerElement, orientation, _animationDuration) {\n  containerElement[extraSizeForInsertion] = 0;\n  var map = orientation === 'horizontal' ? horizontalMap : verticalMap;\n  var propMapper = orientationDependentProps(map);\n  var values = {\n    translation: 0\n  };\n  window.addEventListener('resize', function () {\n    invalidateContainerRectangles(containerElement);\n  });\n  setTimeout(function () {\n    invalidate();\n  }, 10);\n  function invalidate () {\n    invalidateContainerRectangles(containerElement);\n    invalidateContainerScale(containerElement);\n  }\n  function invalidateContainerRectangles (containerElement) {\n    values.rect = getContainerRect(containerElement);\n    var visibleRect = getVisibleRect(containerElement, values.rect);\n    if (isVisible(visibleRect)) {\n      values.lastVisibleRect = values.visibleRect;\n    }\n    values.visibleRect = visibleRect;\n  }\n  function invalidateContainerScale (containerElement) {\n    var rect = containerElement.getBoundingClientRect();\n    values.scaleX = containerElement.offsetWidth ? ((rect.right - rect.left) / containerElement.offsetWidth) : 1;\n    values.scaleY = containerElement.offsetHeight ? ((rect.bottom - rect.top) / containerElement.offsetHeight) : 1;\n  }\n  function getContainerRectangles () {\n    return {\n      rect: values.rect,\n      visibleRect: values.visibleRect,\n      lastVisibleRect: values.lastVisibleRect\n    };\n  }\n  function getBeginEndOfDOMRect (rect) {\n    return {\n      begin: propMapper.get(rect, 'begin'),\n      end: propMapper.get(rect, 'end')\n    };\n  }\n  function getBeginEndOfContainer () {\n    var begin = propMapper.get(values.rect, 'begin') + values.translation;\n    var end = propMapper.get(values.rect, 'end') + values.translation;\n    return { begin: begin, end: end };\n  }\n  function getBeginEndOfContainerVisibleRect () {\n    var begin = propMapper.get(values.visibleRect, 'begin') + values.translation;\n    var end = propMapper.get(values.visibleRect, 'end') + values.translation;\n    return { begin: begin, end: end };\n  }\n  function getSize (element) {\n    var htmlElement = element;\n    if (htmlElement.tagName) {\n      var rect = htmlElement.getBoundingClientRect();\n      return orientation === 'vertical' ? rect.bottom - rect.top : rect.right - rect.left;\n    }\n    return propMapper.get(element, 'size') * propMapper.get(values, 'scale');\n  }\n  function getDistanceToOffsetParent (element) {\n    var distance = propMapper.get(element, 'distanceToParent') + (element[translationValue] || 0);\n    return distance * propMapper.get(values, 'scale');\n  }\n  function getBeginEnd (element) {\n    var begin = getDistanceToOffsetParent(element) + (propMapper.get(values.rect, 'begin') + values.translation) - propMapper.get(containerElement, 'scrollValue');\n    return {\n      begin: begin,\n      end: begin + getSize(element) * propMapper.get(values, 'scale')\n    };\n  }\n  function setSize (element, size) {\n    propMapper.set(element, 'setSize', size);\n  }\n  function getAxisValue (position) {\n    return propMapper.get(position, 'dragPosition');\n  }\n  function setTranslation (element, translation) {\n    if (!translation) {\n      element.style.removeProperty('transform');\n    } else {\n      propMapper.set(element.style, 'translate', translation);\n    }\n    element[translationValue] = translation;\n  }\n  function getTranslation (element) {\n    return element[translationValue];\n  }\n  function setVisibility (element, isVisible) {\n    if (element[visibilityValue] === undefined || element[visibilityValue] !== isVisible) {\n      if (isVisible) {\n        element.style.removeProperty('visibility');\n      } else {\n        element.style.visibility = 'hidden';\n      }\n      element[visibilityValue] = isVisible;\n    }\n  }\n  function isVisible$1 (element) {\n    return element[visibilityValue] === undefined || element[visibilityValue];\n  }\n  function isInVisibleRect (x, y) {\n    var ref = values.visibleRect;\n    var left = ref.left;\n    var top = ref.top;\n    var right = ref.right;\n    var bottom = ref.bottom;\n    // if there is no wrapper in rect size will be 0 and wont accept any drop\n    // so make sure at least there is 30px difference\n    if (bottom - top < 2) {\n      bottom = top + 30;\n    }\n    var containerRect = values.rect;\n    if (orientation === 'vertical') {\n      return x > containerRect.left && x < containerRect.right && y > top && y < bottom;\n    } else {\n      return x > left && x < right && y > containerRect.top && y < containerRect.bottom;\n    }\n  }\n  function getTopLeftOfElementBegin (begin) {\n    var top = 0;\n    var left = 0;\n    if (orientation === 'horizontal') {\n      left = begin;\n      top = values.rect.top;\n    } else {\n      left = values.rect.left;\n      top = begin;\n    }\n    return {\n      top: top, left: left\n    };\n  }\n  function getScrollSize (element) {\n    return propMapper.get(element, 'scrollSize');\n  }\n  function getScrollValue (element) {\n    return propMapper.get(element, 'scrollValue');\n  }\n  function setScrollValue (element, val) {\n    return propMapper.set(element, 'scrollValue', val);\n  }\n  function getPosition (position) {\n    return getAxisValue(position);\n  }\n  function invalidateRects () {\n    invalidateContainerRectangles(containerElement);\n  }\n  function setBegin (style, value) {\n    propMapper.set(style, 'begin', value);\n  }\n  return {\n    getSize: getSize,\n    getContainerRectangles: getContainerRectangles,\n    getBeginEndOfDOMRect: getBeginEndOfDOMRect,\n    getBeginEndOfContainer: getBeginEndOfContainer,\n    getBeginEndOfContainerVisibleRect: getBeginEndOfContainerVisibleRect,\n    getBeginEnd: getBeginEnd,\n    getAxisValue: getAxisValue,\n    setTranslation: setTranslation,\n    getTranslation: getTranslation,\n    setVisibility: setVisibility,\n    isVisible: isVisible$1,\n    isInVisibleRect: isInVisibleRect,\n    setSize: setSize,\n    getTopLeftOfElementBegin: getTopLeftOfElementBegin,\n    getScrollSize: getScrollSize,\n    getScrollValue: getScrollValue,\n    setScrollValue: setScrollValue,\n    invalidate: invalidate,\n    invalidateRects: invalidateRects,\n    getPosition: getPosition,\n    setBegin: setBegin,\n  };\n}\n\n/* eslint-disable no-lone-blocks */\nvar maxSpeed = 1500; // px/s\n\nvar ScrollAxis = {\n  x: 'x',\n  y: 'y',\n  xy: 'xy'\n};\nfunction getScrollParams (position, axis, rect) {\n  var left = rect.left;\n  var right = rect.right;\n  var top = rect.top;\n  var bottom = rect.bottom;\n  var x = position.x;\n  var y = position.y;\n  if (x < left || x > right || y < top || y > bottom) {\n    return null;\n  }\n  var begin;\n  var end;\n  var pos;\n  if (axis === 'x') {\n    begin = left;\n    end = right;\n    pos = x;\n  } else {\n    begin = top;\n    end = bottom;\n    pos = y;\n  }\n  var scrollerSize = end - begin;\n  var moveDistance = scrollerSize > 400 ? 100 : scrollerSize / 4;\n  if (end - pos < moveDistance) {\n    return {\n      direction: 'end',\n      speedFactor: (moveDistance - (end - pos)) / moveDistance\n    };\n  } else if (pos - begin < moveDistance) {\n    return {\n      direction: 'begin',\n      speedFactor: (moveDistance - (pos - begin)) / moveDistance\n    };\n  }\n  return null;\n}\nfunction addScrollValue (element, axis, value) {\n  if (element) {\n    if (element !== window) {\n      if (axis === 'x') {\n        element.scrollLeft += value;\n      } else {\n        element.scrollTop += value;\n      }\n    } else {\n      if (axis === 'x') {\n        element.scrollBy(value, 0);\n      } else {\n        element.scrollBy(0, value);\n      }\n    }\n  }\n}\nvar createAnimator = function (element, axis) {\n  if ( axis === void 0 ) axis = 'y';\n\n  var request = null;\n  var startTime = null;\n  var direction = null;\n  var speed = null;\n  function animate (_direction, _speed) {\n    direction = _direction;\n    speed = _speed;\n    start();\n  }\n  function start () {\n    if (request === null) {\n      request = requestAnimationFrame(function (timestamp) {\n        if (startTime === null) {\n          startTime = timestamp;\n        }\n        var timeDiff = timestamp - startTime;\n        startTime = timestamp;\n        var distanceDiff = (timeDiff / 1000) * speed;\n        distanceDiff = direction === 'begin' ? (0 - distanceDiff) : distanceDiff;\n        addScrollValue(element, axis, distanceDiff);\n        request = null;\n        start();\n      });\n    }\n  }\n  function stop () {\n    if (request !== null) {\n      cancelAnimationFrame(request);\n      request = null;\n    }\n    startTime = null;\n  }\n  return {\n    animate: animate,\n    stop: stop\n  };\n};\nfunction rectangleGetter (element) {\n  return function () {\n    return getVisibleRect(element, element.getBoundingClientRect());\n  };\n}\nfunction getScrollerAnimator (container) {\n  var scrollerAnimators = [];\n  var current = container.element;\n  while (current) {\n    var scrollingAxis = getScrollingAxis(current);\n    if (scrollingAxis && !hasClass(current, preventAutoScrollClass)) {\n      var axisAnimations = {};\n      switch (scrollingAxis) {\n        case ScrollAxis.xy:\n          {\n            axisAnimations.x = {\n              animator: createAnimator(current, 'x'),\n            };\n            axisAnimations.y = {\n              animator: createAnimator(current, 'y'),\n            };\n          }\n          break;\n        case ScrollAxis.x:\n          {\n            axisAnimations.x = {\n              animator: createAnimator(current, 'x'),\n            };\n          }\n          break;\n        case ScrollAxis.y:\n          {\n            axisAnimations.y = {\n              animator: createAnimator(current, 'y'),\n            };\n          }\n          break;\n      }\n      scrollerAnimators.push({\n        axisAnimations: axisAnimations,\n        getRect: rectangleGetter(current),\n        scrollerElement: current,\n      });\n    }\n    current = current.parentElement;\n  }\n  return scrollerAnimators;\n}\nfunction setScrollParams (animatorInfos, position) {\n  animatorInfos.forEach(function (animator) {\n    var axisAnimations = animator.axisAnimations;\n    var getRect = animator.getRect;\n    var rect = getRect();\n    if (axisAnimations.x) {\n      axisAnimations.x.scrollParams = getScrollParams(position, 'x', rect);\n      animator.cachedRect = rect;\n    }\n    if (axisAnimations.y) {\n      axisAnimations.y.scrollParams = getScrollParams(position, 'y', rect);\n      animator.cachedRect = rect;\n    }\n  });\n}\nfunction getTopmostScrollAnimator (animatorInfos, position) {\n  var current = document.elementFromPoint(position.x, position.y);\n  while (current) {\n    var scrollAnimator = animatorInfos.find(function (p) { return p.scrollerElement === current; });\n    if (scrollAnimator) {\n      return scrollAnimator;\n    }\n    current = current.parentElement;\n  }\n  return null;\n}\nfunction dragScroller (containers, maxScrollSpeed) {\n  if ( maxScrollSpeed === void 0 ) maxScrollSpeed = maxSpeed;\n\n  var animatorInfos = containers.reduce(function (acc, container) {\n    var filteredAnimators = getScrollerAnimator(container).filter(function (p) {\n      return !acc.find(function (q) { return q.scrollerElement === p.scrollerElement; });\n    });\n    return acc.concat( filteredAnimators);\n  }, []);\n  return function (ref) {\n    var draggableInfo = ref.draggableInfo;\n    var reset = ref.reset;\n\n    if (reset) {\n      animatorInfos.forEach(function (p) {\n        p.axisAnimations.x && p.axisAnimations.x.animator.stop();\n        p.axisAnimations.y && p.axisAnimations.y.animator.stop();\n      });\n      return;\n    }\n    if (draggableInfo) {\n      setScrollParams(animatorInfos, draggableInfo.mousePosition);\n      animatorInfos.forEach(function (animator) {\n        var ref = animator.axisAnimations;\n        var x = ref.x;\n        var y = ref.y;\n        if (x) {\n          if (x.scrollParams) {\n            var ref$1 = x.scrollParams;\n            var direction = ref$1.direction;\n            var speedFactor = ref$1.speedFactor;\n            x.animator.animate(direction, speedFactor * maxScrollSpeed);\n          } else {\n            x.animator.stop();\n          }\n        }\n        if (y) {\n          if (y.scrollParams) {\n            var ref$2 = y.scrollParams;\n            var direction$1 = ref$2.direction;\n            var speedFactor$1 = ref$2.speedFactor;\n            y.animator.animate(direction$1, speedFactor$1 * maxScrollSpeed);\n          } else {\n            y.animator.stop();\n          }\n        }\n      });\n      var overlappingAnimators = animatorInfos.filter(function (p) { return p.cachedRect; });\n      if (overlappingAnimators.length && overlappingAnimators.length > 1) {\n        // stop animations except topmost\n        var topScrollerAnimator = getTopmostScrollAnimator(overlappingAnimators, draggableInfo.mousePosition);\n        if (topScrollerAnimator) {\n          overlappingAnimators.forEach(function (p) {\n            if (p !== topScrollerAnimator) {\n              p.axisAnimations.x && p.axisAnimations.x.animator.stop();\n              p.axisAnimations.y && p.axisAnimations.y.animator.stop();\n            }\n          });\n        }\n      }\n    }\n  };\n}\n\n/* eslint-disable no-void */\n/* eslint-disable no-empty */\n/* eslint-disable no-extend-native */\nfunction applyPolyfills () {\n  (function (constructor) {\n    if (constructor && constructor.prototype && !constructor.prototype.matches) {\n      constructor.prototype.matches =\n                constructor.prototype.matchesSelector ||\n                    constructor.prototype.mozMatchesSelector ||\n                    constructor.prototype.msMatchesSelector ||\n                    constructor.prototype.oMatchesSelector ||\n                    constructor.prototype.webkitMatchesSelector ||\n                    function (s) {\n                      var matches = (this.document || this.ownerDocument).querySelectorAll(s); var i = matches.length;\n                      while (--i >= 0 && matches.item(i) !== this) { }\n                      return i > -1;\n                    };\n    }\n  })(Element);\n  // Production steps of ECMA-262, Edition 5, 15.4.4.17\n  // Reference: http://es5.github.io/#x15.4.4.17\n  if (!Array.prototype.some) {\n    Array.prototype.some = function (fun /*, thisArg */) {\n      if (this == null) {\n        throw new TypeError('Array.prototype.some called on null or undefined');\n      }\n      if (typeof fun !== 'function') {\n        throw new TypeError();\n      }\n      var t = Object(this);\n      var len = t.length >>> 0;\n      var thisArg = arguments.length >= 2 ? arguments[1] : void 0;\n      for (var i = 0; i < len; i++) {\n        if (i in t && fun.call(thisArg, t[i], i, t)) {\n          return true;\n        }\n      }\n      return false;\n    };\n  }\n}\nif (typeof window !== 'undefined') {\n  applyPolyfills();\n}\n\nvar verticalWrapperClass = {\n  overflow: 'hidden',\n  display: 'block'\n};\nvar horizontalWrapperClass = {\n  height: '100%',\n  display: 'table-cell',\n  'vertical-align': 'top',\n};\nvar stretcherElementHorizontalClass = {\n  display: 'inline-block'\n};\nvar css = {};\ncss[(\".\" + containerClass)] = {\n    position: 'relative',\n    'min-height': '30px',\n    'min-width': '30px'\n  };\ncss[(\".\" + containerClass + \".horizontal\")] = {\n    display: 'table',\n  };\ncss[(\".\" + containerClass + \".horizontal > .\" + stretcherElementClass)] = stretcherElementHorizontalClass;\ncss[(\".\" + containerClass + \".horizontal > .\" + wrapperClass)] = horizontalWrapperClass;\ncss[(\".\" + containerClass + \".vertical > .\" + wrapperClass)] = verticalWrapperClass;\ncss[(\".\" + wrapperClass)] = {\n    'box-sizing': 'border-box'\n  };\ncss[(\".\" + wrapperClass + \".horizontal\")] = horizontalWrapperClass;\ncss[(\".\" + wrapperClass + \".vertical\")] = verticalWrapperClass;\ncss[(\".\" + wrapperClass + \".animated\")] = {\n    transition: 'transform ease',\n  };\ncss[(\".\" + ghostClass)] = {\n    'box-sizing': 'border-box',\n    // 'background-color': 'transparent',\n    // '-webkit-font-smoothing': 'subpixel-antialiased'\n  };\ncss[(\".\" + ghostClass + \".animated\")] = {\n    transition: 'all ease-in-out'\n  };\ncss[(\".\" + ghostClass + \" *\")] = {\n    'pointer-events': 'none'\n  };\ncss[(\".\" + disableTouchActions + \" *\")] = {\n    'touch-action': 'none',\n    '-ms-touch-action': 'none'\n  };\ncss[(\".\" + noUserSelectClass)] = {\n    '-webkit-touch-callout': 'none',\n    '-webkit-user-select': 'none',\n    '-khtml-user-select': 'none',\n    '-moz-user-select': 'none',\n    '-ms-user-select': 'none',\n    'user-select': 'none'\n  };\ncss[(\".\" + dropPlaceholderInnerClass)] = {\n    flex: '1'\n  };\ncss[(\".\" + containerClass + \".horizontal > .\" + dropPlaceholderWrapperClass)] = {\n    height: '100%',\n    overflow: 'hidden',\n    display: 'table-cell',\n    'vertical-align': 'top',\n  };\ncss[(\".\" + containerClass + \".vertical > .\" + dropPlaceholderWrapperClass)] = {\n    overflow: 'hidden',\n    display: 'block',\n    width: '100%',\n  };\ncss[(\".\" + dropPlaceholderFlexContainerClass)] = {\n    width: '100%',\n    height: '100%',\n    display: 'flex',\n    'justify-content': 'stretch',\n    'align-items': 'stretch'\n  };\ncss[(\".\" + dropPlaceholderDefaultClass)] = {\n    'background-color': 'rgba(150, 150, 150, 0.1)',\n    border: '1px solid #ccc',\n  };\nfunction convertToCssString (css) {\n  return Object.keys(css).reduce(function (styleString, propName) {\n    var propValue = css[propName];\n    if (typeof (propValue) === 'object') {\n      return (\"\" + styleString + propName + \"{\" + (convertToCssString(propValue)) + \"}\");\n    }\n    return (\"\" + styleString + propName + \":\" + propValue + \";\");\n  }, '');\n}\nfunction addStyleToHead () {\n  if (typeof (window) !== 'undefined') {\n    var head = window.document.head || window.document.getElementsByTagName('head')[0];\n    var style = window.document.createElement('style');\n    style.id = 'dndrop-style-definitions';\n    var cssString = convertToCssString(css);\n    style.type = 'text/css';\n    if (style.styleSheet) {\n      style.styleSheet.cssText = cssString;\n    } else {\n      style.appendChild(window.document.createTextNode(cssString));\n    }\n    head.appendChild(style);\n  }\n}\nfunction addCursorStyleToBody (cursor) {\n  if (cursor && typeof (window) !== 'undefined') {\n    var head = window.document.head || window.document.getElementsByTagName('head')[0];\n    var style = window.document.createElement('style');\n    var cssString = convertToCssString({\n      'body *': {\n        cursor: (cursor + \" !important\")\n      }\n    });\n    style.type = 'text/css';\n    if (style.styleSheet) {\n      style.styleSheet.cssText = cssString;\n    } else {\n      style.appendChild(window.document.createTextNode(cssString));\n    }\n    head.appendChild(style);\n    return style;\n  }\n  return null;\n}\nfunction removeStyle (styleElement) {\n  if (styleElement && typeof (window) !== 'undefined') {\n    var head = window.document.head || window.document.getElementsByTagName('head')[0];\n    head.removeChild(styleElement);\n  }\n}\n\nvar grabEvents = ['mousedown', 'touchstart'];\nvar moveEvents = ['mousemove', 'touchmove'];\nvar releaseEvents = ['mouseup', 'touchend'];\nvar dragListeningContainers = null;\nvar grabbedElement = null;\nvar ghostInfo = null;\nvar draggableInfo = null;\nvar containers = [];\nvar isDragging = false;\nvar isCanceling = false;\nvar dropAnimationStarted = false;\nvar missedDrag = false;\nvar handleDrag = null;\nvar handleScroll = null;\nvar sourceContainerLockAxis = null;\nvar cursorStyleElement = null;\nvar containerRectableWatcher = watchRectangles();\nvar isMobile = isMobile$1();\nfunction listenEvents () {\n  if (typeof window !== 'undefined') {\n    addGrabListeners();\n  }\n}\nfunction addGrabListeners () {\n  grabEvents.forEach(function (e) {\n    window.document.addEventListener(e, onMouseDown, { passive: false });\n  });\n}\n\nfunction removeGrabListeners () {\n  var touchstart = grabEvents[1];\n  window.document.removeEventListener(touchstart, onMouseDown, { passive: false });\n}\nfunction addMoveListeners () {\n  moveEvents.forEach(function (e) {\n    window.document.addEventListener(e, onMouseMove, { passive: false });\n  });\n}\nfunction removeMoveListeners () {\n  moveEvents.forEach(function (e) {\n    window.document.removeEventListener(e, onMouseMove, { passive: false });\n  });\n}\nfunction addReleaseListeners () {\n  releaseEvents.forEach(function (e) {\n    window.document.addEventListener(e, onMouseUp, { passive: false });\n  });\n}\nfunction removeReleaseListeners () {\n  releaseEvents.forEach(function (e) {\n    window.document.removeEventListener(e, onMouseUp, { passive: false });\n  });\n}\nfunction getGhostParent () {\n  if (draggableInfo && draggableInfo.ghostParent) {\n    return draggableInfo.ghostParent;\n  }\n  if (grabbedElement) {\n    return grabbedElement.parentElement || window.document.body;\n  } else {\n    return window.document.body;\n  }\n}\nfunction getGhostElement (wrapperElement, ref, container, cursor) {\n  var x = ref.x;\n  var y = ref.y;\n\n  var wrapperRect = wrapperElement.getBoundingClientRect();\n  var left = wrapperRect.left;\n  var top = wrapperRect.top;\n  var right = wrapperRect.right;\n  var bottom = wrapperRect.bottom;\n  var wrapperVisibleRect = getIntersection(container.layout.getContainerRectangles().visibleRect, wrapperRect);\n  var midX = wrapperVisibleRect.left + (wrapperVisibleRect.right - wrapperVisibleRect.left) / 2;\n  var midY = wrapperVisibleRect.top + (wrapperVisibleRect.bottom - wrapperVisibleRect.top) / 2;\n  var ghost = wrapperElement.cloneNode(true);\n  ghost.style.zIndex = '1000';\n  ghost.style.boxSizing = 'border-box';\n  ghost.style.position = 'fixed';\n  ghost.style.top = '0px';\n  ghost.style.left = '0px';\n  ghost.style.transform = 'none';\n  ghost.style.removeProperty('transform');\n  if (container.shouldUseTransformForGhost()) {\n    ghost.style.transform = \"translate3d(\" + left + \"px, \" + top + \"px, 0)\";\n  } else {\n    ghost.style.top = top + \"px\";\n    ghost.style.left = left + \"px\";\n  }\n  ghost.style.width = (right - left) + 'px';\n  ghost.style.height = (bottom - top) + 'px';\n  ghost.style.overflow = 'visible';\n  ghost.style.transition = null;\n  ghost.style.removeProperty('transition');\n  ghost.style.pointerEvents = 'none';\n  ghost.style.userSelect = 'none';\n  if (container.getOptions().dragClass) {\n    setTimeout(function () {\n      addClass(ghost.firstElementChild, container.getOptions().dragClass);\n      var dragCursor = window.getComputedStyle(ghost.firstElementChild).cursor;\n      cursorStyleElement = addCursorStyleToBody(dragCursor);\n    });\n  } else {\n    cursorStyleElement = addCursorStyleToBody(cursor);\n  }\n  addClass(ghost, container.getOptions().orientation || 'vertical');\n  addClass(ghost, ghostClass);\n  return {\n    ghost: ghost,\n    centerDelta: { x: midX - x, y: midY - y },\n    positionDelta: { left: left - x, top: top - y },\n    topLeft: {\n      x: left,\n      y: top\n    }\n  };\n}\nfunction getDraggableInfo (draggableElement) {\n  var container = containers.filter(function (p) { return draggableElement.parentElement === p.element; })[0];\n  var draggableIndex = container.draggables.indexOf(draggableElement);\n  var getGhostParent = container.getOptions().getGhostParent;\n  var draggableRect = draggableElement.getBoundingClientRect();\n  return {\n    container: container,\n    element: draggableElement,\n    size: {\n      offsetHeight: draggableRect.bottom - draggableRect.top,\n      offsetWidth: draggableRect.right - draggableRect.left,\n    },\n    elementIndex: draggableIndex,\n    payload: container.getOptions().getChildPayload ? container.getOptions().getChildPayload(draggableIndex) : undefined,\n    targetElement: null,\n    position: { x: 0, y: 0 },\n    groupName: container.getOptions().groupName,\n    ghostParent: getGhostParent ? getGhostParent() : null,\n    invalidateShadow: null,\n    mousePosition: null,\n    relevantContainers: null\n  };\n}\nfunction handleDropAnimation (callback) {\n  function endDrop () {\n    removeClass(ghostInfo.ghost, 'animated');\n    ghostInfo.ghost.style.transitionDuration = null;\n    getGhostParent().removeChild(ghostInfo.ghost);\n    callback();\n  }\n  function animateGhostToPosition (ref, duration, dropClass) {\n    var top = ref.top;\n    var left = ref.left;\n\n    addClass(ghostInfo.ghost, 'animated');\n    if (dropClass) {\n      addClass(ghostInfo.ghost.firstElementChild, dropClass);\n    }\n    ghostInfo.topLeft.x = left;\n    ghostInfo.topLeft.y = top;\n    translateGhost(duration);\n    setTimeout(function () {\n      endDrop();\n    }, duration + 20);\n  }\n  function shouldAnimateDrop (options) {\n    return options.shouldAnimateDrop\n      ? options.shouldAnimateDrop(draggableInfo.container.getOptions(), draggableInfo.payload)\n      : true;\n  }\n  function disappearAnimation (duration, clb) {\n    addClass(ghostInfo.ghost, 'animated');\n    translateGhost(duration, 0.9, true);\n    // ghostInfo.ghost.style.transitionDuration = duration + 'ms';\n    // ghostInfo.ghost.style.opacity = '0';\n    // ghostInfo.ghost.style.transform = 'scale(0.90)';\n    setTimeout(function () {\n      clb();\n    }, duration + 20);\n  }\n  if (draggableInfo.targetElement) {\n    var container = containers.filter(function (p) { return p.element === draggableInfo.targetElement; })[0];\n    if (shouldAnimateDrop(container.getOptions())) {\n      var dragResult = container.getDragResult();\n      animateGhostToPosition(dragResult.shadowBeginEnd.rect, Math.max(150, container.getOptions().animationDuration / 2), container.getOptions().dropClass);\n    } else {\n      endDrop();\n    }\n  } else {\n    var container$1 = containers.filter(function (p) { return p === draggableInfo.container; })[0];\n    if (container$1) {\n      var ref = container$1.getOptions();\n      var behaviour = ref.behaviour;\n      var removeOnDropOut = ref.removeOnDropOut;\n      if ((behaviour === 'move' || behaviour === 'contain') && (isCanceling || !removeOnDropOut) && container$1.getDragResult()) {\n        var rectangles = container$1.layout.getContainerRectangles();\n        // container is hidden somehow\n        // move ghost back to last seen position\n        if (!isVisible(rectangles.visibleRect) && isVisible(rectangles.lastVisibleRect)) {\n          animateGhostToPosition({\n            top: rectangles.lastVisibleRect.top,\n            left: rectangles.lastVisibleRect.left\n          }, container$1.getOptions().animationDuration, container$1.getOptions().dropClass);\n        } else {\n          var ref$1 = container$1.getDragResult();\n          var removedIndex = ref$1.removedIndex;\n          var elementSize = ref$1.elementSize;\n          var layout = container$1.layout;\n          // drag ghost to back\n          container$1.getTranslateCalculator({\n            dragResult: {\n              removedIndex: removedIndex,\n              addedIndex: removedIndex,\n              elementSize: elementSize,\n              pos: undefined,\n              shadowBeginEnd: undefined,\n            },\n          });\n          var prevDraggableEnd = removedIndex > 0\n            ? layout.getBeginEnd(container$1.draggables[removedIndex - 1]).end\n            : layout.getBeginEndOfContainer().begin;\n          animateGhostToPosition(layout.getTopLeftOfElementBegin(prevDraggableEnd), container$1.getOptions().animationDuration, container$1.getOptions().dropClass);\n        }\n      } else {\n        disappearAnimation(container$1.getOptions().animationDuration, endDrop);\n      }\n    } else {\n      // container is disposed due to removal\n      disappearAnimation(defaultOptions.animationDuration, endDrop);\n    }\n  }\n}\nvar handleDragStartConditions = (function handleDragStartConditions () {\n  var startEvent;\n  var delay;\n  var clb;\n  var timer = null;\n  var moveThreshold = 1;\n  var maxMoveInDelay = 5;\n  function onMove (event) {\n    var ref = getPointerEvent(event);\n    var currentX = ref.clientX;\n    var currentY = ref.clientY;\n    if (!delay) {\n      if (Math.abs(startEvent.clientX - currentX) > moveThreshold || Math.abs(startEvent.clientY - currentY) > moveThreshold) {\n        return callCallback();\n      }\n    } else {\n      if (Math.abs(startEvent.clientX - currentX) > maxMoveInDelay || Math.abs(startEvent.clientY - currentY) > maxMoveInDelay) {\n        deregisterEvent();\n      }\n    }\n  }\n  function onUp () {\n    deregisterEvent();\n  }\n  function onHTMLDrag () {\n    deregisterEvent();\n  }\n  function registerEvents () {\n    if (delay) {\n      timer = setTimeout(callCallback, delay);\n    }\n    moveEvents.forEach(function (e) { return window.document.addEventListener(e, onMove); }, {\n      passive: false,\n    });\n    releaseEvents.forEach(function (e) { return window.document.addEventListener(e, onUp); }, {\n      passive: false,\n    });\n    window.document.addEventListener('drag', onHTMLDrag, {\n      passive: false,\n    });\n  }\n  function deregisterEvent () {\n    clearTimeout(timer);\n    moveEvents.forEach(function (e) { return window.document.removeEventListener(e, onMove); }, {\n      passive: false,\n    });\n    releaseEvents.forEach(function (e) { return window.document.removeEventListener(e, onUp); }, {\n      passive: false,\n    });\n    window.document.removeEventListener('drag', onHTMLDrag, {\n      passive: false,\n    });\n  }\n  function callCallback () {\n    clearTimeout(timer);\n    deregisterEvent();\n    clb();\n  }\n  return function (_startEvent, _delay, _clb) {\n    startEvent = getPointerEvent(_startEvent);\n    delay = typeof _delay === 'number' ? _delay : isMobile ? 200 : 0;\n    clb = _clb;\n    registerEvents();\n  };\n})();\nfunction onMouseDown (event) {\n  var e = getPointerEvent(event);\n  if (!isDragging && (e.button === undefined || e.button === 0)) {\n    grabbedElement = getParent(e.target, '.' + wrapperClass);\n    if (grabbedElement) {\n      var containerElement = getParent(grabbedElement, '.' + containerClass);\n      var container = containers.filter(function (p) { return p.element === containerElement; })[0];\n      var dragHandleSelector = container.getOptions().dragHandleSelector;\n      var nonDragAreaSelector = container.getOptions().nonDragAreaSelector;\n      var startDrag = true;\n      if (dragHandleSelector && !getParent(e.target, dragHandleSelector)) {\n        startDrag = false;\n      }\n      if (nonDragAreaSelector && getParent(e.target, nonDragAreaSelector)) {\n        startDrag = false;\n      }\n      if (startDrag) {\n        container.layout.invalidate();\n        addClass(window.document.body, disableTouchActions);\n        addClass(window.document.body, noUserSelectClass);\n        var onMouseUp = function () {\n          removeClass(window.document.body, disableTouchActions);\n          removeClass(window.document.body, noUserSelectClass);\n          window.document.removeEventListener('mouseup', onMouseUp);\n          window.document.removeEventListener('touchend', onMouseUp);\n        };\n        window.document.addEventListener('mouseup', onMouseUp);\n        window.document.addEventListener('touchend', onMouseUp);\n        handleDragStartConditions(e, container.getOptions().dragBeginDelay, function () {\n          clearSelection();\n          initiateDrag(e, getElementCursor(event.target));\n          addMoveListeners();\n          addReleaseListeners();\n        });\n      }\n    }\n  }\n}\nfunction handleMouseMoveForContainer (ref, orientation) {\n  var clientX = ref.clientX;\n  var clientY = ref.clientY;\n  if ( orientation === void 0 ) orientation = 'vertical';\n\n  var beginEnd = draggableInfo.container.layout.getBeginEndOfContainerVisibleRect();\n  var mousePos;\n  var axis;\n  var leftTop;\n  var size;\n  if (orientation === 'vertical') {\n    mousePos = clientY;\n    axis = 'y';\n    leftTop = 'top';\n    size = draggableInfo.size.offsetHeight;\n  } else {\n    mousePos = clientX;\n    axis = 'x';\n    leftTop = 'left';\n    size = draggableInfo.size.offsetWidth;\n  }\n  var beginBoundary = beginEnd.begin;\n  var endBoundary = beginEnd.end - size;\n  var positionInBoundary = Math.max(beginBoundary, Math.min(endBoundary, (mousePos + ghostInfo.positionDelta[leftTop])));\n  ghostInfo.topLeft[axis] = positionInBoundary;\n  draggableInfo.position[axis] = Math.max(beginEnd.begin, Math.min(beginEnd.end, (mousePos + ghostInfo.centerDelta[axis])));\n  draggableInfo.mousePosition[axis] = Math.max(beginEnd.begin, Math.min(beginEnd.end, mousePos));\n  if (draggableInfo.position[axis] < (beginEnd.begin + (size / 2))) {\n    draggableInfo.position[axis] = beginEnd.begin + 2;\n  }\n  if (draggableInfo.position[axis] > (beginEnd.end - (size / 2))) {\n    draggableInfo.position[axis] = beginEnd.end - 2;\n  }\n}\nfunction onMouseMove (event) {\n  event.preventDefault();\n  var e = getPointerEvent(event);\n  if (!draggableInfo) {\n    initiateDrag(e, getElementCursor(event.target));\n  } else {\n    var containerOptions = draggableInfo.container.getOptions();\n    var isContainDrag = containerOptions.behaviour === 'contain';\n    if (isContainDrag) {\n      handleMouseMoveForContainer(e, containerOptions.orientation);\n    } else if (sourceContainerLockAxis) {\n      if (sourceContainerLockAxis === 'y') {\n        ghostInfo.topLeft.y = e.clientY + ghostInfo.positionDelta.top;\n        draggableInfo.position.y = e.clientY + ghostInfo.centerDelta.y;\n        draggableInfo.mousePosition.y = e.clientY;\n      } else if (sourceContainerLockAxis === 'x') {\n        ghostInfo.topLeft.x = e.clientX + ghostInfo.positionDelta.left;\n        draggableInfo.position.x = e.clientX + ghostInfo.centerDelta.x;\n        draggableInfo.mousePosition.x = e.clientX;\n      }\n    } else {\n      ghostInfo.topLeft.x = e.clientX + ghostInfo.positionDelta.left;\n      ghostInfo.topLeft.y = e.clientY + ghostInfo.positionDelta.top;\n      draggableInfo.position.x = e.clientX + ghostInfo.centerDelta.x;\n      draggableInfo.position.y = e.clientY + ghostInfo.centerDelta.y;\n      draggableInfo.mousePosition.x = e.clientX;\n      draggableInfo.mousePosition.y = e.clientY;\n    }\n    translateGhost();\n    if (!handleDrag(draggableInfo)) {\n      missedDrag = true;\n    } else {\n      missedDrag = false;\n    }\n    if (missedDrag) {\n      debouncedHandleMissedDragFrame();\n    }\n  }\n}\nvar debouncedHandleMissedDragFrame = debounce(handleMissedDragFrame, 20, false);\nfunction handleMissedDragFrame () {\n  if (missedDrag) {\n    missedDrag = false;\n    handleDragImmediate(draggableInfo, dragListeningContainers);\n  }\n}\nfunction onMouseUp () {\n  removeMoveListeners();\n  removeReleaseListeners();\n  removeGrabListeners();\n  if (handleScroll && typeof handleScroll === 'function') { handleScroll({ reset: true }); }\n  if (cursorStyleElement) {\n    removeStyle(cursorStyleElement);\n    cursorStyleElement = null;\n  }\n  if (draggableInfo) {\n    containerRectableWatcher.stop();\n    handleMissedDragFrame();\n    dropAnimationStarted = true;\n    handleDropAnimation(function () {\n      isDragging = false;\n      fireOnDragStartEnd(false);\n      var containers = dragListeningContainers || [];\n      var containerToCallDrop = containers.shift();\n      while (containerToCallDrop !== undefined) {\n        containerToCallDrop.handleDrop(draggableInfo);\n        containerToCallDrop = containers.shift();\n      }\n      dragListeningContainers = null;\n      grabbedElement = null;\n      ghostInfo = null;\n      draggableInfo = null;\n      sourceContainerLockAxis = null;\n      handleDrag = null;\n      dropAnimationStarted = false;\n    });\n  }\n}\nfunction getPointerEvent (e) {\n  return e.touches ? e.touches[0] : e;\n}\nfunction handleDragImmediate (draggableInfo, dragListeningContainers) {\n  var containerBoxChanged = false;\n  dragListeningContainers.forEach(function (p) {\n    var dragResult = p.handleDrag(draggableInfo);\n    containerBoxChanged = !!dragResult.containerBoxChanged || false;\n    dragResult.containerBoxChanged = false;\n  });\n  if (containerBoxChanged) {\n    containerBoxChanged = false;\n    requestAnimationFrame(function () {\n      containers.forEach(function (p) {\n        p.layout.invalidateRects();\n        p.onTranslated();\n      });\n    });\n  }\n}\nfunction dragHandler (dragListeningContainers) {\n  var targetContainers = dragListeningContainers;\n  var animationFrame = null;\n  return function (draggableInfo) {\n    if (animationFrame === null && isDragging && !dropAnimationStarted) {\n      animationFrame = requestAnimationFrame(function () {\n        if (isDragging && !dropAnimationStarted) {\n          handleDragImmediate(draggableInfo, targetContainers);\n          handleScroll({ draggableInfo: draggableInfo });\n        }\n        animationFrame = null;\n      });\n      return true;\n    }\n    return false;\n  };\n}\nfunction getScrollHandler (container, dragListeningContainers) {\n  if (container.getOptions().autoScrollEnabled) {\n    return dragScroller(dragListeningContainers, container.getScrollMaxSpeed());\n  } else {\n    return function (props) { return null; };\n  }\n}\nfunction fireOnDragStartEnd (isStart) {\n  var container = draggableInfo.container;\n  var payload = draggableInfo.payload;\n  containers.forEach(function (p) {\n    if (container.getOptions().fireRelatedEventsOnly && p !== container) { return; }\n    var ref = p.getOptions();\n    var onDragStart = ref.onDragStart;\n    var onDragEnd = ref.onDragEnd;\n    var fn = isStart ? onDragStart : onDragEnd;\n    if (fn) {\n      var options = {\n        isSource: p === container,\n        payload: payload,\n        willAcceptDrop: false\n      };\n      if (p.isDragRelevant(container, payload)) {\n        options.willAcceptDrop = true;\n      }\n      fn(options);\n    }\n  });\n}\nfunction initiateDrag (position, cursor) {\n  if (grabbedElement !== null) {\n    if (grabbedElement.classList.contains('dndrop-not-draggable')) { return; }\n    isDragging = true;\n    var container = (containers.filter(function (p) { return grabbedElement.parentElement === p.element; })[0]);\n    container.setDraggables();\n    sourceContainerLockAxis = container.getOptions().lockAxis ? container.getOptions().lockAxis.toLowerCase() : null;\n    draggableInfo = getDraggableInfo(grabbedElement);\n    ghostInfo = getGhostElement(grabbedElement, { x: position.clientX, y: position.clientY }, draggableInfo.container, cursor);\n    draggableInfo.position = {\n      x: position.clientX + ghostInfo.centerDelta.x,\n      y: position.clientY + ghostInfo.centerDelta.y,\n    };\n    draggableInfo.mousePosition = {\n      x: position.clientX,\n      y: position.clientY,\n    };\n    dragListeningContainers = containers.filter(function (p) { return p.isDragRelevant(container, draggableInfo.payload); });\n    draggableInfo.relevantContainers = dragListeningContainers;\n    handleDrag = dragHandler(dragListeningContainers);\n    if (handleScroll && typeof handleScroll === 'function') {\n      handleScroll({ reset: true, draggableInfo: undefined });\n    }\n    handleScroll = getScrollHandler(container, dragListeningContainers);\n    dragListeningContainers.forEach(function (p) { return p.prepareDrag(p, dragListeningContainers); });\n    fireOnDragStartEnd(true);\n    handleDrag(draggableInfo);\n    getGhostParent().appendChild(ghostInfo.ghost);\n    containerRectableWatcher.start();\n  }\n}\nvar ghostAnimationFrame = null;\nfunction translateGhost (translateDuration, scale, fadeOut) {\n  if ( translateDuration === void 0 ) translateDuration = 0;\n  if ( scale === void 0 ) scale = 1;\n  if ( fadeOut === void 0 ) fadeOut = false;\n\n  var ghost = ghostInfo.ghost;\n  var ghostInfo_topLeft = ghostInfo.topLeft;\n  var x = ghostInfo_topLeft.x;\n  var y = ghostInfo_topLeft.y;\n  var useTransform = draggableInfo.container ? draggableInfo.container.shouldUseTransformForGhost() : true;\n  var transformString = useTransform ? (\"translate3d(\" + x + \"px,\" + y + \"px, 0)\") : null;\n  if (scale !== 1) {\n    transformString = transformString ? (transformString + \" scale(\" + scale + \")\") : (\"scale(\" + scale + \")\");\n  }\n  if (translateDuration > 0) {\n    ghostInfo.ghost.style.transitionDuration = translateDuration + 'ms';\n    requestAnimationFrame(function () {\n      transformString && (ghost.style.transform = transformString);\n      if (!useTransform) {\n        ghost.style.left = x + 'px';\n        ghost.style.top = y + 'px';\n      }\n      ghostAnimationFrame = null;\n      if (fadeOut) {\n        ghost.style.opacity = '0';\n      }\n    });\n    return;\n  }\n  if (ghostAnimationFrame === null) {\n    ghostAnimationFrame = requestAnimationFrame(function () {\n      transformString && (ghost.style.transform = transformString);\n      if (!useTransform) {\n        ghost.style.left = x + 'px';\n        ghost.style.top = y + 'px';\n      }\n      ghostAnimationFrame = null;\n      if (fadeOut) {\n        ghost.style.opacity = '0';\n      }\n    });\n  }\n}\nfunction registerContainer (container) {\n  containers.push(container);\n  if (isDragging && draggableInfo) {\n    if (container.isDragRelevant(draggableInfo.container, draggableInfo.payload)) {\n      dragListeningContainers.push(container);\n      container.prepareDrag(container, dragListeningContainers);\n      if (handleScroll && typeof handleScroll === 'function') {\n        handleScroll({ reset: true, draggableInfo: undefined });\n      }\n      handleScroll = getScrollHandler(container, dragListeningContainers);\n      handleDrag = dragHandler(dragListeningContainers);\n      container.handleDrag(draggableInfo);\n    }\n  }\n}\nfunction unregisterContainer (container) {\n  containers.splice(containers.indexOf(container), 1);\n  if (isDragging && draggableInfo) {\n    if (draggableInfo.container === container) {\n      container.fireRemoveElement();\n    }\n    if (draggableInfo.targetElement === container.element) {\n      draggableInfo.targetElement = null;\n    }\n    var indexInDragListeners = dragListeningContainers.indexOf(container);\n    if (indexInDragListeners > -1) {\n      dragListeningContainers.splice(indexInDragListeners, 1);\n      if (handleScroll && typeof handleScroll === 'function') {\n        handleScroll({ reset: true, draggableInfo: undefined });\n      }\n      handleScroll = getScrollHandler(container, dragListeningContainers);\n      handleDrag = dragHandler(dragListeningContainers);\n    }\n  }\n}\nfunction watchRectangles () {\n  var animationHandle = null;\n  var isStarted = false;\n  function _start () {\n    animationHandle = requestAnimationFrame(function () {\n      dragListeningContainers.forEach(function (p) { return p.layout.invalidateRects(); });\n      setTimeout(function () {\n        if (animationHandle !== null) { _start(); }\n      }, 50);\n    });\n  }\n  function stop () {\n    if (animationHandle !== null) {\n      cancelAnimationFrame(animationHandle);\n      animationHandle = null;\n    }\n    isStarted = false;\n  }\n  return {\n    start: function () {\n      if (!isStarted) {\n        isStarted = true;\n        _start();\n      }\n    },\n    stop: stop\n  };\n}\nfunction cancelDrag () {\n  if (isDragging && !isCanceling && !dropAnimationStarted) {\n    isCanceling = true;\n    missedDrag = false;\n    var outOfBoundsDraggableInfo = Object.assign({}, draggableInfo, {\n      targetElement: null,\n      position: { x: Number.MAX_SAFE_INTEGER, y: Number.MAX_SAFE_INTEGER },\n      mousePosition: { x: Number.MAX_SAFE_INTEGER, y: Number.MAX_SAFE_INTEGER },\n    });\n    dragListeningContainers.forEach(function (container) {\n      container.handleDrag(outOfBoundsDraggableInfo);\n    });\n    if (draggableInfo) {\n      draggableInfo.targetElement = null;\n      draggableInfo.cancelDrop = true;\n      onMouseUp();\n      isCanceling = false;\n    }\n  }\n}\nfunction Mediator () {\n  listenEvents();\n  return {\n    register: function (container) {\n      registerContainer(container);\n    },\n    unregister: function (container) {\n      unregisterContainer(container);\n    },\n    isDragging: function () {\n      return isDragging;\n    },\n    cancelDrag: cancelDrag,\n  };\n}\nif (typeof window !== 'undefined') {\n  addStyleToHead();\n}\nvar Mediator$1 = Mediator();\n\nfunction setAnimation (\n  element,\n  add,\n  animationDuration\n) {\n  if ( animationDuration === void 0 ) animationDuration = defaultOptions.animationDuration;\n\n  if (add) {\n    addClass(element, animationClass);\n    element.style.transitionDuration = animationDuration + 'ms';\n  } else {\n    removeClass(element, animationClass);\n    element.style.removeProperty('transition-duration');\n  }\n}\n\nfunction isDragRelevant (ref) {\n  var element = ref.element;\n  var getOptions = ref.getOptions;\n\n  return function (sourceContainer, payload) {\n    var options = getOptions();\n\n    var sourceOptions = sourceContainer.getOptions();\n    if (options.behaviour === 'copy') { return false; }\n\n    var parentWrapper = getParent(element, '.' + wrapperClass);\n    if (parentWrapper === sourceContainer.element) {\n      return false;\n    }\n\n    if (sourceContainer.element === element) { return true; }\n    if (\n      sourceOptions.groupName &&\n      sourceOptions.groupName === options.groupName\n    ) {\n      return true;\n    }\n\n    if (options.shouldAcceptDrop) {\n      return options.shouldAcceptDrop(sourceContainer.getOptions(), payload);\n    }\n\n    return false;\n  };\n}\n\nfunction wrapChild$1 (child) {\n  if (smoothDnD.wrapChild) {\n    var div = window.document.createElement('div');\n    div.className = \"\" + wrapperClass;\n    child.parentElement.insertBefore(div, child);\n    div.appendChild(child);\n    return div;\n  }\n\n  return child;\n}\n\nfunction wrapChildren (element) {\n  var draggables = [];\n  Array.prototype.forEach.call(element.children, function (child) {\n    if (child.nodeType === Node.ELEMENT_NODE) {\n      var wrapper = child;\n      if (!hasClass(child, wrapperClass)) {\n        wrapper = wrapChild$1(child);\n      }\n      wrapper[translationValue] = 0;\n      draggables.push(wrapper);\n    } else {\n      element.removeChild(child);\n    }\n  });\n  return draggables;\n}\n\nfunction unwrapChildren (element) {\n  if (smoothDnD.wrapChild) {\n    Array.prototype.forEach.call(element.children, function (child) {\n      if (child.nodeType === Node.ELEMENT_NODE) {\n        if (hasClass(child, wrapperClass)) {\n          element.insertBefore(child.firstElementChild, child);\n          element.removeChild(child);\n        }\n      }\n    });\n  }\n}\n\nfunction findDraggebleAtPos (ref) {\n  var layout = ref.layout;\n\n  var find = function (\n    draggables,\n    pos,\n    startIndex,\n    endIndex,\n    withRespectToMiddlePoints\n  ) {\n    if ( withRespectToMiddlePoints === void 0 ) withRespectToMiddlePoints = false;\n\n    if (endIndex < startIndex) {\n      return startIndex;\n    }\n    // binary serach draggable\n    if (startIndex === endIndex) {\n      var ref = layout.getBeginEnd(draggables[startIndex]);\n      var begin = ref.begin;\n      var end = ref.end;\n      // mouse pos is inside draggable\n      // now decide which index to return\n      // if (pos > begin && pos <= end) {\n      if (withRespectToMiddlePoints) {\n        return pos < (end + begin) / 2 ? startIndex : startIndex + 1;\n      } else {\n        return startIndex;\n      }\n      // } else {\n      //   return null;\n      // }\n    } else {\n      var middleIndex = Math.floor((endIndex + startIndex) / 2);\n      var ref$1 = layout.getBeginEnd(draggables[middleIndex]);\n      var begin$1 = ref$1.begin;\n      var end$1 = ref$1.end;\n      if (pos < begin$1) {\n        return find(\n          draggables,\n          pos,\n          startIndex,\n          middleIndex - 1,\n          withRespectToMiddlePoints\n        );\n      } else if (pos > end$1) {\n        return find(\n          draggables,\n          pos,\n          middleIndex + 1,\n          endIndex,\n          withRespectToMiddlePoints\n        );\n      } else {\n        if (withRespectToMiddlePoints) {\n          return pos < (end$1 + begin$1) / 2 ? middleIndex : middleIndex + 1;\n        } else {\n          return middleIndex;\n        }\n      }\n    }\n  };\n\n  return function (draggables, pos, withRespectToMiddlePoints) {\n    if ( withRespectToMiddlePoints === void 0 ) withRespectToMiddlePoints = false;\n\n    return find(\n      draggables,\n      pos,\n      0,\n      draggables.length - 1,\n      withRespectToMiddlePoints\n    );\n  };\n}\n\nfunction resetDraggables (ref) {\n  var element = ref.element;\n  var draggables = ref.draggables;\n  var layout = ref.layout;\n\n  return function () {\n    draggables.forEach(function (p) {\n      setAnimation(p, false);\n      layout.setTranslation(p, 0);\n      layout.setVisibility(p, true);\n    });\n\n    if (element[stretcherElementInstance]) {\n      element[stretcherElementInstance].parentNode.removeChild(\n        element[stretcherElementInstance]\n      );\n      element[stretcherElementInstance] = null;\n    }\n  };\n}\n\nfunction setTargetContainer (draggableInfo, element, set) {\n  if ( set === void 0 ) set = true;\n\n  if (element && set) {\n    draggableInfo.targetElement = element;\n  } else {\n    if (draggableInfo.targetElement === element) {\n      draggableInfo.targetElement = null;\n    }\n  }\n}\n\nfunction handleDrop (ref) {\n  var element = ref.element;\n  var draggables = ref.draggables;\n  var layout = ref.layout;\n  var getOptions = ref.getOptions;\n\n  var draggablesReset = resetDraggables({\n    element: element,\n    draggables: draggables,\n    layout: layout,\n    getOptions: getOptions,\n  });\n  var dropHandler = (smoothDnD.dropHandler || domDropHandler)({\n    element: element,\n    draggables: draggables,\n    layout: layout,\n    getOptions: getOptions,\n  });\n  return function (\n    draggableInfo,\n    ref,\n    forDispose\n  ) {\n    var addedIndex = ref.addedIndex;\n    var removedIndex = ref.removedIndex;\n    if ( forDispose === void 0 ) forDispose = false;\n\n    draggablesReset();\n    // if drop zone is valid => complete drag, else emit dropNotAllowed and everything will be reverted by draggablesReset()\n    if (draggableInfo && !draggableInfo.cancelDrop) {\n      if (\n        draggableInfo.targetElement ||\n        getOptions().removeOnDropOut ||\n        forDispose\n      ) {\n        var indexNotNull = function (index) { return index !== null; };\n\n        var actualAddIndex =\n          indexNotNull(addedIndex)\n            ? indexNotNull(removedIndex) && removedIndex < addedIndex\n              ? addedIndex - 1\n              : addedIndex\n            : null;\n\n        var payload = draggableInfo.payload;\n        var element = draggableInfo.element;\n\n        var dropHandlerParams = {\n          removedIndex: removedIndex,\n          addedIndex: actualAddIndex,\n          payload: payload,\n          element: element.firstElementChild || element,\n        };\n        var shouldHandleDrop =\n          !draggableInfo.container.getOptions().fireRelatedEventsOnly ||\n          indexNotNull(removedIndex) ||\n          indexNotNull(actualAddIndex);\n        if (shouldHandleDrop) {\n          dropHandler(dropHandlerParams, getOptions().onDrop);\n        }\n      } else if (getOptions().dropNotAllowed) {\n        var payload$1 = draggableInfo.payload;\n        var container = draggableInfo.container;\n        return getOptions().dropNotAllowed({ payload: payload$1, container: container });\n      }\n    }\n  };\n}\n\nfunction getContainerProps (element, getOptions) {\n  var draggables = wrapChildren(element);\n  var options = getOptions();\n  // set flex classes before layout is inited for scroll listener\n  addClass(element, (containerClass + \" \" + (options.orientation)));\n  var layout = layoutManager(\n    element,\n    options.orientation,\n    options.animationDuration\n  );\n  return {\n    element: element,\n    draggables: draggables,\n    getOptions: getOptions,\n    layout: layout,\n  };\n}\n\nfunction getRemovedItem (ref) {\n  var element = ref.element;\n  var getOptions = ref.getOptions;\n\n  var prevRemovedIndex = null;\n  return function (ref) {\n    var draggableInfo = ref.draggableInfo;\n\n    var removedIndex = prevRemovedIndex;\n    if (\n      prevRemovedIndex == null &&\n      draggableInfo.container.element === element &&\n      getOptions().behaviour !== 'copy'\n    ) {\n      removedIndex = prevRemovedIndex = draggableInfo.elementIndex;\n    }\n\n    return { removedIndex: removedIndex };\n  };\n}\n\nfunction setRemovedItemVisibilty (ref) {\n  var draggables = ref.draggables;\n  var layout = ref.layout;\n\n  return function (ref) {\n    var dragResult = ref.dragResult;\n\n    if (dragResult.removedIndex !== null) {\n      layout.setVisibility(draggables[dragResult.removedIndex], false);\n    }\n  };\n}\n\nfunction getPosition (ref) {\n  var element = ref.element;\n  var layout = ref.layout;\n\n  return function (ref) {\n    var draggableInfo = ref.draggableInfo;\n\n    var hitElement = document.elementFromPoint(\n      draggableInfo.position.x,\n      draggableInfo.position.y\n    );\n\n    // TODO: if center is out of bounds use mouse position for hittest\n    // if (!hitElement) {\n    //   hitElement = document.elementFromPoint(draggableInfo.mousePosition.x, draggableInfo.mousePosition.y);\n    // }\n\n    if (hitElement) {\n      var container = getParentRelevantContainerElement(\n        hitElement,\n        draggableInfo.relevantContainers\n      );\n      if (container && container.element === element) {\n        return {\n          pos: layout.getPosition(draggableInfo.position),\n        };\n      }\n    }\n\n    return {\n      pos: null,\n    };\n  };\n}\n\nfunction getElementSize (ref) {\n  var layout = ref.layout;\n\n  var elementSize = null;\n  return function (ref) {\n    var draggableInfo = ref.draggableInfo;\n    var dragResult = ref.dragResult;\n\n    if (dragResult.pos === null) {\n      return (elementSize = null);\n    } else {\n      elementSize = elementSize || layout.getSize(draggableInfo.size);\n    }\n    return { elementSize: elementSize };\n  };\n}\n\nfunction handleTargetContainer (ref) {\n  var element = ref.element;\n\n  return function (ref) {\n    var draggableInfo = ref.draggableInfo;\n    var dragResult = ref.dragResult;\n\n    setTargetContainer(draggableInfo, element, !!dragResult.pos);\n  };\n}\n\nfunction getDragInsertionIndex (ref) {\n  var draggables = ref.draggables;\n  var layout = ref.layout;\n\n  var findDraggable = findDraggebleAtPos({ layout: layout });\n  return function (ref) {\n    var ref_dragResult = ref.dragResult;\n    var shadowBeginEnd = ref_dragResult.shadowBeginEnd;\n    var pos = ref_dragResult.pos;\n\n    if (!shadowBeginEnd) {\n      var index = findDraggable(draggables, pos, true);\n      return index !== null ? index : draggables.length;\n    } else {\n      if (\n        shadowBeginEnd.begin + shadowBeginEnd.beginAdjustment <= pos &&\n        shadowBeginEnd.end >= pos\n      ) {\n        // position inside ghost\n        return null;\n      }\n    }\n\n    if (pos < shadowBeginEnd.begin + shadowBeginEnd.beginAdjustment) {\n      return findDraggable(draggables, pos);\n    } else if (pos > shadowBeginEnd.end) {\n      return findDraggable(draggables, pos) + 1;\n    } else {\n      return draggables.length;\n    }\n  };\n}\n\nfunction getDragInsertionIndexForDropZone () {\n  return function (ref) {\n    var pos = ref.dragResult.pos;\n\n    return pos !== null ? { addedIndex: 0 } : { addedIndex: null };\n  };\n}\n\nfunction getShadowBeginEndForDropZone (ref) {\n  var layout = ref.layout;\n\n  var prevAddedIndex = null;\n  return function (ref) {\n    var addedIndex = ref.dragResult.addedIndex;\n\n    if (addedIndex !== prevAddedIndex) {\n      prevAddedIndex = addedIndex;\n      var ref$1 = layout.getBeginEndOfContainer();\n      var begin = ref$1.begin;\n      return {\n        shadowBeginEnd: {\n          rect: layout.getTopLeftOfElementBegin(begin),\n        },\n      };\n    }\n\n    return null;\n  };\n}\n\nfunction drawDropPlaceholder (ref) {\n  var layout = ref.layout;\n  var element = ref.element;\n  var getOptions = ref.getOptions;\n\n  var prevAddedIndex = null;\n  return function (ref) {\n    var ref_dragResult = ref.dragResult;\n    var elementSize = ref_dragResult.elementSize;\n    var shadowBeginEnd = ref_dragResult.shadowBeginEnd;\n    var addedIndex = ref_dragResult.addedIndex;\n    var dropPlaceholderContainer = ref_dragResult.dropPlaceholderContainer;\n\n    var options = getOptions();\n    if (options.dropPlaceholder) {\n      var ref$1 =\n        typeof options.dropPlaceholder === 'boolean'\n          ? {}\n          : options.dropPlaceholder;\n      var animationDuration = ref$1.animationDuration;\n      var className = ref$1.className;\n      var showOnTop = ref$1.showOnTop;\n      if (addedIndex !== null) {\n        if (!dropPlaceholderContainer) {\n          var innerElement = document.createElement('div');\n          var flex = document.createElement('div');\n          flex.className = dropPlaceholderFlexContainerClass;\n          innerElement.className = dropPlaceholderInnerClass + \" \" + (className || dropPlaceholderDefaultClass);\n          dropPlaceholderContainer = document.createElement('div');\n          dropPlaceholderContainer.className = \"\" + dropPlaceholderWrapperClass;\n          dropPlaceholderContainer.style.position = 'absolute';\n\n          if (animationDuration !== undefined) {\n            dropPlaceholderContainer.style.transition = \"all \" + animationDuration + \"ms ease\";\n          }\n\n          dropPlaceholderContainer.appendChild(flex);\n          flex.appendChild(innerElement);\n          layout.setSize(dropPlaceholderContainer.style, elementSize + 'px');\n\n          dropPlaceholderContainer.style.pointerEvents = 'none';\n\n          if (showOnTop) {\n            element.appendChild(dropPlaceholderContainer);\n          } else {\n            element.insertBefore(\n              dropPlaceholderContainer,\n              element.firstElementChild\n            );\n          }\n        }\n\n        if (prevAddedIndex !== addedIndex && shadowBeginEnd.dropArea) {\n          layout.setBegin(\n            dropPlaceholderContainer.style,\n            shadowBeginEnd.dropArea.begin -\n              layout.getBeginEndOfContainer().begin +\n              'px'\n          );\n        }\n        prevAddedIndex = addedIndex;\n\n        return {\n          dropPlaceholderContainer: dropPlaceholderContainer,\n        };\n      } else {\n        if (dropPlaceholderContainer && prevAddedIndex !== null) {\n          element.removeChild(dropPlaceholderContainer);\n        }\n        prevAddedIndex = null;\n\n        return {\n          dropPlaceholderContainer: undefined,\n        };\n      }\n    }\n\n    return null;\n  };\n}\n\nfunction invalidateShadowBeginEndIfNeeded (params) {\n  var shadowBoundsGetter = getShadowBeginEnd(params);\n  return function (ref) {\n    var draggableInfo = ref.draggableInfo;\n    var dragResult = ref.dragResult;\n\n    if (draggableInfo.invalidateShadow) {\n      return shadowBoundsGetter({ draggableInfo: draggableInfo, dragResult: dragResult });\n    }\n    return null;\n  };\n}\n\nfunction getNextAddedIndex (params) {\n  var getIndexForPos = getDragInsertionIndex(params);\n  return function (ref) {\n    var dragResult = ref.dragResult;\n\n    var index = null;\n    if (dragResult.pos !== null) {\n      index = getIndexForPos({ dragResult: dragResult });\n      if (index === null) {\n        index = dragResult.addedIndex;\n      }\n    }\n    return {\n      addedIndex: index,\n    };\n  };\n}\n\nfunction resetShadowAdjustment () {\n  var lastAddedIndex = null;\n  return function (ref) {\n    var ref_dragResult = ref.dragResult;\n    var addedIndex = ref_dragResult.addedIndex;\n    var shadowBeginEnd = ref_dragResult.shadowBeginEnd;\n\n    if (\n      addedIndex !== lastAddedIndex &&\n      lastAddedIndex !== null &&\n      shadowBeginEnd\n    ) {\n      shadowBeginEnd.beginAdjustment = 0;\n    }\n    lastAddedIndex = addedIndex;\n  };\n}\n\nfunction handleInsertionSizeChange (ref) {\n  var element = ref.element;\n  var draggables = ref.draggables;\n  var layout = ref.layout;\n  var getOptions = ref.getOptions;\n\n  var strectherElement = null;\n  return function (ref) {\n    var ref_dragResult = ref.dragResult;\n    var addedIndex = ref_dragResult.addedIndex;\n    var removedIndex = ref_dragResult.removedIndex;\n    var elementSize = ref_dragResult.elementSize;\n\n    if (removedIndex === null) {\n      if (addedIndex !== null) {\n        if (!strectherElement) {\n          var containerBeginEnd = layout.getBeginEndOfContainer();\n          containerBeginEnd.end =\n            containerBeginEnd.begin + layout.getSize(element);\n          var hasScrollBar =\n            layout.getScrollSize(element) > layout.getSize(element);\n          var containerEnd = hasScrollBar\n            ? containerBeginEnd.begin +\n              layout.getScrollSize(element) -\n              layout.getScrollValue(element)\n            : containerBeginEnd.end;\n          var lastDraggableEnd =\n            draggables.length > 0\n              ? layout.getBeginEnd(draggables[draggables.length - 1]).end -\n                draggables[draggables.length - 1][translationValue]\n              : containerBeginEnd.begin;\n          if (lastDraggableEnd + elementSize > containerEnd) {\n            strectherElement = window.document.createElement('div');\n            strectherElement.className =\n              stretcherElementClass + ' ' + getOptions().orientation;\n            var stretcherSize =\n              draggables.length > 0\n                ? elementSize + lastDraggableEnd - containerEnd\n                : elementSize;\n            layout.setSize(strectherElement.style, (stretcherSize + \"px\"));\n            element.appendChild(strectherElement);\n            element[stretcherElementInstance] = strectherElement;\n            return {\n              containerBoxChanged: true,\n            };\n          }\n        }\n      } else {\n        if (strectherElement) {\n          layout.setTranslation(strectherElement, 0);\n          var toRemove = strectherElement;\n          strectherElement = null;\n          element.removeChild(toRemove);\n          element[stretcherElementInstance] = null;\n          return {\n            containerBoxChanged: true,\n          };\n        }\n      }\n    }\n\n    return undefined;\n  };\n}\n\nfunction calculateTranslations (ref) {\n  var draggables = ref.draggables;\n  var layout = ref.layout;\n\n  var prevAddedIndex = null;\n  var prevRemovedIndex = null;\n  return function (ref) {\n    var ref_dragResult = ref.dragResult;\n    var addedIndex = ref_dragResult.addedIndex;\n    var removedIndex = ref_dragResult.removedIndex;\n    var elementSize = ref_dragResult.elementSize;\n\n    if (addedIndex !== prevAddedIndex || removedIndex !== prevRemovedIndex) {\n      for (var index = 0; index < draggables.length; index++) {\n        if (index !== removedIndex) {\n          var draggable = draggables[index];\n          var translate = 0;\n          if (removedIndex !== null && removedIndex < index) {\n            translate -= elementSize;\n          }\n          if (addedIndex !== null && addedIndex <= index) {\n            translate += elementSize;\n          }\n          layout.setTranslation(draggable, translate);\n        }\n      }\n\n      prevAddedIndex = addedIndex;\n      prevRemovedIndex = removedIndex;\n\n      return { addedIndex: addedIndex, removedIndex: removedIndex };\n    }\n\n    return undefined;\n  };\n}\n\nfunction getShadowBeginEnd (ref) {\n  var draggables = ref.draggables;\n  var layout = ref.layout;\n\n  var prevAddedIndex = null;\n  return function (ref) {\n    var draggableInfo = ref.draggableInfo;\n    var dragResult = ref.dragResult;\n\n    var addedIndex = dragResult.addedIndex;\n    var removedIndex = dragResult.removedIndex;\n    var elementSize = dragResult.elementSize;\n    var pos = dragResult.pos;\n    var shadowBeginEnd = dragResult.shadowBeginEnd;\n    if (pos !== null) {\n      if (\n        addedIndex !== null &&\n        (draggableInfo.invalidateShadow || addedIndex !== prevAddedIndex)\n      ) {\n        // if (prevAddedIndex) prevAddedIndex = addedIndex;\n        var beforeIndex = addedIndex - 1;\n        var begin = Number.MIN_SAFE_INTEGER;\n        var dropAreaBegin = 0;\n        var dropAreaEnd = 0;\n        var afterBounds = null;\n        var beforeBounds = null;\n        if (beforeIndex === removedIndex) {\n          beforeIndex--;\n        }\n        if (beforeIndex > -1) {\n          var beforeSize = layout.getSize(draggables[beforeIndex]);\n          beforeBounds = layout.getBeginEnd(draggables[beforeIndex]);\n          if (elementSize < beforeSize) {\n            var threshold = (beforeSize - elementSize) / 2;\n            begin = beforeBounds.end - threshold;\n          } else {\n            begin = beforeBounds.end;\n          }\n          dropAreaBegin = beforeBounds.end;\n        } else {\n          beforeBounds = { end: layout.getBeginEndOfContainer().begin };\n          dropAreaBegin = layout.getBeginEndOfContainer().begin;\n        }\n\n        var end = Number.MAX_SAFE_INTEGER;\n        var afterIndex = addedIndex;\n        if (afterIndex === removedIndex) {\n          afterIndex++;\n        }\n        if (afterIndex < draggables.length) {\n          var afterSize = layout.getSize(draggables[afterIndex]);\n          afterBounds = layout.getBeginEnd(draggables[afterIndex]);\n\n          if (elementSize < afterSize) {\n            var threshold$1 = (afterSize - elementSize) / 2;\n            end = afterBounds.begin + threshold$1;\n          } else {\n            end = afterBounds.begin;\n          }\n          dropAreaEnd = afterBounds.begin;\n        } else {\n          afterBounds = { begin: layout.getContainerRectangles().rect.end };\n          dropAreaEnd =\n            layout.getContainerRectangles().rect.end -\n            layout.getContainerRectangles().rect.begin;\n        }\n\n        var shadowRectTopLeft =\n          beforeBounds && afterBounds\n            ? layout.getTopLeftOfElementBegin(beforeBounds.end)\n            : null;\n\n        prevAddedIndex = addedIndex;\n        return {\n          shadowBeginEnd: {\n            dropArea: {\n              begin: dropAreaBegin,\n              end: dropAreaEnd,\n            },\n            begin: begin,\n            end: end,\n            rect: shadowRectTopLeft,\n            beginAdjustment: shadowBeginEnd\n              ? shadowBeginEnd.beginAdjustment\n              : 0,\n          },\n        };\n      } else {\n        return null;\n      }\n    } else {\n      prevAddedIndex = null;\n      return {\n        shadowBeginEnd: null,\n      };\n    }\n  };\n}\n\nfunction handleFirstInsertShadowAdjustment () {\n  var lastAddedIndex = null;\n  return function (ref) {\n    var ref_dragResult = ref.dragResult;\n    var pos = ref_dragResult.pos;\n    var addedIndex = ref_dragResult.addedIndex;\n    var shadowBeginEnd = ref_dragResult.shadowBeginEnd;\n\n    if (pos !== null) {\n      if (addedIndex != null && lastAddedIndex === null) {\n        if (pos < shadowBeginEnd.begin) {\n          var beginAdjustment = pos - shadowBeginEnd.begin - 5;\n          shadowBeginEnd.beginAdjustment = beginAdjustment;\n        }\n        lastAddedIndex = addedIndex;\n      }\n    } else {\n      lastAddedIndex = null;\n    }\n  };\n}\n\nfunction fireDragEnterLeaveEvents (ref) {\n  var getOptions = ref.getOptions;\n\n  var wasDragIn = false;\n  var options = getOptions();\n  return function (ref) {\n    var pos = ref.dragResult.pos;\n\n    var isDragIn = !!pos;\n    if (isDragIn !== wasDragIn) {\n      wasDragIn = isDragIn;\n      if (isDragIn) {\n        options.onDragEnter && options.onDragEnter();\n      } else {\n        options.onDragLeave && options.onDragLeave();\n      }\n    }\n\n    return undefined;\n  };\n}\n\nfunction fireOnDropReady (ref) {\n  var getOptions = ref.getOptions;\n\n  var lastAddedIndex = null;\n  var options = getOptions();\n  return function (ref) {\n    var ref_dragResult = ref.dragResult;\n    var addedIndex = ref_dragResult.addedIndex;\n    var removedIndex = ref_dragResult.removedIndex;\n    var ref_draggableInfo = ref.draggableInfo;\n    var payload = ref_draggableInfo.payload;\n    var element = ref_draggableInfo.element;\n\n    if (\n      options.onDropReady &&\n      addedIndex !== null &&\n      lastAddedIndex !== addedIndex\n    ) {\n      lastAddedIndex = addedIndex;\n      var adjustedAddedIndex = addedIndex;\n\n      if (removedIndex !== null && addedIndex > removedIndex) {\n        adjustedAddedIndex--;\n      }\n\n      options.onDropReady({\n        addedIndex: adjustedAddedIndex,\n        removedIndex: removedIndex,\n        payload: payload,\n        element: element ? (element.firstElementChild || element) : undefined,\n      });\n    }\n  };\n}\n\nfunction getDragHandler (params) {\n  if (params.getOptions().behaviour === 'drop-zone') {\n    // sorting is disabled in container, addedIndex will always be 0 if dropped in\n    return compose(params)(\n      getRemovedItem,\n      setRemovedItemVisibilty,\n      getPosition,\n      getElementSize,\n      handleTargetContainer,\n      getDragInsertionIndexForDropZone,\n      getShadowBeginEndForDropZone,\n      fireDragEnterLeaveEvents,\n      fireOnDropReady\n    );\n  } else {\n    return compose(params)(\n      getRemovedItem,\n      setRemovedItemVisibilty,\n      getPosition,\n      getElementSize,\n      handleTargetContainer,\n      invalidateShadowBeginEndIfNeeded,\n      getNextAddedIndex,\n      resetShadowAdjustment,\n      handleInsertionSizeChange,\n      calculateTranslations,\n      getShadowBeginEnd,\n      drawDropPlaceholder,\n      handleFirstInsertShadowAdjustment,\n      fireDragEnterLeaveEvents,\n      fireOnDropReady\n    );\n  }\n}\n\nfunction getDefaultDragResult () {\n  return {\n    addedIndex: null,\n    removedIndex: null,\n    elementSize: null,\n    pos: null,\n    shadowBeginEnd: null,\n  };\n}\n\nfunction compose (params) {\n  return function () {\n    var functions = [], len = arguments.length;\n    while ( len-- ) functions[ len ] = arguments[ len ];\n\n    var hydratedFunctions = functions.map(function (p) { return p(params); });\n    var result = null;\n    return function (draggableInfo) {\n      result = hydratedFunctions.reduce(function (dragResult, fn) {\n        return Object.assign(dragResult, fn({ draggableInfo: draggableInfo, dragResult: dragResult }));\n      }, result || getDefaultDragResult());\n      return result;\n    };\n  };\n}\n\n// Container definition begin\nfunction Container$1 (element) {\n  return function (options) {\n    var containerOptions = Object.assign({}, defaultOptions, options);\n    var dragResult = null;\n    var lastDraggableInfo = null;\n    var props = getContainerProps(element, getOptions);\n    var dragHandler = getDragHandler(props);\n    var dropHandler = handleDrop(props);\n    var scrollListener = listenScrollParent(element, onScroll);\n\n    function processLastDraggableInfo () {\n      if (lastDraggableInfo !== null) {\n        lastDraggableInfo.invalidateShadow = true;\n        dragResult = dragHandler(lastDraggableInfo);\n        lastDraggableInfo.invalidateShadow = false;\n      }\n    }\n\n    function setDraggables (draggables, element) {\n      var newDraggables = wrapChildren(element);\n      for (var i = 0; i < newDraggables.length; i++) {\n        draggables[i] = newDraggables[i];\n      }\n\n      for (var i$1 = 0; i$1 < draggables.length - newDraggables.length; i$1++) {\n        draggables.pop();\n      }\n    }\n\n    function prepareDrag (container, relevantContainers) {\n      var element = container.element;\n      var draggables = props.draggables;\n      setDraggables(draggables, element);\n      container.layout.invalidateRects();\n      draggables.forEach(function (p) { return setAnimation(p, true, getOptions().animationDuration); }\n      );\n      scrollListener.start();\n    }\n\n    function onScroll () {\n      props.layout.invalidateRects();\n      processLastDraggableInfo();\n    }\n\n    function dispose (container) {\n      scrollListener.dispose();\n      unwrapChildren(container.element);\n    }\n\n    function setOptions (options, merge) {\n      if ( merge === void 0 ) merge = true;\n\n      if (merge === false) {\n        containerOptions = Object.assign({}, defaultOptions, options);\n      } else {\n        containerOptions = Object.assign(\n          {},\n          defaultOptions,\n          containerOptions,\n          options\n        );\n      }\n    }\n\n    function getOptions () {\n      return containerOptions;\n    }\n\n    var container = {\n      element: element,\n      draggables: props.draggables,\n      isDragRelevant: isDragRelevant(props),\n      layout: props.layout,\n      dispose: dispose,\n      prepareDrag: prepareDrag,\n      handleDrag: function handleDrag (draggableInfo) {\n        lastDraggableInfo = draggableInfo;\n        dragResult = dragHandler(draggableInfo);\n        return dragResult;\n      },\n      handleDrop: function handleDrop (draggableInfo) {\n        scrollListener.stop();\n        if (dragResult && dragResult.dropPlaceholderContainer) {\n          element.removeChild(dragResult.dropPlaceholderContainer);\n        }\n        lastDraggableInfo = null;\n        dragHandler = getDragHandler(props);\n        dropHandler(draggableInfo, dragResult);\n        dragResult = null;\n      },\n      fireRemoveElement: function fireRemoveElement () {\n        // will be called when container is disposed while dragging so ignore addedIndex\n        dropHandler(\n          lastDraggableInfo,\n          Object.assign({}, dragResult, { addedIndex: null }),\n          true\n        );\n        dragResult = null;\n      },\n      getDragResult: function getDragResult () {\n        return dragResult;\n      },\n      getTranslateCalculator: function getTranslateCalculator (dragresult) {\n        return calculateTranslations(props)(dragresult);\n      },\n      onTranslated: function () {\n        processLastDraggableInfo();\n      },\n      setDraggables: function () {\n        setDraggables(props.draggables, element);\n      },\n      getScrollMaxSpeed: function getScrollMaxSpeed () {\n        return smoothDnD.maxScrollSpeed;\n      },\n      shouldUseTransformForGhost: function shouldUseTransformForGhost () {\n        return smoothDnD.useTransformForGhost === true;\n      },\n      getOptions: getOptions,\n      setOptions: setOptions,\n    };\n\n    return container;\n  };\n}\n\n// exported part of container\nvar smoothDnD = function (element, options) {\n  var containerIniter = Container$1(element);\n  var container = containerIniter(options);\n  element[containerInstance] = container;\n  Mediator$1.register(container);\n  return {\n    dispose: function dispose () {\n      Mediator$1.unregister(container);\n      container.dispose(container);\n    },\n    setOptions: function setOptions (options, merge) {\n      container.setOptions(options, merge);\n    },\n  };\n};\n\n// wrap all draggables by default\n// in react,vue,angular this value will be set to false\nsmoothDnD.wrapChild = true;\nsmoothDnD.cancelDrag = function () {\n  Mediator$1.cancelDrag();\n};\n\nsmoothDnD.isDragging = function () {\n  return Mediator$1.isDragging();\n};\n\nvar isArray = function (obj) {\n  return Object.prototype.toString.call(obj) === '[object Array]';\n};\n\nfunction getTagProps (ctx, tagClasses) {\n  var tag = ctx.$props.tag;\n  if (tag) {\n    if (typeof tag === 'string') {\n      var result = { value: tag };\n      if (tagClasses) {\n        result.props = { class: tagClasses };\n      }\n      return result;\n    } else if (typeof tag === 'object') {\n      var result$1 = { value: tag.value || 'div', props: tag.props || {} };\n\n      if (tagClasses) {\n        if (result$1.props.class) {\n          if (isArray(result$1.props.class)) {\n            result$1.props.class.push(tagClasses);\n          } else {\n            result$1.props.class = [tagClasses, result$1.props.class];\n          }\n        } else {\n          result$1.props.class = tagClasses;\n        }\n      }\n\n      return result$1;\n    }\n  }\n  return { value: 'div' };\n}\n\nfunction validateTagProp (tag) {\n  if (tag) {\n    if (typeof tag === 'string') { return true; }\n    if (typeof tag === 'object') {\n      if (\n        typeof tag.value === 'string' ||\n        typeof tag.value === 'function' ||\n        typeof tag.value === 'object'\n      ) {\n        return true;\n      }\n    }\n    return false;\n  }\n  return true;\n}\n\n/* eslint-disable curly */\nsmoothDnD.dropHandler = reactDropHandler().handler;\nsmoothDnD.wrapChild = false;\n\nvar eventEmitterMap = {\n  // eslint-disable-next-line quote-props\n  drop: 'onDrop',\n  'drag-end': 'onDragEnd',\n  'drag-start': 'onDragStart',\n  'drag-enter': 'onDragEnter',\n  'drag-leave': 'onDragLeave',\n  'drop-ready': 'onDropReady',\n  'drop-not-allowed': 'dropNotAllowed'\n};\n\nfunction getContainerOptions (props, context) {\n  var options = Object.keys(props).reduce(function (result, key) {\n    var optionName = key;\n    var prop = props[optionName];\n\n    if (prop !== undefined) {\n      if (typeof prop === 'function') {\n        if (eventEmitterMap[optionName]) {\n          result[eventEmitterMap[optionName]] = function (params) {\n            context.$emit(optionName, params);\n          };\n        } else {\n          result[optionName] = function () {\n            var params = [], len = arguments.length;\n            while ( len-- ) params[ len ] = arguments[ len ];\n\n            return prop.apply(void 0, params);\n          };\n        }\n      } else {\n        result[optionName] = prop;\n      }\n    }\n\n    return result;\n  }, {});\n\n  return options;\n}\n\nvar mapOptions = function (context) {\n  var props = Object.assign({}, context.$props, context.$attrs);\n  return getContainerOptions(props, context);\n};\n\nvar Container = {\n  name: 'Container',\n  mounted: function mounted () {\n    this.containerElement = this.$refs.container || this.$el;\n    this.container = smoothDnD(this.containerElement, mapOptions(this));\n  },\n  updated: function updated () {\n    if (\n      this.$refs.container !== this.containerElement &&\n      this.$el !== this.containerElement\n    ) {\n      if (this.container) {\n        this.container.dispose();\n      }\n      this.containerElement = this.$refs.container || this.$el;\n      this.container = smoothDnD(this.containerElement, mapOptions(this));\n      return;\n    }\n\n    this.container.setOptions(mapOptions(this));\n  },\n  destroyed: function destroyed () {\n    if (this.container) {\n      this.container.dispose();\n    }\n  },\n  props: {\n    behaviour: String,\n    groupName: String,\n    orientation: String,\n    dragHandleSelector: String,\n    nonDragAreaSelector: String,\n    dragBeginDelay: Number,\n    animationDuration: Number,\n    autoScrollEnabled: { type: Boolean, default: true },\n    lockAxis: String,\n    dragClass: String,\n    dropClass: String,\n    removeOnDropOut: { type: Boolean, default: false },\n    'drag-start': Function,\n    'drag-end': Function,\n    drop: Function,\n    getChildPayload: Function,\n    shouldAnimateDrop: Function,\n    fireRelatedEventsOnly: { type: Boolean, default: false },\n    shouldAcceptDrop: Function,\n    'drag-enter': Function,\n    'drag-leave': Function,\n    tag: {\n      validator: validateTagProp,\n      default: 'div',\n    },\n    getGhostParent: Function,\n    'drop-ready': Function,\n    dropPlaceholder: [Object, Boolean],\n  },\n  render: function () {\n    var tagProps = getTagProps(this);\n    return h(\n      tagProps.value,\n      Object.assign({}, { ref: 'container' }, tagProps.props),\n      this.$slots.default()\n    );\n  },\n};\n\nvar wrapChild = function (createElement, ctx) {\n  var tagProps = getTagProps(ctx, [\n    'dndrop-draggable-wrapper',\n    ctx.dragNotAllowed ? 'dndrop-not-draggable' : '' ]);\n  return createElement(\n    tagProps.value,\n    Object.assign({}, tagProps.props),\n    ctx.$slots.default()\n  );\n};\n\nvar Draggable = {\n  name: 'Draggable',\n  props: {\n    tag: {\n      validator: validateTagProp,\n      default: 'div',\n    },\n    dragNotAllowed: {\n      type: Boolean,\n      default: false,\n    },\n  },\n  render: function () {\n    return wrapChild(h, this);\n  }\n};\n\nexport { Container, Draggable, smoothDnD };\n"], "mappings": ";;;;;;AAQA,IAAI,oBAAoB;AACxB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,mBAAmB;AACvB,IAAI,kBAAkB;AACtB,IAAI,aAAa;AAEjB,IAAI,iBAAiB;AAErB,IAAI,wBAAwB;AAC5B,IAAI,wBAAwB;AAC5B,IAAI,2BAA2B;AAE/B,IAAI,sBAAsB;AAC1B,IAAI,oBAAoB;AAExB,IAAI,yBAAyB;AAE7B,IAAI,8BAA8B;AAClC,IAAI,4BAA4B;AAChC,IAAI,8BAA8B;AAClC,IAAI,oCAAoC;AAExC,IAAI,iBAAiB;AAAA,EACnB,WAAW;AAAA,EACX,WAAW;AAAA;AAAA,EACX,aAAa;AAAA;AAAA,EACb,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,mBAAmB;AACrB;AAEA,IAAI,gBAAgB,SAAU,QAAQ,OAAO;AAC3C,SAAO,OAAO,YAAY,OAAO,SAAS,KAAK,CAAC;AAClD;AAEA,IAAI,aAAa,SAAU,QAAQ,OAAO,OAAO;AAC/C,MAAI,SAAS,OAAO,SAAS,QAAQ;AACnC,WAAO,YAAY,KAAK;AAAA,EAC1B,OAAO;AACL,WAAO,aAAa,OAAO,OAAO,SAAS,KAAK,CAAC;AAAA,EACnD;AACF;AAEA,SAAS,eAAgB,KAAK;AAC5B,MAAI;AACJ,MAAI,aAAa,IAAI;AAErB,SAAO,SAAU,YAAY,QAAQ;AACnC,QAAI,eAAe,WAAW;AAC9B,QAAI,aAAa,WAAW;AAC5B,QAAI,UAAU,WAAW;AACzB,QAAI,iBAAiB;AACrB,QAAI,iBAAiB,MAAM;AACzB,uBAAiB,cAAc,SAAS,YAAY;AACpD,iBAAW,OAAO,cAAc,CAAC;AAAA,IACnC;AAEA,QAAI,eAAe,MAAM;AACvB,UAAI,UAAU,OAAO,SAAS,cAAc,KAAK;AACjD,cAAQ,YAAY;AACpB,cAAQ;AAAA,QACN,kBAAkB,eAAe,oBAC7B,eAAe,oBACf;AAAA,MACN;AACA,iBAAW,SAAS,SAAS,UAAU;AACvC,UAAI,cAAc,WAAW,QAAQ;AACnC,mBAAW,KAAK,OAAO;AAAA,MACzB,OAAO;AACL,mBAAW,OAAO,YAAY,GAAG,OAAO;AAAA,MAC1C;AAAA,IACF;AAEA,QAAI,QAAQ;AACV,aAAO,UAAU;AAAA,IACnB;AAAA,EACF;AACF;AAEA,SAAS,mBAAoB;AAC3B,MAAI,UAAU,WAAY;AACxB,WAAO,SAAU,YAAY,QAAQ;AACnC,UAAI,QAAQ;AACV,eAAO,UAAU;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,EACF;AACF;AAGA,IAAI,kBAAkB,SAAU,OAAO,OAAO;AAC5C,SAAO;AAAA,IACL,MAAM,KAAK,IAAI,MAAM,MAAM,MAAM,IAAI;AAAA,IACrC,KAAK,KAAK,IAAI,MAAM,KAAK,MAAM,GAAG;AAAA,IAClC,OAAO,KAAK,IAAI,MAAM,OAAO,MAAM,KAAK;AAAA,IACxC,QAAQ,KAAK,IAAI,MAAM,QAAQ,MAAM,MAAM;AAAA,EAC7C;AACF;AAEA,IAAI,eAAe;AAAA,EACjB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AACN;AAEA,IAAI,wBAAwB,SAAU,OAAO,OAAO,MAAM;AACxD,MAAI,SAAS,KAAK;AAChB,WAAO;AAAA,MACL,MAAM,KAAK,IAAI,MAAM,MAAM,MAAM,IAAI;AAAA,MACrC,KAAK,MAAM;AAAA,MACX,OAAO,KAAK,IAAI,MAAM,OAAO,MAAM,KAAK;AAAA,MACxC,QAAQ,MAAM;AAAA,IAChB;AAAA,EACF,OAAO;AACL,WAAO;AAAA,MACL,MAAM,MAAM;AAAA,MACZ,KAAK,KAAK,IAAI,MAAM,KAAK,MAAM,GAAG;AAAA,MAClC,OAAO,MAAM;AAAA,MACb,QAAQ,KAAK,IAAI,MAAM,QAAQ,MAAM,MAAM;AAAA,IAC7C;AAAA,EACF;AACF;AACA,IAAI,mBAAmB,SAAU,SAAS;AACxC,MAAI,QAAQ,QAAQ,sBAAsB;AAC1C,MAAI,OAAO;AAAA,IACT,MAAM,MAAM;AAAA,IACZ,OAAO,MAAM;AAAA,IACb,KAAK,MAAM;AAAA,IACX,QAAQ,MAAM;AAAA,EAChB;AACA,MAAI,eAAe,SAAS,GAAG,KAAK,CAAC,oBAAoB,SAAS,GAAG,GAAG;AACtE,QAAI,QAAQ,KAAK,QAAQ,KAAK;AAC9B,SAAK,QAAQ,KAAK,QAAQ,QAAQ,cAAc;AAAA,EAClD;AACA,MAAI,eAAe,SAAS,GAAG,KAAK,CAAC,oBAAoB,SAAS,GAAG,GAAG;AACtE,QAAI,SAAS,KAAK,SAAS,KAAK;AAChC,SAAK,SAAS,KAAK,SAAS,QAAQ,eAAe;AAAA,EACrD;AACA,SAAO;AACT;AACA,IAAI,mBAAmB,SAAU,SAAS;AACxC,MAAI,QAAQ,OAAO,iBAAiB,OAAO;AAC3C,MAAI,WAAW,MAAM;AACrB,MAAI,UAAU,aAAa,UAAU,aAAa;AAClD,MAAI,SAAS;AAAE,WAAO,aAAa;AAAA,EAAI;AACvC,MAAI,YAAY,MAAM,YAAY;AAClC,MAAI,UAAU,cAAc,UAAU,cAAc;AACpD,MAAI,YAAY,MAAM,YAAY;AAClC,MAAI,UAAU,cAAc,UAAU,cAAc;AACpD,MAAI,WAAW,SAAS;AAAE,WAAO,aAAa;AAAA,EAAI;AAClD,MAAI,SAAS;AAAE,WAAO,aAAa;AAAA,EAAG;AACtC,MAAI,SAAS;AAAE,WAAO,aAAa;AAAA,EAAG;AACtC,SAAO;AACT;AACA,IAAI,cAAc,SAAU,SAAS,MAAM;AACzC,MAAI,QAAQ,OAAO,iBAAiB,OAAO;AAC3C,MAAI,WAAW,MAAM;AACrB,MAAI,eAAe,MAAO,cAAc,IAAK;AAC7C,MAAI,UAAU,aAAa,UAAU,aAAa;AAClD,MAAI,kBAAkB,iBAAiB,UAAU,iBAAiB;AAClE,SAAO,WAAW;AACpB;AACA,IAAI,sBAAsB,SAAU,SAAS,MAAM;AACjD,MAAI,QAAQ,OAAO,iBAAiB,OAAO;AAC3C,MAAI,WAAW,MAAM;AACrB,MAAI,eAAe,MAAO,cAAc,IAAK;AAC7C,MAAI,UAAU,aAAa,UAAU,aAAa,YAAY,aAAa;AAC3E,MAAI,kBAAkB,iBAAiB,UAAU,iBAAiB,YAAY,iBAAiB;AAC/F,SAAO,WAAW;AACpB;AACA,IAAI,iBAAiB,SAAU,SAAS,MAAM;AAC5C,MAAI,SAAS,KAAK;AAChB,WAAO,QAAQ,cAAc,QAAQ;AAAA,EACvC,OAAO;AACL,WAAO,QAAQ,eAAe,QAAQ;AAAA,EACxC;AACF;AACA,IAAI,iBAAiB,SAAU,SAAS,aAAa;AACnD,MAAI,iBAAiB;AACrB,MAAI,OAAO,eAAe,iBAAiB,OAAO;AAClD,mBAAiB,QAAQ;AACzB,SAAO,gBAAgB;AACrB,QAAI,eAAe,gBAAgB,GAAG,KAAK,oBAAoB,gBAAgB,GAAG,GAAG;AACnF,aAAO,sBAAsB,MAAM,eAAe,sBAAsB,GAAG,GAAG;AAAA,IAChF;AACA,QAAI,eAAe,gBAAgB,GAAG,KAAK,oBAAoB,gBAAgB,GAAG,GAAG;AACnF,aAAO,sBAAsB,MAAM,eAAe,sBAAsB,GAAG,GAAG;AAAA,IAChF;AACA,qBAAiB,eAAe;AAAA,EAClC;AACA,SAAO;AACT;AACA,IAAI,oCAAoC,SAAU,SAAS,oBAAoB;AAC7E,MAAI,UAAU;AACd,SAAO,SAAS;AACd,QAAI,QAAQ,iBAAiB,GAAG;AAC9B,UAAI,YAAY,QAAQ,iBAAiB;AACzC,UAAI,mBAAmB,KAAK,SAAU,GAAG;AAAE,eAAO,MAAM;AAAA,MAAW,CAAC,GAAG;AACrE,eAAO;AAAA,MACT;AAAA,IACF;AACA,cAAU,QAAQ;AAAA,EACpB;AACA,SAAO;AACT;AACA,IAAI,qBAAqB,SAAU,SAAS,KAAK;AAC/C,MAAI,YAAY,CAAC;AACjB,eAAa;AACb,WAAS,eAAgB;AACvB,QAAI,iBAAiB;AACrB,WAAO,gBAAgB;AACrB,UAAI,YAAY,gBAAgB,GAAG,KAAK,YAAY,gBAAgB,GAAG,GAAG;AACxE,kBAAU,KAAK,cAAc;AAAA,MAC/B;AACA,uBAAiB,eAAe;AAAA,IAClC;AAAA,EACF;AACA,WAAS,UAAW;AAClB,SAAK;AACL,gBAAY;AAAA,EACd;AACA,WAAS,QAAS;AAChB,QAAI,WAAW;AACb,gBAAU,QAAQ,SAAU,GAAG;AAAE,eAAO,EAAE,iBAAiB,UAAU,GAAG;AAAA,MAAG,CAAC;AAC5E,aAAO,iBAAiB,UAAU,GAAG;AAAA,IACvC;AAAA,EACF;AACA,WAAS,OAAQ;AACf,QAAI,WAAW;AACb,gBAAU,QAAQ,SAAU,GAAG;AAAE,eAAO,EAAE,oBAAoB,UAAU,GAAG;AAAA,MAAG,CAAC;AAC/E,aAAO,oBAAoB,UAAU,GAAG;AAAA,IAC1C;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,YAAY,SAAU,SAAS,UAAU;AAC3C,MAAI,UAAU;AACd,SAAO,SAAS;AACd,QAAI,QAAQ,QAAQ,QAAQ,GAAG;AAC7B,aAAO;AAAA,IACT;AACA,cAAU,QAAQ;AAAA,EACpB;AACA,SAAO;AACT;AACA,IAAI,WAAW,SAAU,SAAS,KAAK;AACrC,SAAQ,QAAQ,UACb,MAAM,GAAG,EACT,IAAI,SAAU,GAAG;AAAE,WAAO;AAAA,EAAG,CAAC,EAC9B,QAAQ,GAAG,IAAI;AACpB;AACA,IAAI,WAAW,SAAU,SAAS,KAAK;AACrC,MAAI,SAAS;AACX,QAAI,UAAU,QAAQ,UAAU,MAAM,GAAG,EAAE,OAAO,SAAU,GAAG;AAAE,aAAO;AAAA,IAAG,CAAC;AAC5E,QAAI,QAAQ,QAAQ,GAAG,MAAM,IAAI;AAC/B,cAAQ,QAAQ,GAAG;AACnB,cAAQ,YAAY,QAAQ,KAAK,GAAG;AAAA,IACtC;AAAA,EACF;AACF;AACA,IAAI,cAAc,SAAU,SAAS,KAAK;AACxC,MAAI,SAAS;AACX,QAAI,UAAU,QAAQ,UAAU,MAAM,GAAG,EAAE,OAAO,SAAU,GAAG;AAAE,aAAO,KAAK,MAAM;AAAA,IAAK,CAAC;AACzF,YAAQ,YAAY,QAAQ,KAAK,GAAG;AAAA,EACtC;AACF;AACA,IAAI,WAAW,SAAU,IAAI,OAAO,WAAW;AAC7C,MAAI,QAAQ;AACZ,SAAO,WAAY;AACjB,QAAI,SAAS,CAAC,GAAG,MAAM,UAAU;AACjC,WAAQ,MAAQ,QAAQ,GAAI,IAAI,UAAW,GAAI;AAE/C,QAAI,OAAO;AACT,mBAAa,KAAK;AAAA,IACpB;AACA,QAAI,aAAa,CAAC,OAAO;AACvB,SAAG,KAAK,MAAM,IAAI,CAAE,IAAK,EAAE,OAAQ,MAAO,CAAC;AAAA,IAC7C,OAAO;AACL,cAAQ,WAAW,WAAY;AAC7B,gBAAQ;AACR,WAAG,KAAK,MAAM,IAAI,CAAE,IAAK,EAAE,OAAQ,MAAO,CAAC;AAAA,MAC7C,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AACF;AACA,IAAI,aAAa,WAAY;AAC3B,MAAI,OAAO,WAAW,aAAa;AACjC,QAAI,OAAO,UAAU,UAAU,MAAM,UAAU,KACvC,OAAO,UAAU,UAAU,MAAM,QAAQ,KACzC,OAAO,UAAU,UAAU,MAAM,SAAS,KAC1C,OAAO,UAAU,UAAU,MAAM,OAAO,KACxC,OAAO,UAAU,UAAU,MAAM,OAAO,KACxC,OAAO,UAAU,UAAU,MAAM,aAAa,KAC9C,OAAO,UAAU,UAAU,MAAM,gBAAgB,GAAG;AAC1D,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,iBAAiB,WAAY;AAC/B,MAAI,OAAO,cAAc;AAEvB,QAAI,OAAO,aAAa,EAAE,OAAO;AAG/B,aAAO,aAAa,EAAE,MAAM;AAAA,IAE9B,WAAW,OAAO,aAAa,EAAE,iBAAiB;AAGhD,aAAO,aAAa,EAAE,gBAAgB;AAAA,IACxC;AAAA,EACF,WAAW,OAAO,SAAS,WAAW;AAEpC,WAAO,SAAS,UAAU,MAAM;AAAA,EAClC;AACF;AACA,IAAI,mBAAmB,SAAU,SAAS;AACxC,MAAI,SAAS;AACX,QAAI,QAAQ,OAAO,iBAAiB,OAAO;AAC3C,QAAI,OAAO;AACT,aAAO,MAAM;AAAA,IACf;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,UAAW,MAAM;AACxB,SAAO,EAAE,KAAK,UAAU,KAAK,OAAO,KAAK,SAAS,KAAK;AACzD;AAGA,IAAI,gBAAgB;AAAA,EAClB,MAAM;AAAA,EACN,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX,OAAO;AAAA,EACP,KAAK;AAAA,EACL,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,IACP,WAAW,SAAU,KAAK;AAAE,aAAQ,iBAAiB,MAAM;AAAA,IAAc;AAAA,EAC3E;AACF;AACA,IAAI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX,OAAO;AAAA,EACP,KAAK;AAAA,EACL,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,IACP,WAAW,SAAU,KAAK;AAAE,aAAQ,mBAAmB,MAAM;AAAA,IAAW;AAAA,EAC1E;AACF;AACA,SAAS,0BAA2B,KAAK;AACvC,WAAS,IAAK,KAAK,MAAM;AACvB,QAAI,aAAa,IAAI,IAAI;AACzB,WAAO,IAAI,cAAc,IAAI;AAAA,EAC/B;AACA,WAAS,IAAK,KAAK,MAAM,OAAO;AAC9B,QAAI,IAAI,IAAI,CAAC,IAAI,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,IAAI,EAAE,KAAK,IAAI;AAAA,EAClE;AACA,SAAO,EAAE,KAAU,IAAS;AAC9B;AACA,SAAS,cAAe,kBAAkB,aAAa,oBAAoB;AACzE,mBAAiB,qBAAqB,IAAI;AAC1C,MAAI,MAAM,gBAAgB,eAAe,gBAAgB;AACzD,MAAI,aAAa,0BAA0B,GAAG;AAC9C,MAAI,SAAS;AAAA,IACX,aAAa;AAAA,EACf;AACA,SAAO,iBAAiB,UAAU,WAAY;AAC5C,kCAA8B,gBAAgB;AAAA,EAChD,CAAC;AACD,aAAW,WAAY;AACrB,eAAW;AAAA,EACb,GAAG,EAAE;AACL,WAAS,aAAc;AACrB,kCAA8B,gBAAgB;AAC9C,6BAAyB,gBAAgB;AAAA,EAC3C;AACA,WAAS,8BAA+BA,mBAAkB;AACxD,WAAO,OAAO,iBAAiBA,iBAAgB;AAC/C,QAAI,cAAc,eAAeA,mBAAkB,OAAO,IAAI;AAC9D,QAAI,UAAU,WAAW,GAAG;AAC1B,aAAO,kBAAkB,OAAO;AAAA,IAClC;AACA,WAAO,cAAc;AAAA,EACvB;AACA,WAAS,yBAA0BA,mBAAkB;AACnD,QAAI,OAAOA,kBAAiB,sBAAsB;AAClD,WAAO,SAASA,kBAAiB,eAAgB,KAAK,QAAQ,KAAK,QAAQA,kBAAiB,cAAe;AAC3G,WAAO,SAASA,kBAAiB,gBAAiB,KAAK,SAAS,KAAK,OAAOA,kBAAiB,eAAgB;AAAA,EAC/G;AACA,WAAS,yBAA0B;AACjC,WAAO;AAAA,MACL,MAAM,OAAO;AAAA,MACb,aAAa,OAAO;AAAA,MACpB,iBAAiB,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,WAAS,qBAAsB,MAAM;AACnC,WAAO;AAAA,MACL,OAAO,WAAW,IAAI,MAAM,OAAO;AAAA,MACnC,KAAK,WAAW,IAAI,MAAM,KAAK;AAAA,IACjC;AAAA,EACF;AACA,WAAS,yBAA0B;AACjC,QAAI,QAAQ,WAAW,IAAI,OAAO,MAAM,OAAO,IAAI,OAAO;AAC1D,QAAI,MAAM,WAAW,IAAI,OAAO,MAAM,KAAK,IAAI,OAAO;AACtD,WAAO,EAAE,OAAc,IAAS;AAAA,EAClC;AACA,WAAS,oCAAqC;AAC5C,QAAI,QAAQ,WAAW,IAAI,OAAO,aAAa,OAAO,IAAI,OAAO;AACjE,QAAI,MAAM,WAAW,IAAI,OAAO,aAAa,KAAK,IAAI,OAAO;AAC7D,WAAO,EAAE,OAAc,IAAS;AAAA,EAClC;AACA,WAAS,QAAS,SAAS;AACzB,QAAI,cAAc;AAClB,QAAI,YAAY,SAAS;AACvB,UAAI,OAAO,YAAY,sBAAsB;AAC7C,aAAO,gBAAgB,aAAa,KAAK,SAAS,KAAK,MAAM,KAAK,QAAQ,KAAK;AAAA,IACjF;AACA,WAAO,WAAW,IAAI,SAAS,MAAM,IAAI,WAAW,IAAI,QAAQ,OAAO;AAAA,EACzE;AACA,WAAS,0BAA2B,SAAS;AAC3C,QAAI,WAAW,WAAW,IAAI,SAAS,kBAAkB,KAAK,QAAQ,gBAAgB,KAAK;AAC3F,WAAO,WAAW,WAAW,IAAI,QAAQ,OAAO;AAAA,EAClD;AACA,WAAS,YAAa,SAAS;AAC7B,QAAI,QAAQ,0BAA0B,OAAO,KAAK,WAAW,IAAI,OAAO,MAAM,OAAO,IAAI,OAAO,eAAe,WAAW,IAAI,kBAAkB,aAAa;AAC7J,WAAO;AAAA,MACL;AAAA,MACA,KAAK,QAAQ,QAAQ,OAAO,IAAI,WAAW,IAAI,QAAQ,OAAO;AAAA,IAChE;AAAA,EACF;AACA,WAAS,QAAS,SAAS,MAAM;AAC/B,eAAW,IAAI,SAAS,WAAW,IAAI;AAAA,EACzC;AACA,WAAS,aAAc,UAAU;AAC/B,WAAO,WAAW,IAAI,UAAU,cAAc;AAAA,EAChD;AACA,WAAS,eAAgB,SAAS,aAAa;AAC7C,QAAI,CAAC,aAAa;AAChB,cAAQ,MAAM,eAAe,WAAW;AAAA,IAC1C,OAAO;AACL,iBAAW,IAAI,QAAQ,OAAO,aAAa,WAAW;AAAA,IACxD;AACA,YAAQ,gBAAgB,IAAI;AAAA,EAC9B;AACA,WAAS,eAAgB,SAAS;AAChC,WAAO,QAAQ,gBAAgB;AAAA,EACjC;AACA,WAAS,cAAe,SAASC,YAAW;AAC1C,QAAI,QAAQ,eAAe,MAAM,UAAa,QAAQ,eAAe,MAAMA,YAAW;AACpF,UAAIA,YAAW;AACb,gBAAQ,MAAM,eAAe,YAAY;AAAA,MAC3C,OAAO;AACL,gBAAQ,MAAM,aAAa;AAAA,MAC7B;AACA,cAAQ,eAAe,IAAIA;AAAA,IAC7B;AAAA,EACF;AACA,WAAS,YAAa,SAAS;AAC7B,WAAO,QAAQ,eAAe,MAAM,UAAa,QAAQ,eAAe;AAAA,EAC1E;AACA,WAAS,gBAAiB,GAAG,GAAG;AAC9B,QAAI,MAAM,OAAO;AACjB,QAAI,OAAO,IAAI;AACf,QAAI,MAAM,IAAI;AACd,QAAI,QAAQ,IAAI;AAChB,QAAI,SAAS,IAAI;AAGjB,QAAI,SAAS,MAAM,GAAG;AACpB,eAAS,MAAM;AAAA,IACjB;AACA,QAAI,gBAAgB,OAAO;AAC3B,QAAI,gBAAgB,YAAY;AAC9B,aAAO,IAAI,cAAc,QAAQ,IAAI,cAAc,SAAS,IAAI,OAAO,IAAI;AAAA,IAC7E,OAAO;AACL,aAAO,IAAI,QAAQ,IAAI,SAAS,IAAI,cAAc,OAAO,IAAI,cAAc;AAAA,IAC7E;AAAA,EACF;AACA,WAAS,yBAA0B,OAAO;AACxC,QAAI,MAAM;AACV,QAAI,OAAO;AACX,QAAI,gBAAgB,cAAc;AAChC,aAAO;AACP,YAAM,OAAO,KAAK;AAAA,IACpB,OAAO;AACL,aAAO,OAAO,KAAK;AACnB,YAAM;AAAA,IACR;AACA,WAAO;AAAA,MACL;AAAA,MAAU;AAAA,IACZ;AAAA,EACF;AACA,WAAS,cAAe,SAAS;AAC/B,WAAO,WAAW,IAAI,SAAS,YAAY;AAAA,EAC7C;AACA,WAAS,eAAgB,SAAS;AAChC,WAAO,WAAW,IAAI,SAAS,aAAa;AAAA,EAC9C;AACA,WAAS,eAAgB,SAAS,KAAK;AACrC,WAAO,WAAW,IAAI,SAAS,eAAe,GAAG;AAAA,EACnD;AACA,WAASC,aAAa,UAAU;AAC9B,WAAO,aAAa,QAAQ;AAAA,EAC9B;AACA,WAAS,kBAAmB;AAC1B,kCAA8B,gBAAgB;AAAA,EAChD;AACA,WAAS,SAAU,OAAO,OAAO;AAC/B,eAAW,IAAI,OAAO,SAAS,KAAK;AAAA,EACtC;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAaA;AAAA,IACb;AAAA,EACF;AACF;AAGA,IAAI,WAAW;AAEf,IAAI,aAAa;AAAA,EACf,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AACN;AACA,SAAS,gBAAiB,UAAU,MAAM,MAAM;AAC9C,MAAI,OAAO,KAAK;AAChB,MAAI,QAAQ,KAAK;AACjB,MAAI,MAAM,KAAK;AACf,MAAI,SAAS,KAAK;AAClB,MAAI,IAAI,SAAS;AACjB,MAAI,IAAI,SAAS;AACjB,MAAI,IAAI,QAAQ,IAAI,SAAS,IAAI,OAAO,IAAI,QAAQ;AAClD,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,SAAS,KAAK;AAChB,YAAQ;AACR,UAAM;AACN,UAAM;AAAA,EACR,OAAO;AACL,YAAQ;AACR,UAAM;AACN,UAAM;AAAA,EACR;AACA,MAAI,eAAe,MAAM;AACzB,MAAI,eAAe,eAAe,MAAM,MAAM,eAAe;AAC7D,MAAI,MAAM,MAAM,cAAc;AAC5B,WAAO;AAAA,MACL,WAAW;AAAA,MACX,cAAc,gBAAgB,MAAM,QAAQ;AAAA,IAC9C;AAAA,EACF,WAAW,MAAM,QAAQ,cAAc;AACrC,WAAO;AAAA,MACL,WAAW;AAAA,MACX,cAAc,gBAAgB,MAAM,UAAU;AAAA,IAChD;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAgB,SAAS,MAAM,OAAO;AAC7C,MAAI,SAAS;AACX,QAAI,YAAY,QAAQ;AACtB,UAAI,SAAS,KAAK;AAChB,gBAAQ,cAAc;AAAA,MACxB,OAAO;AACL,gBAAQ,aAAa;AAAA,MACvB;AAAA,IACF,OAAO;AACL,UAAI,SAAS,KAAK;AAChB,gBAAQ,SAAS,OAAO,CAAC;AAAA,MAC3B,OAAO;AACL,gBAAQ,SAAS,GAAG,KAAK;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAI,iBAAiB,SAAU,SAAS,MAAM;AAC5C,MAAK,SAAS,OAAS,QAAO;AAE9B,MAAI,UAAU;AACd,MAAI,YAAY;AAChB,MAAI,YAAY;AAChB,MAAI,QAAQ;AACZ,WAAS,QAAS,YAAY,QAAQ;AACpC,gBAAY;AACZ,YAAQ;AACR,UAAM;AAAA,EACR;AACA,WAAS,QAAS;AAChB,QAAI,YAAY,MAAM;AACpB,gBAAU,sBAAsB,SAAU,WAAW;AACnD,YAAI,cAAc,MAAM;AACtB,sBAAY;AAAA,QACd;AACA,YAAI,WAAW,YAAY;AAC3B,oBAAY;AACZ,YAAI,eAAgB,WAAW,MAAQ;AACvC,uBAAe,cAAc,UAAW,IAAI,eAAgB;AAC5D,uBAAe,SAAS,MAAM,YAAY;AAC1C,kBAAU;AACV,cAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AACA,WAAS,OAAQ;AACf,QAAI,YAAY,MAAM;AACpB,2BAAqB,OAAO;AAC5B,gBAAU;AAAA,IACZ;AACA,gBAAY;AAAA,EACd;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,gBAAiB,SAAS;AACjC,SAAO,WAAY;AACjB,WAAO,eAAe,SAAS,QAAQ,sBAAsB,CAAC;AAAA,EAChE;AACF;AACA,SAAS,oBAAqB,WAAW;AACvC,MAAI,oBAAoB,CAAC;AACzB,MAAI,UAAU,UAAU;AACxB,SAAO,SAAS;AACd,QAAI,gBAAgB,iBAAiB,OAAO;AAC5C,QAAI,iBAAiB,CAAC,SAAS,SAAS,sBAAsB,GAAG;AAC/D,UAAI,iBAAiB,CAAC;AACtB,cAAQ,eAAe;AAAA,QACrB,KAAK,WAAW;AACd;AACE,2BAAe,IAAI;AAAA,cACjB,UAAU,eAAe,SAAS,GAAG;AAAA,YACvC;AACA,2BAAe,IAAI;AAAA,cACjB,UAAU,eAAe,SAAS,GAAG;AAAA,YACvC;AAAA,UACF;AACA;AAAA,QACF,KAAK,WAAW;AACd;AACE,2BAAe,IAAI;AAAA,cACjB,UAAU,eAAe,SAAS,GAAG;AAAA,YACvC;AAAA,UACF;AACA;AAAA,QACF,KAAK,WAAW;AACd;AACE,2BAAe,IAAI;AAAA,cACjB,UAAU,eAAe,SAAS,GAAG;AAAA,YACvC;AAAA,UACF;AACA;AAAA,MACJ;AACA,wBAAkB,KAAK;AAAA,QACrB;AAAA,QACA,SAAS,gBAAgB,OAAO;AAAA,QAChC,iBAAiB;AAAA,MACnB,CAAC;AAAA,IACH;AACA,cAAU,QAAQ;AAAA,EACpB;AACA,SAAO;AACT;AACA,SAAS,gBAAiB,eAAe,UAAU;AACjD,gBAAc,QAAQ,SAAU,UAAU;AACxC,QAAI,iBAAiB,SAAS;AAC9B,QAAI,UAAU,SAAS;AACvB,QAAI,OAAO,QAAQ;AACnB,QAAI,eAAe,GAAG;AACpB,qBAAe,EAAE,eAAe,gBAAgB,UAAU,KAAK,IAAI;AACnE,eAAS,aAAa;AAAA,IACxB;AACA,QAAI,eAAe,GAAG;AACpB,qBAAe,EAAE,eAAe,gBAAgB,UAAU,KAAK,IAAI;AACnE,eAAS,aAAa;AAAA,IACxB;AAAA,EACF,CAAC;AACH;AACA,SAAS,yBAA0B,eAAe,UAAU;AAC1D,MAAI,UAAU,SAAS,iBAAiB,SAAS,GAAG,SAAS,CAAC;AAC9D,SAAO,SAAS;AACd,QAAI,iBAAiB,cAAc,KAAK,SAAU,GAAG;AAAE,aAAO,EAAE,oBAAoB;AAAA,IAAS,CAAC;AAC9F,QAAI,gBAAgB;AAClB,aAAO;AAAA,IACT;AACA,cAAU,QAAQ;AAAA,EACpB;AACA,SAAO;AACT;AACA,SAAS,aAAcC,aAAY,gBAAgB;AACjD,MAAK,mBAAmB,OAAS,kBAAiB;AAElD,MAAI,gBAAgBA,YAAW,OAAO,SAAU,KAAK,WAAW;AAC9D,QAAI,oBAAoB,oBAAoB,SAAS,EAAE,OAAO,SAAU,GAAG;AACzE,aAAO,CAAC,IAAI,KAAK,SAAU,GAAG;AAAE,eAAO,EAAE,oBAAoB,EAAE;AAAA,MAAiB,CAAC;AAAA,IACnF,CAAC;AACD,WAAO,IAAI,OAAQ,iBAAiB;AAAA,EACtC,GAAG,CAAC,CAAC;AACL,SAAO,SAAU,KAAK;AACpB,QAAIC,iBAAgB,IAAI;AACxB,QAAI,QAAQ,IAAI;AAEhB,QAAI,OAAO;AACT,oBAAc,QAAQ,SAAU,GAAG;AACjC,UAAE,eAAe,KAAK,EAAE,eAAe,EAAE,SAAS,KAAK;AACvD,UAAE,eAAe,KAAK,EAAE,eAAe,EAAE,SAAS,KAAK;AAAA,MACzD,CAAC;AACD;AAAA,IACF;AACA,QAAIA,gBAAe;AACjB,sBAAgB,eAAeA,eAAc,aAAa;AAC1D,oBAAc,QAAQ,SAAU,UAAU;AACxC,YAAIC,OAAM,SAAS;AACnB,YAAI,IAAIA,KAAI;AACZ,YAAI,IAAIA,KAAI;AACZ,YAAI,GAAG;AACL,cAAI,EAAE,cAAc;AAClB,gBAAI,QAAQ,EAAE;AACd,gBAAI,YAAY,MAAM;AACtB,gBAAI,cAAc,MAAM;AACxB,cAAE,SAAS,QAAQ,WAAW,cAAc,cAAc;AAAA,UAC5D,OAAO;AACL,cAAE,SAAS,KAAK;AAAA,UAClB;AAAA,QACF;AACA,YAAI,GAAG;AACL,cAAI,EAAE,cAAc;AAClB,gBAAI,QAAQ,EAAE;AACd,gBAAI,cAAc,MAAM;AACxB,gBAAI,gBAAgB,MAAM;AAC1B,cAAE,SAAS,QAAQ,aAAa,gBAAgB,cAAc;AAAA,UAChE,OAAO;AACL,cAAE,SAAS,KAAK;AAAA,UAClB;AAAA,QACF;AAAA,MACF,CAAC;AACD,UAAI,uBAAuB,cAAc,OAAO,SAAU,GAAG;AAAE,eAAO,EAAE;AAAA,MAAY,CAAC;AACrF,UAAI,qBAAqB,UAAU,qBAAqB,SAAS,GAAG;AAElE,YAAI,sBAAsB,yBAAyB,sBAAsBD,eAAc,aAAa;AACpG,YAAI,qBAAqB;AACvB,+BAAqB,QAAQ,SAAU,GAAG;AACxC,gBAAI,MAAM,qBAAqB;AAC7B,gBAAE,eAAe,KAAK,EAAE,eAAe,EAAE,SAAS,KAAK;AACvD,gBAAE,eAAe,KAAK,EAAE,eAAe,EAAE,SAAS,KAAK;AAAA,YACzD;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAKA,SAAS,iBAAkB;AACzB,GAAC,SAAU,aAAa;AACtB,QAAI,eAAe,YAAY,aAAa,CAAC,YAAY,UAAU,SAAS;AAC1E,kBAAY,UAAU,UACZ,YAAY,UAAU,mBAClB,YAAY,UAAU,sBACtB,YAAY,UAAU,qBACtB,YAAY,UAAU,oBACtB,YAAY,UAAU,yBACtB,SAAU,GAAG;AACX,YAAI,WAAW,KAAK,YAAY,KAAK,eAAe,iBAAiB,CAAC;AAAG,YAAI,IAAI,QAAQ;AACzF,eAAO,EAAE,KAAK,KAAK,QAAQ,KAAK,CAAC,MAAM,MAAM;AAAA,QAAE;AAC/C,eAAO,IAAI;AAAA,MACb;AAAA,IAChB;AAAA,EACF,GAAG,OAAO;AAGV,MAAI,CAAC,MAAM,UAAU,MAAM;AACzB,UAAM,UAAU,OAAO,SAAU,KAAoB;AACnD,UAAI,QAAQ,MAAM;AAChB,cAAM,IAAI,UAAU,kDAAkD;AAAA,MACxE;AACA,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU;AAAA,MACtB;AACA,UAAI,IAAI,OAAO,IAAI;AACnB,UAAI,MAAM,EAAE,WAAW;AACvB,UAAI,UAAU,UAAU,UAAU,IAAI,UAAU,CAAC,IAAI;AACrD,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAI,KAAK,KAAK,IAAI,KAAK,SAAS,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG;AAC3C,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,IAAI,OAAO,WAAW,aAAa;AACjC,iBAAe;AACjB;AAEA,IAAI,uBAAuB;AAAA,EACzB,UAAU;AAAA,EACV,SAAS;AACX;AACA,IAAI,yBAAyB;AAAA,EAC3B,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,kBAAkB;AACpB;AACA,IAAI,kCAAkC;AAAA,EACpC,SAAS;AACX;AACA,IAAI,MAAM,CAAC;AACX,IAAK,MAAM,cAAe,IAAI;AAAA,EAC1B,UAAU;AAAA,EACV,cAAc;AAAA,EACd,aAAa;AACf;AACF,IAAK,MAAM,iBAAiB,aAAc,IAAI;AAAA,EAC1C,SAAS;AACX;AACF,IAAK,MAAM,iBAAiB,oBAAoB,qBAAsB,IAAI;AAC1E,IAAK,MAAM,iBAAiB,oBAAoB,YAAa,IAAI;AACjE,IAAK,MAAM,iBAAiB,kBAAkB,YAAa,IAAI;AAC/D,IAAK,MAAM,YAAa,IAAI;AAAA,EACxB,cAAc;AAChB;AACF,IAAK,MAAM,eAAe,aAAc,IAAI;AAC5C,IAAK,MAAM,eAAe,WAAY,IAAI;AAC1C,IAAK,MAAM,eAAe,WAAY,IAAI;AAAA,EACtC,YAAY;AACd;AACF,IAAK,MAAM,UAAW,IAAI;AAAA,EACtB,cAAc;AAAA;AAAA;AAGhB;AACF,IAAK,MAAM,aAAa,WAAY,IAAI;AAAA,EACpC,YAAY;AACd;AACF,IAAK,MAAM,aAAa,IAAK,IAAI;AAAA,EAC7B,kBAAkB;AACpB;AACF,IAAK,MAAM,sBAAsB,IAAK,IAAI;AAAA,EACtC,gBAAgB;AAAA,EAChB,oBAAoB;AACtB;AACF,IAAK,MAAM,iBAAkB,IAAI;AAAA,EAC7B,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,eAAe;AACjB;AACF,IAAK,MAAM,yBAA0B,IAAI;AAAA,EACrC,MAAM;AACR;AACF,IAAK,MAAM,iBAAiB,oBAAoB,2BAA4B,IAAI;AAAA,EAC5E,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,SAAS;AAAA,EACT,kBAAkB;AACpB;AACF,IAAK,MAAM,iBAAiB,kBAAkB,2BAA4B,IAAI;AAAA,EAC1E,UAAU;AAAA,EACV,SAAS;AAAA,EACT,OAAO;AACT;AACF,IAAK,MAAM,iCAAkC,IAAI;AAAA,EAC7C,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,mBAAmB;AAAA,EACnB,eAAe;AACjB;AACF,IAAK,MAAM,2BAA4B,IAAI;AAAA,EACvC,oBAAoB;AAAA,EACpB,QAAQ;AACV;AACF,SAAS,mBAAoBE,MAAK;AAChC,SAAO,OAAO,KAAKA,IAAG,EAAE,OAAO,SAAU,aAAa,UAAU;AAC9D,QAAI,YAAYA,KAAI,QAAQ;AAC5B,QAAI,OAAQ,cAAe,UAAU;AACnC,aAAQ,KAAK,cAAc,WAAW,MAAO,mBAAmB,SAAS,IAAK;AAAA,IAChF;AACA,WAAQ,KAAK,cAAc,WAAW,MAAM,YAAY;AAAA,EAC1D,GAAG,EAAE;AACP;AACA,SAAS,iBAAkB;AACzB,MAAI,OAAQ,WAAY,aAAa;AACnC,QAAI,OAAO,OAAO,SAAS,QAAQ,OAAO,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACjF,QAAI,QAAQ,OAAO,SAAS,cAAc,OAAO;AACjD,UAAM,KAAK;AACX,QAAI,YAAY,mBAAmB,GAAG;AACtC,UAAM,OAAO;AACb,QAAI,MAAM,YAAY;AACpB,YAAM,WAAW,UAAU;AAAA,IAC7B,OAAO;AACL,YAAM,YAAY,OAAO,SAAS,eAAe,SAAS,CAAC;AAAA,IAC7D;AACA,SAAK,YAAY,KAAK;AAAA,EACxB;AACF;AACA,SAAS,qBAAsB,QAAQ;AACrC,MAAI,UAAU,OAAQ,WAAY,aAAa;AAC7C,QAAI,OAAO,OAAO,SAAS,QAAQ,OAAO,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACjF,QAAI,QAAQ,OAAO,SAAS,cAAc,OAAO;AACjD,QAAI,YAAY,mBAAmB;AAAA,MACjC,UAAU;AAAA,QACR,QAAS,SAAS;AAAA,MACpB;AAAA,IACF,CAAC;AACD,UAAM,OAAO;AACb,QAAI,MAAM,YAAY;AACpB,YAAM,WAAW,UAAU;AAAA,IAC7B,OAAO;AACL,YAAM,YAAY,OAAO,SAAS,eAAe,SAAS,CAAC;AAAA,IAC7D;AACA,SAAK,YAAY,KAAK;AACtB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,YAAa,cAAc;AAClC,MAAI,gBAAgB,OAAQ,WAAY,aAAa;AACnD,QAAI,OAAO,OAAO,SAAS,QAAQ,OAAO,SAAS,qBAAqB,MAAM,EAAE,CAAC;AACjF,SAAK,YAAY,YAAY;AAAA,EAC/B;AACF;AAEA,IAAI,aAAa,CAAC,aAAa,YAAY;AAC3C,IAAI,aAAa,CAAC,aAAa,WAAW;AAC1C,IAAI,gBAAgB,CAAC,WAAW,UAAU;AAC1C,IAAI,0BAA0B;AAC9B,IAAI,iBAAiB;AACrB,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,aAAa,CAAC;AAClB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,uBAAuB;AAC3B,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,eAAe;AACnB,IAAI,0BAA0B;AAC9B,IAAI,qBAAqB;AACzB,IAAI,2BAA2B,gBAAgB;AAC/C,IAAI,WAAW,WAAW;AAC1B,SAAS,eAAgB;AACvB,MAAI,OAAO,WAAW,aAAa;AACjC,qBAAiB;AAAA,EACnB;AACF;AACA,SAAS,mBAAoB;AAC3B,aAAW,QAAQ,SAAU,GAAG;AAC9B,WAAO,SAAS,iBAAiB,GAAG,aAAa,EAAE,SAAS,MAAM,CAAC;AAAA,EACrE,CAAC;AACH;AAEA,SAAS,sBAAuB;AAC9B,MAAI,aAAa,WAAW,CAAC;AAC7B,SAAO,SAAS,oBAAoB,YAAY,aAAa,EAAE,SAAS,MAAM,CAAC;AACjF;AACA,SAAS,mBAAoB;AAC3B,aAAW,QAAQ,SAAU,GAAG;AAC9B,WAAO,SAAS,iBAAiB,GAAG,aAAa,EAAE,SAAS,MAAM,CAAC;AAAA,EACrE,CAAC;AACH;AACA,SAAS,sBAAuB;AAC9B,aAAW,QAAQ,SAAU,GAAG;AAC9B,WAAO,SAAS,oBAAoB,GAAG,aAAa,EAAE,SAAS,MAAM,CAAC;AAAA,EACxE,CAAC;AACH;AACA,SAAS,sBAAuB;AAC9B,gBAAc,QAAQ,SAAU,GAAG;AACjC,WAAO,SAAS,iBAAiB,GAAG,WAAW,EAAE,SAAS,MAAM,CAAC;AAAA,EACnE,CAAC;AACH;AACA,SAAS,yBAA0B;AACjC,gBAAc,QAAQ,SAAU,GAAG;AACjC,WAAO,SAAS,oBAAoB,GAAG,WAAW,EAAE,SAAS,MAAM,CAAC;AAAA,EACtE,CAAC;AACH;AACA,SAAS,iBAAkB;AACzB,MAAI,iBAAiB,cAAc,aAAa;AAC9C,WAAO,cAAc;AAAA,EACvB;AACA,MAAI,gBAAgB;AAClB,WAAO,eAAe,iBAAiB,OAAO,SAAS;AAAA,EACzD,OAAO;AACL,WAAO,OAAO,SAAS;AAAA,EACzB;AACF;AACA,SAAS,gBAAiB,gBAAgB,KAAK,WAAW,QAAQ;AAChE,MAAI,IAAI,IAAI;AACZ,MAAI,IAAI,IAAI;AAEZ,MAAI,cAAc,eAAe,sBAAsB;AACvD,MAAI,OAAO,YAAY;AACvB,MAAI,MAAM,YAAY;AACtB,MAAI,QAAQ,YAAY;AACxB,MAAI,SAAS,YAAY;AACzB,MAAI,qBAAqB,gBAAgB,UAAU,OAAO,uBAAuB,EAAE,aAAa,WAAW;AAC3G,MAAI,OAAO,mBAAmB,QAAQ,mBAAmB,QAAQ,mBAAmB,QAAQ;AAC5F,MAAI,OAAO,mBAAmB,OAAO,mBAAmB,SAAS,mBAAmB,OAAO;AAC3F,MAAI,QAAQ,eAAe,UAAU,IAAI;AACzC,QAAM,MAAM,SAAS;AACrB,QAAM,MAAM,YAAY;AACxB,QAAM,MAAM,WAAW;AACvB,QAAM,MAAM,MAAM;AAClB,QAAM,MAAM,OAAO;AACnB,QAAM,MAAM,YAAY;AACxB,QAAM,MAAM,eAAe,WAAW;AACtC,MAAI,UAAU,2BAA2B,GAAG;AAC1C,UAAM,MAAM,YAAY,iBAAiB,OAAO,SAAS,MAAM;AAAA,EACjE,OAAO;AACL,UAAM,MAAM,MAAM,MAAM;AACxB,UAAM,MAAM,OAAO,OAAO;AAAA,EAC5B;AACA,QAAM,MAAM,QAAS,QAAQ,OAAQ;AACrC,QAAM,MAAM,SAAU,SAAS,MAAO;AACtC,QAAM,MAAM,WAAW;AACvB,QAAM,MAAM,aAAa;AACzB,QAAM,MAAM,eAAe,YAAY;AACvC,QAAM,MAAM,gBAAgB;AAC5B,QAAM,MAAM,aAAa;AACzB,MAAI,UAAU,WAAW,EAAE,WAAW;AACpC,eAAW,WAAY;AACrB,eAAS,MAAM,mBAAmB,UAAU,WAAW,EAAE,SAAS;AAClE,UAAI,aAAa,OAAO,iBAAiB,MAAM,iBAAiB,EAAE;AAClE,2BAAqB,qBAAqB,UAAU;AAAA,IACtD,CAAC;AAAA,EACH,OAAO;AACL,yBAAqB,qBAAqB,MAAM;AAAA,EAClD;AACA,WAAS,OAAO,UAAU,WAAW,EAAE,eAAe,UAAU;AAChE,WAAS,OAAO,UAAU;AAC1B,SAAO;AAAA,IACL;AAAA,IACA,aAAa,EAAE,GAAG,OAAO,GAAG,GAAG,OAAO,EAAE;AAAA,IACxC,eAAe,EAAE,MAAM,OAAO,GAAG,KAAK,MAAM,EAAE;AAAA,IAC9C,SAAS;AAAA,MACP,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACF;AACA,SAAS,iBAAkB,kBAAkB;AAC3C,MAAI,YAAY,WAAW,OAAO,SAAU,GAAG;AAAE,WAAO,iBAAiB,kBAAkB,EAAE;AAAA,EAAS,CAAC,EAAE,CAAC;AAC1G,MAAI,iBAAiB,UAAU,WAAW,QAAQ,gBAAgB;AAClE,MAAIC,kBAAiB,UAAU,WAAW,EAAE;AAC5C,MAAI,gBAAgB,iBAAiB,sBAAsB;AAC3D,SAAO;AAAA,IACL;AAAA,IACA,SAAS;AAAA,IACT,MAAM;AAAA,MACJ,cAAc,cAAc,SAAS,cAAc;AAAA,MACnD,aAAa,cAAc,QAAQ,cAAc;AAAA,IACnD;AAAA,IACA,cAAc;AAAA,IACd,SAAS,UAAU,WAAW,EAAE,kBAAkB,UAAU,WAAW,EAAE,gBAAgB,cAAc,IAAI;AAAA,IAC3G,eAAe;AAAA,IACf,UAAU,EAAE,GAAG,GAAG,GAAG,EAAE;AAAA,IACvB,WAAW,UAAU,WAAW,EAAE;AAAA,IAClC,aAAaA,kBAAiBA,gBAAe,IAAI;AAAA,IACjD,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,oBAAoB;AAAA,EACtB;AACF;AACA,SAAS,oBAAqB,UAAU;AACtC,WAAS,UAAW;AAClB,gBAAY,UAAU,OAAO,UAAU;AACvC,cAAU,MAAM,MAAM,qBAAqB;AAC3C,mBAAe,EAAE,YAAY,UAAU,KAAK;AAC5C,aAAS;AAAA,EACX;AACA,WAAS,uBAAwBF,MAAK,UAAU,WAAW;AACzD,QAAI,MAAMA,KAAI;AACd,QAAI,OAAOA,KAAI;AAEf,aAAS,UAAU,OAAO,UAAU;AACpC,QAAI,WAAW;AACb,eAAS,UAAU,MAAM,mBAAmB,SAAS;AAAA,IACvD;AACA,cAAU,QAAQ,IAAI;AACtB,cAAU,QAAQ,IAAI;AACtB,mBAAe,QAAQ;AACvB,eAAW,WAAY;AACrB,cAAQ;AAAA,IACV,GAAG,WAAW,EAAE;AAAA,EAClB;AACA,WAAS,kBAAmB,SAAS;AACnC,WAAO,QAAQ,oBACX,QAAQ,kBAAkB,cAAc,UAAU,WAAW,GAAG,cAAc,OAAO,IACrF;AAAA,EACN;AACA,WAAS,mBAAoB,UAAU,KAAK;AAC1C,aAAS,UAAU,OAAO,UAAU;AACpC,mBAAe,UAAU,KAAK,IAAI;AAIlC,eAAW,WAAY;AACrB,UAAI;AAAA,IACN,GAAG,WAAW,EAAE;AAAA,EAClB;AACA,MAAI,cAAc,eAAe;AAC/B,QAAI,YAAY,WAAW,OAAO,SAAU,GAAG;AAAE,aAAO,EAAE,YAAY,cAAc;AAAA,IAAe,CAAC,EAAE,CAAC;AACvG,QAAI,kBAAkB,UAAU,WAAW,CAAC,GAAG;AAC7C,UAAI,aAAa,UAAU,cAAc;AACzC,6BAAuB,WAAW,eAAe,MAAM,KAAK,IAAI,KAAK,UAAU,WAAW,EAAE,oBAAoB,CAAC,GAAG,UAAU,WAAW,EAAE,SAAS;AAAA,IACtJ,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF,OAAO;AACL,QAAI,cAAc,WAAW,OAAO,SAAU,GAAG;AAAE,aAAO,MAAM,cAAc;AAAA,IAAW,CAAC,EAAE,CAAC;AAC7F,QAAI,aAAa;AACf,UAAI,MAAM,YAAY,WAAW;AACjC,UAAI,YAAY,IAAI;AACpB,UAAI,kBAAkB,IAAI;AAC1B,WAAK,cAAc,UAAU,cAAc,eAAe,eAAe,CAAC,oBAAoB,YAAY,cAAc,GAAG;AACzH,YAAI,aAAa,YAAY,OAAO,uBAAuB;AAG3D,YAAI,CAAC,UAAU,WAAW,WAAW,KAAK,UAAU,WAAW,eAAe,GAAG;AAC/E,iCAAuB;AAAA,YACrB,KAAK,WAAW,gBAAgB;AAAA,YAChC,MAAM,WAAW,gBAAgB;AAAA,UACnC,GAAG,YAAY,WAAW,EAAE,mBAAmB,YAAY,WAAW,EAAE,SAAS;AAAA,QACnF,OAAO;AACL,cAAI,QAAQ,YAAY,cAAc;AACtC,cAAI,eAAe,MAAM;AACzB,cAAI,cAAc,MAAM;AACxB,cAAI,SAAS,YAAY;AAEzB,sBAAY,uBAAuB;AAAA,YACjC,YAAY;AAAA,cACV;AAAA,cACA,YAAY;AAAA,cACZ;AAAA,cACA,KAAK;AAAA,cACL,gBAAgB;AAAA,YAClB;AAAA,UACF,CAAC;AACD,cAAI,mBAAmB,eAAe,IAClC,OAAO,YAAY,YAAY,WAAW,eAAe,CAAC,CAAC,EAAE,MAC7D,OAAO,uBAAuB,EAAE;AACpC,iCAAuB,OAAO,yBAAyB,gBAAgB,GAAG,YAAY,WAAW,EAAE,mBAAmB,YAAY,WAAW,EAAE,SAAS;AAAA,QAC1J;AAAA,MACF,OAAO;AACL,2BAAmB,YAAY,WAAW,EAAE,mBAAmB,OAAO;AAAA,MACxE;AAAA,IACF,OAAO;AAEL,yBAAmB,eAAe,mBAAmB,OAAO;AAAA,IAC9D;AAAA,EACF;AACF;AACA,IAAI,4BAA6B,yBAASG,6BAA6B;AACrE,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ;AACZ,MAAI,gBAAgB;AACpB,MAAI,iBAAiB;AACrB,WAAS,OAAQ,OAAO;AACtB,QAAI,MAAM,gBAAgB,KAAK;AAC/B,QAAI,WAAW,IAAI;AACnB,QAAI,WAAW,IAAI;AACnB,QAAI,CAAC,OAAO;AACV,UAAI,KAAK,IAAI,WAAW,UAAU,QAAQ,IAAI,iBAAiB,KAAK,IAAI,WAAW,UAAU,QAAQ,IAAI,eAAe;AACtH,eAAO,aAAa;AAAA,MACtB;AAAA,IACF,OAAO;AACL,UAAI,KAAK,IAAI,WAAW,UAAU,QAAQ,IAAI,kBAAkB,KAAK,IAAI,WAAW,UAAU,QAAQ,IAAI,gBAAgB;AACxH,wBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,WAAS,OAAQ;AACf,oBAAgB;AAAA,EAClB;AACA,WAAS,aAAc;AACrB,oBAAgB;AAAA,EAClB;AACA,WAAS,iBAAkB;AACzB,QAAI,OAAO;AACT,cAAQ,WAAW,cAAc,KAAK;AAAA,IACxC;AACA,eAAW,QAAQ,SAAU,GAAG;AAAE,aAAO,OAAO,SAAS,iBAAiB,GAAG,MAAM;AAAA,IAAG,GAAG;AAAA,MACvF,SAAS;AAAA,IACX,CAAC;AACD,kBAAc,QAAQ,SAAU,GAAG;AAAE,aAAO,OAAO,SAAS,iBAAiB,GAAG,IAAI;AAAA,IAAG,GAAG;AAAA,MACxF,SAAS;AAAA,IACX,CAAC;AACD,WAAO,SAAS,iBAAiB,QAAQ,YAAY;AAAA,MACnD,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,WAAS,kBAAmB;AAC1B,iBAAa,KAAK;AAClB,eAAW,QAAQ,SAAU,GAAG;AAAE,aAAO,OAAO,SAAS,oBAAoB,GAAG,MAAM;AAAA,IAAG,GAAG;AAAA,MAC1F,SAAS;AAAA,IACX,CAAC;AACD,kBAAc,QAAQ,SAAU,GAAG;AAAE,aAAO,OAAO,SAAS,oBAAoB,GAAG,IAAI;AAAA,IAAG,GAAG;AAAA,MAC3F,SAAS;AAAA,IACX,CAAC;AACD,WAAO,SAAS,oBAAoB,QAAQ,YAAY;AAAA,MACtD,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AACA,WAAS,eAAgB;AACvB,iBAAa,KAAK;AAClB,oBAAgB;AAChB,QAAI;AAAA,EACN;AACA,SAAO,SAAU,aAAa,QAAQ,MAAM;AAC1C,iBAAa,gBAAgB,WAAW;AACxC,YAAQ,OAAO,WAAW,WAAW,SAAS,WAAW,MAAM;AAC/D,UAAM;AACN,mBAAe;AAAA,EACjB;AACF,EAAG;AACH,SAAS,YAAa,OAAO;AAC3B,MAAI,IAAI,gBAAgB,KAAK;AAC7B,MAAI,CAAC,eAAe,EAAE,WAAW,UAAa,EAAE,WAAW,IAAI;AAC7D,qBAAiB,UAAU,EAAE,QAAQ,MAAM,YAAY;AACvD,QAAI,gBAAgB;AAClB,UAAI,mBAAmB,UAAU,gBAAgB,MAAM,cAAc;AACrE,UAAI,YAAY,WAAW,OAAO,SAAU,GAAG;AAAE,eAAO,EAAE,YAAY;AAAA,MAAkB,CAAC,EAAE,CAAC;AAC5F,UAAI,qBAAqB,UAAU,WAAW,EAAE;AAChD,UAAI,sBAAsB,UAAU,WAAW,EAAE;AACjD,UAAI,YAAY;AAChB,UAAI,sBAAsB,CAAC,UAAU,EAAE,QAAQ,kBAAkB,GAAG;AAClE,oBAAY;AAAA,MACd;AACA,UAAI,uBAAuB,UAAU,EAAE,QAAQ,mBAAmB,GAAG;AACnE,oBAAY;AAAA,MACd;AACA,UAAI,WAAW;AACb,kBAAU,OAAO,WAAW;AAC5B,iBAAS,OAAO,SAAS,MAAM,mBAAmB;AAClD,iBAAS,OAAO,SAAS,MAAM,iBAAiB;AAChD,YAAIC,aAAY,WAAY;AAC1B,sBAAY,OAAO,SAAS,MAAM,mBAAmB;AACrD,sBAAY,OAAO,SAAS,MAAM,iBAAiB;AACnD,iBAAO,SAAS,oBAAoB,WAAWA,UAAS;AACxD,iBAAO,SAAS,oBAAoB,YAAYA,UAAS;AAAA,QAC3D;AACA,eAAO,SAAS,iBAAiB,WAAWA,UAAS;AACrD,eAAO,SAAS,iBAAiB,YAAYA,UAAS;AACtD,kCAA0B,GAAG,UAAU,WAAW,EAAE,gBAAgB,WAAY;AAC9E,yBAAe;AACf,uBAAa,GAAG,iBAAiB,MAAM,MAAM,CAAC;AAC9C,2BAAiB;AACjB,8BAAoB;AAAA,QACtB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,4BAA6B,KAAK,aAAa;AACtD,MAAI,UAAU,IAAI;AAClB,MAAI,UAAU,IAAI;AAClB,MAAK,gBAAgB,OAAS,eAAc;AAE5C,MAAI,WAAW,cAAc,UAAU,OAAO,kCAAkC;AAChF,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,gBAAgB,YAAY;AAC9B,eAAW;AACX,WAAO;AACP,cAAU;AACV,WAAO,cAAc,KAAK;AAAA,EAC5B,OAAO;AACL,eAAW;AACX,WAAO;AACP,cAAU;AACV,WAAO,cAAc,KAAK;AAAA,EAC5B;AACA,MAAI,gBAAgB,SAAS;AAC7B,MAAI,cAAc,SAAS,MAAM;AACjC,MAAI,qBAAqB,KAAK,IAAI,eAAe,KAAK,IAAI,aAAc,WAAW,UAAU,cAAc,OAAO,CAAE,CAAC;AACrH,YAAU,QAAQ,IAAI,IAAI;AAC1B,gBAAc,SAAS,IAAI,IAAI,KAAK,IAAI,SAAS,OAAO,KAAK,IAAI,SAAS,KAAM,WAAW,UAAU,YAAY,IAAI,CAAE,CAAC;AACxH,gBAAc,cAAc,IAAI,IAAI,KAAK,IAAI,SAAS,OAAO,KAAK,IAAI,SAAS,KAAK,QAAQ,CAAC;AAC7F,MAAI,cAAc,SAAS,IAAI,IAAK,SAAS,QAAS,OAAO,GAAK;AAChE,kBAAc,SAAS,IAAI,IAAI,SAAS,QAAQ;AAAA,EAClD;AACA,MAAI,cAAc,SAAS,IAAI,IAAK,SAAS,MAAO,OAAO,GAAK;AAC9D,kBAAc,SAAS,IAAI,IAAI,SAAS,MAAM;AAAA,EAChD;AACF;AACA,SAAS,YAAa,OAAO;AAC3B,QAAM,eAAe;AACrB,MAAI,IAAI,gBAAgB,KAAK;AAC7B,MAAI,CAAC,eAAe;AAClB,iBAAa,GAAG,iBAAiB,MAAM,MAAM,CAAC;AAAA,EAChD,OAAO;AACL,QAAI,mBAAmB,cAAc,UAAU,WAAW;AAC1D,QAAI,gBAAgB,iBAAiB,cAAc;AACnD,QAAI,eAAe;AACjB,kCAA4B,GAAG,iBAAiB,WAAW;AAAA,IAC7D,WAAW,yBAAyB;AAClC,UAAI,4BAA4B,KAAK;AACnC,kBAAU,QAAQ,IAAI,EAAE,UAAU,UAAU,cAAc;AAC1D,sBAAc,SAAS,IAAI,EAAE,UAAU,UAAU,YAAY;AAC7D,sBAAc,cAAc,IAAI,EAAE;AAAA,MACpC,WAAW,4BAA4B,KAAK;AAC1C,kBAAU,QAAQ,IAAI,EAAE,UAAU,UAAU,cAAc;AAC1D,sBAAc,SAAS,IAAI,EAAE,UAAU,UAAU,YAAY;AAC7D,sBAAc,cAAc,IAAI,EAAE;AAAA,MACpC;AAAA,IACF,OAAO;AACL,gBAAU,QAAQ,IAAI,EAAE,UAAU,UAAU,cAAc;AAC1D,gBAAU,QAAQ,IAAI,EAAE,UAAU,UAAU,cAAc;AAC1D,oBAAc,SAAS,IAAI,EAAE,UAAU,UAAU,YAAY;AAC7D,oBAAc,SAAS,IAAI,EAAE,UAAU,UAAU,YAAY;AAC7D,oBAAc,cAAc,IAAI,EAAE;AAClC,oBAAc,cAAc,IAAI,EAAE;AAAA,IACpC;AACA,mBAAe;AACf,QAAI,CAAC,WAAW,aAAa,GAAG;AAC9B,mBAAa;AAAA,IACf,OAAO;AACL,mBAAa;AAAA,IACf;AACA,QAAI,YAAY;AACd,qCAA+B;AAAA,IACjC;AAAA,EACF;AACF;AACA,IAAI,iCAAiC,SAAS,uBAAuB,IAAI,KAAK;AAC9E,SAAS,wBAAyB;AAChC,MAAI,YAAY;AACd,iBAAa;AACb,wBAAoB,eAAe,uBAAuB;AAAA,EAC5D;AACF;AACA,SAAS,YAAa;AACpB,sBAAoB;AACpB,yBAAuB;AACvB,sBAAoB;AACpB,MAAI,gBAAgB,OAAO,iBAAiB,YAAY;AAAE,iBAAa,EAAE,OAAO,KAAK,CAAC;AAAA,EAAG;AACzF,MAAI,oBAAoB;AACtB,gBAAY,kBAAkB;AAC9B,yBAAqB;AAAA,EACvB;AACA,MAAI,eAAe;AACjB,6BAAyB,KAAK;AAC9B,0BAAsB;AACtB,2BAAuB;AACvB,wBAAoB,WAAY;AAC9B,mBAAa;AACb,yBAAmB,KAAK;AACxB,UAAIN,cAAa,2BAA2B,CAAC;AAC7C,UAAI,sBAAsBA,YAAW,MAAM;AAC3C,aAAO,wBAAwB,QAAW;AACxC,4BAAoB,WAAW,aAAa;AAC5C,8BAAsBA,YAAW,MAAM;AAAA,MACzC;AACA,gCAA0B;AAC1B,uBAAiB;AACjB,kBAAY;AACZ,sBAAgB;AAChB,gCAA0B;AAC1B,mBAAa;AACb,6BAAuB;AAAA,IACzB,CAAC;AAAA,EACH;AACF;AACA,SAAS,gBAAiB,GAAG;AAC3B,SAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AACpC;AACA,SAAS,oBAAqBC,gBAAeM,0BAAyB;AACpE,MAAI,sBAAsB;AAC1B,EAAAA,yBAAwB,QAAQ,SAAU,GAAG;AAC3C,QAAI,aAAa,EAAE,WAAWN,cAAa;AAC3C,0BAAsB,CAAC,CAAC,WAAW,uBAAuB;AAC1D,eAAW,sBAAsB;AAAA,EACnC,CAAC;AACD,MAAI,qBAAqB;AACvB,0BAAsB;AACtB,0BAAsB,WAAY;AAChC,iBAAW,QAAQ,SAAU,GAAG;AAC9B,UAAE,OAAO,gBAAgB;AACzB,UAAE,aAAa;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAAS,YAAaM,0BAAyB;AAC7C,MAAI,mBAAmBA;AACvB,MAAI,iBAAiB;AACrB,SAAO,SAAUN,gBAAe;AAC9B,QAAI,mBAAmB,QAAQ,cAAc,CAAC,sBAAsB;AAClE,uBAAiB,sBAAsB,WAAY;AACjD,YAAI,cAAc,CAAC,sBAAsB;AACvC,8BAAoBA,gBAAe,gBAAgB;AACnD,uBAAa,EAAE,eAAeA,eAAc,CAAC;AAAA,QAC/C;AACA,yBAAiB;AAAA,MACnB,CAAC;AACD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AACA,SAAS,iBAAkB,WAAWM,0BAAyB;AAC7D,MAAI,UAAU,WAAW,EAAE,mBAAmB;AAC5C,WAAO,aAAaA,0BAAyB,UAAU,kBAAkB,CAAC;AAAA,EAC5E,OAAO;AACL,WAAO,SAAU,OAAO;AAAE,aAAO;AAAA,IAAM;AAAA,EACzC;AACF;AACA,SAAS,mBAAoB,SAAS;AACpC,MAAI,YAAY,cAAc;AAC9B,MAAI,UAAU,cAAc;AAC5B,aAAW,QAAQ,SAAU,GAAG;AAC9B,QAAI,UAAU,WAAW,EAAE,yBAAyB,MAAM,WAAW;AAAE;AAAA,IAAQ;AAC/E,QAAI,MAAM,EAAE,WAAW;AACvB,QAAI,cAAc,IAAI;AACtB,QAAI,YAAY,IAAI;AACpB,QAAI,KAAK,UAAU,cAAc;AACjC,QAAI,IAAI;AACN,UAAI,UAAU;AAAA,QACZ,UAAU,MAAM;AAAA,QAChB;AAAA,QACA,gBAAgB;AAAA,MAClB;AACA,UAAI,EAAE,eAAe,WAAW,OAAO,GAAG;AACxC,gBAAQ,iBAAiB;AAAA,MAC3B;AACA,SAAG,OAAO;AAAA,IACZ;AAAA,EACF,CAAC;AACH;AACA,SAAS,aAAc,UAAU,QAAQ;AACvC,MAAI,mBAAmB,MAAM;AAC3B,QAAI,eAAe,UAAU,SAAS,sBAAsB,GAAG;AAAE;AAAA,IAAQ;AACzE,iBAAa;AACb,QAAI,YAAa,WAAW,OAAO,SAAU,GAAG;AAAE,aAAO,eAAe,kBAAkB,EAAE;AAAA,IAAS,CAAC,EAAE,CAAC;AACzG,cAAU,cAAc;AACxB,8BAA0B,UAAU,WAAW,EAAE,WAAW,UAAU,WAAW,EAAE,SAAS,YAAY,IAAI;AAC5G,oBAAgB,iBAAiB,cAAc;AAC/C,gBAAY,gBAAgB,gBAAgB,EAAE,GAAG,SAAS,SAAS,GAAG,SAAS,QAAQ,GAAG,cAAc,WAAW,MAAM;AACzH,kBAAc,WAAW;AAAA,MACvB,GAAG,SAAS,UAAU,UAAU,YAAY;AAAA,MAC5C,GAAG,SAAS,UAAU,UAAU,YAAY;AAAA,IAC9C;AACA,kBAAc,gBAAgB;AAAA,MAC5B,GAAG,SAAS;AAAA,MACZ,GAAG,SAAS;AAAA,IACd;AACA,8BAA0B,WAAW,OAAO,SAAU,GAAG;AAAE,aAAO,EAAE,eAAe,WAAW,cAAc,OAAO;AAAA,IAAG,CAAC;AACvH,kBAAc,qBAAqB;AACnC,iBAAa,YAAY,uBAAuB;AAChD,QAAI,gBAAgB,OAAO,iBAAiB,YAAY;AACtD,mBAAa,EAAE,OAAO,MAAM,eAAe,OAAU,CAAC;AAAA,IACxD;AACA,mBAAe,iBAAiB,WAAW,uBAAuB;AAClE,4BAAwB,QAAQ,SAAU,GAAG;AAAE,aAAO,EAAE,YAAY,GAAG,uBAAuB;AAAA,IAAG,CAAC;AAClG,uBAAmB,IAAI;AACvB,eAAW,aAAa;AACxB,mBAAe,EAAE,YAAY,UAAU,KAAK;AAC5C,6BAAyB,MAAM;AAAA,EACjC;AACF;AACA,IAAI,sBAAsB;AAC1B,SAAS,eAAgB,mBAAmB,OAAO,SAAS;AAC1D,MAAK,sBAAsB,OAAS,qBAAoB;AACxD,MAAK,UAAU,OAAS,SAAQ;AAChC,MAAK,YAAY,OAAS,WAAU;AAEpC,MAAI,QAAQ,UAAU;AACtB,MAAI,oBAAoB,UAAU;AAClC,MAAI,IAAI,kBAAkB;AAC1B,MAAI,IAAI,kBAAkB;AAC1B,MAAI,eAAe,cAAc,YAAY,cAAc,UAAU,2BAA2B,IAAI;AACpG,MAAI,kBAAkB,eAAgB,iBAAiB,IAAI,QAAQ,IAAI,WAAY;AACnF,MAAI,UAAU,GAAG;AACf,sBAAkB,kBAAmB,kBAAkB,YAAY,QAAQ,MAAQ,WAAW,QAAQ;AAAA,EACxG;AACA,MAAI,oBAAoB,GAAG;AACzB,cAAU,MAAM,MAAM,qBAAqB,oBAAoB;AAC/D,0BAAsB,WAAY;AAChC,0BAAoB,MAAM,MAAM,YAAY;AAC5C,UAAI,CAAC,cAAc;AACjB,cAAM,MAAM,OAAO,IAAI;AACvB,cAAM,MAAM,MAAM,IAAI;AAAA,MACxB;AACA,4BAAsB;AACtB,UAAI,SAAS;AACX,cAAM,MAAM,UAAU;AAAA,MACxB;AAAA,IACF,CAAC;AACD;AAAA,EACF;AACA,MAAI,wBAAwB,MAAM;AAChC,0BAAsB,sBAAsB,WAAY;AACtD,0BAAoB,MAAM,MAAM,YAAY;AAC5C,UAAI,CAAC,cAAc;AACjB,cAAM,MAAM,OAAO,IAAI;AACvB,cAAM,MAAM,MAAM,IAAI;AAAA,MACxB;AACA,4BAAsB;AACtB,UAAI,SAAS;AACX,cAAM,MAAM,UAAU;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AACA,SAAS,kBAAmB,WAAW;AACrC,aAAW,KAAK,SAAS;AACzB,MAAI,cAAc,eAAe;AAC/B,QAAI,UAAU,eAAe,cAAc,WAAW,cAAc,OAAO,GAAG;AAC5E,8BAAwB,KAAK,SAAS;AACtC,gBAAU,YAAY,WAAW,uBAAuB;AACxD,UAAI,gBAAgB,OAAO,iBAAiB,YAAY;AACtD,qBAAa,EAAE,OAAO,MAAM,eAAe,OAAU,CAAC;AAAA,MACxD;AACA,qBAAe,iBAAiB,WAAW,uBAAuB;AAClE,mBAAa,YAAY,uBAAuB;AAChD,gBAAU,WAAW,aAAa;AAAA,IACpC;AAAA,EACF;AACF;AACA,SAAS,oBAAqB,WAAW;AACvC,aAAW,OAAO,WAAW,QAAQ,SAAS,GAAG,CAAC;AAClD,MAAI,cAAc,eAAe;AAC/B,QAAI,cAAc,cAAc,WAAW;AACzC,gBAAU,kBAAkB;AAAA,IAC9B;AACA,QAAI,cAAc,kBAAkB,UAAU,SAAS;AACrD,oBAAc,gBAAgB;AAAA,IAChC;AACA,QAAI,uBAAuB,wBAAwB,QAAQ,SAAS;AACpE,QAAI,uBAAuB,IAAI;AAC7B,8BAAwB,OAAO,sBAAsB,CAAC;AACtD,UAAI,gBAAgB,OAAO,iBAAiB,YAAY;AACtD,qBAAa,EAAE,OAAO,MAAM,eAAe,OAAU,CAAC;AAAA,MACxD;AACA,qBAAe,iBAAiB,WAAW,uBAAuB;AAClE,mBAAa,YAAY,uBAAuB;AAAA,IAClD;AAAA,EACF;AACF;AACA,SAAS,kBAAmB;AAC1B,MAAI,kBAAkB;AACtB,MAAI,YAAY;AAChB,WAAS,SAAU;AACjB,sBAAkB,sBAAsB,WAAY;AAClD,8BAAwB,QAAQ,SAAU,GAAG;AAAE,eAAO,EAAE,OAAO,gBAAgB;AAAA,MAAG,CAAC;AACnF,iBAAW,WAAY;AACrB,YAAI,oBAAoB,MAAM;AAAE,iBAAO;AAAA,QAAG;AAAA,MAC5C,GAAG,EAAE;AAAA,IACP,CAAC;AAAA,EACH;AACA,WAAS,OAAQ;AACf,QAAI,oBAAoB,MAAM;AAC5B,2BAAqB,eAAe;AACpC,wBAAkB;AAAA,IACpB;AACA,gBAAY;AAAA,EACd;AACA,SAAO;AAAA,IACL,OAAO,WAAY;AACjB,UAAI,CAAC,WAAW;AACd,oBAAY;AACZ,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,aAAc;AACrB,MAAI,cAAc,CAAC,eAAe,CAAC,sBAAsB;AACvD,kBAAc;AACd,iBAAa;AACb,QAAI,2BAA2B,OAAO,OAAO,CAAC,GAAG,eAAe;AAAA,MAC9D,eAAe;AAAA,MACf,UAAU,EAAE,GAAG,OAAO,kBAAkB,GAAG,OAAO,iBAAiB;AAAA,MACnE,eAAe,EAAE,GAAG,OAAO,kBAAkB,GAAG,OAAO,iBAAiB;AAAA,IAC1E,CAAC;AACD,4BAAwB,QAAQ,SAAU,WAAW;AACnD,gBAAU,WAAW,wBAAwB;AAAA,IAC/C,CAAC;AACD,QAAI,eAAe;AACjB,oBAAc,gBAAgB;AAC9B,oBAAc,aAAa;AAC3B,gBAAU;AACV,oBAAc;AAAA,IAChB;AAAA,EACF;AACF;AACA,SAAS,WAAY;AACnB,eAAa;AACb,SAAO;AAAA,IACL,UAAU,SAAU,WAAW;AAC7B,wBAAkB,SAAS;AAAA,IAC7B;AAAA,IACA,YAAY,SAAU,WAAW;AAC/B,0BAAoB,SAAS;AAAA,IAC/B;AAAA,IACA,YAAY,WAAY;AACtB,aAAO;AAAA,IACT;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAI,OAAO,WAAW,aAAa;AACjC,iBAAe;AACjB;AACA,IAAI,aAAa,SAAS;AAE1B,SAAS,aACP,SACA,KACA,mBACA;AACA,MAAK,sBAAsB,OAAS,qBAAoB,eAAe;AAEvE,MAAI,KAAK;AACP,aAAS,SAAS,cAAc;AAChC,YAAQ,MAAM,qBAAqB,oBAAoB;AAAA,EACzD,OAAO;AACL,gBAAY,SAAS,cAAc;AACnC,YAAQ,MAAM,eAAe,qBAAqB;AAAA,EACpD;AACF;AAEA,SAAS,eAAgB,KAAK;AAC5B,MAAI,UAAU,IAAI;AAClB,MAAI,aAAa,IAAI;AAErB,SAAO,SAAU,iBAAiB,SAAS;AACzC,QAAI,UAAU,WAAW;AAEzB,QAAI,gBAAgB,gBAAgB,WAAW;AAC/C,QAAI,QAAQ,cAAc,QAAQ;AAAE,aAAO;AAAA,IAAO;AAElD,QAAI,gBAAgB,UAAU,SAAS,MAAM,YAAY;AACzD,QAAI,kBAAkB,gBAAgB,SAAS;AAC7C,aAAO;AAAA,IACT;AAEA,QAAI,gBAAgB,YAAY,SAAS;AAAE,aAAO;AAAA,IAAM;AACxD,QACE,cAAc,aACd,cAAc,cAAc,QAAQ,WACpC;AACA,aAAO;AAAA,IACT;AAEA,QAAI,QAAQ,kBAAkB;AAC5B,aAAO,QAAQ,iBAAiB,gBAAgB,WAAW,GAAG,OAAO;AAAA,IACvE;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,YAAa,OAAO;AAC3B,MAAI,UAAU,WAAW;AACvB,QAAI,MAAM,OAAO,SAAS,cAAc,KAAK;AAC7C,QAAI,YAAY,KAAK;AACrB,UAAM,cAAc,aAAa,KAAK,KAAK;AAC3C,QAAI,YAAY,KAAK;AACrB,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,SAAS,aAAc,SAAS;AAC9B,MAAI,aAAa,CAAC;AAClB,QAAM,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAU,OAAO;AAC9D,QAAI,MAAM,aAAa,KAAK,cAAc;AACxC,UAAI,UAAU;AACd,UAAI,CAAC,SAAS,OAAO,YAAY,GAAG;AAClC,kBAAU,YAAY,KAAK;AAAA,MAC7B;AACA,cAAQ,gBAAgB,IAAI;AAC5B,iBAAW,KAAK,OAAO;AAAA,IACzB,OAAO;AACL,cAAQ,YAAY,KAAK;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAEA,SAAS,eAAgB,SAAS;AAChC,MAAI,UAAU,WAAW;AACvB,UAAM,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAU,OAAO;AAC9D,UAAI,MAAM,aAAa,KAAK,cAAc;AACxC,YAAI,SAAS,OAAO,YAAY,GAAG;AACjC,kBAAQ,aAAa,MAAM,mBAAmB,KAAK;AACnD,kBAAQ,YAAY,KAAK;AAAA,QAC3B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,SAAS,mBAAoB,KAAK;AAChC,MAAI,SAAS,IAAI;AAEjB,MAAI,OAAO,SACT,YACA,KACA,YACA,UACA,2BACA;AACA,QAAK,8BAA8B,OAAS,6BAA4B;AAExE,QAAI,WAAW,YAAY;AACzB,aAAO;AAAA,IACT;AAEA,QAAI,eAAe,UAAU;AAC3B,UAAIL,OAAM,OAAO,YAAY,WAAW,UAAU,CAAC;AACnD,UAAI,QAAQA,KAAI;AAChB,UAAI,MAAMA,KAAI;AAId,UAAI,2BAA2B;AAC7B,eAAO,OAAO,MAAM,SAAS,IAAI,aAAa,aAAa;AAAA,MAC7D,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IAIF,OAAO;AACL,UAAI,cAAc,KAAK,OAAO,WAAW,cAAc,CAAC;AACxD,UAAI,QAAQ,OAAO,YAAY,WAAW,WAAW,CAAC;AACtD,UAAI,UAAU,MAAM;AACpB,UAAI,QAAQ,MAAM;AAClB,UAAI,MAAM,SAAS;AACjB,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA,cAAc;AAAA,UACd;AAAA,QACF;AAAA,MACF,WAAW,MAAM,OAAO;AACtB,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA,cAAc;AAAA,UACd;AAAA,UACA;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,2BAA2B;AAC7B,iBAAO,OAAO,QAAQ,WAAW,IAAI,cAAc,cAAc;AAAA,QACnE,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO,SAAU,YAAY,KAAK,2BAA2B;AAC3D,QAAK,8BAA8B,OAAS,6BAA4B;AAExE,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,SAAS;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,gBAAiB,KAAK;AAC7B,MAAI,UAAU,IAAI;AAClB,MAAI,aAAa,IAAI;AACrB,MAAI,SAAS,IAAI;AAEjB,SAAO,WAAY;AACjB,eAAW,QAAQ,SAAU,GAAG;AAC9B,mBAAa,GAAG,KAAK;AACrB,aAAO,eAAe,GAAG,CAAC;AAC1B,aAAO,cAAc,GAAG,IAAI;AAAA,IAC9B,CAAC;AAED,QAAI,QAAQ,wBAAwB,GAAG;AACrC,cAAQ,wBAAwB,EAAE,WAAW;AAAA,QAC3C,QAAQ,wBAAwB;AAAA,MAClC;AACA,cAAQ,wBAAwB,IAAI;AAAA,IACtC;AAAA,EACF;AACF;AAEA,SAAS,mBAAoBD,gBAAe,SAAS,KAAK;AACxD,MAAK,QAAQ,OAAS,OAAM;AAE5B,MAAI,WAAW,KAAK;AAClB,IAAAA,eAAc,gBAAgB;AAAA,EAChC,OAAO;AACL,QAAIA,eAAc,kBAAkB,SAAS;AAC3C,MAAAA,eAAc,gBAAgB;AAAA,IAChC;AAAA,EACF;AACF;AAEA,SAAS,WAAY,KAAK;AACxB,MAAI,UAAU,IAAI;AAClB,MAAI,aAAa,IAAI;AACrB,MAAI,SAAS,IAAI;AACjB,MAAI,aAAa,IAAI;AAErB,MAAI,kBAAkB,gBAAgB;AAAA,IACpC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,eAAe,UAAU,eAAe,gBAAgB;AAAA,IAC1D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAO,SACLA,gBACAC,MACA,YACA;AACA,QAAI,aAAaA,KAAI;AACrB,QAAI,eAAeA,KAAI;AACvB,QAAK,eAAe,OAAS,cAAa;AAE1C,oBAAgB;AAEhB,QAAID,kBAAiB,CAACA,eAAc,YAAY;AAC9C,UACEA,eAAc,iBACd,WAAW,EAAE,mBACb,YACA;AACA,YAAI,eAAe,SAAU,OAAO;AAAE,iBAAO,UAAU;AAAA,QAAM;AAE7D,YAAI,iBACF,aAAa,UAAU,IACnB,aAAa,YAAY,KAAK,eAAe,aAC3C,aAAa,IACb,aACF;AAEN,YAAI,UAAUA,eAAc;AAC5B,YAAIO,WAAUP,eAAc;AAE5B,YAAI,oBAAoB;AAAA,UACtB;AAAA,UACA,YAAY;AAAA,UACZ;AAAA,UACA,SAASO,SAAQ,qBAAqBA;AAAA,QACxC;AACA,YAAI,mBACF,CAACP,eAAc,UAAU,WAAW,EAAE,yBACtC,aAAa,YAAY,KACzB,aAAa,cAAc;AAC7B,YAAI,kBAAkB;AACpB,sBAAY,mBAAmB,WAAW,EAAE,MAAM;AAAA,QACpD;AAAA,MACF,WAAW,WAAW,EAAE,gBAAgB;AACtC,YAAI,YAAYA,eAAc;AAC9B,YAAI,YAAYA,eAAc;AAC9B,eAAO,WAAW,EAAE,eAAe,EAAE,SAAS,WAAW,UAAqB,CAAC;AAAA,MACjF;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,kBAAmB,SAAS,YAAY;AAC/C,MAAI,aAAa,aAAa,OAAO;AACrC,MAAI,UAAU,WAAW;AAEzB,WAAS,SAAU,iBAAiB,MAAO,QAAQ,WAAa;AAChE,MAAI,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,EACV;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,eAAgB,KAAK;AAC5B,MAAI,UAAU,IAAI;AAClB,MAAI,aAAa,IAAI;AAErB,MAAI,mBAAmB;AACvB,SAAO,SAAUC,MAAK;AACpB,QAAID,iBAAgBC,KAAI;AAExB,QAAI,eAAe;AACnB,QACE,oBAAoB,QACpBD,eAAc,UAAU,YAAY,WACpC,WAAW,EAAE,cAAc,QAC3B;AACA,qBAAe,mBAAmBA,eAAc;AAAA,IAClD;AAEA,WAAO,EAAE,aAA2B;AAAA,EACtC;AACF;AAEA,SAAS,wBAAyB,KAAK;AACrC,MAAI,aAAa,IAAI;AACrB,MAAI,SAAS,IAAI;AAEjB,SAAO,SAAUC,MAAK;AACpB,QAAI,aAAaA,KAAI;AAErB,QAAI,WAAW,iBAAiB,MAAM;AACpC,aAAO,cAAc,WAAW,WAAW,YAAY,GAAG,KAAK;AAAA,IACjE;AAAA,EACF;AACF;AAEA,SAAS,YAAa,KAAK;AACzB,MAAI,UAAU,IAAI;AAClB,MAAI,SAAS,IAAI;AAEjB,SAAO,SAAUA,MAAK;AACpB,QAAID,iBAAgBC,KAAI;AAExB,QAAI,aAAa,SAAS;AAAA,MACxBD,eAAc,SAAS;AAAA,MACvBA,eAAc,SAAS;AAAA,IACzB;AAOA,QAAI,YAAY;AACd,UAAI,YAAY;AAAA,QACd;AAAA,QACAA,eAAc;AAAA,MAChB;AACA,UAAI,aAAa,UAAU,YAAY,SAAS;AAC9C,eAAO;AAAA,UACL,KAAK,OAAO,YAAYA,eAAc,QAAQ;AAAA,QAChD;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AACF;AAEA,SAAS,eAAgB,KAAK;AAC5B,MAAI,SAAS,IAAI;AAEjB,MAAI,cAAc;AAClB,SAAO,SAAUC,MAAK;AACpB,QAAID,iBAAgBC,KAAI;AACxB,QAAI,aAAaA,KAAI;AAErB,QAAI,WAAW,QAAQ,MAAM;AAC3B,aAAQ,cAAc;AAAA,IACxB,OAAO;AACL,oBAAc,eAAe,OAAO,QAAQD,eAAc,IAAI;AAAA,IAChE;AACA,WAAO,EAAE,YAAyB;AAAA,EACpC;AACF;AAEA,SAAS,sBAAuB,KAAK;AACnC,MAAI,UAAU,IAAI;AAElB,SAAO,SAAUC,MAAK;AACpB,QAAID,iBAAgBC,KAAI;AACxB,QAAI,aAAaA,KAAI;AAErB,uBAAmBD,gBAAe,SAAS,CAAC,CAAC,WAAW,GAAG;AAAA,EAC7D;AACF;AAEA,SAAS,sBAAuB,KAAK;AACnC,MAAI,aAAa,IAAI;AACrB,MAAI,SAAS,IAAI;AAEjB,MAAI,gBAAgB,mBAAmB,EAAE,OAAe,CAAC;AACzD,SAAO,SAAUC,MAAK;AACpB,QAAI,iBAAiBA,KAAI;AACzB,QAAI,iBAAiB,eAAe;AACpC,QAAI,MAAM,eAAe;AAEzB,QAAI,CAAC,gBAAgB;AACnB,UAAI,QAAQ,cAAc,YAAY,KAAK,IAAI;AAC/C,aAAO,UAAU,OAAO,QAAQ,WAAW;AAAA,IAC7C,OAAO;AACL,UACE,eAAe,QAAQ,eAAe,mBAAmB,OACzD,eAAe,OAAO,KACtB;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,QAAI,MAAM,eAAe,QAAQ,eAAe,iBAAiB;AAC/D,aAAO,cAAc,YAAY,GAAG;AAAA,IACtC,WAAW,MAAM,eAAe,KAAK;AACnC,aAAO,cAAc,YAAY,GAAG,IAAI;AAAA,IAC1C,OAAO;AACL,aAAO,WAAW;AAAA,IACpB;AAAA,EACF;AACF;AAEA,SAAS,mCAAoC;AAC3C,SAAO,SAAU,KAAK;AACpB,QAAI,MAAM,IAAI,WAAW;AAEzB,WAAO,QAAQ,OAAO,EAAE,YAAY,EAAE,IAAI,EAAE,YAAY,KAAK;AAAA,EAC/D;AACF;AAEA,SAAS,6BAA8B,KAAK;AAC1C,MAAI,SAAS,IAAI;AAEjB,MAAI,iBAAiB;AACrB,SAAO,SAAUA,MAAK;AACpB,QAAI,aAAaA,KAAI,WAAW;AAEhC,QAAI,eAAe,gBAAgB;AACjC,uBAAiB;AACjB,UAAI,QAAQ,OAAO,uBAAuB;AAC1C,UAAI,QAAQ,MAAM;AAClB,aAAO;AAAA,QACL,gBAAgB;AAAA,UACd,MAAM,OAAO,yBAAyB,KAAK;AAAA,QAC7C;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,oBAAqB,KAAK;AACjC,MAAI,SAAS,IAAI;AACjB,MAAI,UAAU,IAAI;AAClB,MAAI,aAAa,IAAI;AAErB,MAAI,iBAAiB;AACrB,SAAO,SAAUA,MAAK;AACpB,QAAI,iBAAiBA,KAAI;AACzB,QAAI,cAAc,eAAe;AACjC,QAAI,iBAAiB,eAAe;AACpC,QAAI,aAAa,eAAe;AAChC,QAAI,2BAA2B,eAAe;AAE9C,QAAI,UAAU,WAAW;AACzB,QAAI,QAAQ,iBAAiB;AAC3B,UAAI,QACF,OAAO,QAAQ,oBAAoB,YAC/B,CAAC,IACD,QAAQ;AACd,UAAI,oBAAoB,MAAM;AAC9B,UAAI,YAAY,MAAM;AACtB,UAAI,YAAY,MAAM;AACtB,UAAI,eAAe,MAAM;AACvB,YAAI,CAAC,0BAA0B;AAC7B,cAAI,eAAe,SAAS,cAAc,KAAK;AAC/C,cAAI,OAAO,SAAS,cAAc,KAAK;AACvC,eAAK,YAAY;AACjB,uBAAa,YAAY,4BAA4B,OAAO,aAAa;AACzE,qCAA2B,SAAS,cAAc,KAAK;AACvD,mCAAyB,YAAY,KAAK;AAC1C,mCAAyB,MAAM,WAAW;AAE1C,cAAI,sBAAsB,QAAW;AACnC,qCAAyB,MAAM,aAAa,SAAS,oBAAoB;AAAA,UAC3E;AAEA,mCAAyB,YAAY,IAAI;AACzC,eAAK,YAAY,YAAY;AAC7B,iBAAO,QAAQ,yBAAyB,OAAO,cAAc,IAAI;AAEjE,mCAAyB,MAAM,gBAAgB;AAE/C,cAAI,WAAW;AACb,oBAAQ,YAAY,wBAAwB;AAAA,UAC9C,OAAO;AACL,oBAAQ;AAAA,cACN;AAAA,cACA,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAEA,YAAI,mBAAmB,cAAc,eAAe,UAAU;AAC5D,iBAAO;AAAA,YACL,yBAAyB;AAAA,YACzB,eAAe,SAAS,QACtB,OAAO,uBAAuB,EAAE,QAChC;AAAA,UACJ;AAAA,QACF;AACA,yBAAiB;AAEjB,eAAO;AAAA,UACL;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,4BAA4B,mBAAmB,MAAM;AACvD,kBAAQ,YAAY,wBAAwB;AAAA,QAC9C;AACA,yBAAiB;AAEjB,eAAO;AAAA,UACL,0BAA0B;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,iCAAkC,QAAQ;AACjD,MAAI,qBAAqB,kBAAkB,MAAM;AACjD,SAAO,SAAU,KAAK;AACpB,QAAID,iBAAgB,IAAI;AACxB,QAAI,aAAa,IAAI;AAErB,QAAIA,eAAc,kBAAkB;AAClC,aAAO,mBAAmB,EAAE,eAAeA,gBAAe,WAAuB,CAAC;AAAA,IACpF;AACA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAmB,QAAQ;AAClC,MAAI,iBAAiB,sBAAsB,MAAM;AACjD,SAAO,SAAU,KAAK;AACpB,QAAI,aAAa,IAAI;AAErB,QAAI,QAAQ;AACZ,QAAI,WAAW,QAAQ,MAAM;AAC3B,cAAQ,eAAe,EAAE,WAAuB,CAAC;AACjD,UAAI,UAAU,MAAM;AAClB,gBAAQ,WAAW;AAAA,MACrB;AAAA,IACF;AACA,WAAO;AAAA,MACL,YAAY;AAAA,IACd;AAAA,EACF;AACF;AAEA,SAAS,wBAAyB;AAChC,MAAI,iBAAiB;AACrB,SAAO,SAAU,KAAK;AACpB,QAAI,iBAAiB,IAAI;AACzB,QAAI,aAAa,eAAe;AAChC,QAAI,iBAAiB,eAAe;AAEpC,QACE,eAAe,kBACf,mBAAmB,QACnB,gBACA;AACA,qBAAe,kBAAkB;AAAA,IACnC;AACA,qBAAiB;AAAA,EACnB;AACF;AAEA,SAAS,0BAA2B,KAAK;AACvC,MAAI,UAAU,IAAI;AAClB,MAAI,aAAa,IAAI;AACrB,MAAI,SAAS,IAAI;AACjB,MAAI,aAAa,IAAI;AAErB,MAAI,mBAAmB;AACvB,SAAO,SAAUC,MAAK;AACpB,QAAI,iBAAiBA,KAAI;AACzB,QAAI,aAAa,eAAe;AAChC,QAAI,eAAe,eAAe;AAClC,QAAI,cAAc,eAAe;AAEjC,QAAI,iBAAiB,MAAM;AACzB,UAAI,eAAe,MAAM;AACvB,YAAI,CAAC,kBAAkB;AACrB,cAAI,oBAAoB,OAAO,uBAAuB;AACtD,4BAAkB,MAChB,kBAAkB,QAAQ,OAAO,QAAQ,OAAO;AAClD,cAAI,eACF,OAAO,cAAc,OAAO,IAAI,OAAO,QAAQ,OAAO;AACxD,cAAI,eAAe,eACf,kBAAkB,QAClB,OAAO,cAAc,OAAO,IAC5B,OAAO,eAAe,OAAO,IAC7B,kBAAkB;AACtB,cAAI,mBACF,WAAW,SAAS,IAChB,OAAO,YAAY,WAAW,WAAW,SAAS,CAAC,CAAC,EAAE,MACtD,WAAW,WAAW,SAAS,CAAC,EAAE,gBAAgB,IAClD,kBAAkB;AACxB,cAAI,mBAAmB,cAAc,cAAc;AACjD,+BAAmB,OAAO,SAAS,cAAc,KAAK;AACtD,6BAAiB,YACf,wBAAwB,MAAM,WAAW,EAAE;AAC7C,gBAAI,gBACF,WAAW,SAAS,IAChB,cAAc,mBAAmB,eACjC;AACN,mBAAO,QAAQ,iBAAiB,OAAQ,gBAAgB,IAAK;AAC7D,oBAAQ,YAAY,gBAAgB;AACpC,oBAAQ,wBAAwB,IAAI;AACpC,mBAAO;AAAA,cACL,qBAAqB;AAAA,YACvB;AAAA,UACF;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAI,kBAAkB;AACpB,iBAAO,eAAe,kBAAkB,CAAC;AACzC,cAAI,WAAW;AACf,6BAAmB;AACnB,kBAAQ,YAAY,QAAQ;AAC5B,kBAAQ,wBAAwB,IAAI;AACpC,iBAAO;AAAA,YACL,qBAAqB;AAAA,UACvB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,sBAAuB,KAAK;AACnC,MAAI,aAAa,IAAI;AACrB,MAAI,SAAS,IAAI;AAEjB,MAAI,iBAAiB;AACrB,MAAI,mBAAmB;AACvB,SAAO,SAAUA,MAAK;AACpB,QAAI,iBAAiBA,KAAI;AACzB,QAAI,aAAa,eAAe;AAChC,QAAI,eAAe,eAAe;AAClC,QAAI,cAAc,eAAe;AAEjC,QAAI,eAAe,kBAAkB,iBAAiB,kBAAkB;AACtE,eAAS,QAAQ,GAAG,QAAQ,WAAW,QAAQ,SAAS;AACtD,YAAI,UAAU,cAAc;AAC1B,cAAI,YAAY,WAAW,KAAK;AAChC,cAAI,YAAY;AAChB,cAAI,iBAAiB,QAAQ,eAAe,OAAO;AACjD,yBAAa;AAAA,UACf;AACA,cAAI,eAAe,QAAQ,cAAc,OAAO;AAC9C,yBAAa;AAAA,UACf;AACA,iBAAO,eAAe,WAAW,SAAS;AAAA,QAC5C;AAAA,MACF;AAEA,uBAAiB;AACjB,yBAAmB;AAEnB,aAAO,EAAE,YAAwB,aAA2B;AAAA,IAC9D;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,kBAAmB,KAAK;AAC/B,MAAI,aAAa,IAAI;AACrB,MAAI,SAAS,IAAI;AAEjB,MAAI,iBAAiB;AACrB,SAAO,SAAUA,MAAK;AACpB,QAAID,iBAAgBC,KAAI;AACxB,QAAI,aAAaA,KAAI;AAErB,QAAI,aAAa,WAAW;AAC5B,QAAI,eAAe,WAAW;AAC9B,QAAI,cAAc,WAAW;AAC7B,QAAI,MAAM,WAAW;AACrB,QAAI,iBAAiB,WAAW;AAChC,QAAI,QAAQ,MAAM;AAChB,UACE,eAAe,SACdD,eAAc,oBAAoB,eAAe,iBAClD;AAEA,YAAI,cAAc,aAAa;AAC/B,YAAI,QAAQ,OAAO;AACnB,YAAI,gBAAgB;AACpB,YAAI,cAAc;AAClB,YAAI,cAAc;AAClB,YAAI,eAAe;AACnB,YAAI,gBAAgB,cAAc;AAChC;AAAA,QACF;AACA,YAAI,cAAc,IAAI;AACpB,cAAI,aAAa,OAAO,QAAQ,WAAW,WAAW,CAAC;AACvD,yBAAe,OAAO,YAAY,WAAW,WAAW,CAAC;AACzD,cAAI,cAAc,YAAY;AAC5B,gBAAI,aAAa,aAAa,eAAe;AAC7C,oBAAQ,aAAa,MAAM;AAAA,UAC7B,OAAO;AACL,oBAAQ,aAAa;AAAA,UACvB;AACA,0BAAgB,aAAa;AAAA,QAC/B,OAAO;AACL,yBAAe,EAAE,KAAK,OAAO,uBAAuB,EAAE,MAAM;AAC5D,0BAAgB,OAAO,uBAAuB,EAAE;AAAA,QAClD;AAEA,YAAI,MAAM,OAAO;AACjB,YAAI,aAAa;AACjB,YAAI,eAAe,cAAc;AAC/B;AAAA,QACF;AACA,YAAI,aAAa,WAAW,QAAQ;AAClC,cAAI,YAAY,OAAO,QAAQ,WAAW,UAAU,CAAC;AACrD,wBAAc,OAAO,YAAY,WAAW,UAAU,CAAC;AAEvD,cAAI,cAAc,WAAW;AAC3B,gBAAI,eAAe,YAAY,eAAe;AAC9C,kBAAM,YAAY,QAAQ;AAAA,UAC5B,OAAO;AACL,kBAAM,YAAY;AAAA,UACpB;AACA,wBAAc,YAAY;AAAA,QAC5B,OAAO;AACL,wBAAc,EAAE,OAAO,OAAO,uBAAuB,EAAE,KAAK,IAAI;AAChE,wBACE,OAAO,uBAAuB,EAAE,KAAK,MACrC,OAAO,uBAAuB,EAAE,KAAK;AAAA,QACzC;AAEA,YAAI,oBACF,gBAAgB,cACZ,OAAO,yBAAyB,aAAa,GAAG,IAChD;AAEN,yBAAiB;AACjB,eAAO;AAAA,UACL,gBAAgB;AAAA,YACd,UAAU;AAAA,cACR,OAAO;AAAA,cACP,KAAK;AAAA,YACP;AAAA,YACA;AAAA,YACA;AAAA,YACA,MAAM;AAAA,YACN,iBAAiB,iBACb,eAAe,kBACf;AAAA,UACN;AAAA,QACF;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,uBAAiB;AACjB,aAAO;AAAA,QACL,gBAAgB;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,oCAAqC;AAC5C,MAAI,iBAAiB;AACrB,SAAO,SAAU,KAAK;AACpB,QAAI,iBAAiB,IAAI;AACzB,QAAI,MAAM,eAAe;AACzB,QAAI,aAAa,eAAe;AAChC,QAAI,iBAAiB,eAAe;AAEpC,QAAI,QAAQ,MAAM;AAChB,UAAI,cAAc,QAAQ,mBAAmB,MAAM;AACjD,YAAI,MAAM,eAAe,OAAO;AAC9B,cAAI,kBAAkB,MAAM,eAAe,QAAQ;AACnD,yBAAe,kBAAkB;AAAA,QACnC;AACA,yBAAiB;AAAA,MACnB;AAAA,IACF,OAAO;AACL,uBAAiB;AAAA,IACnB;AAAA,EACF;AACF;AAEA,SAAS,yBAA0B,KAAK;AACtC,MAAI,aAAa,IAAI;AAErB,MAAI,YAAY;AAChB,MAAI,UAAU,WAAW;AACzB,SAAO,SAAUC,MAAK;AACpB,QAAI,MAAMA,KAAI,WAAW;AAEzB,QAAI,WAAW,CAAC,CAAC;AACjB,QAAI,aAAa,WAAW;AAC1B,kBAAY;AACZ,UAAI,UAAU;AACZ,gBAAQ,eAAe,QAAQ,YAAY;AAAA,MAC7C,OAAO;AACL,gBAAQ,eAAe,QAAQ,YAAY;AAAA,MAC7C;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAEA,SAAS,gBAAiB,KAAK;AAC7B,MAAI,aAAa,IAAI;AAErB,MAAI,iBAAiB;AACrB,MAAI,UAAU,WAAW;AACzB,SAAO,SAAUA,MAAK;AACpB,QAAI,iBAAiBA,KAAI;AACzB,QAAI,aAAa,eAAe;AAChC,QAAI,eAAe,eAAe;AAClC,QAAI,oBAAoBA,KAAI;AAC5B,QAAI,UAAU,kBAAkB;AAChC,QAAI,UAAU,kBAAkB;AAEhC,QACE,QAAQ,eACR,eAAe,QACf,mBAAmB,YACnB;AACA,uBAAiB;AACjB,UAAI,qBAAqB;AAEzB,UAAI,iBAAiB,QAAQ,aAAa,cAAc;AACtD;AAAA,MACF;AAEA,cAAQ,YAAY;AAAA,QAClB,YAAY;AAAA,QACZ;AAAA,QACA;AAAA,QACA,SAAS,UAAW,QAAQ,qBAAqB,UAAW;AAAA,MAC9D,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,eAAgB,QAAQ;AAC/B,MAAI,OAAO,WAAW,EAAE,cAAc,aAAa;AAEjD,WAAO,QAAQ,MAAM;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,OAAO;AACL,WAAO,QAAQ,MAAM;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAEA,SAAS,uBAAwB;AAC/B,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,aAAa;AAAA,IACb,KAAK;AAAA,IACL,gBAAgB;AAAA,EAClB;AACF;AAEA,SAAS,QAAS,QAAQ;AACxB,SAAO,WAAY;AACjB,QAAI,YAAY,CAAC,GAAG,MAAM,UAAU;AACpC,WAAQ,MAAQ,WAAW,GAAI,IAAI,UAAW,GAAI;AAElD,QAAI,oBAAoB,UAAU,IAAI,SAAU,GAAG;AAAE,aAAO,EAAE,MAAM;AAAA,IAAG,CAAC;AACxE,QAAI,SAAS;AACb,WAAO,SAAUD,gBAAe;AAC9B,eAAS,kBAAkB,OAAO,SAAU,YAAY,IAAI;AAC1D,eAAO,OAAO,OAAO,YAAY,GAAG,EAAE,eAAeA,gBAAe,WAAuB,CAAC,CAAC;AAAA,MAC/F,GAAG,UAAU,qBAAqB,CAAC;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAGA,SAAS,YAAa,SAAS;AAC7B,SAAO,SAAU,SAAS;AACxB,QAAI,mBAAmB,OAAO,OAAO,CAAC,GAAG,gBAAgB,OAAO;AAChE,QAAI,aAAa;AACjB,QAAI,oBAAoB;AACxB,QAAI,QAAQ,kBAAkB,SAAS,UAAU;AACjD,QAAIQ,eAAc,eAAe,KAAK;AACtC,QAAI,cAAc,WAAW,KAAK;AAClC,QAAI,iBAAiB,mBAAmB,SAAS,QAAQ;AAEzD,aAAS,2BAA4B;AACnC,UAAI,sBAAsB,MAAM;AAC9B,0BAAkB,mBAAmB;AACrC,qBAAaA,aAAY,iBAAiB;AAC1C,0BAAkB,mBAAmB;AAAA,MACvC;AAAA,IACF;AAEA,aAAS,cAAe,YAAYD,UAAS;AAC3C,UAAI,gBAAgB,aAAaA,QAAO;AACxC,eAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,mBAAW,CAAC,IAAI,cAAc,CAAC;AAAA,MACjC;AAEA,eAAS,MAAM,GAAG,MAAM,WAAW,SAAS,cAAc,QAAQ,OAAO;AACvE,mBAAW,IAAI;AAAA,MACjB;AAAA,IACF;AAEA,aAAS,YAAaE,YAAW,oBAAoB;AACnD,UAAIF,WAAUE,WAAU;AACxB,UAAI,aAAa,MAAM;AACvB,oBAAc,YAAYF,QAAO;AACjC,MAAAE,WAAU,OAAO,gBAAgB;AACjC,iBAAW;AAAA,QAAQ,SAAU,GAAG;AAAE,iBAAO,aAAa,GAAG,MAAM,WAAW,EAAE,iBAAiB;AAAA,QAAG;AAAA,MAChG;AACA,qBAAe,MAAM;AAAA,IACvB;AAEA,aAAS,WAAY;AACnB,YAAM,OAAO,gBAAgB;AAC7B,+BAAyB;AAAA,IAC3B;AAEA,aAAS,QAASA,YAAW;AAC3B,qBAAe,QAAQ;AACvB,qBAAeA,WAAU,OAAO;AAAA,IAClC;AAEA,aAAS,WAAYC,UAAS,OAAO;AACnC,UAAK,UAAU,OAAS,SAAQ;AAEhC,UAAI,UAAU,OAAO;AACnB,2BAAmB,OAAO,OAAO,CAAC,GAAG,gBAAgBA,QAAO;AAAA,MAC9D,OAAO;AACL,2BAAmB,OAAO;AAAA,UACxB,CAAC;AAAA,UACD;AAAA,UACA;AAAA,UACAA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,aAAS,aAAc;AACrB,aAAO;AAAA,IACT;AAEA,QAAI,YAAY;AAAA,MACd;AAAA,MACA,YAAY,MAAM;AAAA,MAClB,gBAAgB,eAAe,KAAK;AAAA,MACpC,QAAQ,MAAM;AAAA,MACd;AAAA,MACA;AAAA,MACA,YAAY,SAASC,YAAYX,gBAAe;AAC9C,4BAAoBA;AACpB,qBAAaQ,aAAYR,cAAa;AACtC,eAAO;AAAA,MACT;AAAA,MACA,YAAY,SAASY,YAAYZ,gBAAe;AAC9C,uBAAe,KAAK;AACpB,YAAI,cAAc,WAAW,0BAA0B;AACrD,kBAAQ,YAAY,WAAW,wBAAwB;AAAA,QACzD;AACA,4BAAoB;AACpB,QAAAQ,eAAc,eAAe,KAAK;AAClC,oBAAYR,gBAAe,UAAU;AACrC,qBAAa;AAAA,MACf;AAAA,MACA,mBAAmB,SAAS,oBAAqB;AAE/C;AAAA,UACE;AAAA,UACA,OAAO,OAAO,CAAC,GAAG,YAAY,EAAE,YAAY,KAAK,CAAC;AAAA,UAClD;AAAA,QACF;AACA,qBAAa;AAAA,MACf;AAAA,MACA,eAAe,SAAS,gBAAiB;AACvC,eAAO;AAAA,MACT;AAAA,MACA,wBAAwB,SAAS,uBAAwB,YAAY;AACnE,eAAO,sBAAsB,KAAK,EAAE,UAAU;AAAA,MAChD;AAAA,MACA,cAAc,WAAY;AACxB,iCAAyB;AAAA,MAC3B;AAAA,MACA,eAAe,WAAY;AACzB,sBAAc,MAAM,YAAY,OAAO;AAAA,MACzC;AAAA,MACA,mBAAmB,SAAS,oBAAqB;AAC/C,eAAO,UAAU;AAAA,MACnB;AAAA,MACA,4BAA4B,SAAS,6BAA8B;AACjE,eAAO,UAAU,yBAAyB;AAAA,MAC5C;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAGA,IAAI,YAAY,SAAU,SAAS,SAAS;AAC1C,MAAI,kBAAkB,YAAY,OAAO;AACzC,MAAI,YAAY,gBAAgB,OAAO;AACvC,UAAQ,iBAAiB,IAAI;AAC7B,aAAW,SAAS,SAAS;AAC7B,SAAO;AAAA,IACL,SAAS,SAAS,UAAW;AAC3B,iBAAW,WAAW,SAAS;AAC/B,gBAAU,QAAQ,SAAS;AAAA,IAC7B;AAAA,IACA,YAAY,SAAS,WAAYU,UAAS,OAAO;AAC/C,gBAAU,WAAWA,UAAS,KAAK;AAAA,IACrC;AAAA,EACF;AACF;AAIA,UAAU,YAAY;AACtB,UAAU,aAAa,WAAY;AACjC,aAAW,WAAW;AACxB;AAEA,UAAU,aAAa,WAAY;AACjC,SAAO,WAAW,WAAW;AAC/B;AAEA,IAAI,UAAU,SAAU,KAAK;AAC3B,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AACjD;AAEA,SAAS,YAAa,KAAK,YAAY;AACrC,MAAI,MAAM,IAAI,OAAO;AACrB,MAAI,KAAK;AACP,QAAI,OAAO,QAAQ,UAAU;AAC3B,UAAI,SAAS,EAAE,OAAO,IAAI;AAC1B,UAAI,YAAY;AACd,eAAO,QAAQ,EAAE,OAAO,WAAW;AAAA,MACrC;AACA,aAAO;AAAA,IACT,WAAW,OAAO,QAAQ,UAAU;AAClC,UAAI,WAAW,EAAE,OAAO,IAAI,SAAS,OAAO,OAAO,IAAI,SAAS,CAAC,EAAE;AAEnE,UAAI,YAAY;AACd,YAAI,SAAS,MAAM,OAAO;AACxB,cAAI,QAAQ,SAAS,MAAM,KAAK,GAAG;AACjC,qBAAS,MAAM,MAAM,KAAK,UAAU;AAAA,UACtC,OAAO;AACL,qBAAS,MAAM,QAAQ,CAAC,YAAY,SAAS,MAAM,KAAK;AAAA,UAC1D;AAAA,QACF,OAAO;AACL,mBAAS,MAAM,QAAQ;AAAA,QACzB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO,EAAE,OAAO,MAAM;AACxB;AAEA,SAAS,gBAAiB,KAAK;AAC7B,MAAI,KAAK;AACP,QAAI,OAAO,QAAQ,UAAU;AAAE,aAAO;AAAA,IAAM;AAC5C,QAAI,OAAO,QAAQ,UAAU;AAC3B,UACE,OAAO,IAAI,UAAU,YACrB,OAAO,IAAI,UAAU,cACrB,OAAO,IAAI,UAAU,UACrB;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAGA,UAAU,cAAc,iBAAiB,EAAE;AAC3C,UAAU,YAAY;AAEtB,IAAI,kBAAkB;AAAA;AAAA,EAEpB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,cAAc;AAAA,EACd,oBAAoB;AACtB;AAEA,SAAS,oBAAqB,OAAO,SAAS;AAC5C,MAAI,UAAU,OAAO,KAAK,KAAK,EAAE,OAAO,SAAU,QAAQ,KAAK;AAC7D,QAAI,aAAa;AACjB,QAAI,OAAO,MAAM,UAAU;AAE3B,QAAI,SAAS,QAAW;AACtB,UAAI,OAAO,SAAS,YAAY;AAC9B,YAAI,gBAAgB,UAAU,GAAG;AAC/B,iBAAO,gBAAgB,UAAU,CAAC,IAAI,SAAU,QAAQ;AACtD,oBAAQ,MAAM,YAAY,MAAM;AAAA,UAClC;AAAA,QACF,OAAO;AACL,iBAAO,UAAU,IAAI,WAAY;AAC/B,gBAAI,SAAS,CAAC,GAAG,MAAM,UAAU;AACjC,mBAAQ,MAAQ,QAAQ,GAAI,IAAI,UAAW,GAAI;AAE/C,mBAAO,KAAK,MAAM,QAAQ,MAAM;AAAA,UAClC;AAAA,QACF;AAAA,MACF,OAAO;AACL,eAAO,UAAU,IAAI;AAAA,MACvB;AAAA,IACF;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,SAAO;AACT;AAEA,IAAI,aAAa,SAAU,SAAS;AAClC,MAAI,QAAQ,OAAO,OAAO,CAAC,GAAG,QAAQ,QAAQ,QAAQ,MAAM;AAC5D,SAAO,oBAAoB,OAAO,OAAO;AAC3C;AAEA,IAAI,YAAY;AAAA,EACd,MAAM;AAAA,EACN,SAAS,SAAS,UAAW;AAC3B,SAAK,mBAAmB,KAAK,MAAM,aAAa,KAAK;AACrD,SAAK,YAAY,UAAU,KAAK,kBAAkB,WAAW,IAAI,CAAC;AAAA,EACpE;AAAA,EACA,SAAS,SAAS,UAAW;AAC3B,QACE,KAAK,MAAM,cAAc,KAAK,oBAC9B,KAAK,QAAQ,KAAK,kBAClB;AACA,UAAI,KAAK,WAAW;AAClB,aAAK,UAAU,QAAQ;AAAA,MACzB;AACA,WAAK,mBAAmB,KAAK,MAAM,aAAa,KAAK;AACrD,WAAK,YAAY,UAAU,KAAK,kBAAkB,WAAW,IAAI,CAAC;AAClE;AAAA,IACF;AAEA,SAAK,UAAU,WAAW,WAAW,IAAI,CAAC;AAAA,EAC5C;AAAA,EACA,WAAW,SAAS,YAAa;AAC/B,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,QAAQ;AAAA,IACzB;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,WAAW;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,mBAAmB,EAAE,MAAM,SAAS,SAAS,KAAK;AAAA,IAClD,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,iBAAiB,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IACjD,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,mBAAmB;AAAA,IACnB,uBAAuB,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IACvD,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,KAAK;AAAA,MACH,WAAW;AAAA,MACX,SAAS;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,iBAAiB,CAAC,QAAQ,OAAO;AAAA,EACnC;AAAA,EACA,QAAQ,WAAY;AAClB,QAAI,WAAW,YAAY,IAAI;AAC/B,WAAO;AAAA,MACL,SAAS;AAAA,MACT,OAAO,OAAO,CAAC,GAAG,EAAE,KAAK,YAAY,GAAG,SAAS,KAAK;AAAA,MACtD,KAAK,OAAO,QAAQ;AAAA,IACtB;AAAA,EACF;AACF;AAEA,IAAI,YAAY,SAAU,eAAe,KAAK;AAC5C,MAAI,WAAW,YAAY,KAAK;AAAA,IAC9B;AAAA,IACA,IAAI,iBAAiB,yBAAyB;AAAA,EAAG,CAAC;AACpD,SAAO;AAAA,IACL,SAAS;AAAA,IACT,OAAO,OAAO,CAAC,GAAG,SAAS,KAAK;AAAA,IAChC,IAAI,OAAO,QAAQ;AAAA,EACrB;AACF;AAEA,IAAI,YAAY;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA,IACL,KAAK;AAAA,MACH,WAAW;AAAA,MACX,SAAS;AAAA,IACX;AAAA,IACA,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,QAAQ,WAAY;AAClB,WAAO,UAAU,GAAG,IAAI;AAAA,EAC1B;AACF;", "names": ["containerElement", "isVisible", "getPosition", "containers", "draggableInfo", "ref", "css", "getGhostParent", "handleDragStartConditions", "onMouseUp", "dragListeningContainers", "element", "<PERSON><PERSON><PERSON><PERSON>", "container", "options", "handleDrag", "handleDrop"]}