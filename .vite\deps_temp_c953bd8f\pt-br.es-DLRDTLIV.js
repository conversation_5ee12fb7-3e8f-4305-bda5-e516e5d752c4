import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/pt-br.es.js
var weekDays = [
  "Segunda-feira",
  "Terça-feira",
  "Quarta-feira",
  "Q<PERSON><PERSON>-feira",
  "Sex<PERSON>-feira",
  "Sábado",
  "Domingo"
];
var months = [
  "Janeiro",
  "Fevereiro",
  "Março",
  "Abril",
  "Maio",
  "Junho",
  "Julho",
  "Agosto",
  "Setembro",
  "Outubro",
  "Novembro",
  "Dezembro"
];
var years = "Anos";
var year = "Ano";
var month = "Mês";
var week = "Semana";
var day = "Dia";
var today = "Hoje";
var noEvent = "Sem eventos";
var allDay = "Dia inteiro";
var deleteEvent = "Remover";
var createEvent = "Criar um evento";
var dateFormat = "dddd D MMMM YYYY";
var ptBr = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  ptBr as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/pt-br.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=pt-br.es-DLRDTLIV.js.map
