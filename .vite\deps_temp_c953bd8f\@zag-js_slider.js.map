{"version": 3, "sources": ["../../node_modules/@zag-js/anatomy/src/create-anatomy.ts", "../../node_modules/@zag-js/dom-query/src/attrs.ts", "../../node_modules/@zag-js/dom-query/src/constants.ts", "../../node_modules/@zag-js/dom-query/src/is.ts", "../../node_modules/@zag-js/dom-query/src/contains.ts", "../../node_modules/@zag-js/dom-query/src/env.ts", "../../node_modules/@zag-js/dom-query/src/data-url.ts", "../../node_modules/@zag-js/dom-query/src/platform.ts", "../../node_modules/@zag-js/dom-query/src/event.ts", "../../node_modules/@zag-js/dom-query/src/get-by-id.ts", "../../node_modules/@zag-js/dom-query/src/sanitize.ts", "../../node_modules/@zag-js/dom-query/src/get-by-text.ts", "../../node_modules/@zag-js/dom-query/src/get-by-typeahead.ts", "../../node_modules/@zag-js/dom-query/src/get-computed-style.ts", "../../node_modules/@zag-js/dom-query/src/get-parent-node.ts", "../../node_modules/@zag-js/dom-query/src/get-scroll-position.ts", "../../node_modules/@zag-js/dom-query/src/tabbable.ts", "../../node_modules/@zag-js/dom-query/src/initial-focus.ts", "../../node_modules/@zag-js/dom-query/src/is-editable-element.ts", "../../node_modules/@zag-js/dom-query/src/is-hidden-element.ts", "../../node_modules/@zag-js/dom-query/src/is-overflow-element.ts", "../../node_modules/@zag-js/dom-query/src/raf.ts", "../../node_modules/@zag-js/dom-query/src/observe-attributes.ts", "../../node_modules/@zag-js/dom-query/src/observe-children.ts", "../../node_modules/@zag-js/dom-query/src/overflow.ts", "../../node_modules/@zag-js/dom-query/src/proxy-tab-focus.ts", "../../node_modules/@zag-js/dom-query/src/query.ts", "../../node_modules/@zag-js/dom-query/src/scope.ts", "../../node_modules/@zag-js/dom-query/src/scroll-into-view.ts", "../../node_modules/@zag-js/dom-query/src/set.ts", "../../node_modules/@zag-js/dom-query/src/visually-hidden.ts", "../../node_modules/@zag-js/dom-query/src/wait-for.ts", "../../node_modules/@zag-js/text-selection/src/index.ts", "../../node_modules/@zag-js/dom-event/src/add-dom-event.ts", "../../node_modules/@zag-js/dom-event/src/assertion.ts", "../../node_modules/@zag-js/dom-event/src/click-link.ts", "../../node_modules/@zag-js/dom-event/src/queue-before-event.ts", "../../node_modules/@zag-js/dom-event/src/fire-event.ts", "../../node_modules/@zag-js/dom-event/src/get-event-key.ts", "../../node_modules/@zag-js/dom-event/src/get-event-point.ts", "../../node_modules/@zag-js/dom-event/src/get-event-step.ts", "../../node_modules/@zag-js/dom-event/src/get-native-event.ts", "../../node_modules/@zag-js/dom-event/src/get-point-value.ts", "../../node_modules/@zag-js/dom-event/src/request-pointer-lock.ts", "../../node_modules/@zag-js/dom-event/src/track-focus-visible.ts", "../../node_modules/@zag-js/dom-event/src/pipe.ts", "../../node_modules/@zag-js/dom-event/src/track-pointer-move.ts", "../../node_modules/@zag-js/dom-event/src/track-press.ts", "../../node_modules/@zag-js/dom-event/src/track-visual-viewport.ts", "../../node_modules/@zag-js/numeric-range/src/index.ts", "../../node_modules/@zag-js/form-utils/src/input-event.ts", "../../node_modules/@zag-js/form-utils/src/form.ts", "../../node_modules/@zag-js/element-size/src/track-size.ts", "../../node_modules/@zag-js/element-size/src/track-sizes.ts", "../../node_modules/@zag-js/utils/src/array.ts", "../../node_modules/@zag-js/utils/src/equal.ts", "../../node_modules/@zag-js/utils/src/functions.ts", "../../node_modules/@zag-js/utils/src/guard.ts", "../../node_modules/@zag-js/utils/src/object.ts", "../../node_modules/@zag-js/utils/src/split-props.ts", "../../node_modules/@zag-js/utils/src/warning.ts", "../../node_modules/@zag-js/slider/src/slider.anatomy.ts", "../../node_modules/@zag-js/slider/src/slider.connect.ts", "../../node_modules/@zag-js/slider/src/slider.dom.ts", "../../node_modules/@zag-js/slider/src/slider.style.ts", "../../node_modules/@zag-js/slider/src/slider.utils.ts", "../../node_modules/@zag-js/slider/src/slider.machine.ts", "../../node_modules/@zag-js/slider/src/slider.props.ts"], "sourcesContent": ["export interface AnatomyPart {\n  selector: string\n  attrs: Record<\"data-scope\" | \"data-part\", string>\n}\n\nexport type AnatomyInstance<T extends string> = Omit<Anatomy<T>, \"parts\">\n\nexport type AnatomyPartName<T> = T extends AnatomyInstance<infer U> ? U : never\n\nexport interface Anatomy<T extends string> {\n  parts: <U extends string>(...parts: U[]) => AnatomyInstance<U>\n  extendWith: <V extends string>(...parts: V[]) => AnatomyInstance<T | V>\n  build: () => Record<T, AnatomyPart>\n  rename: (newName: string) => Anatomy<T>\n  keys: () => T[]\n}\n\nexport const createAnatomy = <T extends string>(name: string, parts = [] as T[]): Anatomy<T> => ({\n  parts: (...values) => {\n    if (isEmpty(parts)) {\n      return createAnatomy(name, values)\n    }\n    throw new Error(\"createAnatomy().parts(...) should only be called once. Did you mean to use .extendWith(...) ?\")\n  },\n  extendWith: (...values) => createAnatomy(name, [...parts, ...values]),\n  rename: (newName) => createAnatomy(newName, parts),\n  keys: () => parts,\n  build: () =>\n    [...new Set(parts)].reduce<Record<string, AnatomyPart>>(\n      (prev, part) =>\n        Object.assign(prev, {\n          [part]: {\n            selector: [\n              `&[data-scope=\"${toKebabCase(name)}\"][data-part=\"${toKebabCase(part)}\"]`,\n              `& [data-scope=\"${toKebabCase(name)}\"][data-part=\"${toKebabCase(part)}\"]`,\n            ].join(\", \"),\n            attrs: { \"data-scope\": toKebabCase(name), \"data-part\": toKebabCase(part) },\n          },\n        }),\n      {},\n    ),\n})\n\nconst toKebabCase = (value: string) =>\n  value\n    .replace(/([A-Z])([A-Z])/g, \"$1-$2\")\n    .replace(/([a-z])([A-Z])/g, \"$1-$2\")\n    .replace(/[\\s_]+/g, \"-\")\n    .toLowerCase()\n\nconst isEmpty = <T>(v: T[]): boolean => v.length === 0\n", "import type { <PERSON><PERSON>ani<PERSON> } from \"./types\"\n\nexport const dataAttr = (guard: boolean | undefined) => (guard ? \"\" : undefined) as Booleanish\nexport const ariaAttr = (guard: boolean | undefined) => (guard ? \"true\" : undefined)\n", "export const MAX_Z_INDEX = 2147483647\n", "export const isHTMLElement = (v: any): v is HTMLElement =>\n  typeof v === \"object\" && v?.nodeType === Node.ELEMENT_NODE && typeof v?.nodeName === \"string\"\n\nexport const isDocument = (el: any): el is Document => el.nodeType === Node.DOCUMENT_NODE\n\nexport const isWindow = (el: any): el is Window => el != null && el === el.window\n\nexport const isVisualViewport = (el: any): el is VisualViewport =>\n  el != null && el.constructor.name === \"VisualViewport\"\n\nexport const getNodeName = (node: Node | Window): string => {\n  if (isHTMLElement(node)) return node.localName || \"\"\n  return \"#document\"\n}\n\nexport function isRootElement(node: Node): boolean {\n  return [\"html\", \"body\", \"#document\"].includes(getNodeName(node))\n}\n\nexport const isNode = (el: any): el is Node => el.nodeType !== undefined\n\nexport const isShadowRoot = (el: any): el is ShadowRoot =>\n  el && isNode(el) && el.nodeType === Node.DOCUMENT_FRAGMENT_NODE && \"host\" in el\n", "import { isHTMLElement } from \"./is\"\n\ntype Target = HTMLElement | EventTarget | null | undefined\n\nexport function contains(parent: Target, child: Target) {\n  if (!parent || !child) return false\n  if (!isHTMLElement(parent) || !isHTMLElement(child)) return false\n  return parent === child || parent.contains(child)\n}\n", "import { isHTMLElement, isDocument, isShadowRoot, isWindow } from \"./is\"\n\nexport function getDocument(el: Element | Window | Node | Document | null) {\n  if (isDocument(el)) return el\n  if (isWindow(el)) return el.document\n  return el?.ownerDocument ?? document\n}\n\nexport function getDocumentElement(el: Element | Node | Window | Document | null): HTMLElement {\n  return getDocument(el).documentElement\n}\n\nexport function getWindow(el: Node | ShadowRoot | Document | undefined) {\n  if (isShadowRoot(el)) return getWindow(el.host)\n  if (isDocument(el)) return el.defaultView ?? window\n  if (isHTMLElement(el)) return el.ownerDocument?.defaultView ?? window\n  return window\n}\n\nexport function getActiveElement(el: HTMLElement): HTMLElement | null {\n  const doc = getDocument(el)\n  let activeElement = doc.activeElement as HTMLElement | null\n\n  while (activeElement?.shadowRoot) {\n    const el = activeElement.shadowRoot.activeElement as HTMLElement | null\n    if (el === activeElement) break\n    else activeElement = el\n  }\n\n  return activeElement\n}\n", "import { getWindow } from \"./env\"\n\nexport type DataUrlType = \"image/png\" | \"image/jpeg\" | \"image/svg+xml\"\n\nexport interface DataUrlOptions {\n  type: DataUrlType\n  quality?: number\n}\n\nexport function getDataUrl(svg: SVGElement | undefined | null, opts: DataUrlOptions): Promise<string> {\n  const { type, quality = 0.92 } = opts\n\n  if (!svg) throw new Error(\"[get-data-url]: could not find the svg element\")\n\n  const win = getWindow(svg)\n  const doc = win.document\n\n  const serializer = new win.XMLSerializer()\n  const source = '<?xml version=\"1.0\" standalone=\"no\"?>\\r\\n' + serializer.serializeToString(svg)\n  const svgString = \"data:image/svg+xml;charset=utf-8,\" + encodeURIComponent(source)\n\n  if (type === \"image/svg+xml\") {\n    return Promise.resolve(svgString)\n  }\n\n  const svgBounds = svg.getBoundingClientRect()\n  const dpr = win.devicePixelRatio || 1\n\n  const canvas = doc.createElement(\"canvas\")\n  const image = new win.Image()\n  image.src = svgString\n\n  canvas.width = svgBounds.width * dpr\n  canvas.height = svgBounds.height * dpr\n\n  const context = canvas.getContext(\"2d\")\n  context!.scale(dpr, dpr)\n\n  return new Promise((resolve) => {\n    image.onload = () => {\n      context!.drawImage(image, 0, 0)\n      resolve(canvas.toDataURL(type, quality))\n    }\n  })\n}\n", "export const isDom = () => typeof document !== \"undefined\"\n\nexport function getPlatform() {\n  const agent = (navigator as any).userAgentData\n  return agent?.platform ?? navigator.platform\n}\n\nconst pt = (v: RegExp) => isDom() && v.test(getPlatform())\nconst ua = (v: RegExp) => isDom() && v.test(navigator.userAgent)\nconst vn = (v: RegExp) => isDom() && v.test(navigator.vendor)\n\nexport const isTouchDevice = () => isDom() && !!navigator.maxTouchPoints\nexport const isMac = () => pt(/^Mac/)\nexport const isSafari = () => isApple() && vn(/apple/i)\nexport const isFirefox = () => ua(/firefox\\//i)\nexport const isApple = () => pt(/mac|iphone|ipad|ipod/i)\nexport const isIos = () => pt(/iP(hone|ad|od)|iOS/)\nexport const isWebKit = () => ua(/AppleWebKit/)\n\nexport const isModKey = (event: Pick<KeyboardEvent, \"metaKey\" | \"ctrlKey\">) =>\n  isApple() ? event.metaKey : event.ctrlKey\n", "import { contains } from \"./contains\"\nimport { isApple } from \"./platform\"\n\nexport function getBeforeInputValue(event: Pick<InputEvent, \"currentTarget\">) {\n  const { selectionStart, selectionEnd, value } = event.currentTarget as HTMLInputElement\n  return value.slice(0, selectionStart!) + (event as any).data + value.slice(selectionEnd!)\n}\n\nfunction getComposedPath(event: any): EventTarget[] | undefined {\n  return event.composedPath?.() ?? event.nativeEvent?.composedPath?.()\n}\n\nexport function getEventTarget<T extends EventTarget>(\n  event: Partial<Pick<UIEvent, \"target\" | \"composedPath\">>,\n): T | null {\n  const composedPath = getComposedPath(event)\n  return (composedPath?.[0] ?? event.target) as T | null\n}\n\nexport const isSelfTarget = (event: Partial<Pick<UIEvent, \"currentTarget\" | \"target\" | \"composedPath\">>) => {\n  return contains(event.currentTarget as Node, getEventTarget(event))\n}\n\nexport function isOpeningInNewTab(event: Pick<MouseEvent, \"currentTarget\" | \"metaKey\" | \"ctrlKey\">) {\n  const element = event.currentTarget as HTMLAnchorElement | HTMLButtonElement | HTMLInputElement | null\n  if (!element) return false\n\n  const isAppleDevice = isApple()\n  if (isAppleDevice && !event.metaKey) return false\n  if (!isAppleDevice && !event.ctrlKey) return false\n\n  const localName = element.localName\n\n  if (localName === \"a\") return true\n  if (localName === \"button\" && element.type === \"submit\") return true\n  if (localName === \"input\" && element.type === \"submit\") return true\n\n  return false\n}\n\nexport function isDownloadingEvent(event: Pick<MouseEvent, \"altKey\" | \"currentTarget\">) {\n  const element = event.currentTarget as HTMLAnchorElement | HTMLButtonElement | HTMLInputElement | null\n  if (!element) return false\n\n  const localName = element.localName\n  if (!event.altKey) return false\n\n  if (localName === \"a\") return true\n  if (localName === \"button\" && element.type === \"submit\") return true\n  if (localName === \"input\" && element.type === \"submit\") return true\n\n  return false\n}\n\nexport function isComposingEvent(event: any) {\n  return event.nativeEvent?.isComposing ?? event.isComposing\n}\n", "export type ItemToId<T> = (v: T) => string\n\nexport const defaultItemToId = <T extends HTMLElement>(v: T) => v.id\n\nexport function itemById<T extends HTMLElement>(v: T[], id: string, itemToId: ItemToId<T> = defaultItemToId) {\n  return v.find((item) => itemToId(item) === id)\n}\n\nexport function indexOfId<T extends HTMLElement>(v: T[], id: string, itemToId: ItemToId<T> = defaultItemToId) {\n  const item = itemById(v, id, itemToId)\n  return item ? v.indexOf(item) : -1\n}\n\nexport function nextById<T extends HTMLElement>(v: T[], id: string, loop = true) {\n  let idx = indexOfId(v, id)\n  idx = loop ? (idx + 1) % v.length : Math.min(idx + 1, v.length - 1)\n  return v[idx]\n}\n\nexport function prevById<T extends HTMLElement>(v: T[], id: string, loop = true) {\n  let idx = indexOfId(v, id)\n  if (idx === -1) return loop ? v[v.length - 1] : null\n  idx = loop ? (idx - 1 + v.length) % v.length : Math.max(0, idx - 1)\n  return v[idx]\n}\n", "export const sanitize = (str: string) =>\n  str\n    .split(\"\")\n    .map((char) => {\n      const code = char.charCodeAt(0)\n      if (code > 0 && code < 128) return char\n      if (code >= 128 && code <= 255) return `/x${code.toString(16)}`.replace(\"/\", \"\\\\\")\n      return \"\"\n    })\n    .join(\"\")\n    .trim()\n", "import { defaultItemToId, indexOfId, type ItemToId } from \"./get-by-id\"\nimport { sanitize } from \"./sanitize\"\n\nconst getValueText = <T extends HTMLElement>(item: T) => sanitize(item.dataset.valuetext ?? item.textContent ?? \"\")\n\nconst match = (valueText: string, query: string) => valueText.trim().toLowerCase().startsWith(query.toLowerCase())\n\nconst wrap = <T>(v: T[], idx: number) => {\n  return v.map((_, index) => v[(Math.max(idx, 0) + index) % v.length])\n}\n\nexport function getByText<T extends HTMLElement>(\n  v: T[],\n  text: string,\n  currentId?: string | null,\n  itemToId: ItemToId<T> = defaultItemToId,\n) {\n  const index = currentId ? indexOfId(v, currentId, itemToId) : -1\n  let items = currentId ? wrap(v, index) : v\n\n  const isSingleKey = text.length === 1\n\n  if (isSingleKey) {\n    items = items.filter((item) => itemToId(item) !== currentId)\n  }\n\n  return items.find((item) => match(getValueText(item), text))\n}\n", "import { getByText } from \"./get-by-text\"\nimport type { ItemToId } from \"./get-by-id\"\n\nexport interface TypeaheadState {\n  keysSoFar: string\n  timer: number\n}\n\nexport interface TypeaheadOptions {\n  state: TypeaheadState\n  activeId: string | null\n  key: string\n  timeout?: number\n  itemToId?: ItemToId<HTMLElement>\n}\n\nfunction getByTypeaheadImpl<T extends HTMLElement>(_items: T[], options: TypeaheadOptions) {\n  const { state, activeId, key, timeout = 350, itemToId } = options\n\n  const search = state.keysSoFar + key\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0])\n\n  const query = isRepeated ? search[0] : search\n\n  let items = _items.slice()\n\n  const next = getByText(items, query, activeId, itemToId)\n\n  function cleanup() {\n    clearTimeout(state.timer)\n    state.timer = -1\n  }\n\n  function update(value: string) {\n    state.keysSoFar = value\n    cleanup()\n\n    if (value !== \"\") {\n      state.timer = +setTimeout(() => {\n        update(\"\")\n        cleanup()\n      }, timeout)\n    }\n  }\n\n  update(search)\n\n  return next\n}\nexport const getByTypeahead = /*#__PURE__*/ Object.assign(getByTypeaheadImpl, {\n  defaultOptions: { keysSoFar: \"\", timer: -1 },\n  isValidEvent: isValidTypeaheadEvent,\n})\n\nfunction isValidTypeaheadEvent(event: Pick<KeyboardEvent, \"key\" | \"ctrlKey\" | \"metaKey\">) {\n  return event.key.length === 1 && !event.ctrlKey && !event.metaKey\n}\n", "import { getWindow } from \"./env\"\n\nconst styleCache = new WeakMap<Element, CSSStyleDeclaration>()\n\nexport function getComputedStyle(el: Element) {\n  if (!styleCache.has(el)) {\n    styleCache.set(el, getWindow(el).getComputedStyle(el))\n  }\n  return styleCache.get(el)!\n}\n", "import { getDocumentElement } from \"./env\"\nimport { getNodeName, isShadowRoot } from \"./is\"\n\nexport function getParentNode(node: Node): Node {\n  if (getNodeName(node) === \"html\") {\n    return node\n  }\n\n  const result =\n    // Step into the shadow DOM of the parent of a slotted node.\n    (node as any).assignedSlot ||\n    // DOM Element detected.\n    node.parentNode ||\n    // ShadowRoot detected.\n    (isShadowRoot(node) && node.host) ||\n    // Fallback.\n    getDocumentElement(node)\n\n  return isShadowRoot(result) ? result.host : result\n}\n", "import { isHTMLElement } from \"./is\"\n\nexport interface ScrollPosition {\n  scrollLeft: number\n  scrollTop: number\n}\n\nexport function getScrollPosition(element: HTMLElement | Window): ScrollPosition {\n  if (isHTMLElement(element)) {\n    return { scrollLeft: element.scrollLeft, scrollTop: element.scrollTop }\n  }\n  return { scrollLeft: element.scrollX, scrollTop: element.scrollY }\n}\n", "const isHTMLElement = (element: any): element is HTMLElement =>\n  typeof element === \"object\" && element !== null && element.nodeType === 1\n\nconst isFrame = (element: any): element is HTMLIFrameElement => isHTMLElement(element) && element.tagName === \"IFRAME\"\n\nfunction isVisible(el: any) {\n  if (!isHTMLElement(el)) return false\n  return el.offsetWidth > 0 || el.offsetHeight > 0 || el.getClientRects().length > 0\n}\n\ntype IncludeContainerType = boolean | \"if-empty\"\n\nfunction hasNegativeTabIndex(element: Element) {\n  const tabIndex = parseInt(element.getAttribute(\"tabindex\") || \"0\", 10)\n  return tabIndex < 0\n}\n\nconst focusableSelector =\n  /*#__PURE__*/ \"input:not([type='hidden']):not([disabled]), select:not([disabled]), \" +\n  \"textarea:not([disabled]), a[href], button:not([disabled]), [tabindex], \" +\n  \"iframe, object, embed, area[href], audio[controls], video[controls], \" +\n  \"[contenteditable]:not([contenteditable='false']), details > summary:first-of-type\"\n\n/**\n * Returns the focusable elements within the element\n */\nexport const getFocusables = (\n  container: Pick<HTMLElement, \"querySelectorAll\"> | null,\n  includeContainer: IncludeContainerType = false,\n) => {\n  if (!container) return []\n  const elements = Array.from(container.querySelectorAll<HTMLElement>(focusableSelector))\n\n  const include = includeContainer == true || (includeContainer == \"if-empty\" && elements.length === 0)\n  if (include && isHTMLElement(container) && isFocusable(container)) {\n    elements.unshift(container)\n  }\n\n  const focusableElements = elements.filter(isFocusable)\n\n  focusableElements.forEach((element, i) => {\n    if (isFrame(element) && element.contentDocument) {\n      const frameBody = element.contentDocument.body\n      focusableElements.splice(i, 1, ...getFocusables(frameBody))\n    }\n  })\n\n  return focusableElements\n}\n\n/**\n * Whether this element is focusable\n */\nexport function isFocusable(element: HTMLElement | null): element is HTMLElement {\n  if (!element || element.closest(\"[inert]\")) return false\n  return element.matches(focusableSelector) && isVisible(element)\n}\n\nexport function getFirstFocusable(\n  container: HTMLElement | null,\n  includeContainer?: IncludeContainerType,\n): HTMLElement | null {\n  const [first] = getFocusables(container, includeContainer)\n  return first || null\n}\n\n/**\n * Returns the tabbable elements within the element\n */\nexport function getTabbables(container: HTMLElement | null, includeContainer?: IncludeContainerType) {\n  if (!container) return []\n  const elements = Array.from(container.querySelectorAll<HTMLElement>(focusableSelector))\n  const tabbableElements = elements.filter(isTabbable)\n\n  if (includeContainer && isTabbable(container)) {\n    tabbableElements.unshift(container)\n  }\n\n  tabbableElements.forEach((element, i) => {\n    if (isFrame(element) && element.contentDocument) {\n      const frameBody = element.contentDocument.body\n      const allFrameTabbable = getTabbables(frameBody)\n      tabbableElements.splice(i, 1, ...allFrameTabbable)\n    }\n  })\n\n  if (!tabbableElements.length && includeContainer) {\n    return elements\n  }\n\n  return tabbableElements\n}\n\n/**\n * Whether this element is tabbable\n */\nexport function isTabbable(el: HTMLElement | null): el is HTMLElement {\n  if (el != null && el.tabIndex > 0) return true\n  return isFocusable(el) && !hasNegativeTabIndex(el)\n}\n\n/**\n * Returns the first focusable element within the element\n */\nexport function getFirstTabbable(\n  container: HTMLElement | null,\n  includeContainer?: IncludeContainerType,\n): HTMLElement | null {\n  const [first] = getTabbables(container, includeContainer)\n  return first || null\n}\n\n/**\n * Returns the last focusable element within the element\n */\nexport function getLastTabbable(\n  container: HTMLElement | null,\n  includeContainer?: IncludeContainerType,\n): HTMLElement | null {\n  const elements = getTabbables(container, includeContainer)\n  return elements[elements.length - 1] || null\n}\n\n/**\n * Returns the first and last focusable elements within the element\n */\nexport function getTabbableEdges(\n  container: HTMLElement | null,\n  includeContainer?: IncludeContainerType,\n): [HTMLElement, HTMLElement] | [null, null] {\n  const elements = getTabbables(container, includeContainer)\n  const first = elements[0] || null\n  const last = elements[elements.length - 1] || null\n  return [first, last]\n}\n\n/**\n * Returns the next tabbable element after the current element\n */\nexport function getNextTabbable(container: HTMLElement | null, current?: HTMLElement | null): HTMLElement | null {\n  const tabbables = getTabbables(container)\n  const doc = container?.ownerDocument || document\n  const currentElement = current ?? (doc.activeElement as HTMLElement | null)\n  if (!currentElement) return null\n  const index = tabbables.indexOf(currentElement)\n  return tabbables[index + 1] || null\n}\n", "import { getTabbableEdges, getTabbables } from \"./tabbable\"\n\nexport interface InitialFocusOptions {\n  root: HTMLElement | null\n  getInitialEl?: () => HTMLElement | null\n  enabled?: boolean\n  filter?: (el: HTMLElement) => boolean\n}\n\nexport function getInitialFocus(options: InitialFocusOptions): HTMLElement | undefined {\n  const { root, getInitialEl, filter, enabled = true } = options\n\n  if (!enabled) return\n\n  let node: HTMLElement | null | undefined = null\n\n  node ||= typeof getInitialEl === \"function\" ? getInitialEl() : getInitialEl\n  node ||= root?.querySelector<HTMLElement>(\"[data-autofocus],[autofocus]\")\n\n  if (!node) {\n    const tabbables = getTabbables(root)\n    node = filter ? tabbables.filter(filter)[0] : tabbables[0]\n  }\n\n  return node || root || undefined\n}\n\nexport function isValidTabEvent(event: Pick<KeyboardEvent, \"shiftKey\" | \"currentTarget\">): boolean {\n  const container = event.currentTarget as HTMLElement | null\n  if (!container) return false\n\n  const [firstTabbable, lastTabbable] = getTabbableEdges(container)\n  const doc = container.ownerDocument || document\n\n  if (doc.activeElement === firstTabbable && event.shiftKey) return false\n  if (doc.activeElement === lastTabbable && !event.shiftKey) return false\n  if (!firstTabbable && !lastTabbable) return false\n\n  return true\n}\n", "import { getWindow } from \"./env\"\nimport { isHTMLElement } from \"./is\"\n\nexport function isEditableElement(el: HTMLElement | EventTarget | null) {\n  if (el == null || !isHTMLElement(el)) {\n    return false\n  }\n\n  try {\n    const win = getWindow(el)\n    return (\n      (el instanceof win.HTMLInputElement && el.selectionStart != null) ||\n      /(textarea|select)/.test(el.localName) ||\n      el.isContentEditable\n    )\n  } catch {\n    return false\n  }\n}\n", "export function isHiddenElement(node: HTMLElement) {\n  if (node.parentElement && isHiddenElement(node.parentElement)) return true\n  return node.hidden\n}\n", "import { getWindow } from \"./env\"\n\nconst OVERFLOW_RE = /auto|scroll|overlay|hidden|clip/\n\nexport function isOverflowElement(el: HTMLElement): boolean {\n  const win = getWindow(el)\n  const { overflow, overflowX, overflowY, display } = win.getComputedStyle(el)\n  return OVERFLOW_RE.test(overflow + overflowY + overflowX) && ![\"inline\", \"contents\"].includes(display)\n}\n", "export function nextTick(fn: VoidFunction) {\n  const set = new Set<VoidFunction>()\n  function raf(fn: VoidFunction) {\n    const id = globalThis.requestAnimationFrame(fn)\n    set.add(() => globalThis.cancelAnimationFrame(id))\n  }\n  raf(() => raf(fn))\n  return function cleanup() {\n    set.forEach((fn) => fn())\n  }\n}\n\nexport function raf(fn: VoidFunction) {\n  const id = globalThis.requestAnimationFrame(fn)\n  return () => {\n    globalThis.cancelAnimationFrame(id)\n  }\n}\n", "import { raf } from \"./raf\"\n\ntype MaybeElement = HTMLElement | null\ntype NodeOrFn = MaybeElement | (() => MaybeElement)\n\nexport interface ObserveAttributeOptions {\n  attributes: string[]\n  callback(record: MutationRecord): void\n  defer?: boolean\n}\n\nfunction observeAttributesImpl(node: MaybeElement, options: ObserveAttributeOptions) {\n  if (!node) return\n  const { attributes, callback: fn } = options\n  const win = node.ownerDocument.defaultView || window\n  const obs = new win.MutationObserver((changes) => {\n    for (const change of changes) {\n      if (change.type === \"attributes\" && change.attributeName && attributes.includes(change.attributeName)) {\n        fn(change)\n      }\n    }\n  })\n  obs.observe(node, { attributes: true, attributeFilter: attributes })\n  return () => obs.disconnect()\n}\n\nexport function observeAttributes(nodeOrFn: NodeOrFn, options: ObserveAttributeOptions) {\n  const { defer } = options\n  const func = defer ? raf : (v: any) => v()\n  const cleanups: (VoidFunction | undefined)[] = []\n  cleanups.push(\n    func(() => {\n      const node = typeof nodeOrFn === \"function\" ? nodeOrFn() : nodeOrFn\n      cleanups.push(observeAttributesImpl(node, options))\n    }),\n  )\n  return () => {\n    cleanups.forEach((fn) => fn?.())\n  }\n}\n", "import { raf } from \"./raf\"\n\ntype MaybeElement = HTMLElement | null\ntype NodeOrFn = MaybeElement | (() => MaybeElement)\n\nexport interface ObserveChildrenOptions {\n  callback: MutationCallback\n  defer?: boolean\n}\n\nfunction observeChildrenImpl(node: MaybeElement, options: ObserveChildrenOptions) {\n  const { callback: fn } = options\n  if (!node) return\n  const win = node.ownerDocument.defaultView || window\n  const obs = new win.MutationObserver(fn)\n  obs.observe(node, { childList: true, subtree: true })\n  return () => obs.disconnect()\n}\n\nexport function observeChildren(nodeOrFn: NodeOrFn, options: ObserveChildrenOptions) {\n  const { defer } = options\n  const func = defer ? raf : (v: any) => v()\n  const cleanups: (VoidFunction | undefined)[] = []\n  cleanups.push(\n    func(() => {\n      const node = typeof nodeOrFn === \"function\" ? nodeOrFn() : nodeOrFn\n      cleanups.push(observeChildrenImpl(node, options))\n    }),\n  )\n  return () => {\n    cleanups.forEach((fn) => fn?.())\n  }\n}\n", "import { getDocument, getWindow } from \"./env\"\nimport { getParentNode } from \"./get-parent-node\"\nimport { isHTMLElement, isRootElement, isVisualViewport } from \"./is\"\nimport { isOverflowElement } from \"./is-overflow-element\"\n\nexport type OverflowAncestor = Array<VisualViewport | Window | HTMLElement | null>\n\nexport function getNearestOverflowAncestor(el: Node): HTMLElement {\n  const parentNode = getParentNode(el)\n\n  if (isRootElement(parentNode)) {\n    return getDocument(parentNode).body\n  }\n\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode\n  }\n\n  return getNearestOverflowAncestor(parentNode)\n}\n\nexport function getOverflowAncestors(el: HTMLElement, list: OverflowAncestor = []): OverflowAncestor {\n  const scrollableAncestor = getNearestOverflowAncestor(el)\n  const isBody = scrollableAncestor === el.ownerDocument.body\n  const win = getWindow(scrollableAncestor)\n\n  if (isBody) {\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [])\n  }\n\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, []))\n}\n\nconst getRect = (el: HTMLElement | Window | VisualViewport) => {\n  if (isHTMLElement(el)) {\n    return el.getBoundingClientRect()\n  }\n\n  if (isVisualViewport(el)) {\n    return { top: 0, left: 0, bottom: el.height, right: el.width }\n  }\n\n  return { top: 0, left: 0, bottom: el.innerHeight, right: el.innerWidth }\n}\n\nexport function isInView(el: HTMLElement | Window | VisualViewport, ancestor: HTMLElement | Window | VisualViewport) {\n  if (!isHTMLElement(el)) return true\n\n  const ancestorRect = getRect(ancestor)\n  const elRect = el.getBoundingClientRect()\n\n  return (\n    elRect.top >= ancestorRect.top &&\n    elRect.left >= ancestorRect.left &&\n    elRect.bottom <= ancestorRect.bottom &&\n    elRect.right <= ancestorRect.right\n  )\n}\n", "import { raf } from \"./raf\"\nimport { getNextTabbable, getTabbableEdges } from \"./tabbable\"\n\ntype MaybeElement = HTMLElement | null\ntype NodeOrFn = MaybeElement | (() => MaybeElement)\n\ninterface ProxyTabFocusOptions<T = MaybeElement> {\n  triggerElement?: T\n  onFocus?: (elementToFocus: HTMLElement) => void\n  defer?: boolean\n}\n\n/**\n * Proxies tab focus within a container to a reference element\n * when the container is rendered in a portal\n */\n\nfunction proxyTabFocusImpl(container: MaybeElement, options: ProxyTabFocusOptions = {}) {\n  const { triggerElement, onFocus } = options\n\n  const doc = container?.ownerDocument || document\n  const body = doc.body\n\n  function onKeyDown(event: KeyboardEvent) {\n    if (event.key !== \"Tab\") return\n\n    let elementToFocus: MaybeElement | undefined = null\n\n    // get all tabbable elements within the container\n    const [firstTabbable, lastTabbable] = getTabbableEdges(container, true)\n\n    const noTabbableElements = !firstTabbable && !lastTabbable\n\n    // if we're focused on the first tabbable element and the user tabs backwards\n    // we want to focus the reference element\n    if (event.shiftKey && (doc.activeElement === firstTabbable || noTabbableElements)) {\n      elementToFocus = triggerElement\n    } else if (!event.shiftKey && doc.activeElement === triggerElement) {\n      // if we're focused on the reference element and the user tabs forwards\n      // we want to focus the first tabbable element\n      elementToFocus = firstTabbable\n    } else if (!event.shiftKey && (doc.activeElement === lastTabbable || noTabbableElements)) {\n      // if we're focused on the last tabbable element and the user tabs forwards\n      // we want to focus the next tabbable element after the reference element\n      elementToFocus = getNextTabbable(body, triggerElement)\n    }\n\n    if (!elementToFocus) return\n\n    event.preventDefault()\n\n    if (typeof onFocus === \"function\") {\n      onFocus(elementToFocus)\n    } else {\n      elementToFocus.focus()\n    }\n  }\n\n  // listen for the tab key in the capture phase\n  doc?.addEventListener(\"keydown\", onKeyDown, true)\n\n  return () => {\n    doc?.removeEventListener(\"keydown\", onKeyDown, true)\n  }\n}\n\nexport function proxyTabFocus(container: NodeOrFn, options: ProxyTabFocusOptions<NodeOrFn>) {\n  const { defer, triggerElement, ...restOptions } = options\n  const func = defer ? raf : (v: any) => v()\n  const cleanups: (VoidFunction | undefined)[] = []\n  cleanups.push(\n    func(() => {\n      const node = typeof container === \"function\" ? container() : container\n      const trigger = typeof triggerElement === \"function\" ? triggerElement() : triggerElement\n      cleanups.push(proxyTabFocusImpl(node, { triggerElement: trigger, ...restOptions }))\n    }),\n  )\n  return () => {\n    cleanups.forEach((fn) => fn?.())\n  }\n}\n", "type Root = Document | Element | null | undefined\n\nexport function queryAll<T extends Element = HTMLElement>(root: Root, selector: string) {\n  return Array.from(root?.querySelectorAll<T>(selector) ?? [])\n}\n\nexport function query<T extends Element = HTMLElement>(root: Root, selector: string) {\n  return root?.querySelector<T>(selector) ?? null\n}\n", "import { getDocument } from \"./env\"\n\nexport interface ScopeContext {\n  getRootNode?(): Document | ShadowRoot | Node\n}\n\nexport function createScope<T>(methods: T) {\n  const dom = {\n    getRootNode: (ctx: ScopeContext) => (ctx.getRootNode?.() ?? document) as Document | ShadowRoot,\n    getDoc: (ctx: ScopeContext) => getDocument(dom.getRootNode(ctx)),\n    getWin: (ctx: ScopeContext) => dom.getDoc(ctx).defaultView ?? window,\n    getActiveElement: (ctx: ScopeContext) => dom.getRootNode(ctx).activeElement,\n    isActiveElement: (ctx: ScopeContext, elem: HTMLElement | null) => elem === dom.getActiveElement(ctx),\n    getById: <T extends Element = HTMLElement>(ctx: ScopeContext, id: string) =>\n      dom.getRootNode(ctx).getElementById(id) as T | null,\n    setValue: <T extends { value: string }>(elem: T | null, value: string | number | null | undefined) => {\n      if (elem == null || value == null) return\n      const valueAsString = value.toString()\n      if (elem.value === valueAsString) return\n      elem.value = value.toString()\n    },\n  }\n\n  return { ...dom, ...methods }\n}\n", "import { isOverflowElement } from \"./is-overflow-element\"\n\nexport interface ScrollOptions extends ScrollIntoViewOptions {\n  rootEl: HTMLElement | null\n}\n\nfunction isScrollable(el: HTMLElement): boolean {\n  return el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth\n}\n\nexport function scrollIntoView(el: HTMLElement | null | undefined, options?: ScrollOptions): void {\n  const { rootEl, ...scrollOptions } = options || {}\n\n  if (!el || !rootEl) {\n    return\n  }\n\n  if (!isOverflowElement(rootEl) || !isScrollable(rootEl)) {\n    return\n  }\n\n  el.scrollIntoView(scrollOptions)\n}\n", "const cleanups = new WeakMap<Element, Map<string, () => void>>()\n\nexport function set(element: Element, key: string, setup: () => () => void) {\n  if (!cleanups.has(element)) {\n    cleanups.set(element, new Map())\n  }\n\n  const elementCleanups = cleanups.get(element)!\n  const prevCleanup = elementCleanups.get(key)\n\n  if (!prevCleanup) {\n    elementCleanups.set(key, setup())\n    return () => {\n      elementCleanups.get(key)?.()\n      elementCleanups.delete(key)\n    }\n  }\n\n  const cleanup = setup()\n\n  const nextCleanup = () => {\n    cleanup()\n    prevCleanup()\n    elementCleanups.delete(key)\n  }\n\n  elementCleanups.set(key, nextCleanup)\n\n  return () => {\n    const isCurrent = elementCleanups.get(key) === nextCleanup\n    if (!isCurrent) return\n    cleanup()\n    elementCleanups.set(key, prevCleanup)\n  }\n}\n\nexport function setAttribute(element: Element, attr: string, value: string) {\n  const setup = () => {\n    const previousValue = element.getAttribute(attr)\n    element.setAttribute(attr, value)\n    return () => {\n      if (previousValue == null) {\n        element.removeAttribute(attr)\n      } else {\n        element.setAttribute(attr, previousValue)\n      }\n    }\n  }\n\n  return set(element, attr, setup)\n}\n\nexport function setProperty<T extends Element, K extends keyof T & string>(element: T, property: K, value: T[K]) {\n  const setup = () => {\n    const exists = property in element\n    const previousValue = element[property]\n    element[property] = value\n    return () => {\n      if (!exists) {\n        delete element[property]\n      } else {\n        element[property] = previousValue\n      }\n    }\n  }\n\n  return set(element, property, setup)\n}\n\nexport function setStyle(element: HTMLElement | null | undefined, style: Partial<CSSStyleDeclaration>) {\n  if (!element) return () => {}\n\n  const setup = () => {\n    const prevStyle = element.style.cssText\n    Object.assign(element.style, style)\n    return () => {\n      element.style.cssText = prevStyle\n    }\n  }\n\n  return set(element, \"style\", setup)\n}\n", "export const visuallyHiddenStyle = {\n  border: \"0\",\n  clip: \"rect(0 0 0 0)\",\n  height: \"1px\",\n  margin: \"-1px\",\n  overflow: \"hidden\",\n  padding: \"0\",\n  position: \"absolute\",\n  width: \"1px\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\",\n} as const\n", "import { isHTMLElement } from \"./is\"\n\ntype ElementGetter = () => Element | null\n\nconst fps = 1000 / 60\n\nexport function waitForElement(query: ElementGetter, cb: (el: HTMLElement) => void) {\n  const el = query()\n\n  if (isHTMLElement(el) && el.isConnected) {\n    cb(el)\n    return () => void 0\n  } else {\n    const timerId = setInterval(() => {\n      const el = query()\n      if (isHTMLElement(el) && el.isConnected) {\n        cb(el)\n        clearInterval(timerId)\n      }\n    }, fps)\n\n    return () => clearInterval(timerId)\n  }\n}\n\nexport function waitForElements(queries: ElementGetter[], cb: (el: HTMLElement) => void) {\n  const cleanups: VoidFunction[] = []\n\n  queries?.forEach((query) => {\n    const clean = waitForElement(query, cb)\n    cleanups.push(clean)\n  })\n\n  return () => {\n    cleanups.forEach((fn) => fn())\n  }\n}\n", "import { isIos, nextTick, raf } from \"@zag-js/dom-query\"\n\ntype State = \"default\" | \"disabled\" | \"restoring\"\n\nlet state: State = \"default\"\nlet userSelect = \"\"\nconst elementMap = new WeakMap<HTMLElement, string>()\n\nexport interface DisableTextSelectionOptions<T = MaybeElement> {\n  target?: T\n  doc?: Document\n  defer?: boolean\n}\n\nfunction disableTextSelectionImpl(options: DisableTextSelectionOptions = {}) {\n  const { target, doc } = options\n\n  const docNode = doc ?? document\n  const rootEl = docNode.documentElement\n\n  if (isIos()) {\n    if (state === \"default\") {\n      userSelect = rootEl.style.webkitUserSelect\n      rootEl.style.webkitUserSelect = \"none\"\n    }\n\n    state = \"disabled\"\n  } else if (target) {\n    elementMap.set(target, target.style.userSelect)\n    target.style.userSelect = \"none\"\n  }\n\n  return () => restoreTextSelection({ target, doc: docNode })\n}\n\nexport function restoreTextSelection(options: DisableTextSelectionOptions = {}) {\n  const { target, doc } = options\n\n  const docNode = doc ?? document\n  const rootEl = docNode.documentElement\n\n  if (isIos()) {\n    if (state !== \"disabled\") return\n    state = \"restoring\"\n\n    setTimeout(() => {\n      nextTick(() => {\n        if (state === \"restoring\") {\n          if (rootEl.style.webkitUserSelect === \"none\") {\n            rootEl.style.webkitUserSelect = userSelect || \"\"\n          }\n          userSelect = \"\"\n          state = \"default\"\n        }\n      })\n    }, 300)\n  } else {\n    if (target && elementMap.has(target)) {\n      const prevUserSelect = elementMap.get(target)\n\n      if (target.style.userSelect === \"none\") {\n        target.style.userSelect = prevUserSelect ?? \"\"\n      }\n\n      if (target.getAttribute(\"style\") === \"\") {\n        target.removeAttribute(\"style\")\n      }\n      elementMap.delete(target)\n    }\n  }\n}\n\ntype MaybeElement = HTMLElement | null | undefined\n\ntype NodeOrFn = MaybeElement | (() => MaybeElement)\n\nexport function disableTextSelection(options: DisableTextSelectionOptions<NodeOrFn> = {}) {\n  const { defer, target, ...restOptions } = options\n  const func = defer ? raf : (v: any) => v()\n  const cleanups: (VoidFunction | undefined)[] = []\n  cleanups.push(\n    func(() => {\n      const node = typeof target === \"function\" ? target() : target\n      cleanups.push(disableTextSelectionImpl({ ...restOptions, target: node }))\n    }),\n  )\n  return () => {\n    cleanups.forEach((fn) => fn?.())\n  }\n}\n", "interface EventMap extends DocumentEventMap, WindowEventMap, HTMLElementEventMap {}\n\ntype Node = Document | HTMLElement | EventTarget | null\n\ntype Target = (() => Node) | Node\n\nexport const addDomEvent = <K extends keyof EventMap>(\n  target: Target,\n  eventName: K,\n  handler: (event: EventMap[K]) => void,\n  options?: boolean | AddEventListenerOptions,\n) => {\n  const node = typeof target === \"function\" ? target() : target\n  node?.addEventListener(eventName, handler as any, options)\n  return () => {\n    node?.removeEventListener(eventName, handler as any, options)\n  }\n}\n", "import { isMac } from \"@zag-js/dom-query\"\n\nexport function isKeyboardClick(e: Pick<MouseEvent, \"detail\" | \"clientX\" | \"clientY\">) {\n  return e.detail === 0 || (e.clientX === 0 && e.clientY === 0)\n}\n\nexport function isPrintableKey(e: Pick<KeyboardEvent, \"key\" | \"ctrlKey\" | \"metaKey\">): boolean {\n  return e.key.length === 1 && !e.ctrlKey && !e.metaKey\n}\n\nexport function isVirtualPointerEvent(e: PointerEvent) {\n  return (\n    (e.width === 0 && e.height === 0) ||\n    (e.width === 1 && e.height === 1 && e.pressure === 0 && e.detail === 0 && e.pointerType === \"mouse\")\n  )\n}\n\nexport function isVirtualClick(e: MouseEvent | PointerEvent): boolean {\n  if ((e as any).mozInputSource === 0 && e.isTrusted) return true\n  return e.detail === 0 && !(e as PointerEvent).pointerType\n}\n\nexport const isLeftClick = (e: Pick<MouseEvent, \"button\">) => e.button === 0\n\nexport const isContextMenuEvent = (e: Pick<MouseEvent, \"button\" | \"ctrlKey\" | \"metaKey\">) => {\n  return e.button === 2 || (isMac() && e.ctrlKey && e.button === 0)\n}\n\nexport const isModifierKey = (e: Pick<KeyboardEvent, \"ctrlKey\" | \"metaKey\" | \"altKey\">) =>\n  e.ctrlKey || e.altKey || e.metaKey\n", "import { isFirefox } from \"@zag-js/dom-query\"\nimport { queueBeforeEvent } from \"./queue-before-event\"\n\nfunction isLinkElement(element: HTMLElement | null | undefined) {\n  return element?.matches(\"a[href]\") ?? false\n}\n\nexport function clickIfLink(element: HTMLElement | null | undefined) {\n  if (!isLinkElement(element)) return\n  const click = () => element!.click()\n  if (isFirefox()) {\n    queueBeforeEvent(element!, \"keyup\", click)\n  } else {\n    queueMicrotask(click)\n  }\n}\n", "export function queueBeforeEvent(element: EventTarget, type: string, cb: () => void) {\n  const createTimer = (callback: () => void) => {\n    const timerId = requestAnimationFrame(callback)\n    return () => cancelAnimationFrame(timerId)\n  }\n\n  const cancelTimer = createTimer(() => {\n    element.removeEventListener(type, callSync, true)\n    cb()\n  })\n  const callSync = () => {\n    cancelTimer()\n    cb()\n  }\n\n  element.addEventListener(type, callSync, { once: true, capture: true })\n  return cancelTimer\n}\n", "export function fireCustomEvent(el: HTMLElement | null, type: string, init?: CustomEventInit) {\n  if (!el) return\n  const win = el.ownerDocument.defaultView || window\n  const event = new win.CustomEvent(type, init)\n  return el.dispatchEvent(event)\n}\n\nexport function fireBlurEvent(el: HTMLElement, init?: FocusEventInit) {\n  const win = el.ownerDocument.defaultView || window\n  const event = new win.FocusEvent(\"blur\", init)\n  const allowed = el.dispatchEvent(event)\n  const bubbleInit = { ...init, bubbles: true }\n  el.dispatchEvent(new win.FocusEvent(\"focusout\", bubbleInit))\n  return allowed\n}\n", "import type { EventKeyOptions } from \"./types\"\n\nconst keyMap: Record<string, string> = {\n  Up: \"ArrowUp\",\n  Down: \"ArrowDown\",\n  Esc: \"Escape\",\n  \" \": \"Space\",\n  \",\": \"Comma\",\n  Left: \"<PERSON>Left\",\n  Right: \"ArrowRight\",\n}\n\nconst rtlKeyMap: Record<string, string> = {\n  ArrowLeft: \"ArrowRight\",\n  ArrowRight: \"ArrowLeft\",\n}\n\n/**\n * Determine the event key based on text direction.\n */\nexport function getEventKey(event: Pick<KeyboardEvent, \"key\">, options: EventKeyOptions = {}) {\n  const { dir = \"ltr\", orientation = \"horizontal\" } = options\n\n  let { key } = event\n  key = keyMap[key] ?? key // normalize key\n\n  const isRtl = dir === \"rtl\" && orientation === \"horizontal\"\n\n  if (isRtl && key in rtlKeyMap) {\n    key = rtlKeyMap[key]\n  }\n\n  return key\n}\n", "type PointType = \"page\" | \"client\"\n\nfunction pointFromTouch(e: TouchEvent, type: PointType = \"client\") {\n  const point = e.touches[0] || e.changedTouches[0]\n  return { x: point[`${type}X`], y: point[`${type}Y`] }\n}\n\nfunction pointFromMouse(point: MouseEvent | PointerEvent, type: PointType = \"client\") {\n  return { x: point[`${type}X`], y: point[`${type}Y`] }\n}\n\ntype AnyPointerEvent = MouseEvent | TouchEvent | PointerEvent\n\nconst isTouchEvent = (event: AnyPointerEvent): event is TouchEvent => \"touches\" in event && event.touches.length > 0\n\nexport function getEventPoint(event: any, type: PointType = \"client\") {\n  return isTouchEvent(event) ? pointFromTouch(event, type) : pointFromMouse(event, type)\n}\n", "const PAGE_KEYS = new Set([\"PageUp\", \"PageDown\"])\nconst ARROW_KEYS = new Set([\"ArrowUp\", \"ArrowDown\", \"ArrowLeft\", \"ArrowRight\"])\n\n/**\n * Determine the step factor for keyboard events\n */\nexport function getEventStep(event: Pick<KeyboardEvent, \"ctrlKey\" | \"metaKey\" | \"key\" | \"shiftKey\">) {\n  if (event.ctrlKey || event.metaKey) {\n    return 0.1\n  } else {\n    const isPageKey = PAGE_KEYS.has(event.key)\n    const isSkipKey = isPageKey || (event.shiftKey && ARROW_KEYS.has(event.key))\n    return isSkipKey ? 10 : 1\n  }\n}\n", "import type { JSX } from \"@zag-js/types\"\n\ntype NativeEvent<E> =\n  JSX.ChangeEvent<any> extends E ? InputEvent : E extends JSX.SyntheticEvent<any, infer T> ? T : never\n\nexport function getNativeEvent<E>(event: E): NativeEvent<E> {\n  return (event as any).nativeEvent ?? event\n}\n", "function clamp(value: number) {\n  return Math.max(0, Math.min(1, value))\n}\n\nexport type Point = {\n  x: number\n  y: number\n}\n\ntype PercentValueOptions = {\n  inverted?: boolean | { x?: boolean; y?: boolean }\n  dir?: \"ltr\" | \"rtl\"\n  orientation?: \"vertical\" | \"horizontal\"\n}\n\nexport function getRelativePoint(point: Point, element: HTMLElement) {\n  const { left, top, width, height } = element.getBoundingClientRect()\n\n  const offset = { x: point.x - left, y: point.y - top }\n  const percent = { x: clamp(offset.x / width), y: clamp(offset.y / height) }\n\n  function getPercentValue(options: PercentValueOptions = {}) {\n    const { dir = \"ltr\", orientation = \"horizontal\", inverted } = options\n\n    const invertX = typeof inverted === \"object\" ? inverted.x : inverted\n    const invertY = typeof inverted === \"object\" ? inverted.y : inverted\n\n    if (orientation === \"horizontal\") {\n      return dir === \"rtl\" || invertX ? 1 - percent.x : percent.x\n    }\n\n    return invertY ? 1 - percent.y : percent.y\n  }\n\n  return { offset, percent, getPercentValue }\n}\n", "import { addDomEvent } from \"./add-dom-event\"\n\nexport function requestPointerLock(doc: Document, fn?: (locked: boolean) => void) {\n  const body = doc.body\n\n  const supported = \"pointerLockElement\" in doc || \"mozPointerLockElement\" in doc\n  const isLocked = () => !!doc.pointerLockElement\n\n  function onPointerChange() {\n    fn?.(isLocked())\n  }\n\n  function onPointerError(event: Event) {\n    if (isLocked()) fn?.(false)\n    console.error(\"PointerLock error occured:\", event)\n    doc.exitPointerLock()\n  }\n\n  if (!supported) return\n\n  try {\n    body.requestPointerLock()\n  } catch {}\n\n  // prettier-ignore\n  const cleanup = [\n    addDomEvent(doc, \"pointerlockchange\", onPointerChange, false),\n    addDomEvent(doc, \"pointerlockerror\", onPointerError, false)\n  ]\n\n  return () => {\n    cleanup.forEach((cleanup) => cleanup())\n    doc.exitPointerLock()\n  }\n}\n", "import { getWindow, isMac } from \"@zag-js/dom-query\"\nimport { addDomEvent } from \"./add-dom-event\"\nimport { pipe } from \"./pipe\"\n\nexport interface TrackFocusOptions {\n  /**\n   * Callback to be called when the element receives focus and is focus-visible.\n   */\n  onFocus?(e: FocusEvent): void\n  /**\n   * Callback to be called when the element loses focus.\n   */\n  onBlur?(e: FocusEvent): void\n}\n\nconst isValidKey = (e: KeyboardEvent) => {\n  return !(\n    e.metaKey ||\n    (!isMac() && e.altKey) ||\n    e.ctrlKey ||\n    e.key === \"Control\" ||\n    e.key === \"Shift\" ||\n    e.key === \"Meta\"\n  )\n}\n\nexport function trackFocusVisible(node: Element | null, options: TrackFocusOptions) {\n  if (!node) return\n  const { onFocus, onBlur } = options\n\n  const win = getWindow(node)\n\n  let focused = false\n\n  const handleFocus = (e: FocusEvent) => {\n    let isFocusVisible = false\n\n    try {\n      isFocusVisible = node.matches(\":focus-visible\")\n    } catch {\n      isFocusVisible = true\n    }\n\n    if (!isFocusVisible) return\n\n    focused = true\n    onFocus?.(e)\n  }\n\n  const handleBlur = (e: FocusEvent) => {\n    if (!focused) return\n    focused = false\n    onBlur?.(e)\n  }\n\n  const handleKeydown = (e: KeyboardEvent) => {\n    if (!node.matches(\":focus\") || !isValidKey(e)) return\n    focused = true\n    const evt = new win.FocusEvent(\"focus\")\n    onFocus?.(evt)\n  }\n\n  return pipe(\n    addDomEvent(node, \"focusin\", handleFocus),\n    addDomEvent(node, \"focusout\", handleBlur),\n    addDomEvent(node, \"keydown\", handleKeydown, true),\n  )\n}\n", "export const pipe =\n  <T>(...fns: Array<(arg: T) => T>) =>\n  (arg: T) =>\n    fns.reduce((acc, fn) => fn(acc), arg)\n\nexport const noop = () => void 0\n", "import { disableTextSelection } from \"@zag-js/text-selection\"\nimport { addDomEvent } from \"./add-dom-event\"\nimport { getEventPoint } from \"./get-event-point\"\n\ninterface Point {\n  x: number\n  y: number\n}\n\ninterface TimestampedPoint extends Point {\n  /**\n   * The time when the point was recorded.\n   */\n  timestamp: number\n}\n\nexport interface PointerMoveDetails {\n  /**\n   * The current position of the pointer.\n   */\n  point: Point\n  /**\n   * The event that triggered the move.\n   */\n  event: PointerEvent\n  /**\n   * The velocity of the pointer on the x and y axis.\n   */\n  velocity: Point\n}\n\nexport interface PointerMoveHandlers {\n  /**\n   * Called when the pointer is released.\n   */\n  onPointerUp: VoidFunction\n  /**\n   * Called when the pointer moves.\n   */\n  onPointerMove: (details: PointerMoveDetails) => void\n}\n\nexport function trackPointerMove(doc: Document, handlers: PointerMoveHandlers) {\n  const { onPointerMove, onPointerUp } = handlers\n\n  const history: TimestampedPoint[] = []\n\n  const handleMove = (event: PointerEvent) => {\n    const point = getEventPoint(event)\n    history.push({ ...point, timestamp: performance.now() })\n\n    const distance = Math.sqrt(point.x ** 2 + point.y ** 2)\n    const moveBuffer = event.pointerType === \"touch\" ? 10 : 5\n\n    if (distance < moveBuffer) return\n\n    // Because Safari doesn't trigger mouseup events when it's above a `<select>`\n    if (event.pointerType === \"mouse\" && event.button === 0) {\n      onPointerUp()\n      return\n    }\n\n    onPointerMove({ point, event, velocity: getVelocity(history, 0.1) })\n  }\n\n  const cleanups = [\n    addDomEvent(doc, \"pointermove\", handleMove, false),\n    addDomEvent(doc, \"pointerup\", onPointerUp, false),\n    addDomEvent(doc, \"pointercancel\", onPointerUp, false),\n    addDomEvent(doc, \"contextmenu\", onPointerUp, false),\n    disableTextSelection({ doc }),\n  ]\n\n  return () => {\n    cleanups.forEach((cleanup) => cleanup())\n    history.length = 0\n  }\n}\n\nfunction lastDevicePoint(history: TimestampedPoint[]): TimestampedPoint {\n  return history[history.length - 1]\n}\n\nfunction ms(seconds: number): number {\n  return seconds * 1000\n}\n\nfunction sec(milliseconds: number): number {\n  return milliseconds / 1000\n}\n\nfunction getVelocity(history: TimestampedPoint[], timeDelta: number): Point {\n  if (history.length < 2) return { x: 0, y: 0 }\n\n  let i = history.length - 1\n  let timestampedPoint: TimestampedPoint | null = null\n  const lastPoint = lastDevicePoint(history)\n\n  while (i >= 0) {\n    timestampedPoint = history[i]\n    if (lastPoint.timestamp - timestampedPoint.timestamp > ms(timeDelta)) {\n      break\n    }\n    i--\n  }\n\n  if (!timestampedPoint) return { x: 0, y: 0 }\n\n  const time = sec(lastPoint.timestamp - timestampedPoint.timestamp)\n  if (time === 0) return { x: 0, y: 0 }\n\n  const currentVelocity = {\n    x: (lastPoint.x - timestampedPoint.x) / time,\n    y: (lastPoint.y - timestampedPoint.y) / time,\n  }\n\n  if (currentVelocity.x === Infinity) currentVelocity.x = 0\n  if (currentVelocity.y === Infinity) currentVelocity.y = 0\n\n  return {\n    x: Math.abs(currentVelocity.x),\n    y: Math.abs(currentVelocity.y),\n  }\n}\n", "import { contains, getDocument, getEventTarget, getWindow } from \"@zag-js/dom-query\"\nimport { addDomEvent } from \"./add-dom-event\"\nimport { getEventPoint } from \"./get-event-point\"\nimport { noop, pipe } from \"./pipe\"\n\ninterface Point {\n  x: number\n  y: number\n}\n\ninterface TapDetails {\n  /**\n   * The current position of the pointer.\n   */\n  point: Point\n  /**\n   * The event that triggered the move.\n   */\n  event: PointerEvent\n}\n\nexport interface TrackPressOptions {\n  /**\n   * The element that will be used to track the pointer events.\n   */\n  pointerNode: Element | null\n  /**\n   * The element that will be used to track the keyboard focus events.\n   */\n  keyboardNode?: Element | null\n  /**\n   * A function that determines if the key is valid for the press event.\n   */\n  isValidKey?(event: KeyboardEvent): boolean\n  /**\n   * A function that will be called when the pointer is pressed.\n   */\n  onPress?(details: TapDetails): void\n  /**\n   * A function that will be called when the pointer is pressed down.\n   */\n  onPressStart?(details: TapDetails): void\n  /**\n   * A function that will be called when the pointer is pressed up or cancelled.\n   */\n  onPressEnd?(details: TapDetails): void\n}\n\nexport function trackPress(options: TrackPressOptions) {\n  const {\n    pointerNode,\n    keyboardNode = pointerNode,\n    onPress,\n    onPressStart,\n    onPressEnd,\n    isValidKey = (e) => e.key === \"Enter\",\n  } = options\n\n  if (!pointerNode) return noop\n\n  const win = getWindow(pointerNode)\n  const doc = getDocument(pointerNode)\n\n  let removeStartListeners: VoidFunction = noop\n  let removeEndListeners: VoidFunction = noop\n  let removeAccessibleListeners: VoidFunction = noop\n\n  const getInfo = (event: PointerEvent): TapDetails => ({\n    point: getEventPoint(event),\n    event,\n  })\n\n  function startPress(event: PointerEvent) {\n    onPressStart?.(getInfo(event))\n  }\n\n  function cancelPress(event: PointerEvent) {\n    onPressEnd?.(getInfo(event))\n  }\n\n  const startPointerPress = (startEvent: PointerEvent) => {\n    removeEndListeners()\n\n    const endPointerPress = (endEvent: PointerEvent) => {\n      const target = getEventTarget<Element>(endEvent)\n      if (contains(pointerNode, target)) {\n        onPress?.(getInfo(endEvent))\n      } else {\n        onPressEnd?.(getInfo(endEvent))\n      }\n    }\n\n    const removePointerUpListener = addDomEvent(win, \"pointerup\", endPointerPress, { passive: !onPress })\n    const removePointerCancelListener = addDomEvent(win, \"pointercancel\", cancelPress, { passive: !onPressEnd })\n\n    removeEndListeners = pipe(removePointerUpListener, removePointerCancelListener)\n\n    if (doc.activeElement === keyboardNode && startEvent.pointerType === \"mouse\") {\n      startEvent.preventDefault()\n    }\n\n    startPress(startEvent)\n  }\n\n  const removePointerListener = addDomEvent(pointerNode, \"pointerdown\", startPointerPress, { passive: !onPressStart })\n  const removeFocusListener = addDomEvent(keyboardNode, \"focus\", startAccessiblePress)\n\n  removeStartListeners = pipe(removePointerListener, removeFocusListener)\n\n  function startAccessiblePress() {\n    const handleKeydown = (keydownEvent: KeyboardEvent) => {\n      if (!isValidKey(keydownEvent)) return\n\n      const handleKeyup = (keyupEvent: KeyboardEvent) => {\n        if (!isValidKey(keyupEvent)) return\n        const evt = new win.PointerEvent(\"pointerup\")\n        const info = getInfo(evt)\n        onPress?.(info)\n        onPressEnd?.(info)\n      }\n\n      removeEndListeners()\n      removeEndListeners = addDomEvent(keyboardNode, \"keyup\", handleKeyup)\n\n      const evt = new win.PointerEvent(\"pointerdown\")\n      startPress(evt)\n    }\n\n    const handleBlur = () => {\n      const evt = new win.PointerEvent(\"pointercancel\")\n      cancelPress(evt)\n    }\n\n    const removeKeydownListener = addDomEvent(keyboardNode, \"keydown\", handleKeydown)\n    const removeBlurListener = addDomEvent(keyboardNode, \"blur\", handleBlur)\n\n    removeAccessibleListeners = pipe(removeKeydownListener, removeBlurListener)\n  }\n\n  return function () {\n    removeStartListeners()\n    removeEndListeners()\n    removeAccessibleListeners()\n  }\n}\n", "import { addDomEvent } from \"./add-dom-event\"\n\nexport interface ViewportSize {\n  width: number\n  height: number\n}\n\nexport function trackVisualViewport(doc: Document, fn: (data: ViewportSize) => void) {\n  const win = doc?.defaultView || window\n\n  const onResize = () => {\n    fn?.(getViewportSize(win))\n  }\n\n  onResize()\n\n  return addDomEvent(win.visualViewport ?? win, \"resize\", onResize)\n}\n\nfunction getViewportSize(win: Window): ViewportSize {\n  return {\n    width: win.visualViewport?.width || win.innerWidth,\n    height: win.visualViewport?.height || win.innerHeight,\n  }\n}\n", "export function getMinValueAtIndex(index: number, values: number[], minValue: number) {\n  return index === 0 ? minValue : values[index - 1]\n}\n\nexport function getMaxValueAtIndex(index: number, values: number[], maxValue: number) {\n  return index === values.length - 1 ? maxValue : values[index + 1]\n}\n\nexport function isValueAtMax(value: number, maxValue: number) {\n  return value >= maxValue\n}\n\nexport function isValueAtMin(value: number, minValue: number) {\n  return value <= minValue\n}\n\nexport function isValueWithinRange(value: number, minValue: number, maxValue: number) {\n  return value >= minValue && value <= maxValue\n}\n\nexport function getRoundedValue(value: number, minValue: number, step: number) {\n  return Math.round((value - minValue) / step) * step + minValue\n}\n\nexport function clampValue(value: number, minValue: number, maxValue: number) {\n  return Math.min(Math.max(value, minValue), maxValue)\n}\n\nexport function getValuePercent(value: number, minValue: number, maxValue: number) {\n  return (value - minValue) / (maxValue - minValue)\n}\n\nexport function getPercentValue(percent: number, minValue: number, maxValue: number, step: number) {\n  const value = percent * (maxValue - minValue) + minValue\n  const roundedValue = getRoundedValue(value, minValue, step)\n  return clampValue(roundedValue, minValue, maxValue)\n}\n\nexport function roundToStepPrecision(value: number, step: number) {\n  let roundedValue = value\n  let stepString = step.toString()\n  let pointIndex = stepString.indexOf(\".\")\n  let precision = pointIndex >= 0 ? stepString.length - pointIndex : 0\n  if (precision > 0) {\n    let pow = Math.pow(10, precision)\n    roundedValue = Math.round(roundedValue * pow) / pow\n  }\n  return roundedValue\n}\n\nexport function snapValueToStep(value: number, min: number | undefined, max: number | undefined, step: number): number {\n  min = Number(min)\n  max = Number(max)\n  let remainder = (value - (isNaN(min) ? 0 : min)) % step\n  let snappedValue = roundToStepPrecision(\n    Math.abs(remainder) * 2 >= step ? value + Math.sign(remainder) * (step - Math.abs(remainder)) : value - remainder,\n    step,\n  )\n\n  if (!isNaN(min)) {\n    if (snappedValue < min) {\n      snappedValue = min\n    } else if (!isNaN(max) && snappedValue > max) {\n      snappedValue = min + Math.floor(roundToStepPrecision((max - min) / step, step)) * step\n    }\n  } else if (!isNaN(max) && snappedValue > max) {\n    snappedValue = Math.floor(roundToStepPrecision(max / step, step)) * step\n  }\n\n  // correct floating point behavior by rounding to step precision\n  snappedValue = roundToStepPrecision(snappedValue, step)\n\n  return snappedValue\n}\n\nfunction setValueAtIndex<T>(values: T[], index: number, value: T) {\n  if (values[index] === value) return values\n  return [...values.slice(0, index), value, ...values.slice(index + 1)]\n}\n\ntype RangeContext = {\n  min: number\n  max: number\n  step: number\n  values: number[]\n}\n\nexport function getValueSetterAtIndex(index: number, ctx: RangeContext) {\n  const minValueAtIndex = getMinValueAtIndex(index, ctx.values, ctx.min)\n  const maxValueAtIndex = getMaxValueAtIndex(index, ctx.values, ctx.max)\n  let nextValues = ctx.values.slice()\n\n  return function setValue(value: number) {\n    let nextValue = snapValueToStep(value, minValueAtIndex, maxValueAtIndex, ctx.step)\n    nextValues = setValueAtIndex(nextValues, index, value)\n    nextValues[index] = nextValue\n    return nextValues\n  }\n}\n\nexport function getNextStepValue(index: number, ctx: RangeContext) {\n  const nextValue = ctx.values[index] + ctx.step\n  return getValueSetterAtIndex(index, ctx)(nextValue)\n}\n\nexport function getPreviousStepValue(index: number, ctx: RangeContext) {\n  const nextValue = ctx.values[index] - ctx.step\n  return getValueSetterAtIndex(index, ctx)(nextValue)\n}\n\nexport function getClosestValueIndex(values: number[], targetValue: number) {\n  let targetIndex = values.findIndex((value) => targetValue - value < 0)\n\n  // If the index is zero then the closetThumb is the first one\n  if (targetIndex === 0) {\n    return targetIndex\n  }\n\n  // If no index is found they've clicked past all the thumbs\n  if (targetIndex === -1) {\n    return values.length - 1\n  }\n\n  let valueBefore = values[targetIndex - 1]\n  let valueAfter = values[targetIndex]\n\n  // Pick the last left/start thumb, unless they are stacked on top of each other, then pick the right/end one\n  if (Math.abs(valueBefore - targetValue) < Math.abs(valueAfter - targetValue)) {\n    return targetIndex - 1\n  }\n\n  return targetIndex\n}\n\nexport function getValueRanges(values: number[], minValue: number, maxValue: number, gap: number) {\n  return values.map((value, index) => {\n    const min = index === 0 ? minValue : values[index - 1] + gap\n    const max = index === values.length - 1 ? maxValue : values[index + 1] - gap\n    return { min, max, value }\n  })\n}\n\nexport function getValueTransformer(valueA: number[], valueB: number[]) {\n  const input = { min: valueA[0], max: valueA[1] }\n  const output = { min: valueB[0], max: valueB[1] }\n\n  return function getValue(value: number) {\n    if (input.min === input.max || output.min === output.max) return output.min\n    const ratio = (output.max - output.min) / (input.max - input.min)\n    return output.min + ratio * (value - input.min)\n  }\n}\n\nexport function toFixedNumber(value: number, digits = 0, base: number = 10): number {\n  const pow = Math.pow(base, digits)\n  return Math.round(value * pow) / pow\n}\n\nexport function mod(value: number, modulo: number) {\n  return ((value % modulo) + modulo) % modulo\n}\n", "interface DescriptorOptions {\n  type?: \"HTMLInputElement\" | \"HTMLTextAreaElement\" | \"HTMLSelectElement\"\n  property?: \"value\" | \"checked\"\n}\n\nconst getWindow = (el: HTMLElement) => el.ownerDocument.defaultView || window\n\nfunction getDescriptor(el: HTMLElement, options: DescriptorOptions) {\n  const { type = \"HTMLInputElement\", property = \"value\" } = options\n  const proto = getWindow(el)[type].prototype\n  return Object.getOwnPropertyDescriptor(proto, property) ?? {}\n}\n\nexport function setElementValue(el: HTMLElement, value: string, option: DescriptorOptions = {}) {\n  const descriptor = getDescriptor(el, option)\n  descriptor.set?.call(el, value)\n  el.setAttribute(\"value\", value)\n}\n\nexport function setElementChecked(el: HTMLElement, checked: boolean) {\n  const descriptor = getDescriptor(el, { type: \"HTMLInputElement\", property: \"checked\" })\n  descriptor.set?.call(el, checked)\n  // react applies the `checked` automatically when we call the descriptor\n  // but for consistency with vanilla JS, we need to do it manually as well\n  if (checked) el.setAttribute(\"checked\", \"\")\n  else el.removeAttribute(\"checked\")\n}\n\nexport type InputEventOptions = {\n  value: string | number\n  bubbles?: boolean\n}\n\nexport function dispatchInputValueEvent(el: HTMLElement | null, options: InputEventOptions) {\n  const { value, bubbles = true } = options\n\n  if (!el) return\n\n  const win = getWindow(el)\n  if (!(el instanceof win.HTMLInputElement)) return\n\n  setElementValue(el, `${value}`)\n  el.dispatchEvent(new win.Event(\"input\", { bubbles }))\n}\n\nexport type CheckedEventOptions = {\n  checked: boolean\n  bubbles?: boolean\n}\n\nexport function dispatchInputCheckedEvent(el: HTMLElement | null, options: CheckedEventOptions) {\n  const { checked, bubbles = true } = options\n\n  if (!el) return\n\n  const win = getWindow(el)\n  if (!(el instanceof win.HTMLInputElement)) return\n\n  setElementChecked(el, checked)\n  el.dispatchEvent(new win.Event(\"click\", { bubbles }))\n}\n", "export function getClosestForm(el: HTMLElement) {\n  if (isFormElement(el)) return el.form\n  else return el.closest(\"form\")\n}\n\nfunction isFormElement(el: HTMLElement): el is HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement {\n  return el.matches(\"textarea, input, select, button\")\n}\n\nfunction trackFormReset(el: HTMLElement | null | undefined, callback: () => void) {\n  if (!el) return\n  const form = getClosestForm(el)\n  form?.addEventListener(\"reset\", callback, { passive: true })\n  return () => {\n    form?.removeEventListener(\"reset\", callback)\n  }\n}\n\nfunction trackFieldsetDisabled(el: HTMLElement | null | undefined, callback: (disabled: boolean) => void) {\n  const fieldset = el?.closest(\"fieldset\")\n  if (!fieldset) return\n  callback(fieldset.disabled)\n  const win = fieldset.ownerDocument.defaultView || window\n  const obs = new win.MutationObserver(() => callback(fieldset.disabled))\n  obs.observe(fieldset, {\n    attributes: true,\n    attributeFilter: [\"disabled\"],\n  })\n  return () => obs.disconnect()\n}\n\nexport function isNativeDisabled(el: HTMLElement) {\n  return el.matches(\":disabled\")\n}\n\nexport type FormControlOptions = {\n  onFieldsetDisabledChange: (disabled: boolean) => void\n  onFormReset: () => void\n}\n\nexport function trackFormControl(el: HTMLElement | null, options: FormControlOptions) {\n  if (!el) return\n\n  const { onFieldsetDisabledChange, onFormReset } = options\n\n  const cleanups = [trackFormReset(el, onFormReset), trackFieldsetDisabled(el, onFieldsetDisabledChange)]\n\n  return () => {\n    cleanups.forEach((cleanup) => cleanup?.())\n  }\n}\n", "export interface ElementSize {\n  width: number\n  height: number\n}\n\nexport type ElementSizeCallback = (size: ElementSize | undefined) => void\n\nexport function trackElementSize(element: HTMLElement | null, callback: ElementSizeCallback) {\n  if (!element) {\n    callback(undefined)\n    return\n  }\n\n  callback({ width: element.offsetWidth, height: element.offsetHeight })\n\n  const win = element.ownerDocument.defaultView ?? window\n\n  const observer = new win.ResizeObserver((entries) => {\n    if (!Array.isArray(entries) || !entries.length) return\n\n    const [entry] = entries\n    let width: number\n    let height: number\n\n    if (\"borderBoxSize\" in entry) {\n      const borderSizeEntry = entry[\"borderBoxSize\"]\n      const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry\n      width = borderSize[\"inlineSize\"]\n      height = borderSize[\"blockSize\"]\n    } else {\n      width = element.offsetWidth\n      height = element.offsetHeight\n    }\n\n    callback({ width, height })\n  })\n\n  observer.observe(element, { box: \"border-box\" })\n\n  return () => observer.unobserve(element)\n}\n", "import { trackElementSize, type ElementSize } from \"./track-size\"\n\nexport interface TrackElementsSizeOptions<T extends HTMLElement | null> {\n  getNodes: () => T[]\n  observeMutation?: boolean\n  callback: (size: ElementSize | undefined, index: number) => void\n}\n\nexport function trackElementsSize<T extends HTMLElement | null>(options: TrackElementsSizeOptions<T>) {\n  const { getNodes, observeMutation = true, callback } = options\n\n  const cleanups: Array<VoidFunction | undefined> = []\n\n  let firstNode: T | null = null\n\n  function trigger() {\n    const elements = getNodes()\n    firstNode = elements[0]\n    const fns = elements.map((element, index) =>\n      trackElementSize(element, (size) => {\n        callback(size, index)\n      }),\n    )\n    cleanups.push(...fns)\n  }\n\n  trigger()\n\n  if (observeMutation) {\n    const fn = trackMutation(firstNode, trigger)\n    cleanups.push(fn)\n  }\n\n  return () => {\n    cleanups.forEach((cleanup) => {\n      cleanup?.()\n    })\n  }\n}\n\nfunction trackMutation(el: HTMLElement | null, cb: () => void) {\n  if (!el || !el.parentElement) return\n  const win = el.ownerDocument?.defaultView ?? window\n  const observer = new win.MutationObserver(() => {\n    cb()\n  })\n  observer.observe(el.parentElement, { childList: true })\n  return () => {\n    observer.disconnect()\n  }\n}\n", "export function toArray<T>(v: T | T[] | undefined | null): T[] {\n  if (!v) return []\n  return Array.isArray(v) ? v : [v]\n}\n\nexport const fromLength = (length: number) => Array.from(Array(length).keys())\n\nexport const first = <T>(v: T[]): T | undefined => v[0]\n\nexport const last = <T>(v: T[]): T | undefined => v[v.length - 1]\n\nexport const isEmpty = <T>(v: T[]): boolean => v.length === 0\n\nexport const has = <T>(v: T[], t: any): boolean => v.indexOf(t) !== -1\n\nexport const add = <T>(v: T[], ...items: T[]): T[] => v.concat(items)\n\nexport const remove = <T>(v: T[], item: T): T[] => v.filter((t) => t !== item)\n\nexport const removeAt = <T>(v: T[], i: number): T[] => v.filter((_, idx) => idx !== i)\n\nexport const uniq = <T>(v: T[]): T[] => Array.from(new Set(v))\n\nexport const addOrRemove = <T>(v: T[], item: T): T[] => {\n  if (has(v, item)) return remove(v, item)\n  return add(v, item)\n}\n\nexport function clear<T>(v: T[]): T[] {\n  while (v.length > 0) v.pop()\n  return v\n}\n\nexport type IndexOptions = {\n  step?: number\n  loop?: boolean\n}\n\nexport function nextIndex<T>(v: T[], idx: number, opts: IndexOptions = {}): number {\n  const { step = 1, loop = true } = opts\n  const next = idx + step\n  const len = v.length\n  const last = len - 1\n  if (idx === -1) return step > 0 ? 0 : last\n  if (next < 0) return loop ? last : 0\n  if (next >= len) return loop ? 0 : idx > len ? len : idx\n  return next\n}\n\nexport function next<T>(v: T[], idx: number, opts: IndexOptions = {}): T | undefined {\n  return v[nextIndex(v, idx, opts)]\n}\n\nexport function prevIndex<T>(v: T[], idx: number, opts: IndexOptions = {}): number {\n  const { step = 1, loop = true } = opts\n  return nextIndex(v, idx, { step: -step, loop })\n}\n\nexport function prev<T>(v: T[], index: number, opts: IndexOptions = {}): T | undefined {\n  return v[prevIndex(v, index, opts)]\n}\n\nexport const chunk = <T>(v: T[], size: number): T[][] => {\n  const res: T[][] = []\n  return v.reduce((rows, value, index) => {\n    if (index % size === 0) rows.push([value])\n    else last(rows)?.push(value)\n    return rows\n  }, res)\n}\n", "const isArrayLike = (value: any) => value?.constructor.name === \"Array\"\n\nconst isArrayEqual = (a: any[], b: any[]): boolean => {\n  if (a.length !== b.length) return false\n  for (let i = 0; i < a.length; i++) {\n    if (!isEqual(a[i], b[i])) return false\n  }\n  return true\n}\n\nexport const isEqual = (a: any, b: any): boolean => {\n  if (Object.is(a, b)) return true\n\n  if ((a == null && b != null) || (a != null && b == null)) return false\n\n  if (typeof a?.isEqual === \"function\" && typeof b?.isEqual === \"function\") {\n    return a.isEqual(b)\n  }\n\n  if (typeof a === \"function\" && typeof b === \"function\") {\n    return a.toString() === b.toString()\n  }\n\n  if (isArrayLike(a) && isArrayLike(b)) {\n    return isArrayEqual(Array.from(a), Array.from(b))\n  }\n\n  if (!(typeof a === \"object\") || !(typeof b === \"object\")) return false\n\n  const keys = Object.keys(b ?? Object.create(null))\n  const length = keys.length\n\n  for (let i = 0; i < length; i++) {\n    const hasKey = Reflect.has(a, keys[i])\n    if (!hasKey) return false\n  }\n\n  for (let i = 0; i < length; i++) {\n    const key = keys[i]\n    if (!isEqual(a[key], b[key])) return false\n  }\n\n  return true\n}\n", "export type MaybeFunction<T> = T | (() => T)\n\nexport type Nullable<T> = T | null | undefined\n\nexport const runIfFn = <T>(\n  v: T | undefined,\n  ...a: T extends (...a: any[]) => void ? Parameters<T> : never\n): T extends (...a: any[]) => void ? NonNullable<ReturnType<T>> : NonNullable<T> => {\n  const res = typeof v === \"function\" ? v(...a) : v\n  return res ?? undefined\n}\n\nexport const cast = <T>(v: unknown): T => v as T\n\nexport const noop = () => {}\n\nexport const callAll =\n  <T extends (...a: any[]) => void>(...fns: (T | undefined)[]) =>\n  (...a: Parameters<T>) => {\n    fns.forEach(function (fn) {\n      fn?.(...a)\n    })\n  }\n\nexport const uuid = /*#__PURE__*/ (() => {\n  let id = 0\n  return () => {\n    id++\n    return id.toString(36)\n  }\n})()\n\nexport function match<V extends string | number = string, R = unknown>(\n  key: V,\n  record: Record<V, R | ((...args: any[]) => R)>,\n  ...args: any[]\n): R {\n  if (key in record) {\n    const fn = record[key]\n    return typeof fn === \"function\" ? fn(...args) : fn\n  }\n\n  const error = new Error(`No matching key: ${JSON.stringify(key)} in ${JSON.stringify(Object.keys(record))}`)\n  Error.captureStackTrace?.(error, match)\n\n  throw error\n}\n\nexport const tryCatch = <R>(fn: () => R, fallback: () => R) => {\n  try {\n    return fn()\n  } catch (error) {\n    if (error instanceof Error) {\n      Error.captureStackTrace?.(error, tryCatch)\n    }\n    return fallback?.()\n  }\n}\n", "export const isDev = () => process.env.NODE_ENV !== \"production\"\nexport const isArray = (v: any): v is any[] => Array.isArray(v)\nexport const isBoolean = (v: any): v is boolean => v === true || v === false\nexport const isObject = (v: any): v is Record<string, any> => !(v == null || typeof v !== \"object\" || isArray(v))\nexport const isNumber = (v: any): v is number => typeof v === \"number\" && !Number.isNaN(v)\nexport const isString = (v: any): v is string => typeof v === \"string\"\nexport const isFunction = (v: any): v is Function => typeof v === \"function\"\nexport const isNull = (v: any): v is null | undefined => v == null\n\nexport const hasProp = <T extends string>(obj: any, prop: T): obj is Record<T, any> =>\n  Object.prototype.hasOwnProperty.call(obj, prop)\n", "export function compact<T extends Record<string, unknown> | undefined>(obj: T): T {\n  if (!isPlainObject(obj) || obj === undefined) {\n    return obj\n  }\n\n  const keys = Reflect.ownKeys(obj).filter((key) => typeof key === \"string\")\n  const filtered: Partial<T> = {}\n  for (const key of keys) {\n    const value = (obj as any)[key]\n    if (value !== undefined) {\n      filtered[key as keyof T] = compact(value)\n    }\n  }\n  return filtered as T\n}\n\nexport function json(value: any) {\n  return JSON.parse(JSON.stringify(value))\n}\n\nconst isPlainObject = (value: any) => {\n  return value && typeof value === \"object\" && value.constructor === Object\n}\n\nexport function pick<T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {\n  const filtered: Partial<T> = {}\n\n  for (const key of keys) {\n    const value = obj[key]\n    if (value !== undefined) {\n      filtered[key] = value\n    }\n  }\n\n  return filtered as any\n}\n", "type Dict = Record<string, any>\n\nexport function splitProps<T extends Dict>(props: T, keys: (keyof T)[]) {\n  const rest: Dict = {}\n  const result: Dict = {}\n\n  const keySet = new Set(keys)\n\n  for (const key in props) {\n    if (keySet.has(key)) {\n      result[key] = props[key]\n    } else {\n      rest[key] = props[key]\n    }\n  }\n\n  return [result, rest]\n}\n\nexport const createSplitProps = <T extends Dict>(keys: (keyof T)[]) => {\n  return function split<Props extends T>(props: Props) {\n    return splitProps(props, keys) as [T, Omit<Props, keyof T>]\n  }\n}\n", "export function warn(m: string): void\nexport function warn(c: boolean, m: string): void\nexport function warn(...a: any[]): void {\n  const m = a.length === 1 ? a[0] : a[1]\n  const c = a.length === 2 ? a[0] : true\n  if (c && process.env.NODE_ENV !== \"production\") {\n    console.warn(m)\n  }\n}\n\nexport function invariant(m: string): void\nexport function invariant(c: boolean, m: string): void\nexport function invariant(...a: any[]): void {\n  const m = a.length === 1 ? a[0] : a[1]\n  const c = a.length === 2 ? a[0] : true\n  if (c && process.env.NODE_ENV !== \"production\") {\n    throw new Error(m)\n  }\n}\n", "import { createAnatomy } from \"@zag-js/anatomy\"\n\nexport const anatomy = createAnatomy(\"slider\").parts(\n  \"root\",\n  \"label\",\n  \"thumb\",\n  \"valueText\",\n  \"track\",\n  \"range\",\n  \"control\",\n  \"markerGroup\",\n  \"marker\",\n)\n\nexport const parts = anatomy.build()\n", "import {\n  getEvent<PERSON>ey,\n  getEventPoint,\n  getEventStep,\n  isLeftClick,\n  isModifier<PERSON>ey,\n  type EventKeyMap,\n} from \"@zag-js/dom-event\"\nimport { ariaAttr, dataAttr } from \"@zag-js/dom-query\"\nimport { getPercentValue, getValuePercent } from \"@zag-js/numeric-range\"\nimport type { NormalizeProps, PropTypes } from \"@zag-js/types\"\nimport { parts } from \"./slider.anatomy\"\nimport { dom } from \"./slider.dom\"\nimport type { MachineApi, Send, State } from \"./slider.types\"\nimport { getRangeAtIndex } from \"./slider.utils\"\n\nexport function connect<T extends PropTypes>(state: State, send: Send, normalize: NormalizeProps<T>): MachineApi<T> {\n  const ariaLabel = state.context[\"aria-label\"]\n  const ariaLabelledBy = state.context[\"aria-labelledby\"]\n  const sliderValue = state.context.value\n\n  const focused = state.matches(\"focus\")\n  const dragging = state.matches(\"dragging\")\n\n  const disabled = state.context.isDisabled\n  const invalid = state.context.invalid\n  const interactive = state.context.isInteractive\n\n  const isHorizontal = state.context.orientation === \"horizontal\"\n  const isVertical = state.context.orientation === \"vertical\"\n\n  function getValuePercentFn(value: number) {\n    return getValuePercent(value, state.context.min, state.context.max)\n  }\n\n  function getPercentValueFn(percent: number) {\n    return getPercentValue(percent, state.context.min, state.context.max, state.context.step)\n  }\n\n  return {\n    value: state.context.value,\n    dragging,\n    focused,\n    setValue(value) {\n      send({ type: \"SET_VALUE\", value: value })\n    },\n    getThumbValue(index) {\n      return sliderValue[index]\n    },\n    setThumbValue(index, value) {\n      send({ type: \"SET_VALUE\", index, value })\n    },\n    getValuePercent: getValuePercentFn,\n    getPercentValue: getPercentValueFn,\n    getThumbPercent(index) {\n      return getValuePercentFn(sliderValue[index])\n    },\n    setThumbPercent(index, percent) {\n      const value = getPercentValueFn(percent)\n      send({ type: \"SET_VALUE\", index, value })\n    },\n    getThumbMin(index) {\n      return getRangeAtIndex(state.context, index).min\n    },\n    getThumbMax(index) {\n      return getRangeAtIndex(state.context, index).max\n    },\n    increment(index) {\n      send({ type: \"INCREMENT\", index })\n    },\n    decrement(index) {\n      send({ type: \"DECREMENT\", index })\n    },\n    focus() {\n      if (!interactive) return\n      send({ type: \"FOCUS\", index: 0 })\n    },\n\n    getLabelProps() {\n      return normalize.label({\n        ...parts.label.attrs,\n        dir: state.context.dir,\n        \"data-disabled\": dataAttr(disabled),\n        \"data-orientation\": state.context.orientation,\n        \"data-invalid\": dataAttr(invalid),\n        \"data-dragging\": dataAttr(dragging),\n        \"data-focus\": dataAttr(focused),\n        id: dom.getLabelId(state.context),\n        htmlFor: dom.getHiddenInputId(state.context, 0),\n        onClick(event) {\n          if (!interactive) return\n          event.preventDefault()\n          dom.getFirstEl(state.context)?.focus()\n        },\n        style: {\n          userSelect: \"none\",\n          WebkitUserSelect: \"none\",\n        },\n      })\n    },\n\n    getRootProps() {\n      return normalize.element({\n        ...parts.root.attrs,\n        \"data-disabled\": dataAttr(disabled),\n        \"data-orientation\": state.context.orientation,\n        \"data-dragging\": dataAttr(dragging),\n        \"data-invalid\": dataAttr(invalid),\n        \"data-focus\": dataAttr(focused),\n        id: dom.getRootId(state.context),\n        dir: state.context.dir,\n        style: dom.getRootStyle(state.context),\n      })\n    },\n\n    getValueTextProps() {\n      return normalize.element({\n        ...parts.valueText.attrs,\n        dir: state.context.dir,\n        \"data-disabled\": dataAttr(disabled),\n        \"data-orientation\": state.context.orientation,\n        \"data-invalid\": dataAttr(invalid),\n        \"data-focus\": dataAttr(focused),\n        id: dom.getValueTextId(state.context),\n      })\n    },\n\n    getTrackProps() {\n      return normalize.element({\n        ...parts.track.attrs,\n        dir: state.context.dir,\n        id: dom.getTrackId(state.context),\n        \"data-disabled\": dataAttr(disabled),\n        \"data-invalid\": dataAttr(invalid),\n        \"data-dragging\": dataAttr(dragging),\n        \"data-orientation\": state.context.orientation,\n        \"data-focus\": dataAttr(focused),\n        style: { position: \"relative\" },\n      })\n    },\n\n    getThumbProps(props) {\n      const { index = 0, name } = props\n\n      const value = sliderValue[index]\n      const range = getRangeAtIndex(state.context, index)\n      const valueText = state.context.getAriaValueText?.({ value, index })\n      const _ariaLabel = Array.isArray(ariaLabel) ? ariaLabel[index] : ariaLabel\n      const _ariaLabelledBy = Array.isArray(ariaLabelledBy) ? ariaLabelledBy[index] : ariaLabelledBy\n\n      return normalize.element({\n        ...parts.thumb.attrs,\n        dir: state.context.dir,\n        \"data-index\": index,\n        \"data-name\": name,\n        id: dom.getThumbId(state.context, index),\n        \"data-disabled\": dataAttr(disabled),\n        \"data-orientation\": state.context.orientation,\n        \"data-focus\": dataAttr(focused && state.context.focusedIndex === index),\n        \"data-dragging\": dataAttr(dragging && state.context.focusedIndex === index),\n        draggable: false,\n        \"aria-disabled\": ariaAttr(disabled),\n        \"aria-label\": _ariaLabel,\n        \"aria-labelledby\": _ariaLabelledBy ?? dom.getLabelId(state.context),\n        \"aria-orientation\": state.context.orientation,\n        \"aria-valuemax\": range.max,\n        \"aria-valuemin\": range.min,\n        \"aria-valuenow\": sliderValue[index],\n        \"aria-valuetext\": valueText,\n        role: \"slider\",\n        tabIndex: disabled ? undefined : 0,\n        style: dom.getThumbStyle(state.context, index),\n        onPointerDown(event) {\n          if (!interactive) return\n          send({ type: \"THUMB_POINTER_DOWN\", index })\n          event.stopPropagation()\n        },\n        onBlur() {\n          if (!interactive) return\n          send(\"BLUR\")\n        },\n        onFocus() {\n          if (!interactive) return\n          send({ type: \"FOCUS\", index })\n        },\n        onKeyDown(event) {\n          if (event.defaultPrevented) return\n          if (!interactive) return\n\n          const step = getEventStep(event) * state.context.step\n\n          const keyMap: EventKeyMap = {\n            ArrowUp() {\n              if (isHorizontal) return\n              send({ type: \"ARROW_INC\", step, src: \"ArrowUp\" })\n            },\n            ArrowDown() {\n              if (isHorizontal) return\n              send({ type: \"ARROW_DEC\", step, src: \"ArrowDown\" })\n            },\n            ArrowLeft() {\n              if (isVertical) return\n              send({ type: \"ARROW_DEC\", step, src: \"ArrowLeft\" })\n            },\n            ArrowRight() {\n              if (isVertical) return\n              send({ type: \"ARROW_INC\", step, src: \"ArrowRight\" })\n            },\n            PageUp() {\n              send({ type: \"ARROW_INC\", step, src: \"PageUp\" })\n            },\n            PageDown() {\n              send({ type: \"ARROW_DEC\", step, src: \"PageDown\" })\n            },\n            Home() {\n              send(\"HOME\")\n            },\n            End() {\n              send(\"END\")\n            },\n          }\n\n          const key = getEventKey(event, state.context)\n          const exec = keyMap[key]\n\n          if (exec) {\n            exec(event)\n            event.preventDefault()\n            event.stopPropagation()\n          }\n        },\n      })\n    },\n\n    getHiddenInputProps(props) {\n      const { index = 0, name } = props\n      return normalize.input({\n        name:\n          name ?? (state.context.name ? state.context.name + (state.context.value.length > 1 ? \"[]\" : \"\") : undefined),\n        form: state.context.form,\n        type: \"text\",\n        hidden: true,\n        defaultValue: state.context.value[index],\n        id: dom.getHiddenInputId(state.context, index),\n      })\n    },\n\n    getRangeProps() {\n      return normalize.element({\n        id: dom.getRangeId(state.context),\n        ...parts.range.attrs,\n        dir: state.context.dir,\n        \"data-dragging\": dataAttr(dragging),\n        \"data-focus\": dataAttr(focused),\n        \"data-invalid\": dataAttr(invalid),\n        \"data-disabled\": dataAttr(disabled),\n        \"data-orientation\": state.context.orientation,\n        style: dom.getRangeStyle(state.context),\n      })\n    },\n\n    getControlProps() {\n      return normalize.element({\n        ...parts.control.attrs,\n        dir: state.context.dir,\n        id: dom.getControlId(state.context),\n        \"data-dragging\": dataAttr(dragging),\n        \"data-disabled\": dataAttr(disabled),\n        \"data-orientation\": state.context.orientation,\n        \"data-invalid\": dataAttr(invalid),\n        \"data-focus\": dataAttr(focused),\n        style: dom.getControlStyle(),\n        onPointerDown(event) {\n          if (!interactive) return\n          if (!isLeftClick(event)) return\n          if (isModifierKey(event)) return\n\n          const point = getEventPoint(event)\n          send({ type: \"POINTER_DOWN\", point })\n\n          event.preventDefault()\n          event.stopPropagation()\n        },\n      })\n    },\n\n    getMarkerGroupProps() {\n      return normalize.element({\n        ...parts.markerGroup.attrs,\n        role: \"presentation\",\n        dir: state.context.dir,\n        \"aria-hidden\": true,\n        \"data-orientation\": state.context.orientation,\n        style: dom.getMarkerGroupStyle(),\n      })\n    },\n\n    getMarkerProps(props) {\n      const style = dom.getMarkerStyle(state.context, props.value)\n      let markerState: \"over-value\" | \"under-value\" | \"at-value\"\n\n      const first = state.context.value[0]\n      const last = state.context.value[state.context.value.length - 1]\n\n      if (props.value < first) {\n        markerState = \"under-value\"\n      } else if (props.value > last) {\n        markerState = \"over-value\"\n      } else {\n        markerState = \"at-value\"\n      }\n\n      return normalize.element({\n        ...parts.marker.attrs,\n        id: dom.getMarkerId(state.context, props.value),\n        role: \"presentation\",\n        dir: state.context.dir,\n        \"data-orientation\": state.context.orientation,\n        \"data-value\": props.value,\n        \"data-disabled\": dataAttr(disabled),\n        \"data-state\": markerState,\n        style,\n      })\n    },\n  }\n}\n", "import { getRelativePoint, type Point } from \"@zag-js/dom-event\"\nimport { createScope, queryAll } from \"@zag-js/dom-query\"\nimport { dispatchInputValueEvent } from \"@zag-js/form-utils\"\nimport { getPercentValue } from \"@zag-js/numeric-range\"\nimport { styleGetterFns } from \"./slider.style\"\nimport type { MachineContext as Ctx } from \"./slider.types\"\n\nexport const dom = createScope({\n  ...styleGetterFns,\n  getRootId: (ctx: Ctx) => ctx.ids?.root ?? `slider:${ctx.id}`,\n  getThumbId: (ctx: Ctx, index: number) => ctx.ids?.thumb?.(index) ?? `slider:${ctx.id}:thumb:${index}`,\n  getHiddenInputId: (ctx: Ctx, index: number) => ctx.ids?.hiddenInput?.(index) ?? `slider:${ctx.id}:input:${index}`,\n  getControlId: (ctx: Ctx) => ctx.ids?.control ?? `slider:${ctx.id}:control`,\n  getTrackId: (ctx: Ctx) => ctx.ids?.track ?? `slider:${ctx.id}:track`,\n  getRangeId: (ctx: Ctx) => ctx.ids?.range ?? `slider:${ctx.id}:range`,\n  getLabelId: (ctx: Ctx) => ctx.ids?.label ?? `slider:${ctx.id}:label`,\n  getValueTextId: (ctx: Ctx) => ctx.ids?.valueText ?? `slider:${ctx.id}:value-text`,\n  getMarkerId: (ctx: Ctx, value: number) => ctx.ids?.marker?.(value) ?? `slider:${ctx.id}:marker:${value}`,\n\n  getRootEl: (ctx: Ctx) => dom.getById(ctx, dom.getRootId(ctx)),\n  getThumbEl: (ctx: Ctx, index: number) => dom.getById(ctx, dom.getThumbId(ctx, index)),\n  getHiddenInputEl: (ctx: Ctx, index: number) => dom.getById<HTMLInputElement>(ctx, dom.getHiddenInputId(ctx, index)),\n  getControlEl: (ctx: Ctx) => dom.getById(ctx, dom.getControlId(ctx)),\n  getElements: (ctx: Ctx) => queryAll(dom.getControlEl(ctx), \"[role=slider]\"),\n  getFirstEl: (ctx: Ctx) => dom.getElements(ctx)[0],\n  getRangeEl: (ctx: Ctx) => dom.getById(ctx, dom.getRangeId(ctx)),\n\n  getValueFromPoint(ctx: Ctx, point: Point) {\n    const controlEl = dom.getControlEl(ctx)\n    if (!controlEl) return\n    const relativePoint = getRelativePoint(point, controlEl)\n    const percent = relativePoint.getPercentValue({\n      orientation: ctx.orientation,\n      dir: ctx.dir,\n      inverted: { y: true },\n    })\n    return getPercentValue(percent, ctx.min, ctx.max, ctx.step)\n  },\n  dispatchChangeEvent(ctx: Ctx) {\n    const valueArray = Array.from(ctx.value)\n    valueArray.forEach((value, index) => {\n      const inputEl = dom.getHiddenInputEl(ctx, index)\n      if (!inputEl) return\n      dispatchInputValueEvent(inputEl, { value })\n    })\n  },\n})\n", "import { getValuePercent, getValueTransformer } from \"@zag-js/numeric-range\"\nimport type { Style } from \"@zag-js/types\"\nimport type { MachineContext as Ctx, SharedContext } from \"./slider.types\"\n\n/* -----------------------------------------------------------------------------\n * Range style calculations\n * -----------------------------------------------------------------------------*/\n\nfunction getBounds<T>(value: T[]): [T, T] {\n  const firstValue = value[0]\n  const lastThumb = value[value.length - 1]\n  return [firstValue, lastThumb]\n}\n\nexport function getRangeOffsets(ctx: Ctx) {\n  const [firstPercent, lastPercent] = getBounds(ctx.valuePercent)\n\n  if (ctx.valuePercent.length === 1) {\n    if (ctx.origin === \"center\") {\n      const isNegative = ctx.valuePercent[0] < 50\n      const start = isNegative ? `${ctx.valuePercent[0]}%` : \"50%\"\n      const end = isNegative ? \"50%\" : `${100 - ctx.valuePercent[0]}%`\n\n      return { start, end }\n    }\n\n    return { start: \"0%\", end: `${100 - lastPercent}%` }\n  }\n\n  return { start: `${firstPercent}%`, end: `${100 - lastPercent}%` }\n}\n\nfunction getRangeStyle(ctx: Pick<SharedContext, \"isVertical\" | \"isRtl\">): Style {\n  if (ctx.isVertical) {\n    return {\n      position: \"absolute\",\n      bottom: \"var(--slider-range-start)\",\n      top: \"var(--slider-range-end)\",\n    }\n  }\n\n  return {\n    position: \"absolute\",\n    [ctx.isRtl ? \"right\" : \"left\"]: \"var(--slider-range-start)\",\n    [ctx.isRtl ? \"left\" : \"right\"]: \"var(--slider-range-end)\",\n  }\n}\n\n/* -----------------------------------------------------------------------------\n * Thumb style calculations\n * -----------------------------------------------------------------------------*/\n\nfunction getVerticalThumbOffset(ctx: SharedContext) {\n  const { height = 0 } = ctx.thumbSize ?? {}\n  const getValue = getValueTransformer([ctx.min, ctx.max], [-height / 2, height / 2])\n  return parseFloat(getValue(ctx.value).toFixed(2))\n}\n\nfunction getHorizontalThumbOffset(ctx: SharedContext) {\n  const { width = 0 } = ctx.thumbSize ?? {}\n\n  if (ctx.isRtl) {\n    const getValue = getValueTransformer([ctx.max, ctx.min], [-width / 2, width / 2])\n    return -1 * parseFloat(getValue(ctx.value).toFixed(2))\n  }\n\n  const getValue = getValueTransformer([ctx.min, ctx.max], [-width / 2, width / 2])\n  return parseFloat(getValue(ctx.value).toFixed(2))\n}\n\nfunction getOffset(ctx: SharedContext, percent: number) {\n  if (ctx.thumbAlignment === \"center\") return `${percent}%`\n  const offset = ctx.isVertical ? getVerticalThumbOffset(ctx) : getHorizontalThumbOffset(ctx)\n  return `calc(${percent}% - ${offset}px)`\n}\n\nfunction getThumbOffset(ctx: SharedContext) {\n  let percent = getValuePercent(ctx.value, ctx.min, ctx.max) * 100\n  return getOffset(ctx, percent)\n}\n\nfunction getVisibility(ctx: Ctx) {\n  let visibility: \"visible\" | \"hidden\" = \"visible\"\n  if (ctx.thumbAlignment === \"contain\" && !ctx.hasMeasuredThumbSize) {\n    visibility = \"hidden\"\n  }\n  return visibility\n}\n\nfunction getThumbStyle(ctx: Ctx, index: number): Style {\n  const placementProp = ctx.isVertical ? \"bottom\" : \"insetInlineStart\"\n  return {\n    visibility: getVisibility(ctx),\n    position: \"absolute\",\n    transform: \"var(--slider-thumb-transform)\",\n    [placementProp]: `var(--slider-thumb-offset-${index})`,\n  }\n}\n\n/* -----------------------------------------------------------------------------\n * Control style calculations\n * -----------------------------------------------------------------------------*/\n\nfunction getControlStyle(): Style {\n  return {\n    touchAction: \"none\",\n    userSelect: \"none\",\n    WebkitUserSelect: \"none\",\n    position: \"relative\",\n  }\n}\n\n/* -----------------------------------------------------------------------------\n * Root style calculations\n * -----------------------------------------------------------------------------*/\n\nfunction getRootStyle(ctx: Ctx): Style {\n  const range = getRangeOffsets(ctx)\n\n  const offsetStyles = ctx.value.reduce<Style>((styles, value, index) => {\n    const offset = getThumbOffset({ ...ctx, value })\n    return { ...styles, [`--slider-thumb-offset-${index}`]: offset }\n  }, {})\n\n  return {\n    ...offsetStyles,\n    \"--slider-thumb-transform\": ctx.isVertical ? \"translateY(50%)\" : ctx.isRtl ? \"translateX(50%)\" : \"translateX(-50%)\",\n    \"--slider-range-start\": range.start,\n    \"--slider-range-end\": range.end,\n  }\n}\n\n/* -----------------------------------------------------------------------------\n * Marker style calculations\n * -----------------------------------------------------------------------------*/\n\nfunction getMarkerStyle(\n  ctx: Pick<SharedContext, \"isHorizontal\" | \"isRtl\" | \"thumbAlignment\" | \"hasMeasuredThumbSize\">,\n  value: number,\n): Style {\n  return {\n    // @ts-expect-error\n    visibility: getVisibility(ctx),\n    position: \"absolute\",\n    pointerEvents: \"none\",\n    // @ts-expect-error\n    [ctx.isHorizontal ? \"insetInlineStart\" : \"bottom\"]: getThumbOffset({ ...ctx, value }),\n    translate: \"var(--tx) var(--ty)\",\n    \"--tx\": ctx.isHorizontal ? (ctx.isRtl ? \"50%\" : \"-50%\") : \"0%\",\n    \"--ty\": !ctx.isHorizontal ? \"50%\" : \"0%\",\n  }\n}\n\n/* -----------------------------------------------------------------------------\n * Label style calculations\n * -----------------------------------------------------------------------------*/\n\nfunction getMarkerGroupStyle(): Style {\n  return {\n    userSelect: \"none\",\n    WebkitUserSelect: \"none\",\n    pointerEvents: \"none\",\n    position: \"relative\",\n  }\n}\n\nexport const styleGetterFns = {\n  getRootStyle,\n  getControlStyle,\n  getThumbStyle,\n  getRangeStyle,\n  getMarkerStyle,\n  getMarkerGroupStyle,\n}\n", "import {\n  clampValue,\n  getClosestValueIndex,\n  getNextStepValue,\n  getPreviousStepValue,\n  getValueRanges,\n  snapValueToStep,\n} from \"@zag-js/numeric-range\"\nimport type { MachineContext as Ctx } from \"./slider.types\"\n\nexport function normalizeValues(ctx: Ctx, nextValues: number[]) {\n  return nextValues.map((value, index, values) => {\n    return constrainValue({ ...ctx, value: values }, value, index)\n  })\n}\n\nexport function clampPercent(percent: number) {\n  return clampValue(percent, 0, 1)\n}\n\nexport function getRangeAtIndex(ctx: Ctx, index: number) {\n  return getValueRanges(ctx.value, ctx.min, ctx.max, ctx.minStepsBetweenThumbs)[index]\n}\n\nexport function constrainValue(ctx: Ctx, value: number, index: number) {\n  const range = getRangeAtIndex(ctx, index)\n  const snapValue = snapValueToStep(value, ctx.min, ctx.max, ctx.step)\n  return clampValue(snapValue, range.min, range.max)\n}\n\nexport function decrement(ctx: Ctx, index?: number, step?: number) {\n  const idx = index ?? ctx.focusedIndex\n  const range = getRangeAtIndex(ctx, idx)\n  const nextValues = getPreviousStepValue(idx, {\n    ...range,\n    step: step ?? ctx.step,\n    values: ctx.value,\n  })\n  nextValues[idx] = clampValue(nextValues[idx], range.min, range.max)\n  return nextValues\n}\n\nexport function increment(ctx: Ctx, index?: number, step?: number) {\n  const idx = index ?? ctx.focusedIndex\n  const range = getRangeAtIndex(ctx, idx)\n  const nextValues = getNextStepValue(idx, {\n    ...range,\n    step: step ?? ctx.step,\n    values: ctx.value,\n  })\n  nextValues[idx] = clampValue(nextValues[idx], range.min, range.max)\n  return nextValues\n}\n\nexport function getClosestIndex(ctx: Ctx, pointValue: number) {\n  return getClosestValueIndex(ctx.value, pointValue)\n}\n\nexport function assignArray(current: number[], next: number[]) {\n  for (let i = 0; i < next.length; i++) {\n    const value = next[i]\n    current[i] = value\n  }\n}\n", "import { createMachine } from \"@zag-js/core\"\nimport { trackPointerMove } from \"@zag-js/dom-event\"\nimport { raf } from \"@zag-js/dom-query\"\nimport { trackElementsSize, type ElementSize } from \"@zag-js/element-size\"\nimport { trackFormControl } from \"@zag-js/form-utils\"\nimport { getValuePercent } from \"@zag-js/numeric-range\"\nimport { compact, isEqual } from \"@zag-js/utils\"\nimport { dom } from \"./slider.dom\"\nimport type { MachineContext, MachineState, UserDefinedContext } from \"./slider.types\"\nimport {\n  assignArray,\n  constrainValue,\n  decrement,\n  getClosestIndex,\n  getRangeAtIndex,\n  increment,\n  normalizeValues,\n} from \"./slider.utils\"\n\nconst isEqualSize = (a: ElementSize | null, b: ElementSize | null) => {\n  return a?.width === b?.width && a?.height === b?.height\n}\n\nexport function machine(userContext: UserDefinedContext) {\n  const ctx = compact(userContext)\n  return createMachine<MachineContext, MachineState>(\n    {\n      id: \"slider\",\n      initial: \"idle\",\n\n      context: {\n        thumbSize: null,\n        thumbAlignment: \"contain\",\n        min: 0,\n        max: 100,\n        step: 1,\n        value: [0],\n        origin: \"start\",\n        orientation: \"horizontal\",\n        dir: \"ltr\",\n        minStepsBetweenThumbs: 0,\n        disabled: false,\n        readOnly: false,\n        ...ctx,\n        focusedIndex: -1,\n        fieldsetDisabled: false,\n      },\n\n      computed: {\n        isHorizontal: (ctx) => ctx.orientation === \"horizontal\",\n        isVertical: (ctx) => ctx.orientation === \"vertical\",\n        isRtl: (ctx) => ctx.orientation === \"horizontal\" && ctx.dir === \"rtl\",\n        isDisabled: (ctx) => !!ctx.disabled || ctx.fieldsetDisabled,\n        isInteractive: (ctx) => !(ctx.readOnly || ctx.isDisabled),\n        hasMeasuredThumbSize: (ctx) => ctx.thumbSize != null,\n        valuePercent(ctx) {\n          return ctx.value.map((value) => 100 * getValuePercent(value, ctx.min, ctx.max))\n        },\n      },\n\n      watch: {\n        value: [\"syncInputElements\"],\n      },\n\n      entry: [\"coarseValue\"],\n\n      activities: [\"trackFormControlState\", \"trackThumbsSize\"],\n\n      on: {\n        SET_VALUE: [\n          {\n            guard: \"hasIndex\",\n            actions: \"setValueAtIndex\",\n          },\n          { actions: \"setValue\" },\n        ],\n        INCREMENT: {\n          actions: \"incrementThumbAtIndex\",\n        },\n        DECREMENT: {\n          actions: \"decrementThumbAtIndex\",\n        },\n      },\n\n      states: {\n        idle: {\n          on: {\n            POINTER_DOWN: {\n              target: \"dragging\",\n              actions: [\"setClosestThumbIndex\", \"setPointerValue\", \"focusActiveThumb\"],\n            },\n            FOCUS: {\n              target: \"focus\",\n              actions: \"setFocusedIndex\",\n            },\n            THUMB_POINTER_DOWN: {\n              target: \"dragging\",\n              actions: [\"setFocusedIndex\", \"focusActiveThumb\"],\n            },\n          },\n        },\n        focus: {\n          entry: \"focusActiveThumb\",\n          on: {\n            POINTER_DOWN: {\n              target: \"dragging\",\n              actions: [\"setClosestThumbIndex\", \"setPointerValue\", \"focusActiveThumb\"],\n            },\n            THUMB_POINTER_DOWN: {\n              target: \"dragging\",\n              actions: [\"setFocusedIndex\", \"focusActiveThumb\"],\n            },\n            ARROW_DEC: {\n              actions: [\"decrementThumbAtIndex\", \"invokeOnChangeEnd\"],\n            },\n            ARROW_INC: {\n              actions: [\"incrementThumbAtIndex\", \"invokeOnChangeEnd\"],\n            },\n            HOME: {\n              actions: [\"setFocusedThumbToMin\", \"invokeOnChangeEnd\"],\n            },\n            END: {\n              actions: [\"setFocusedThumbToMax\", \"invokeOnChangeEnd\"],\n            },\n            BLUR: {\n              target: \"idle\",\n              actions: \"clearFocusedIndex\",\n            },\n          },\n        },\n        dragging: {\n          entry: \"focusActiveThumb\",\n          activities: \"trackPointerMove\",\n          on: {\n            POINTER_UP: {\n              target: \"focus\",\n              actions: \"invokeOnChangeEnd\",\n            },\n            POINTER_MOVE: {\n              actions: \"setPointerValue\",\n            },\n          },\n        },\n      },\n    },\n    {\n      guards: {\n        hasIndex: (_ctx, evt) => evt.index != null,\n      },\n      activities: {\n        trackFormControlState(ctx, _evt, { initialContext }) {\n          return trackFormControl(dom.getRootEl(ctx), {\n            onFieldsetDisabledChange(disabled) {\n              ctx.fieldsetDisabled = disabled\n            },\n            onFormReset() {\n              set.value(ctx, initialContext.value)\n            },\n          })\n        },\n\n        trackPointerMove(ctx, _evt, { send }) {\n          return trackPointerMove(dom.getDoc(ctx), {\n            onPointerMove(info) {\n              send({ type: \"POINTER_MOVE\", point: info.point })\n            },\n            onPointerUp() {\n              send(\"POINTER_UP\")\n            },\n          })\n        },\n        trackThumbsSize(ctx) {\n          if (ctx.thumbAlignment !== \"contain\" || ctx.thumbSize) return\n\n          return trackElementsSize({\n            getNodes: () => dom.getElements(ctx),\n            observeMutation: true,\n            callback(size) {\n              if (!size || isEqualSize(ctx.thumbSize, size)) return\n              ctx.thumbSize = size\n            },\n          })\n        },\n      },\n      actions: {\n        syncInputElements(ctx) {\n          ctx.value.forEach((value, index) => {\n            const inputEl = dom.getHiddenInputEl(ctx, index)\n            dom.setValue(inputEl, value)\n          })\n        },\n        invokeOnChangeEnd(ctx) {\n          ctx.onValueChangeEnd?.({ value: ctx.value })\n        },\n        setClosestThumbIndex(ctx, evt) {\n          const pointValue = dom.getValueFromPoint(ctx, evt.point)\n          if (pointValue == null) return\n\n          const focusedIndex = getClosestIndex(ctx, pointValue)\n          set.focusedIndex(ctx, focusedIndex)\n        },\n        setFocusedIndex(ctx, evt) {\n          set.focusedIndex(ctx, evt.index)\n        },\n        clearFocusedIndex(ctx) {\n          set.focusedIndex(ctx, -1)\n        },\n        setPointerValue(ctx, evt) {\n          const pointerValue = dom.getValueFromPoint(ctx, evt.point)\n          if (pointerValue == null) return\n\n          const value = constrainValue(ctx, pointerValue, ctx.focusedIndex)\n          set.valueAtIndex(ctx, ctx.focusedIndex, value)\n        },\n        focusActiveThumb(ctx) {\n          raf(() => {\n            const thumbEl = dom.getThumbEl(ctx, ctx.focusedIndex)\n            thumbEl?.focus({ preventScroll: true })\n          })\n        },\n        decrementThumbAtIndex(ctx, evt) {\n          const value = decrement(ctx, evt.index, evt.step)\n          set.value(ctx, value)\n        },\n        incrementThumbAtIndex(ctx, evt) {\n          const value = increment(ctx, evt.index, evt.step)\n          set.value(ctx, value)\n        },\n        setFocusedThumbToMin(ctx) {\n          const { min } = getRangeAtIndex(ctx, ctx.focusedIndex)\n          set.valueAtIndex(ctx, ctx.focusedIndex, min)\n        },\n        setFocusedThumbToMax(ctx) {\n          const { max } = getRangeAtIndex(ctx, ctx.focusedIndex)\n          set.valueAtIndex(ctx, ctx.focusedIndex, max)\n        },\n        coarseValue(ctx) {\n          const value = normalizeValues(ctx, ctx.value)\n          set.value(ctx, value)\n        },\n        setValueAtIndex(ctx, evt) {\n          const value = constrainValue(ctx, evt.value, evt.index)\n          set.valueAtIndex(ctx, evt.index, value)\n        },\n        setValue(ctx, evt) {\n          const value = normalizeValues(ctx, evt.value)\n          set.value(ctx, value)\n        },\n      },\n    },\n  )\n}\n\nconst invoke = {\n  change: (ctx: MachineContext) => {\n    ctx.onValueChange?.({\n      value: Array.from(ctx.value),\n    })\n    dom.dispatchChangeEvent(ctx)\n  },\n  focusChange: (ctx: MachineContext) => {\n    ctx.onFocusChange?.({\n      value: Array.from(ctx.value),\n      focusedIndex: ctx.focusedIndex,\n    })\n  },\n}\n\nconst set = {\n  valueAtIndex: (ctx: MachineContext, index: number, value: number) => {\n    if (isEqual(ctx.value[index], value)) return\n    ctx.value[index] = value\n    invoke.change(ctx)\n  },\n  value: (ctx: MachineContext, value: number[]) => {\n    if (isEqual(ctx.value, value)) return\n    assignArray(ctx.value, value)\n    invoke.change(ctx)\n  },\n  focusedIndex: (ctx: MachineContext, index: number) => {\n    if (isEqual(ctx.focusedIndex, index)) return\n    ctx.focusedIndex = index\n    invoke.focusChange(ctx)\n  },\n}\n", "import { createProps } from \"@zag-js/types\"\nimport { createSplitProps } from \"@zag-js/utils\"\nimport type { ThumbProps, UserDefinedContext } from \"./slider.types\"\n\nexport const props = createProps<UserDefinedContext>()([\n  \"aria-label\",\n  \"aria-labelledby\",\n  \"dir\",\n  \"disabled\",\n  \"form\",\n  \"getAriaValueText\",\n  \"getRootNode\",\n  \"id\",\n  \"ids\",\n  \"invalid\",\n  \"max\",\n  \"min\",\n  \"minStepsBetweenThumbs\",\n  \"name\",\n  \"onFocusChange\",\n  \"onValueChange\",\n  \"onValueChangeEnd\",\n  \"orientation\",\n  \"origin\",\n  \"readOnly\",\n  \"step\",\n  \"thumbAlignment\",\n  \"thumbAlignment\",\n  \"thumbSize\",\n  \"value\",\n])\n\nexport const splitProps = createSplitProps<Partial<UserDefinedContext>>(props)\n\nexport const thumbProps = createProps<ThumbProps>()([\"index\", \"name\"])\nexport const splitThumbProps = createSplitProps<ThumbProps>(thumbProps)\n"], "mappings": ";;;;;;;AAiBO,IAAM,gBAAgB,CAAmB,MAAcA,SAAQ,CAAC,OAA0B;EAC/F,OAAO,IAAI,WAAW;AACpB,QAAI,QAAQA,MAAK,GAAG;AAClB,aAAO,cAAc,MAAM,MAAM;IACnC;AACA,UAAM,IAAI,MAAM,+FAA+F;EACjH;EACA,YAAY,IAAI,WAAW,cAAc,MAAM,CAAC,GAAGA,QAAO,GAAG,MAAM,CAAC;EACpE,QAAQ,CAAC,YAAY,cAAc,SAASA,MAAK;EACjD,MAAM,MAAMA;EACZ,OAAO,MACL,CAAC,GAAG,IAAI,IAAIA,MAAK,CAAC,EAAE;IAClB,CAAC,MAAM,SACL,OAAO,OAAO,MAAM;MAClB,CAAC,IAAI,GAAG;QACN,UAAU;UACR,iBAAiB,YAAY,IAAI,CAAC,iBAAiB,YAAY,IAAI,CAAC;UACpE,kBAAkB,YAAY,IAAI,CAAC,iBAAiB,YAAY,IAAI,CAAC;QACvE,EAAE,KAAK,IAAI;QACX,OAAO,EAAE,cAAc,YAAY,IAAI,GAAG,aAAa,YAAY,IAAI,EAAE;MAC3E;IACF,CAAC;IACH,CAAC;EACH;AACJ;AAEA,IAAM,cAAc,CAAC,UACnB,MACG,QAAQ,mBAAmB,OAAO,EAClC,QAAQ,mBAAmB,OAAO,EAClC,QAAQ,WAAW,GAAG,EACtB,YAAY;AAEjB,IAAM,UAAU,CAAI,MAAoB,EAAE,WAAW;;;AChD9C,IAAM,WAAW,CAAC,UAAgC,QAAQ,KAAK;AAC/D,IAAM,WAAW,CAAC,UAAgC,QAAQ,SAAS;AEAnE,IAAM,aAAa,CAAC,OAA4B,GAAG,aAAa,KAAK;AAErE,IAAM,WAAW,CAAC,OAA0B,MAAM,QAAQ,OAAO,GAAG;AEHpE,SAAS,YAAY,IAA+C;AACzE,MAAI,WAAW,EAAE,EAAG,QAAO;AAC3B,MAAI,SAAS,EAAE,EAAG,QAAO,GAAG;AAC5B,UAAO,yBAAI,kBAAiB;AAC9B;AENO,IAAM,QAAQ,MAAM,OAAO,aAAa;AAExC,SAAS,cAAc;AAC5B,QAAM,QAAS,UAAkB;AACjC,UAAO,+BAAO,aAAY,UAAU;AACtC;AAEA,IAAM,KAAK,CAAC,MAAc,MAAM,KAAK,EAAE,KAAK,YAAY,CAAC;AASlD,IAAM,QAAQ,MAAM,GAAG,oBAAoB;AEd3C,IAAM,kBAAkB,CAAwB,MAAS,EAAE;AAE3D,SAAS,SAAgC,GAAQ,IAAY,WAAwB,iBAAiB;AAC3G,SAAO,EAAE,KAAK,CAAC,SAAS,SAAS,IAAI,MAAM,EAAE;AAC/C;AAEO,SAAS,UAAiC,GAAQ,IAAY,WAAwB,iBAAiB;AAC5G,QAAM,OAAO,SAAS,GAAG,IAAI,QAAQ;AACrC,SAAO,OAAO,EAAE,QAAQ,IAAI,IAAI;AAClC;ACXO,IAAM,WAAW,CAAC,QACvB,IACG,MAAM,EAAE,EACR,IAAI,CAAC,SAAS;AACb,QAAM,OAAO,KAAK,WAAW,CAAC;AAC9B,MAAI,OAAO,KAAK,OAAO,IAAK,QAAO;AACnC,MAAI,QAAQ,OAAO,QAAQ,IAAK,QAAO,KAAK,KAAK,SAAS,EAAE,CAAC,GAAG,QAAQ,KAAK,IAAI;AACjF,SAAO;AACT,CAAC,EACA,KAAK,EAAE,EACP,KAAK;ACPV,IAAM,eAAe,CAAwB,SAAY,SAAS,KAAK,QAAQ,aAAa,KAAK,eAAe,EAAE;AAElH,IAAM,QAAQ,CAAC,WAAmBC,WAAkB,UAAU,KAAK,EAAE,YAAY,EAAE,WAAWA,OAAM,YAAY,CAAC;AAEjH,IAAM,OAAO,CAAI,GAAQ,QAAgB;AACvC,SAAO,EAAE,IAAI,CAAC,GAAG,UAAU,GAAG,KAAK,IAAI,KAAK,CAAC,IAAI,SAAS,EAAE,MAAM,CAAC;AACrE;AAEO,SAAS,UACd,GACA,MACA,WACA,WAAwB,iBACxB;AACA,QAAM,QAAQ,YAAY,UAAU,GAAG,WAAW,QAAQ,IAAI;AAC9D,MAAI,QAAQ,YAAY,KAAK,GAAG,KAAK,IAAI;AAEzC,QAAM,cAAc,KAAK,WAAW;AAEpC,MAAI,aAAa;AACf,YAAQ,MAAM,OAAO,CAAC,SAAS,SAAS,IAAI,MAAM,SAAS;EAC7D;AAEA,SAAO,MAAM,KAAK,CAAC,SAAS,MAAM,aAAa,IAAI,GAAG,IAAI,CAAC;AAC7D;ACXA,SAAS,mBAA0C,QAAa,SAA2B;AACzF,QAAM,EAAE,OAAAC,QAAO,UAAU,KAAK,UAAU,KAAK,SAAS,IAAI;AAE1D,QAAM,SAASA,OAAM,YAAY;AACjC,QAAM,aAAa,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM,EAAE,MAAM,CAAC,SAAS,SAAS,OAAO,CAAC,CAAC;AAE7F,QAAMD,SAAQ,aAAa,OAAO,CAAC,IAAI;AAEvC,MAAI,QAAQ,OAAO,MAAM;AAEzB,QAAM,OAAO,UAAU,OAAOA,QAAO,UAAU,QAAQ;AAEvD,WAAS,UAAU;AACjB,iBAAaC,OAAM,KAAK;AACxB,IAAAA,OAAM,QAAQ;EAChB;AAEA,WAAS,OAAO,OAAe;AAC7B,IAAAA,OAAM,YAAY;AAClB,YAAQ;AAER,QAAI,UAAU,IAAI;AAChB,MAAAA,OAAM,QAAQ,CAAC,WAAW,MAAM;AAC9B,eAAO,EAAE;AACT,gBAAQ;MACV,GAAG,OAAO;IACZ;EACF;AAEA,SAAO,MAAM;AAEb,SAAO;AACT;AACO,IAAM,iBAA+B,OAAO,OAAO,oBAAoB;EAC5E,gBAAgB,EAAE,WAAW,IAAI,OAAO,GAAG;EAC3C,cAAc;AAChB,CAAC;AAED,SAAS,sBAAsB,OAA2D;AACxF,SAAO,MAAM,IAAI,WAAW,KAAK,CAAC,MAAM,WAAW,CAAC,MAAM;AAC5D;ASxDO,SAAS,SAAS,IAAkB;AACzC,QAAMC,OAAM,oBAAI,IAAkB;AAClC,WAASC,KAAIC,KAAkB;AAC7B,UAAM,KAAK,WAAW,sBAAsBA,GAAE;AAC9CF,SAAI,IAAI,MAAM,WAAW,qBAAqB,EAAE,CAAC;EACnD;AACAC,OAAI,MAAMA,KAAI,EAAE,CAAC;AACjB,SAAO,SAAS,UAAU;AACxBD,SAAI,QAAQ,CAACE,QAAOA,IAAG,CAAC;EAC1B;AACF;AAEO,SAAS,IAAI,IAAkB;AACpC,QAAM,KAAK,WAAW,sBAAsB,EAAE;AAC9C,SAAO,MAAM;AACX,eAAW,qBAAqB,EAAE;EACpC;AACF;AKfO,SAAS,SAA0C,MAAY,UAAkB;AACtF,SAAO,MAAM,MAAK,6BAAM,iBAAoB,cAAa,CAAC,CAAC;AAC7D;ACEO,SAAS,YAAe,SAAY;AACzC,QAAMC,OAAM;IACV,aAAa,CAAC,QAAA;;AAAuB,wBAAI,gBAAJ,iCAAuB;;IAC5D,QAAQ,CAAC,QAAsB,YAAYA,KAAI,YAAY,GAAG,CAAC;IAC/D,QAAQ,CAAC,QAAsBA,KAAI,OAAO,GAAG,EAAE,eAAe;IAC9D,kBAAkB,CAAC,QAAsBA,KAAI,YAAY,GAAG,EAAE;IAC9D,iBAAiB,CAAC,KAAmB,SAA6B,SAASA,KAAI,iBAAiB,GAAG;IACnG,SAAS,CAAkC,KAAmB,OAC5DA,KAAI,YAAY,GAAG,EAAE,eAAe,EAAE;IACxC,UAAU,CAA8B,MAAgB,UAA8C;AACpG,UAAI,QAAQ,QAAQ,SAAS,KAAM;AACnC,YAAM,gBAAgB,MAAM,SAAS;AACrC,UAAI,KAAK,UAAU,cAAe;AAClC,WAAK,QAAQ,MAAM,SAAS;IAC9B;EACF;AAEA,SAAO,EAAE,GAAGA,MAAK,GAAG,QAAQ;AAC9B;AIpBA,IAAM,MAAM,MAAO;;;ACAnB,IAAI,QAAe;AACnB,IAAI,aAAa;AACjB,IAAM,aAAa,oBAAI,QAA6B;AAQpD,SAAS,yBAAyB,UAAuC,CAAC,GAAG;AAC3E,QAAM,EAAE,QAAQ,IAAI,IAAI;AAExB,QAAM,UAAU,OAAO;AACvB,QAAM,SAAS,QAAQ;AAEvB,MAAI,MAAM,GAAG;AACX,QAAI,UAAU,WAAW;AACvB,mBAAa,OAAO,MAAM;AAC1B,aAAO,MAAM,mBAAmB;IAClC;AAEA,YAAQ;EACV,WAAW,QAAQ;AACjB,eAAW,IAAI,QAAQ,OAAO,MAAM,UAAU;AAC9C,WAAO,MAAM,aAAa;EAC5B;AAEA,SAAO,MAAM,qBAAqB,EAAE,QAAQ,KAAK,QAAQ,CAAC;AAC5D;AAEO,SAAS,qBAAqB,UAAuC,CAAC,GAAG;AAC9E,QAAM,EAAE,QAAQ,IAAI,IAAI;AAExB,QAAM,UAAU,OAAO;AACvB,QAAM,SAAS,QAAQ;AAEvB,MAAI,MAAM,GAAG;AACX,QAAI,UAAU,WAAY;AAC1B,YAAQ;AAER,eAAW,MAAM;AACf,eAAS,MAAM;AACb,YAAI,UAAU,aAAa;AACzB,cAAI,OAAO,MAAM,qBAAqB,QAAQ;AAC5C,mBAAO,MAAM,mBAAmB,cAAc;UAChD;AACA,uBAAa;AACb,kBAAQ;QACV;MACF,CAAC;IACH,GAAG,GAAG;EACR,OAAO;AACL,QAAI,UAAU,WAAW,IAAI,MAAM,GAAG;AACpC,YAAM,iBAAiB,WAAW,IAAI,MAAM;AAE5C,UAAI,OAAO,MAAM,eAAe,QAAQ;AACtC,eAAO,MAAM,aAAa,kBAAkB;MAC9C;AAEA,UAAI,OAAO,aAAa,OAAO,MAAM,IAAI;AACvC,eAAO,gBAAgB,OAAO;MAChC;AACA,iBAAW,OAAO,MAAM;IAC1B;EACF;AACF;AAMO,SAAS,qBAAqB,UAAiD,CAAC,GAAG;AACxF,QAAM,EAAE,OAAO,QAAQ,GAAG,YAAY,IAAI;AAC1C,QAAM,OAAO,QAAQ,MAAM,CAAC,MAAW,EAAE;AACzC,QAAM,WAAyC,CAAC;AAChD,WAAS;IACP,KAAK,MAAM;AACT,YAAM,OAAO,OAAO,WAAW,aAAa,OAAO,IAAI;AACvD,eAAS,KAAK,yBAAyB,EAAE,GAAG,aAAa,QAAQ,KAAK,CAAC,CAAC;IAC1E,CAAC;EACH;AACA,SAAO,MAAM;AACX,aAAS,QAAQ,CAAC,OAAO,0BAAM;EACjC;AACF;;;ACnFO,IAAM,cAAc,CACzB,QACA,WACA,SACA,YACG;AACH,QAAM,OAAO,OAAO,WAAW,aAAa,OAAO,IAAI;AACvD,+BAAM,iBAAiB,WAAW,SAAgB;AAClD,SAAO,MAAM;AACX,iCAAM,oBAAoB,WAAW,SAAgB;EACvD;AACF;ACKO,IAAM,cAAc,CAAC,MAAkC,EAAE,WAAW;AAMpE,IAAM,gBAAgB,CAAC,MAC5B,EAAE,WAAW,EAAE,UAAU,EAAE;AI3B7B,IAAM,SAAiC;EACrC,IAAI;EACJ,MAAM;EACN,KAAK;EACL,KAAK;EACL,KAAK;EACL,MAAM;EACN,OAAO;AACT;AAEA,IAAM,YAAoC;EACxC,WAAW;EACX,YAAY;AACd;AAKO,SAAS,YAAY,OAAmC,UAA2B,CAAC,GAAG;AAC5F,QAAM,EAAE,MAAM,OAAO,cAAc,aAAa,IAAI;AAEpD,MAAI,EAAE,IAAI,IAAI;AACd,QAAM,OAAO,GAAG,KAAK;AAErB,QAAM,QAAQ,QAAQ,SAAS,gBAAgB;AAE/C,MAAI,SAAS,OAAO,WAAW;AAC7B,UAAM,UAAU,GAAG;EACrB;AAEA,SAAO;AACT;AC/BA,SAAS,eAAe,GAAe,OAAkB,UAAU;AACjE,QAAM,QAAQ,EAAE,QAAQ,CAAC,KAAK,EAAE,eAAe,CAAC;AAChD,SAAO,EAAE,GAAG,MAAM,GAAG,IAAI,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,GAAG,EAAE;AACtD;AAEA,SAAS,eAAe,OAAkC,OAAkB,UAAU;AACpF,SAAO,EAAE,GAAG,MAAM,GAAG,IAAI,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,GAAG,EAAE;AACtD;AAIA,IAAM,eAAe,CAAC,UAAgD,aAAa,SAAS,MAAM,QAAQ,SAAS;AAE5G,SAAS,cAAc,OAAY,OAAkB,UAAU;AACpE,SAAO,aAAa,KAAK,IAAI,eAAe,OAAO,IAAI,IAAI,eAAe,OAAO,IAAI;AACvF;ACjBA,IAAM,YAAY,oBAAI,IAAI,CAAC,UAAU,UAAU,CAAC;AAChD,IAAM,aAAa,oBAAI,IAAI,CAAC,WAAW,aAAa,aAAa,YAAY,CAAC;AAKvE,SAAS,aAAa,OAAwE;AACnG,MAAI,MAAM,WAAW,MAAM,SAAS;AAClC,WAAO;EACT,OAAO;AACL,UAAM,YAAY,UAAU,IAAI,MAAM,GAAG;AACzC,UAAM,YAAY,aAAc,MAAM,YAAY,WAAW,IAAI,MAAM,GAAG;AAC1E,WAAO,YAAY,KAAK;EAC1B;AACF;AEdA,SAAS,MAAM,OAAe;AAC5B,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC;AACvC;AAaO,SAAS,iBAAiB,OAAc,SAAsB;AACnE,QAAM,EAAE,MAAM,KAAK,OAAO,OAAO,IAAI,QAAQ,sBAAsB;AAEnE,QAAM,SAAS,EAAE,GAAG,MAAM,IAAI,MAAM,GAAG,MAAM,IAAI,IAAI;AACrD,QAAM,UAAU,EAAE,GAAG,MAAM,OAAO,IAAI,KAAK,GAAG,GAAG,MAAM,OAAO,IAAI,MAAM,EAAE;AAE1E,WAASC,iBAAgB,UAA+B,CAAC,GAAG;AAC1D,UAAM,EAAE,MAAM,OAAO,cAAc,cAAc,SAAS,IAAI;AAE9D,UAAM,UAAU,OAAO,aAAa,WAAW,SAAS,IAAI;AAC5D,UAAM,UAAU,OAAO,aAAa,WAAW,SAAS,IAAI;AAE5D,QAAI,gBAAgB,cAAc;AAChC,aAAO,QAAQ,SAAS,UAAU,IAAI,QAAQ,IAAI,QAAQ;IAC5D;AAEA,WAAO,UAAU,IAAI,QAAQ,IAAI,QAAQ;EAC3C;AAEA,SAAO,EAAE,QAAQ,SAAS,iBAAAA,iBAAgB;AAC5C;AIOO,SAAS,iBAAiB,KAAe,UAA+B;AAC7E,QAAM,EAAE,eAAe,YAAY,IAAI;AAEvC,QAAM,UAA8B,CAAC;AAErC,QAAM,aAAa,CAAC,UAAwB;AAC1C,UAAM,QAAQ,cAAc,KAAK;AACjC,YAAQ,KAAK,EAAE,GAAG,OAAO,WAAW,YAAY,IAAI,EAAE,CAAC;AAEvD,UAAM,WAAW,KAAK,KAAK,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC;AACtD,UAAM,aAAa,MAAM,gBAAgB,UAAU,KAAK;AAExD,QAAI,WAAW,WAAY;AAG3B,QAAI,MAAM,gBAAgB,WAAW,MAAM,WAAW,GAAG;AACvD,kBAAY;AACZ;IACF;AAEA,kBAAc,EAAE,OAAO,OAAO,UAAU,YAAY,SAAS,GAAG,EAAE,CAAC;EACrE;AAEA,QAAM,WAAW;IACf,YAAY,KAAK,eAAe,YAAY,KAAK;IACjD,YAAY,KAAK,aAAa,aAAa,KAAK;IAChD,YAAY,KAAK,iBAAiB,aAAa,KAAK;IACpD,YAAY,KAAK,eAAe,aAAa,KAAK;IAClD,qBAAqB,EAAE,IAAI,CAAC;EAC9B;AAEA,SAAO,MAAM;AACX,aAAS,QAAQ,CAAC,YAAY,QAAQ,CAAC;AACvC,YAAQ,SAAS;EACnB;AACF;AAEA,SAAS,gBAAgB,SAA+C;AACtE,SAAO,QAAQ,QAAQ,SAAS,CAAC;AACnC;AAEA,SAAS,GAAG,SAAyB;AACnC,SAAO,UAAU;AACnB;AAEA,SAAS,IAAI,cAA8B;AACzC,SAAO,eAAe;AACxB;AAEA,SAAS,YAAY,SAA6B,WAA0B;AAC1E,MAAI,QAAQ,SAAS,EAAG,QAAO,EAAE,GAAG,GAAG,GAAG,EAAE;AAE5C,MAAI,IAAI,QAAQ,SAAS;AACzB,MAAI,mBAA4C;AAChD,QAAM,YAAY,gBAAgB,OAAO;AAEzC,SAAO,KAAK,GAAG;AACb,uBAAmB,QAAQ,CAAC;AAC5B,QAAI,UAAU,YAAY,iBAAiB,YAAY,GAAG,SAAS,GAAG;AACpE;IACF;AACA;EACF;AAEA,MAAI,CAAC,iBAAkB,QAAO,EAAE,GAAG,GAAG,GAAG,EAAE;AAE3C,QAAM,OAAO,IAAI,UAAU,YAAY,iBAAiB,SAAS;AACjE,MAAI,SAAS,EAAG,QAAO,EAAE,GAAG,GAAG,GAAG,EAAE;AAEpC,QAAM,kBAAkB;IACtB,IAAI,UAAU,IAAI,iBAAiB,KAAK;IACxC,IAAI,UAAU,IAAI,iBAAiB,KAAK;EAC1C;AAEA,MAAI,gBAAgB,MAAM,SAAU,iBAAgB,IAAI;AACxD,MAAI,gBAAgB,MAAM,SAAU,iBAAgB,IAAI;AAExD,SAAO;IACL,GAAG,KAAK,IAAI,gBAAgB,CAAC;IAC7B,GAAG,KAAK,IAAI,gBAAgB,CAAC;EAC/B;AACF;;;AG3HO,SAAS,mBAAmB,OAAe,QAAkB,UAAkB;AACpF,SAAO,UAAU,IAAI,WAAW,OAAO,QAAQ,CAAC;AAClD;AAEO,SAAS,mBAAmB,OAAe,QAAkB,UAAkB;AACpF,SAAO,UAAU,OAAO,SAAS,IAAI,WAAW,OAAO,QAAQ,CAAC;AAClE;AAcO,SAAS,gBAAgB,OAAe,UAAkB,MAAc;AAC7E,SAAO,KAAK,OAAO,QAAQ,YAAY,IAAI,IAAI,OAAO;AACxD;AAEO,SAAS,WAAW,OAAe,UAAkB,UAAkB;AAC5E,SAAO,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,GAAG,QAAQ;AACrD;AAEO,SAAS,gBAAgB,OAAe,UAAkB,UAAkB;AACjF,UAAQ,QAAQ,aAAa,WAAW;AAC1C;AAEO,SAAS,gBAAgB,SAAiB,UAAkB,UAAkB,MAAc;AACjG,QAAM,QAAQ,WAAW,WAAW,YAAY;AAChD,QAAM,eAAe,gBAAgB,OAAO,UAAU,IAAI;AAC1D,SAAO,WAAW,cAAc,UAAU,QAAQ;AACpD;AAEO,SAAS,qBAAqB,OAAe,MAAc;AAChE,MAAI,eAAe;AACnB,MAAI,aAAa,KAAK,SAAS;AAC/B,MAAI,aAAa,WAAW,QAAQ,GAAG;AACvC,MAAI,YAAY,cAAc,IAAI,WAAW,SAAS,aAAa;AACnE,MAAI,YAAY,GAAG;AACjB,QAAI,MAAM,KAAK,IAAI,IAAI,SAAS;AAChC,mBAAe,KAAK,MAAM,eAAe,GAAG,IAAI;EAClD;AACA,SAAO;AACT;AAEO,SAAS,gBAAgB,OAAe,KAAyB,KAAyB,MAAsB;AACrH,QAAM,OAAO,GAAG;AAChB,QAAM,OAAO,GAAG;AAChB,MAAI,aAAa,SAAS,MAAM,GAAG,IAAI,IAAI,QAAQ;AACnD,MAAI,eAAe;IACjB,KAAK,IAAI,SAAS,IAAI,KAAK,OAAO,QAAQ,KAAK,KAAK,SAAS,KAAK,OAAO,KAAK,IAAI,SAAS,KAAK,QAAQ;IACxG;EACF;AAEA,MAAI,CAAC,MAAM,GAAG,GAAG;AACf,QAAI,eAAe,KAAK;AACtB,qBAAe;IACjB,WAAW,CAAC,MAAM,GAAG,KAAK,eAAe,KAAK;AAC5C,qBAAe,MAAM,KAAK,MAAM,sBAAsB,MAAM,OAAO,MAAM,IAAI,CAAC,IAAI;IACpF;EACF,WAAW,CAAC,MAAM,GAAG,KAAK,eAAe,KAAK;AAC5C,mBAAe,KAAK,MAAM,qBAAqB,MAAM,MAAM,IAAI,CAAC,IAAI;EACtE;AAGA,iBAAe,qBAAqB,cAAc,IAAI;AAEtD,SAAO;AACT;AAEA,SAAS,gBAAmB,QAAa,OAAe,OAAU;AAChE,MAAI,OAAO,KAAK,MAAM,MAAO,QAAO;AACpC,SAAO,CAAC,GAAG,OAAO,MAAM,GAAG,KAAK,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ,CAAC,CAAC;AACtE;AASO,SAAS,sBAAsB,OAAe,KAAmB;AACtE,QAAM,kBAAkB,mBAAmB,OAAO,IAAI,QAAQ,IAAI,GAAG;AACrE,QAAM,kBAAkB,mBAAmB,OAAO,IAAI,QAAQ,IAAI,GAAG;AACrE,MAAI,aAAa,IAAI,OAAO,MAAM;AAElC,SAAO,SAAS,SAAS,OAAe;AACtC,QAAI,YAAY,gBAAgB,OAAO,iBAAiB,iBAAiB,IAAI,IAAI;AACjF,iBAAa,gBAAgB,YAAY,OAAO,KAAK;AACrD,eAAW,KAAK,IAAI;AACpB,WAAO;EACT;AACF;AAEO,SAAS,iBAAiB,OAAe,KAAmB;AACjE,QAAM,YAAY,IAAI,OAAO,KAAK,IAAI,IAAI;AAC1C,SAAO,sBAAsB,OAAO,GAAG,EAAE,SAAS;AACpD;AAEO,SAAS,qBAAqB,OAAe,KAAmB;AACrE,QAAM,YAAY,IAAI,OAAO,KAAK,IAAI,IAAI;AAC1C,SAAO,sBAAsB,OAAO,GAAG,EAAE,SAAS;AACpD;AAEO,SAAS,qBAAqB,QAAkB,aAAqB;AAC1E,MAAI,cAAc,OAAO,UAAU,CAAC,UAAU,cAAc,QAAQ,CAAC;AAGrE,MAAI,gBAAgB,GAAG;AACrB,WAAO;EACT;AAGA,MAAI,gBAAgB,IAAI;AACtB,WAAO,OAAO,SAAS;EACzB;AAEA,MAAI,cAAc,OAAO,cAAc,CAAC;AACxC,MAAI,aAAa,OAAO,WAAW;AAGnC,MAAI,KAAK,IAAI,cAAc,WAAW,IAAI,KAAK,IAAI,aAAa,WAAW,GAAG;AAC5E,WAAO,cAAc;EACvB;AAEA,SAAO;AACT;AAEO,SAAS,eAAe,QAAkB,UAAkB,UAAkB,KAAa;AAChG,SAAO,OAAO,IAAI,CAAC,OAAO,UAAU;AAClC,UAAM,MAAM,UAAU,IAAI,WAAW,OAAO,QAAQ,CAAC,IAAI;AACzD,UAAM,MAAM,UAAU,OAAO,SAAS,IAAI,WAAW,OAAO,QAAQ,CAAC,IAAI;AACzE,WAAO,EAAE,KAAK,KAAK,MAAM;EAC3B,CAAC;AACH;AAEO,SAAS,oBAAoB,QAAkB,QAAkB;AACtE,QAAM,QAAQ,EAAE,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,CAAC,EAAE;AAC/C,QAAM,SAAS,EAAE,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,CAAC,EAAE;AAEhD,SAAO,SAAS,SAAS,OAAe;AACtC,QAAI,MAAM,QAAQ,MAAM,OAAO,OAAO,QAAQ,OAAO,IAAK,QAAO,OAAO;AACxE,UAAM,SAAS,OAAO,MAAM,OAAO,QAAQ,MAAM,MAAM,MAAM;AAC7D,WAAO,OAAO,MAAM,SAAS,QAAQ,MAAM;EAC7C;AACF;;;AClJA,IAAMC,aAAY,CAAC,OAAoB,GAAG,cAAc,eAAe;AAEvE,SAAS,cAAc,IAAiB,SAA4B;AAClE,QAAM,EAAE,OAAO,oBAAoB,WAAW,QAAQ,IAAI;AAC1D,QAAM,QAAQA,WAAU,EAAE,EAAE,IAAI,EAAE;AAClC,SAAO,OAAO,yBAAyB,OAAO,QAAQ,KAAK,CAAC;AAC9D;AAEO,SAAS,gBAAgB,IAAiB,OAAe,SAA4B,CAAC,GAAG;;AAC9F,QAAM,aAAa,cAAc,IAAI,MAAM;AAC3C,mBAAW,QAAX,mBAAgB,KAAK,IAAI;AACzB,KAAG,aAAa,SAAS,KAAK;AAChC;AAgBO,SAAS,wBAAwB,IAAwB,SAA4B;AAC1F,QAAM,EAAE,OAAO,UAAU,KAAK,IAAI;AAElC,MAAI,CAAC,GAAI;AAET,QAAM,MAAMC,WAAU,EAAE;AACxB,MAAI,EAAE,cAAc,IAAI,kBAAmB;AAE3C,kBAAgB,IAAI,GAAG,KAAK,EAAE;AAC9B,KAAG,cAAc,IAAI,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC;AACtD;AC3CO,SAAS,eAAe,IAAiB;AAC9C,MAAI,cAAc,EAAE,EAAG,QAAO,GAAG;MAC5B,QAAO,GAAG,QAAQ,MAAM;AAC/B;AAEA,SAAS,cAAc,IAAmF;AACxG,SAAO,GAAG,QAAQ,iCAAiC;AACrD;AAEA,SAAS,eAAe,IAAoC,UAAsB;AAChF,MAAI,CAAC,GAAI;AACT,QAAM,OAAO,eAAe,EAAE;AAC9B,+BAAM,iBAAiB,SAAS,UAAU,EAAE,SAAS,KAAK;AAC1D,SAAO,MAAM;AACX,iCAAM,oBAAoB,SAAS;EACrC;AACF;AAEA,SAAS,sBAAsB,IAAoC,UAAuC;AACxG,QAAM,WAAW,yBAAI,QAAQ;AAC7B,MAAI,CAAC,SAAU;AACf,WAAS,SAAS,QAAQ;AAC1B,QAAM,MAAM,SAAS,cAAc,eAAe;AAClD,QAAM,MAAM,IAAI,IAAI,iBAAiB,MAAM,SAAS,SAAS,QAAQ,CAAC;AACtE,MAAI,QAAQ,UAAU;IACpB,YAAY;IACZ,iBAAiB,CAAC,UAAU;EAC9B,CAAC;AACD,SAAO,MAAM,IAAI,WAAW;AAC9B;AAWO,SAAS,iBAAiB,IAAwB,SAA6B;AACpF,MAAI,CAAC,GAAI;AAET,QAAM,EAAE,0BAA0B,YAAY,IAAI;AAElD,QAAM,WAAW,CAAC,eAAe,IAAI,WAAW,GAAG,sBAAsB,IAAI,wBAAwB,CAAC;AAEtG,SAAO,MAAM;AACX,aAAS,QAAQ,CAAC,YAAY,oCAAW;EAC3C;AACF;;;AC3CO,SAAS,iBAAiB,SAA6B,UAA+B;AAC3F,MAAI,CAAC,SAAS;AACZ,aAAS,MAAS;AAClB;EACF;AAEA,WAAS,EAAE,OAAO,QAAQ,aAAa,QAAQ,QAAQ,aAAa,CAAC;AAErE,QAAM,MAAM,QAAQ,cAAc,eAAe;AAEjD,QAAM,WAAW,IAAI,IAAI,eAAe,CAAC,YAAY;AACnD,QAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,CAAC,QAAQ,OAAQ;AAEhD,UAAM,CAAC,KAAK,IAAI;AAChB,QAAI;AACJ,QAAI;AAEJ,QAAI,mBAAmB,OAAO;AAC5B,YAAM,kBAAkB,MAAM,eAAe;AAC7C,YAAM,aAAa,MAAM,QAAQ,eAAe,IAAI,gBAAgB,CAAC,IAAI;AACzE,cAAQ,WAAW,YAAY;AAC/B,eAAS,WAAW,WAAW;IACjC,OAAO;AACL,cAAQ,QAAQ;AAChB,eAAS,QAAQ;IACnB;AAEA,aAAS,EAAE,OAAO,OAAO,CAAC;EAC5B,CAAC;AAED,WAAS,QAAQ,SAAS,EAAE,KAAK,aAAa,CAAC;AAE/C,SAAO,MAAM,SAAS,UAAU,OAAO;AACzC;AChCO,SAAS,kBAAgD,SAAsC;AACpG,QAAM,EAAE,UAAU,kBAAkB,MAAM,SAAS,IAAI;AAEvD,QAAM,WAA4C,CAAC;AAEnD,MAAI,YAAsB;AAE1B,WAAS,UAAU;AACjB,UAAM,WAAW,SAAS;AAC1B,gBAAY,SAAS,CAAC;AACtB,UAAM,MAAM,SAAS;MAAI,CAAC,SAAS,UACjC,iBAAiB,SAAS,CAAC,SAAS;AAClC,iBAAS,MAAM,KAAK;MACtB,CAAC;IACH;AACA,aAAS,KAAK,GAAG,GAAG;EACtB;AAEA,UAAQ;AAER,MAAI,iBAAiB;AACnB,UAAM,KAAK,cAAc,WAAW,OAAO;AAC3C,aAAS,KAAK,EAAE;EAClB;AAEA,SAAO,MAAM;AACX,aAAS,QAAQ,CAAC,YAAY;AAC5B;IACF,CAAC;EACH;AACF;AAEA,SAAS,cAAc,IAAwB,IAAgB;;AAC7D,MAAI,CAAC,MAAM,CAAC,GAAG,cAAe;AAC9B,QAAM,QAAM,QAAG,kBAAH,mBAAkB,gBAAe;AAC7C,QAAM,WAAW,IAAI,IAAI,iBAAiB,MAAM;AAC9C,OAAG;EACL,CAAC;AACD,WAAS,QAAQ,GAAG,eAAe,EAAE,WAAW,KAAK,CAAC;AACtD,SAAO,MAAM;AACX,aAAS,WAAW;EACtB;AACF;;;AElDA,IAAM,cAAc,CAAC,WAAe,+BAAO,YAAY,UAAS;AAEhE,IAAM,eAAe,CAAC,GAAU,MAAsB;AACpD,MAAI,EAAE,WAAW,EAAE,OAAQ,QAAO;AAClC,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAG,QAAO;EACnC;AACA,SAAO;AACT;AAEO,IAAM,UAAU,CAAC,GAAQ,MAAoB;AAClD,MAAI,OAAO,GAAG,GAAG,CAAC,EAAG,QAAO;AAE5B,MAAK,KAAK,QAAQ,KAAK,QAAU,KAAK,QAAQ,KAAK,KAAO,QAAO;AAEjE,MAAI,QAAO,uBAAG,aAAY,cAAc,QAAO,uBAAG,aAAY,YAAY;AACxE,WAAO,EAAE,QAAQ,CAAC;EACpB;AAEA,MAAI,OAAO,MAAM,cAAc,OAAO,MAAM,YAAY;AACtD,WAAO,EAAE,SAAS,MAAM,EAAE,SAAS;EACrC;AAEA,MAAI,YAAY,CAAC,KAAK,YAAY,CAAC,GAAG;AACpC,WAAO,aAAa,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,CAAC;EAClD;AAEA,MAAI,EAAE,OAAO,MAAM,aAAa,EAAE,OAAO,MAAM,UAAW,QAAO;AAEjE,QAAM,OAAO,OAAO,KAAK,KAAK,uBAAO,OAAO,IAAI,CAAC;AACjD,QAAM,SAAS,KAAK;AAEpB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAM,SAAS,QAAQ,IAAI,GAAG,KAAK,CAAC,CAAC;AACrC,QAAI,CAAC,OAAQ,QAAO;EACtB;AAEA,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,UAAM,MAAM,KAAK,CAAC;AAClB,QAAI,CAAC,QAAQ,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,EAAG,QAAO;EACvC;AAEA,SAAO;AACT;AG3CO,SAAS,QAAuD,KAAW;AAChF,MAAI,CAAC,cAAc,GAAG,KAAK,QAAQ,QAAW;AAC5C,WAAO;EACT;AAEA,QAAM,OAAO,QAAQ,QAAQ,GAAG,EAAE,OAAO,CAAC,QAAQ,OAAO,QAAQ,QAAQ;AACzE,QAAM,WAAuB,CAAC;AAC9B,aAAW,OAAO,MAAM;AACtB,UAAM,QAAS,IAAY,GAAG;AAC9B,QAAI,UAAU,QAAW;AACvB,eAAS,GAAc,IAAI,QAAQ,KAAK;IAC1C;EACF;AACA,SAAO;AACT;AAMA,IAAM,gBAAgB,CAAC,UAAe;AACpC,SAAO,SAAS,OAAO,UAAU,YAAY,MAAM,gBAAgB;AACrE;ACpBO,SAAS,WAA2BC,QAAU,MAAmB;AACtE,QAAM,OAAa,CAAC;AACpB,QAAM,SAAe,CAAC;AAEtB,QAAM,SAAS,IAAI,IAAI,IAAI;AAE3B,aAAW,OAAOA,QAAO;AACvB,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,GAAG,IAAIA,OAAM,GAAG;IACzB,OAAO;AACL,WAAK,GAAG,IAAIA,OAAM,GAAG;IACvB;EACF;AAEA,SAAO,CAAC,QAAQ,IAAI;AACtB;AAEO,IAAM,mBAAmB,CAAiB,SAAsB;AACrE,SAAO,SAAS,MAAuBA,QAAc;AACnD,WAAO,WAAWA,QAAO,IAAI;EAC/B;AACF;;;AErBO,IAAM,UAAU,cAAc,QAAQ,EAAE;EAC7C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AAEO,IAAM,QAAQ,QAAQ,MAAM;AGNnC,SAAS,UAAa,OAAoB;AACxC,QAAM,aAAa,MAAM,CAAC;AAC1B,QAAM,YAAY,MAAM,MAAM,SAAS,CAAC;AACxC,SAAO,CAAC,YAAY,SAAS;AAC/B;AAEO,SAAS,gBAAgB,KAAU;AACxC,QAAM,CAAC,cAAc,WAAW,IAAI,UAAU,IAAI,YAAY;AAE9D,MAAI,IAAI,aAAa,WAAW,GAAG;AACjC,QAAI,IAAI,WAAW,UAAU;AAC3B,YAAM,aAAa,IAAI,aAAa,CAAC,IAAI;AACzC,YAAM,QAAQ,aAAa,GAAG,IAAI,aAAa,CAAC,CAAC,MAAM;AACvD,YAAM,MAAM,aAAa,QAAQ,GAAG,MAAM,IAAI,aAAa,CAAC,CAAC;AAE7D,aAAO,EAAE,OAAO,IAAI;IACtB;AAEA,WAAO,EAAE,OAAO,MAAM,KAAK,GAAG,MAAM,WAAW,IAAI;EACrD;AAEA,SAAO,EAAE,OAAO,GAAG,YAAY,KAAK,KAAK,GAAG,MAAM,WAAW,IAAI;AACnE;AAEA,SAAS,cAAc,KAAyD;AAC9E,MAAI,IAAI,YAAY;AAClB,WAAO;MACL,UAAU;MACV,QAAQ;MACR,KAAK;IACP;EACF;AAEA,SAAO;IACL,UAAU;IACV,CAAC,IAAI,QAAQ,UAAU,MAAM,GAAG;IAChC,CAAC,IAAI,QAAQ,SAAS,OAAO,GAAG;EAClC;AACF;AAMA,SAAS,uBAAuB,KAAoB;AAClD,QAAM,EAAE,SAAS,EAAE,IAAI,IAAI,aAAa,CAAC;AACzC,QAAM,WAAW,oBAAoB,CAAC,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;AAClF,SAAO,WAAW,SAAS,IAAI,KAAK,EAAE,QAAQ,CAAC,CAAC;AAClD;AAEA,SAAS,yBAAyB,KAAoB;AACpD,QAAM,EAAE,QAAQ,EAAE,IAAI,IAAI,aAAa,CAAC;AAExC,MAAI,IAAI,OAAO;AACb,UAAMC,YAAW,oBAAoB,CAAC,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAChF,WAAO,KAAK,WAAWA,UAAS,IAAI,KAAK,EAAE,QAAQ,CAAC,CAAC;EACvD;AAEA,QAAM,WAAW,oBAAoB,CAAC,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAChF,SAAO,WAAW,SAAS,IAAI,KAAK,EAAE,QAAQ,CAAC,CAAC;AAClD;AAEA,SAAS,UAAU,KAAoB,SAAiB;AACtD,MAAI,IAAI,mBAAmB,SAAU,QAAO,GAAG,OAAO;AACtD,QAAM,SAAS,IAAI,aAAa,uBAAuB,GAAG,IAAI,yBAAyB,GAAG;AAC1F,SAAO,QAAQ,OAAO,OAAO,MAAM;AACrC;AAEA,SAAS,eAAe,KAAoB;AAC1C,MAAI,UAAU,gBAAgB,IAAI,OAAO,IAAI,KAAK,IAAI,GAAG,IAAI;AAC7D,SAAO,UAAU,KAAK,OAAO;AAC/B;AAEA,SAAS,cAAc,KAAU;AAC/B,MAAI,aAAmC;AACvC,MAAI,IAAI,mBAAmB,aAAa,CAAC,IAAI,sBAAsB;AACjE,iBAAa;EACf;AACA,SAAO;AACT;AAEA,SAAS,cAAc,KAAU,OAAsB;AACrD,QAAM,gBAAgB,IAAI,aAAa,WAAW;AAClD,SAAO;IACL,YAAY,cAAc,GAAG;IAC7B,UAAU;IACV,WAAW;IACX,CAAC,aAAa,GAAG,6BAA6B,KAAK;EACrD;AACF;AAMA,SAAS,kBAAyB;AAChC,SAAO;IACL,aAAa;IACb,YAAY;IACZ,kBAAkB;IAClB,UAAU;EACZ;AACF;AAMA,SAAS,aAAa,KAAiB;AACrC,QAAM,QAAQ,gBAAgB,GAAG;AAEjC,QAAM,eAAe,IAAI,MAAM,OAAc,CAAC,QAAQ,OAAO,UAAU;AACrE,UAAM,SAAS,eAAe,EAAE,GAAG,KAAK,MAAM,CAAC;AAC/C,WAAO,EAAE,GAAG,QAAQ,CAAC,yBAAyB,KAAK,EAAE,GAAG,OAAO;EACjE,GAAG,CAAC,CAAC;AAEL,SAAO;IACL,GAAG;IACH,4BAA4B,IAAI,aAAa,oBAAoB,IAAI,QAAQ,oBAAoB;IACjG,wBAAwB,MAAM;IAC9B,sBAAsB,MAAM;EAC9B;AACF;AAMA,SAAS,eACP,KACA,OACO;AACP,SAAO;;IAEL,YAAY,cAAc,GAAG;IAC7B,UAAU;IACV,eAAe;;IAEf,CAAC,IAAI,eAAe,qBAAqB,QAAQ,GAAG,eAAe,EAAE,GAAG,KAAK,MAAM,CAAC;IACpF,WAAW;IACX,QAAQ,IAAI,eAAgB,IAAI,QAAQ,QAAQ,SAAU;IAC1D,QAAQ,CAAC,IAAI,eAAe,QAAQ;EACtC;AACF;AAMA,SAAS,sBAA6B;AACpC,SAAO;IACL,YAAY;IACZ,kBAAkB;IAClB,eAAe;IACf,UAAU;EACZ;AACF;AAEO,IAAM,iBAAiB;EAC5B;EACA;EACA;EACA;EACA;EACA;AACF;ADtKO,IAAM,MAAM,YAAY;EAC7B,GAAG;EACH,WAAW,CAAC,QAAA;;AAAa,sBAAI,QAAJ,mBAAS,SAAQ,UAAU,IAAI,EAAE;;EAC1D,YAAY,CAAC,KAAU,UAAA;;AAAkB,4BAAI,QAAJ,mBAAS,UAAT,4BAAiB,WAAU,UAAU,IAAI,EAAE,UAAU,KAAK;;EACnG,kBAAkB,CAAC,KAAU,UAAA;;AAAkB,4BAAI,QAAJ,mBAAS,gBAAT,4BAAuB,WAAU,UAAU,IAAI,EAAE,UAAU,KAAK;;EAC/G,cAAc,CAAC,QAAA;;AAAa,sBAAI,QAAJ,mBAAS,YAAW,UAAU,IAAI,EAAE;;EAChE,YAAY,CAAC,QAAA;;AAAa,sBAAI,QAAJ,mBAAS,UAAS,UAAU,IAAI,EAAE;;EAC5D,YAAY,CAAC,QAAA;;AAAa,sBAAI,QAAJ,mBAAS,UAAS,UAAU,IAAI,EAAE;;EAC5D,YAAY,CAAC,QAAA;;AAAa,sBAAI,QAAJ,mBAAS,UAAS,UAAU,IAAI,EAAE;;EAC5D,gBAAgB,CAAC,QAAA;;AAAa,sBAAI,QAAJ,mBAAS,cAAa,UAAU,IAAI,EAAE;;EACpE,aAAa,CAAC,KAAU,UAAA;;AAAkB,4BAAI,QAAJ,mBAAS,WAAT,4BAAkB,WAAU,UAAU,IAAI,EAAE,WAAW,KAAK;;EAEtG,WAAW,CAAC,QAAa,IAAI,QAAQ,KAAK,IAAI,UAAU,GAAG,CAAC;EAC5D,YAAY,CAAC,KAAU,UAAkB,IAAI,QAAQ,KAAK,IAAI,WAAW,KAAK,KAAK,CAAC;EACpF,kBAAkB,CAAC,KAAU,UAAkB,IAAI,QAA0B,KAAK,IAAI,iBAAiB,KAAK,KAAK,CAAC;EAClH,cAAc,CAAC,QAAa,IAAI,QAAQ,KAAK,IAAI,aAAa,GAAG,CAAC;EAClE,aAAa,CAAC,QAAa,SAAS,IAAI,aAAa,GAAG,GAAG,eAAe;EAC1E,YAAY,CAAC,QAAa,IAAI,YAAY,GAAG,EAAE,CAAC;EAChD,YAAY,CAAC,QAAa,IAAI,QAAQ,KAAK,IAAI,WAAW,GAAG,CAAC;EAE9D,kBAAkB,KAAU,OAAc;AACxC,UAAM,YAAY,IAAI,aAAa,GAAG;AACtC,QAAI,CAAC,UAAW;AAChB,UAAM,gBAAgB,iBAAiB,OAAO,SAAS;AACvD,UAAM,UAAU,cAAc,gBAAgB;MAC5C,aAAa,IAAI;MACjB,KAAK,IAAI;MACT,UAAU,EAAE,GAAG,KAAK;IACtB,CAAC;AACD,WAAO,gBAAgB,SAAS,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI;EAC5D;EACA,oBAAoB,KAAU;AAC5B,UAAM,aAAa,MAAM,KAAK,IAAI,KAAK;AACvC,eAAW,QAAQ,CAAC,OAAO,UAAU;AACnC,YAAM,UAAU,IAAI,iBAAiB,KAAK,KAAK;AAC/C,UAAI,CAAC,QAAS;AACd,8BAAwB,SAAS,EAAE,MAAM,CAAC;IAC5C,CAAC;EACH;AACF,CAAC;AEpCM,SAAS,gBAAgB,KAAU,YAAsB;AAC9D,SAAO,WAAW,IAAI,CAAC,OAAO,OAAO,WAAW;AAC9C,WAAO,eAAe,EAAE,GAAG,KAAK,OAAO,OAAO,GAAG,OAAO,KAAK;EAC/D,CAAC;AACH;AAMO,SAAS,gBAAgB,KAAU,OAAe;AACvD,SAAO,eAAe,IAAI,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,qBAAqB,EAAE,KAAK;AACrF;AAEO,SAAS,eAAe,KAAU,OAAe,OAAe;AACrE,QAAM,QAAQ,gBAAgB,KAAK,KAAK;AACxC,QAAM,YAAY,gBAAgB,OAAO,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI;AACnE,SAAO,WAAW,WAAW,MAAM,KAAK,MAAM,GAAG;AACnD;AAEO,SAAS,UAAU,KAAU,OAAgB,MAAe;AACjE,QAAM,MAAM,SAAS,IAAI;AACzB,QAAM,QAAQ,gBAAgB,KAAK,GAAG;AACtC,QAAM,aAAa,qBAAqB,KAAK;IAC3C,GAAG;IACH,MAAM,QAAQ,IAAI;IAClB,QAAQ,IAAI;EACd,CAAC;AACD,aAAW,GAAG,IAAI,WAAW,WAAW,GAAG,GAAG,MAAM,KAAK,MAAM,GAAG;AAClE,SAAO;AACT;AAEO,SAAS,UAAU,KAAU,OAAgB,MAAe;AACjE,QAAM,MAAM,SAAS,IAAI;AACzB,QAAM,QAAQ,gBAAgB,KAAK,GAAG;AACtC,QAAM,aAAa,iBAAiB,KAAK;IACvC,GAAG;IACH,MAAM,QAAQ,IAAI;IAClB,QAAQ,IAAI;EACd,CAAC;AACD,aAAW,GAAG,IAAI,WAAW,WAAW,GAAG,GAAG,MAAM,KAAK,MAAM,GAAG;AAClE,SAAO;AACT;AAEO,SAAS,gBAAgB,KAAU,YAAoB;AAC5D,SAAO,qBAAqB,IAAI,OAAO,UAAU;AACnD;AAEO,SAAS,YAAY,SAAmB,MAAgB;AAC7D,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,QAAQ,KAAK,CAAC;AACpB,YAAQ,CAAC,IAAI;EACf;AACF;AH/CO,SAAS,QAA6BC,QAAc,MAAY,WAA6C;AAClH,QAAM,YAAYA,OAAM,QAAQ,YAAY;AAC5C,QAAM,iBAAiBA,OAAM,QAAQ,iBAAiB;AACtD,QAAM,cAAcA,OAAM,QAAQ;AAElC,QAAM,UAAUA,OAAM,QAAQ,OAAO;AACrC,QAAM,WAAWA,OAAM,QAAQ,UAAU;AAEzC,QAAM,WAAWA,OAAM,QAAQ;AAC/B,QAAM,UAAUA,OAAM,QAAQ;AAC9B,QAAM,cAAcA,OAAM,QAAQ;AAElC,QAAM,eAAeA,OAAM,QAAQ,gBAAgB;AACnD,QAAM,aAAaA,OAAM,QAAQ,gBAAgB;AAEjD,WAAS,kBAAkB,OAAe;AACxC,WAAOC,gBAAgB,OAAOD,OAAM,QAAQ,KAAKA,OAAM,QAAQ,GAAG;EACpE;AAEA,WAAS,kBAAkB,SAAiB;AAC1C,WAAOE,gBAAgB,SAASF,OAAM,QAAQ,KAAKA,OAAM,QAAQ,KAAKA,OAAM,QAAQ,IAAI;EAC1F;AAEA,SAAO;IACL,OAAOA,OAAM,QAAQ;IACrB;IACA;IACA,SAAS,OAAO;AACd,WAAK,EAAE,MAAM,aAAa,MAAa,CAAC;IAC1C;IACA,cAAc,OAAO;AACnB,aAAO,YAAY,KAAK;IAC1B;IACA,cAAc,OAAO,OAAO;AAC1B,WAAK,EAAE,MAAM,aAAa,OAAO,MAAM,CAAC;IAC1C;IACA,iBAAiB;IACjB,iBAAiB;IACjB,gBAAgB,OAAO;AACrB,aAAO,kBAAkB,YAAY,KAAK,CAAC;IAC7C;IACA,gBAAgB,OAAO,SAAS;AAC9B,YAAM,QAAQ,kBAAkB,OAAO;AACvC,WAAK,EAAE,MAAM,aAAa,OAAO,MAAM,CAAC;IAC1C;IACA,YAAY,OAAO;AACjB,aAAO,gBAAgBA,OAAM,SAAS,KAAK,EAAE;IAC/C;IACA,YAAY,OAAO;AACjB,aAAO,gBAAgBA,OAAM,SAAS,KAAK,EAAE;IAC/C;IACA,UAAU,OAAO;AACf,WAAK,EAAE,MAAM,aAAa,MAAM,CAAC;IACnC;IACA,UAAU,OAAO;AACf,WAAK,EAAE,MAAM,aAAa,MAAM,CAAC;IACnC;IACA,QAAQ;AACN,UAAI,CAAC,YAAa;AAClB,WAAK,EAAE,MAAM,SAAS,OAAO,EAAE,CAAC;IAClC;IAEA,gBAAgB;AACd,aAAO,UAAU,MAAM;QACrB,GAAG,MAAM,MAAM;QACf,KAAKA,OAAM,QAAQ;QACnB,iBAAiB,SAAS,QAAQ;QAClC,oBAAoBA,OAAM,QAAQ;QAClC,gBAAgB,SAAS,OAAO;QAChC,iBAAiB,SAAS,QAAQ;QAClC,cAAc,SAAS,OAAO;QAC9B,IAAI,IAAI,WAAWA,OAAM,OAAO;QAChC,SAAS,IAAI,iBAAiBA,OAAM,SAAS,CAAC;QAC9C,QAAQ,OAAO;;AACb,cAAI,CAAC,YAAa;AAClB,gBAAM,eAAe;AACrB,oBAAI,WAAWA,OAAM,OAAO,MAA5B,mBAA+B;QACjC;QACA,OAAO;UACL,YAAY;UACZ,kBAAkB;QACpB;MACF,CAAC;IACH;IAEA,eAAe;AACb,aAAO,UAAU,QAAQ;QACvB,GAAG,MAAM,KAAK;QACd,iBAAiB,SAAS,QAAQ;QAClC,oBAAoBA,OAAM,QAAQ;QAClC,iBAAiB,SAAS,QAAQ;QAClC,gBAAgB,SAAS,OAAO;QAChC,cAAc,SAAS,OAAO;QAC9B,IAAI,IAAI,UAAUA,OAAM,OAAO;QAC/B,KAAKA,OAAM,QAAQ;QACnB,OAAO,IAAI,aAAaA,OAAM,OAAO;MACvC,CAAC;IACH;IAEA,oBAAoB;AAClB,aAAO,UAAU,QAAQ;QACvB,GAAG,MAAM,UAAU;QACnB,KAAKA,OAAM,QAAQ;QACnB,iBAAiB,SAAS,QAAQ;QAClC,oBAAoBA,OAAM,QAAQ;QAClC,gBAAgB,SAAS,OAAO;QAChC,cAAc,SAAS,OAAO;QAC9B,IAAI,IAAI,eAAeA,OAAM,OAAO;MACtC,CAAC;IACH;IAEA,gBAAgB;AACd,aAAO,UAAU,QAAQ;QACvB,GAAG,MAAM,MAAM;QACf,KAAKA,OAAM,QAAQ;QACnB,IAAI,IAAI,WAAWA,OAAM,OAAO;QAChC,iBAAiB,SAAS,QAAQ;QAClC,gBAAgB,SAAS,OAAO;QAChC,iBAAiB,SAAS,QAAQ;QAClC,oBAAoBA,OAAM,QAAQ;QAClC,cAAc,SAAS,OAAO;QAC9B,OAAO,EAAE,UAAU,WAAW;MAChC,CAAC;IACH;IAEA,cAAcG,QAAO;;AACnB,YAAM,EAAE,QAAQ,GAAG,KAAK,IAAIA;AAE5B,YAAM,QAAQ,YAAY,KAAK;AAC/B,YAAM,QAAQ,gBAAgBH,OAAM,SAAS,KAAK;AAClD,YAAM,aAAY,WAAAA,OAAM,SAAQ,qBAAd,4BAAiC,EAAE,OAAO,MAAM;AAClE,YAAM,aAAa,MAAM,QAAQ,SAAS,IAAI,UAAU,KAAK,IAAI;AACjE,YAAM,kBAAkB,MAAM,QAAQ,cAAc,IAAI,eAAe,KAAK,IAAI;AAEhF,aAAO,UAAU,QAAQ;QACvB,GAAG,MAAM,MAAM;QACf,KAAKA,OAAM,QAAQ;QACnB,cAAc;QACd,aAAa;QACb,IAAI,IAAI,WAAWA,OAAM,SAAS,KAAK;QACvC,iBAAiB,SAAS,QAAQ;QAClC,oBAAoBA,OAAM,QAAQ;QAClC,cAAc,SAAS,WAAWA,OAAM,QAAQ,iBAAiB,KAAK;QACtE,iBAAiB,SAAS,YAAYA,OAAM,QAAQ,iBAAiB,KAAK;QAC1E,WAAW;QACX,iBAAiB,SAAS,QAAQ;QAClC,cAAc;QACd,mBAAmB,mBAAmB,IAAI,WAAWA,OAAM,OAAO;QAClE,oBAAoBA,OAAM,QAAQ;QAClC,iBAAiB,MAAM;QACvB,iBAAiB,MAAM;QACvB,iBAAiB,YAAY,KAAK;QAClC,kBAAkB;QAClB,MAAM;QACN,UAAU,WAAW,SAAY;QACjC,OAAO,IAAI,cAAcA,OAAM,SAAS,KAAK;QAC7C,cAAc,OAAO;AACnB,cAAI,CAAC,YAAa;AAClB,eAAK,EAAE,MAAM,sBAAsB,MAAM,CAAC;AAC1C,gBAAM,gBAAgB;QACxB;QACA,SAAS;AACP,cAAI,CAAC,YAAa;AAClB,eAAK,MAAM;QACb;QACA,UAAU;AACR,cAAI,CAAC,YAAa;AAClB,eAAK,EAAE,MAAM,SAAS,MAAM,CAAC;QAC/B;QACA,UAAU,OAAO;AACf,cAAI,MAAM,iBAAkB;AAC5B,cAAI,CAAC,YAAa;AAElB,gBAAM,OAAO,aAAa,KAAK,IAAIA,OAAM,QAAQ;AAEjD,gBAAMI,UAAsB;YAC1B,UAAU;AACR,kBAAI,aAAc;AAClB,mBAAK,EAAE,MAAM,aAAa,MAAM,KAAK,UAAU,CAAC;YAClD;YACA,YAAY;AACV,kBAAI,aAAc;AAClB,mBAAK,EAAE,MAAM,aAAa,MAAM,KAAK,YAAY,CAAC;YACpD;YACA,YAAY;AACV,kBAAI,WAAY;AAChB,mBAAK,EAAE,MAAM,aAAa,MAAM,KAAK,YAAY,CAAC;YACpD;YACA,aAAa;AACX,kBAAI,WAAY;AAChB,mBAAK,EAAE,MAAM,aAAa,MAAM,KAAK,aAAa,CAAC;YACrD;YACA,SAAS;AACP,mBAAK,EAAE,MAAM,aAAa,MAAM,KAAK,SAAS,CAAC;YACjD;YACA,WAAW;AACT,mBAAK,EAAE,MAAM,aAAa,MAAM,KAAK,WAAW,CAAC;YACnD;YACA,OAAO;AACL,mBAAK,MAAM;YACb;YACA,MAAM;AACJ,mBAAK,KAAK;YACZ;UACF;AAEA,gBAAM,MAAM,YAAY,OAAOJ,OAAM,OAAO;AAC5C,gBAAM,OAAOI,QAAO,GAAG;AAEvB,cAAI,MAAM;AACR,iBAAK,KAAK;AACV,kBAAM,eAAe;AACrB,kBAAM,gBAAgB;UACxB;QACF;MACF,CAAC;IACH;IAEA,oBAAoBD,QAAO;AACzB,YAAM,EAAE,QAAQ,GAAG,KAAK,IAAIA;AAC5B,aAAO,UAAU,MAAM;QACrB,MACE,SAASH,OAAM,QAAQ,OAAOA,OAAM,QAAQ,QAAQA,OAAM,QAAQ,MAAM,SAAS,IAAI,OAAO,MAAM;QACpG,MAAMA,OAAM,QAAQ;QACpB,MAAM;QACN,QAAQ;QACR,cAAcA,OAAM,QAAQ,MAAM,KAAK;QACvC,IAAI,IAAI,iBAAiBA,OAAM,SAAS,KAAK;MAC/C,CAAC;IACH;IAEA,gBAAgB;AACd,aAAO,UAAU,QAAQ;QACvB,IAAI,IAAI,WAAWA,OAAM,OAAO;QAChC,GAAG,MAAM,MAAM;QACf,KAAKA,OAAM,QAAQ;QACnB,iBAAiB,SAAS,QAAQ;QAClC,cAAc,SAAS,OAAO;QAC9B,gBAAgB,SAAS,OAAO;QAChC,iBAAiB,SAAS,QAAQ;QAClC,oBAAoBA,OAAM,QAAQ;QAClC,OAAO,IAAI,cAAcA,OAAM,OAAO;MACxC,CAAC;IACH;IAEA,kBAAkB;AAChB,aAAO,UAAU,QAAQ;QACvB,GAAG,MAAM,QAAQ;QACjB,KAAKA,OAAM,QAAQ;QACnB,IAAI,IAAI,aAAaA,OAAM,OAAO;QAClC,iBAAiB,SAAS,QAAQ;QAClC,iBAAiB,SAAS,QAAQ;QAClC,oBAAoBA,OAAM,QAAQ;QAClC,gBAAgB,SAAS,OAAO;QAChC,cAAc,SAAS,OAAO;QAC9B,OAAO,IAAI,gBAAgB;QAC3B,cAAc,OAAO;AACnB,cAAI,CAAC,YAAa;AAClB,cAAI,CAAC,YAAY,KAAK,EAAG;AACzB,cAAI,cAAc,KAAK,EAAG;AAE1B,gBAAM,QAAQ,cAAc,KAAK;AACjC,eAAK,EAAE,MAAM,gBAAgB,MAAM,CAAC;AAEpC,gBAAM,eAAe;AACrB,gBAAM,gBAAgB;QACxB;MACF,CAAC;IACH;IAEA,sBAAsB;AACpB,aAAO,UAAU,QAAQ;QACvB,GAAG,MAAM,YAAY;QACrB,MAAM;QACN,KAAKA,OAAM,QAAQ;QACnB,eAAe;QACf,oBAAoBA,OAAM,QAAQ;QAClC,OAAO,IAAI,oBAAoB;MACjC,CAAC;IACH;IAEA,eAAeG,QAAO;AACpB,YAAM,QAAQ,IAAI,eAAeH,OAAM,SAASG,OAAM,KAAK;AAC3D,UAAI;AAEJ,YAAM,QAAQH,OAAM,QAAQ,MAAM,CAAC;AACnC,YAAM,OAAOA,OAAM,QAAQ,MAAMA,OAAM,QAAQ,MAAM,SAAS,CAAC;AAE/D,UAAIG,OAAM,QAAQ,OAAO;AACvB,sBAAc;MAChB,WAAWA,OAAM,QAAQ,MAAM;AAC7B,sBAAc;MAChB,OAAO;AACL,sBAAc;MAChB;AAEA,aAAO,UAAU,QAAQ;QACvB,GAAG,MAAM,OAAO;QAChB,IAAI,IAAI,YAAYH,OAAM,SAASG,OAAM,KAAK;QAC9C,MAAM;QACN,KAAKH,OAAM,QAAQ;QACnB,oBAAoBA,OAAM,QAAQ;QAClC,cAAcG,OAAM;QACpB,iBAAiB,SAAS,QAAQ;QAClC,cAAc;QACd;MACF,CAAC;IACH;EACF;AACF;AIlTA,IAAM,cAAc,CAAC,GAAuB,MAA0B;AACpE,UAAO,uBAAG,YAAU,uBAAG,WAAS,uBAAG,aAAW,uBAAG;AACnD;AAEO,SAAS,QAAQ,aAAiC;AACvD,QAAM,MAAM,QAAQ,WAAW;AAC/B,SAAO;IACL;MACE,IAAI;MACJ,SAAS;MAET,SAAS;QACP,WAAW;QACX,gBAAgB;QAChB,KAAK;QACL,KAAK;QACL,MAAM;QACN,OAAO,CAAC,CAAC;QACT,QAAQ;QACR,aAAa;QACb,KAAK;QACL,uBAAuB;QACvB,UAAU;QACV,UAAU;QACV,GAAG;QACH,cAAc;QACd,kBAAkB;MACpB;MAEA,UAAU;QACR,cAAc,CAACE,SAAQA,KAAI,gBAAgB;QAC3C,YAAY,CAACA,SAAQA,KAAI,gBAAgB;QACzC,OAAO,CAACA,SAAQA,KAAI,gBAAgB,gBAAgBA,KAAI,QAAQ;QAChE,YAAY,CAACA,SAAQ,CAAC,CAACA,KAAI,YAAYA,KAAI;QAC3C,eAAe,CAACA,SAAQ,EAAEA,KAAI,YAAYA,KAAI;QAC9C,sBAAsB,CAACA,SAAQA,KAAI,aAAa;QAChD,aAAaA,MAAK;AAChB,iBAAOA,KAAI,MAAM,IAAI,CAAC,UAAU,MAAMJ,gBAAgB,OAAOI,KAAI,KAAKA,KAAI,GAAG,CAAC;QAChF;MACF;MAEA,OAAO;QACL,OAAO,CAAC,mBAAmB;MAC7B;MAEA,OAAO,CAAC,aAAa;MAErB,YAAY,CAAC,yBAAyB,iBAAiB;MAEvD,IAAI;QACF,WAAW;UACT;YACE,OAAO;YACP,SAAS;UACX;UACA,EAAE,SAAS,WAAW;QACxB;QACA,WAAW;UACT,SAAS;QACX;QACA,WAAW;UACT,SAAS;QACX;MACF;MAEA,QAAQ;QACN,MAAM;UACJ,IAAI;YACF,cAAc;cACZ,QAAQ;cACR,SAAS,CAAC,wBAAwB,mBAAmB,kBAAkB;YACzE;YACA,OAAO;cACL,QAAQ;cACR,SAAS;YACX;YACA,oBAAoB;cAClB,QAAQ;cACR,SAAS,CAAC,mBAAmB,kBAAkB;YACjD;UACF;QACF;QACA,OAAO;UACL,OAAO;UACP,IAAI;YACF,cAAc;cACZ,QAAQ;cACR,SAAS,CAAC,wBAAwB,mBAAmB,kBAAkB;YACzE;YACA,oBAAoB;cAClB,QAAQ;cACR,SAAS,CAAC,mBAAmB,kBAAkB;YACjD;YACA,WAAW;cACT,SAAS,CAAC,yBAAyB,mBAAmB;YACxD;YACA,WAAW;cACT,SAAS,CAAC,yBAAyB,mBAAmB;YACxD;YACA,MAAM;cACJ,SAAS,CAAC,wBAAwB,mBAAmB;YACvD;YACA,KAAK;cACH,SAAS,CAAC,wBAAwB,mBAAmB;YACvD;YACA,MAAM;cACJ,QAAQ;cACR,SAAS;YACX;UACF;QACF;QACA,UAAU;UACR,OAAO;UACP,YAAY;UACZ,IAAI;YACF,YAAY;cACV,QAAQ;cACR,SAAS;YACX;YACA,cAAc;cACZ,SAAS;YACX;UACF;QACF;MACF;IACF;IACA;MACE,QAAQ;QACN,UAAU,CAAC,MAAM,QAAQ,IAAI,SAAS;MACxC;MACA,YAAY;QACV,sBAAsBA,MAAK,MAAM,EAAE,eAAe,GAAG;AACnD,iBAAO,iBAAiB,IAAI,UAAUA,IAAG,GAAG;YAC1C,yBAAyB,UAAU;AACjCA,mBAAI,mBAAmB;YACzB;YACA,cAAc;AACZ,kBAAI,MAAMA,MAAK,eAAe,KAAK;YACrC;UACF,CAAC;QACH;QAEA,iBAAiBA,MAAK,MAAM,EAAE,KAAK,GAAG;AACpC,iBAAO,iBAAiB,IAAI,OAAOA,IAAG,GAAG;YACvC,cAAc,MAAM;AAClB,mBAAK,EAAE,MAAM,gBAAgB,OAAO,KAAK,MAAM,CAAC;YAClD;YACA,cAAc;AACZ,mBAAK,YAAY;YACnB;UACF,CAAC;QACH;QACA,gBAAgBA,MAAK;AACnB,cAAIA,KAAI,mBAAmB,aAAaA,KAAI,UAAW;AAEvD,iBAAO,kBAAkB;YACvB,UAAU,MAAM,IAAI,YAAYA,IAAG;YACnC,iBAAiB;YACjB,SAAS,MAAM;AACb,kBAAI,CAAC,QAAQ,YAAYA,KAAI,WAAW,IAAI,EAAG;AAC/CA,mBAAI,YAAY;YAClB;UACF,CAAC;QACH;MACF;MACA,SAAS;QACP,kBAAkBA,MAAK;AACrBA,eAAI,MAAM,QAAQ,CAAC,OAAO,UAAU;AAClC,kBAAM,UAAU,IAAI,iBAAiBA,MAAK,KAAK;AAC/C,gBAAI,SAAS,SAAS,KAAK;UAC7B,CAAC;QACH;QACA,kBAAkBA,MAAK;;AACrBA,qBAAI,qBAAJA,8BAAuB,EAAE,OAAOA,KAAI,MAAM;QAC5C;QACA,qBAAqBA,MAAK,KAAK;AAC7B,gBAAM,aAAa,IAAI,kBAAkBA,MAAK,IAAI,KAAK;AACvD,cAAI,cAAc,KAAM;AAExB,gBAAM,eAAe,gBAAgBA,MAAK,UAAU;AACpD,cAAI,aAAaA,MAAK,YAAY;QACpC;QACA,gBAAgBA,MAAK,KAAK;AACxB,cAAI,aAAaA,MAAK,IAAI,KAAK;QACjC;QACA,kBAAkBA,MAAK;AACrB,cAAI,aAAaA,MAAK,EAAE;QAC1B;QACA,gBAAgBA,MAAK,KAAK;AACxB,gBAAM,eAAe,IAAI,kBAAkBA,MAAK,IAAI,KAAK;AACzD,cAAI,gBAAgB,KAAM;AAE1B,gBAAM,QAAQ,eAAeA,MAAK,cAAcA,KAAI,YAAY;AAChE,cAAI,aAAaA,MAAKA,KAAI,cAAc,KAAK;QAC/C;QACA,iBAAiBA,MAAK;AACpB,cAAI,MAAM;AACR,kBAAM,UAAU,IAAI,WAAWA,MAAKA,KAAI,YAAY;AACpD,+CAAS,MAAM,EAAE,eAAe,KAAK;UACvC,CAAC;QACH;QACA,sBAAsBA,MAAK,KAAK;AAC9B,gBAAM,QAAQ,UAAUA,MAAK,IAAI,OAAO,IAAI,IAAI;AAChD,cAAI,MAAMA,MAAK,KAAK;QACtB;QACA,sBAAsBA,MAAK,KAAK;AAC9B,gBAAM,QAAQ,UAAUA,MAAK,IAAI,OAAO,IAAI,IAAI;AAChD,cAAI,MAAMA,MAAK,KAAK;QACtB;QACA,qBAAqBA,MAAK;AACxB,gBAAM,EAAE,IAAI,IAAI,gBAAgBA,MAAKA,KAAI,YAAY;AACrD,cAAI,aAAaA,MAAKA,KAAI,cAAc,GAAG;QAC7C;QACA,qBAAqBA,MAAK;AACxB,gBAAM,EAAE,IAAI,IAAI,gBAAgBA,MAAKA,KAAI,YAAY;AACrD,cAAI,aAAaA,MAAKA,KAAI,cAAc,GAAG;QAC7C;QACA,YAAYA,MAAK;AACf,gBAAM,QAAQ,gBAAgBA,MAAKA,KAAI,KAAK;AAC5C,cAAI,MAAMA,MAAK,KAAK;QACtB;QACA,gBAAgBA,MAAK,KAAK;AACxB,gBAAM,QAAQ,eAAeA,MAAK,IAAI,OAAO,IAAI,KAAK;AACtD,cAAI,aAAaA,MAAK,IAAI,OAAO,KAAK;QACxC;QACA,SAASA,MAAK,KAAK;AACjB,gBAAM,QAAQ,gBAAgBA,MAAK,IAAI,KAAK;AAC5C,cAAI,MAAMA,MAAK,KAAK;QACtB;MACF;IACF;EACF;AACF;AAEA,IAAM,SAAS;EACb,QAAQ,CAAC,QAAwB;;AAC/B,cAAI,kBAAJ,6BAAoB;MAClB,OAAO,MAAM,KAAK,IAAI,KAAK;IAC7B;AACA,QAAI,oBAAoB,GAAG;EAC7B;EACA,aAAa,CAAC,QAAwB;;AACpC,cAAI,kBAAJ,6BAAoB;MAClB,OAAO,MAAM,KAAK,IAAI,KAAK;MAC3B,cAAc,IAAI;IACpB;EACF;AACF;AAEA,IAAM,MAAM;EACV,cAAc,CAAC,KAAqB,OAAe,UAAkB;AACnE,QAAI,QAAQ,IAAI,MAAM,KAAK,GAAG,KAAK,EAAG;AACtC,QAAI,MAAM,KAAK,IAAI;AACnB,WAAO,OAAO,GAAG;EACnB;EACA,OAAO,CAAC,KAAqB,UAAoB;AAC/C,QAAI,QAAQ,IAAI,OAAO,KAAK,EAAG;AAC/B,gBAAY,IAAI,OAAO,KAAK;AAC5B,WAAO,OAAO,GAAG;EACnB;EACA,cAAc,CAAC,KAAqB,UAAkB;AACpD,QAAI,QAAQ,IAAI,cAAc,KAAK,EAAG;AACtC,QAAI,eAAe;AACnB,WAAO,YAAY,GAAG;EACxB;AACF;ACxRO,IAAM,QAAQ,YAAgC,EAAE;EACrD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF,CAAC;AAEM,IAAMC,cAAa,iBAA8C,KAAK;AAEtE,IAAM,aAAa,YAAwB,EAAE,CAAC,SAAS,MAAM,CAAC;AAC9D,IAAM,kBAAkB,iBAA6B,UAAU;", "names": ["parts", "query", "state", "set", "raf", "fn", "dom", "getPercentValue", "getWindow", "getWindow", "props", "getValue", "state", "getValuePercent", "getPercentValue", "props", "keyMap", "ctx", "splitProps"]}