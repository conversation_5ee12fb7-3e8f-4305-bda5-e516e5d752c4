{"version": 3, "sources": ["../../node_modules/vue3-timepicker/src/VueTimepicker.vue"], "sourcesContent": ["<script>\nimport { nextTick } from 'vue'\n\nconst CONFIG = {\n  HOUR_TOKENS: ['HH', 'H', 'hh', 'h', 'kk', 'k'],\n  MINUTE_TOKENS: ['mm', 'm'],\n  SECOND_TOKENS: ['ss', 's'],\n  APM_TOKENS: ['A', 'a'],\n  BASIC_TYPES: ['hour', 'minute', 'second', 'apm']\n}\n\nconst DEFAULT_OPTIONS = {\n  format: 'HH:mm',\n  minuteInterval: 1,\n  secondInterval: 1,\n  hourRange: null,\n  minuteRange: null,\n  secondRange: null,\n  hideDisabledHours: false,\n  hideDisabledMinutes: false,\n  hideDisabledSeconds: false,\n  hideDisabledItems: false,\n  advancedKeyboard: false,\n  hideDropdown: false,\n  blurDelay: 300,\n  manualInputTimeout: 1000,\n  dropOffsetHeight: 160\n}\n\nexport default {\n  name: 'VueTimepicker',\n\n  props: {\n    modelValue: { type: [ Object, String ] },\n    format: { type: String },\n    minuteInterval: { type: [ Number, String ] },\n    secondInterval: { type: [ Number, String ] },\n\n    hourRange: { type: Array },\n    minuteRange: { type: Array },\n    secondRange: { type: Array },\n\n    hideDisabledHours: { type: Boolean, default: false },\n    hideDisabledMinutes: { type: Boolean, default: false },\n    hideDisabledSeconds: { type: Boolean, default: false },\n    hideDisabledItems: { type: Boolean, default: false },\n\n    hideClearButton: { type: Boolean, default: false },\n    disabled: { type: Boolean, default: false },\n    closeOnComplete: { type: Boolean, default: false },\n\n    id: { type: String },\n    name: { type: String },\n    inputClass: { type: [ String, Object, Array ] },\n    placeholder: { type: String },\n    tabindex: { type: [ Number, String ], default: 0 },\n    inputWidth: { type: String },\n    autocomplete: { type: String, default: 'off' },\n\n    hourLabel: { type: String },\n    minuteLabel: { type: String },\n    secondLabel: { type: String },\n    apmLabel: { type: String },\n    amText: { type: String },\n    pmText: { type: String },\n\n    blurDelay: { type: [ Number, String ] },\n    advancedKeyboard: { type: Boolean, default: false },\n\n    lazy: { type: Boolean, default: false },\n    autoScroll: { type: Boolean, default: false },\n\n    dropDirection: { type: String, default: 'down' },\n    dropOffsetHeight: { type: [ Number, String ] },\n    containerId: { type: String },\n\n    manualInput: { type: Boolean, default: false },\n    manualInputTimeout: { type: [ Number, String ] },\n    hideDropdown: { type: Boolean, default: false },\n    fixedDropdownButton: { type: Boolean, default: false },\n\n    debugMode: { type: Boolean, default: false }\n  },\n\n  data () {\n    return {\n      timeValue: {},\n\n      hours: [],\n      minutes: [],\n      seconds: [],\n      apms: [],\n\n      isActive: false,\n      showDropdown: false,\n      isFocusing: false,\n      debounceTimer: undefined,\n\n      hourType: 'HH',\n      minuteType: 'mm',\n      secondType: '',\n      apmType: '',\n      hour: '',\n      minute: '',\n      second: '',\n      apm: '',\n      fullValues: undefined,\n      bakDisplayTime: undefined,\n      doClearApmChecking: false,\n\n      selectionTimer: undefined,\n      kbInputTimer: undefined,\n      kbInputLog: '',\n      bakCurrentPos: undefined,\n      forceDropOnTop: false\n    }\n  },\n\n  emits: ['update:modelValue', 'change', 'open', 'close', 'focus', 'blur', 'error'],\n\n  computed: {\n    opts () {\n      const options = Object.assign({}, DEFAULT_OPTIONS)\n\n      if (this.format && this.format.length) {\n        options.format = String(this.format)\n      }\n\n      if (this.isNumber(this.minuteInterval)) {\n        options.minuteInterval = +this.minuteInterval\n      }\n      // minuteInterval failsafe\n      if (!options.minuteInterval || options.minuteInterval < 1 || options.minuteInterval > 60) {\n        if (this.debugMode) {\n          if (options.minuteInterval > 60) {\n            this.debugLog(`\"minute-interval\" should be less than 60. Current value is ${this.minuteInterval}`)\n          } else if (options.minuteInterval === 0 || options.minuteInterval < 1) {\n            this.debugLog(`\"minute-interval\" should be NO less than 1. Current value is ${this.minuteInterval}`)\n          }\n        }\n        if (options.minuteInterval === 0) {\n          options.minuteInterval = 60\n        } else {\n          options.minuteInterval = 1\n        }\n      }\n\n      if (this.isNumber(this.secondInterval)) {\n        options.secondInterval = +this.secondInterval\n      }\n      // secondInterval failsafe\n      if (!options.secondInterval || options.secondInterval < 1 || options.secondInterval > 60) {\n        if (this.debugMode) {\n          if (options.secondInterval > 60) {\n            this.debugLog(`\"second-interval\" should be less than 60. Current value is ${this.secondInterval}`)\n          } else if (options.secondInterval === 0 || options.secondInterval < 1) {\n            this.debugLog(`\"second-interval\" should be NO less than 1. Current value is ${this.secondInterval}`)\n          }\n        }\n        if (options.secondInterval === 0) {\n          options.secondInterval = 60\n        } else {\n          options.secondInterval = 1\n        }\n      }\n\n      if (this.hourRange && Array.isArray(this.hourRange)) {\n        options.hourRange = JSON.parse(JSON.stringify(this.hourRange))\n        if (!this.hourRange.length && this.debugMode) {\n          this.debugLog('The \"hour-range\" array is empty (length === 0)')\n        }\n      }\n\n      if (this.minuteRange && Array.isArray(this.minuteRange)) {\n        options.minuteRange = JSON.parse(JSON.stringify(this.minuteRange))\n        if (!this.minuteRange.length && this.debugMode) {\n          this.debugLog('The \"minute-range\" array is empty (length === 0)')\n        }\n      }\n\n      if (this.secondRange && Array.isArray(this.secondRange)) {\n        options.secondRange = JSON.parse(JSON.stringify(this.secondRange))\n        if (!this.secondRange.length && this.debugMode) {\n          this.debugLog('The \"second-range\" array is empty (length === 0)')\n        }\n      }\n\n      if (this.hideDisabledItems) {\n        options.hideDisabledItems = true\n      }\n\n      if (this.hideDisabledHours || this.hideDisabledItems) {\n        options.hideDisabledHours = true\n      }\n      if (this.hideDisabledMinutes || this.hideDisabledItems) {\n        options.hideDisabledMinutes = true\n      }\n      if (this.hideDisabledSeconds || this.hideDisabledItems) {\n        options.hideDisabledSeconds = true\n      }\n\n      if (this.hideDropdown) {\n        if (this.manualInput) {\n          options.hideDropdown = true\n        } else if (this.debugMode) {\n          this.debugLog('\"hide-dropdown\" only works with \"manual-input\" mode')\n        }\n      }\n\n      if (this.blurDelay && +this.blurDelay > 0) {\n        options.blurDelay = +this.blurDelay\n      }\n\n      if (this.manualInputTimeout && +this.manualInputTimeout > 0) {\n        options.manualInputTimeout = +this.manualInputTimeout\n      }\n\n      if (this.dropOffsetHeight && +this.dropOffsetHeight > 0) {\n        options.dropOffsetHeight = +this.dropOffsetHeight\n      }\n\n      return options\n    },\n\n    useStringValue () {\n      return typeof this.modelValue === 'string'\n    },\n\n    formatString () {\n      return this.opts.format || DEFAULT_OPTIONS.format\n    },\n\n    inUse () {\n      const typesInUse = CONFIG.BASIC_TYPES.filter(type => this.getTokenByType(type))\n      // Sort types and tokens by their sequence in the \"format\" string\n      typesInUse.sort((l, r) => {\n        return this.formatString.indexOf(this.getTokenByType(l) || null) - this.formatString.indexOf(this.getTokenByType(r) || null)\n      })\n      const tokensInUse = typesInUse.map(type => this.getTokenByType(type))\n      return {\n        hour: !!this.hourType,\n        minute: !!this.minuteType,\n        second: !!this.secondType,\n        apm: !!this.apmType,\n        types: typesInUse || [],\n        tokens: tokensInUse || []\n      }\n    },\n\n    displayTime () {\n      let formatString = String(this.formatString)\n      if (this.hour) {\n        formatString = formatString.replace(new RegExp(this.hourType, 'g'), this.hour)\n      }\n      if (this.minute) {\n        formatString = formatString.replace(new RegExp(this.minuteType, 'g'), this.minute)\n      }\n      if (this.second && this.secondType) {\n        formatString = formatString.replace(new RegExp(this.secondType, 'g'), this.second)\n      }\n      if (this.apm && this.apmType) {\n        formatString = formatString.replace(new RegExp(this.apmType, 'g'), this.apm)\n      }\n      return formatString\n    },\n\n    customDisplayTime () {\n      if (!this.amText && !this.pmText) {\n        return this.displayTime\n      }\n      return this.displayTime.replace(new RegExp(this.apm, 'g'), this.apmDisplayText(this.apm))\n    },\n\n    inputIsEmpty () {\n      return this.formatString === this.displayTime\n    },\n\n    allValueSelected () {\n      if (\n        (this.inUse.hour && !this.hour) ||\n        (this.inUse.minute && !this.minute) ||\n        (this.inUse.second && !this.second) ||\n        (this.inUse.apm && !this.apm)\n      ) {\n        return false\n      }\n      return true\n    },\n\n    columnsSequence () {\n      return this.inUse.types.map(type => type) || []\n    },\n\n    showClearBtn () {\n      if (this.hideClearButton || this.disabled) {\n        return false\n      }\n      return !this.inputIsEmpty\n    },\n\n    showDropdownBtn () {\n      if (this.fixedDropdownButton) { return true }\n      if (this.opts.hideDropdown && this.isActive && !this.showDropdown) {\n        return true\n      }\n      return false\n    },\n\n    baseOn12Hours () {\n      return this.hourType === 'h' || this.hourType === 'hh'\n    },\n\n    hourRangeIn24HrFormat () {\n      if (!this.hourType || !this.opts.hourRange) { return false }\n      if (!this.opts.hourRange.length) { return [] }\n\n      const range = []\n      this.opts.hourRange.forEach(value => {\n        if (value instanceof Array) {\n          if (value.length > 2 && this.debugMode) {\n            this.debugLog(`Nested array within \"hour-range\" must contain no more than two items. Only the first two items of ${JSON.stringify(value)} will be taken into account.`)\n          }\n\n          let start = value[0]\n          let end = value[1] || value[0]\n\n          if (this.is12hRange(start)) {\n            start = this.translate12hRange(start)\n          }\n          if (this.is12hRange(end)) {\n            end = this.translate12hRange(end)\n          }\n\n          for (let i = +start; i <= +end; i++) {\n            if (i < 0 || i > 24) { continue }\n            if (!range.includes(i)) {\n              range.push(i)\n            }\n          }\n        } else {\n          if (this.is12hRange(value)) {\n            value = this.translate12hRange(value)\n          } else {\n            value = +value\n          }\n          if (value < 0 || value > 24) { return }\n          if (!range.includes(value)) {\n            range.push(value)\n          }\n        }\n      })\n      range.sort((l, r) => { return l - r })\n      return range\n    },\n\n    restrictedHourRange () {\n      // No restriction\n      if (!this.hourRangeIn24HrFormat) { return false }\n      // 12-Hour\n      if (this.baseOn12Hours) {\n        const range = this.hourRangeIn24HrFormat.map((value) => {\n          if (value === 12) {\n            return '12p'\n          } else if (value === 24 || value === 0) {\n            return '12a'\n          }\n          return value > 12 ? `${value % 12}p` : `${value}a`\n        })\n        return range\n      }\n      // 24-Hour\n      return this.hourRangeIn24HrFormat\n    },\n\n    validHoursList () {\n      if (!this.manualInput) { return false }\n      if (this.restrictedHourRange) {\n        let list = []\n        if (this.baseOn12Hours) {\n          list = this.restrictedHourRange.map(hr => {\n            const l = hr.substr(0, hr.length - 1)\n            const r = hr.substr(-1)\n            return `${this.formatValue(this.hourType, l)}${r}`\n          })\n          const am12Index = list.indexOf('12a')\n          if (am12Index > 0) {\n            // Make '12a' the first item in h/hh\n            list.unshift(list.splice(am12Index, 1)[0])\n          }\n          return list\n        }\n        list = this.restrictedHourRange.map(hr => {\n          return this.formatValue(this.hourType, hr)\n        })\n        if (list.length > 1 && list[0] && list[0] === '24') {\n          // Make '24' the last item in k/kk\n          list.push(list.shift())\n        }\n        return list\n      }\n      if (this.baseOn12Hours) {\n        return [].concat([], this.hours.map(hr => `${hr}a`), this.hours.map(hr => `${hr}p`))\n      }\n      return this.hours\n    },\n\n    has () {\n      const result = {\n        customApmText: false\n      }\n      const apmEnabled = !!this.apmType\n\n      if (apmEnabled && this.hourRangeIn24HrFormat && this.hourRangeIn24HrFormat.length) {\n        const range = [].concat([], this.hourRangeIn24HrFormat)\n        result.am = range.some(value => value < 12 || value === 24)\n        result.pm = range.some(value => value >= 12 && value < 24)\n      } else {\n        result.am = apmEnabled\n        result.pm = apmEnabled\n      }\n      if ((this.amText && this.amText.length) || (this.pmText && this.pmText.length)) {\n        result.customApmText = true\n      }\n      return result\n    },\n\n    minuteRangeList () {\n      if (!this.minuteType || !this.opts.minuteRange) { return false }\n      if (!this.opts.minuteRange.length) { return [] }\n      return this.renderRangeList(this.opts.minuteRange, 'minute')\n    },\n\n    secondRangeList () {\n      if (!this.secondType || !this.opts.secondRange) { return false }\n      if (!this.opts.secondRange.length) { return [] }\n      return this.renderRangeList(this.opts.secondRange, 'second')\n    },\n    \n    hourLabelText () {\n      return this.hourLabel || this.hourType\n    },\n    minuteLabelText () {\n      return this.minuteLabel || this.minuteType\n    },\n    secondLabelText() {\n      return this.secondLabel || this.secondType\n    },\n    apmLabelText () {\n      return this.apmLabel || this.apmType\n    },\n\n    inputWidthStyle () {\n      if (!this.inputWidth || !this.inputWidth.length) { return }\n      return {\n        width: this.inputWidth\n      }\n    },\n\n    tokenRegexBase () {\n      return this.inUse.tokens.join('|')\n    },\n\n    tokenChunks () {\n      if (!this.manualInput && !this.useStringValue) { return false }\n\n      const formatString = String(this.formatString)\n      const tokensRegxStr = `(${this.tokenRegexBase})+?`\n      const tokensMatchAll = this.getMatchAllByRegex(formatString, tokensRegxStr)\n\n      const tokenChunks = []\n      for (let tkMatch of tokensMatchAll) {\n        const rawToken = tkMatch[0]\n        const tokenMatchItem = {\n          index: tkMatch.index,\n          token: rawToken,\n          type: this.getTokenType(rawToken),\n          needsCalibrate: rawToken.length < 2,\n          len: (rawToken || '').length\n        }\n        tokenChunks.push(tokenMatchItem)\n      }\n      return tokenChunks\n    },\n\n    needsPosCalibrate () {\n      if (!this.manualInput) { return false }\n      return this.tokenChunks.some(chk => chk.needsCalibrate)\n    },\n\n    tokenChunksPos () {\n      if (!this.manualInput) { return false }\n      if (!this.needsPosCalibrate) {\n        return this.tokenChunks.map(chk => {\n          return {\n            token: chk.token,\n            type: chk.type,\n            start: chk.index,\n            end: chk.index + chk.len\n          }\n        })\n      }\n      const list = []\n      let calibrateLen = 0\n      this.tokenChunks.forEach(chk => {\n        let chunkCurrentLen\n        // Adjust for customized AM/PM text\n        if (chk.type === 'apm' && this.has.customApmText) {\n          if (this.apm && this.apm.length) {\n            const customApmText = this.apm.toLowerCase() === 'am' ? this.amText : this.pmText\n            chunkCurrentLen = (customApmText && customApmText.length) ? customApmText.length : chk.len\n          } else {\n            chunkCurrentLen = chk.len\n          }\n        // Others\n        } else {\n          chunkCurrentLen = this[chk.type] && this[chk.type].length ? this[chk.type].length : chk.len\n        }\n        list.push({\n          token: chk.token,\n          type: chk.type,\n          start: chk.index + calibrateLen,\n          end: chk.index + calibrateLen + chunkCurrentLen\n        })\n        if (chk.needsCalibrate && chunkCurrentLen > chk.len) {\n          calibrateLen += (chunkCurrentLen - chk.len)\n        }\n      })\n      return list\n    },\n\n    invalidValues () {\n      if (this.inputIsEmpty) { return [] }\n      if (!this.restrictedHourRange && !this.minuteRangeList && !this.secondRangeList && this.opts.minuteInterval === 1 && this.opts.secondInterval === 1) { return [] }\n\n      const result = []\n      if (this.inUse.hour && !this.isEmptyValue(this.hourType, this.hour) && (!this.isValidValue(this.hourType, this.hour) || this.isDisabled('hour', this.hour))) {\n        result.push('hour')\n      }\n      if (this.inUse.minute && !this.isEmptyValue(this.minuteType, this.minute) && (!this.isValidValue(this.minuteType, this.minute) || this.isDisabled('minute', this.minute) || this.notInInterval('minute', this.minute))) {\n        result.push('minute')\n      }\n      if (this.inUse.second && !this.isEmptyValue(this.secondType, this.second) && (!this.isValidValue(this.secondType, this.second) || this.isDisabled('second', this.second) || this.notInInterval('second', this.second))) {\n        result.push('second')\n      }\n      if (this.inUse.apm && !this.isEmptyValue(this.apmType, this.apm) && (!this.isValidValue(this.apmType, this.apm) || this.isDisabled('apm', this.apm))) {\n        result.push('apm')\n      }\n      if (result.length) {\n        return result\n      }\n      return []\n    },\n\n    hasInvalidInput () {\n      return Boolean(this.invalidValues && this.invalidValues.length)\n    },\n\n    autoDirectionEnabled () {\n      return this.dropDirection === 'auto'\n    },\n\n    dropdownDirClass () {\n      if (this.autoDirectionEnabled) {\n        return this.forceDropOnTop ? 'drop-up' : 'drop-down'\n      }\n      return this.dropDirection === 'up' ? 'drop-up' : 'drop-down'      \n    }\n  },\n\n  watch: {\n    'opts.format' (newValue) {\n      this.renderFormat(newValue)\n    },\n    'opts.minuteInterval' (newInteval) {\n      this.renderList('minute', newInteval)\n    },\n    'opts.secondInterval' (newInteval) {\n      this.renderList('second', newInteval)\n    },\n    value: {\n      deep: true,\n      handler () {\n        this.readValues()\n      }\n    },\n    displayTime () {\n      this.fillValues()\n    },\n    disabled (toDisabled) {\n      if (toDisabled) {\n        // Force close dropdown and reset status when disabled\n        if (this.isActive) {\n          this.isActive = false\n        }\n        if (this.showDropdown) {\n          this.showDropdown = false\n        }\n      }\n    },\n    'invalidValues.length' (newLength, oldLength) {\n      if (newLength && newLength >= 1) {\n        this.$emit('error', this.invalidValues)\n      } else if (oldLength && oldLength >= 1) {\n        this.$emit('error', [])\n      }\n    }\n  },\n\n  methods: {\n    formatValue (token, i) {\n      if (!this.isNumber(i)) { return '' }\n      i = +i\n      switch (token) {\n        case 'H':\n        case 'h':\n        case 'k':\n        case 'm':\n        case 's':\n          if (['h', 'k'].includes(token) && i === 0) {\n            return token === 'k' ? '24' : '12'\n          }\n          return String(i)\n        case 'HH':\n        case 'mm':\n        case 'ss':\n        case 'hh':\n        case 'kk':\n          if (['hh', 'kk'].includes(token) && i === 0) {\n            return token === 'kk' ? '24' : '12'\n          }\n          return i < 10 ? `0${i}` : String(i)\n        default:\n          return ''\n      }\n    },\n\n    checkAcceptingType (validValues, formatString) {\n      if (!validValues || !formatString || !formatString.length) { return '' }\n      for (let i = 0; i < validValues.length; i++) {\n        if (formatString.indexOf(validValues[i]) > -1) {\n          return validValues[i]\n        }\n      }\n      return ''\n    },\n\n    renderFormat (newFormat) {\n      newFormat = newFormat || this.opts.format || DEFAULT_OPTIONS.format\n\n      let hourType = this.checkAcceptingType(CONFIG.HOUR_TOKENS, newFormat)\n      let minuteType = this.checkAcceptingType(CONFIG.MINUTE_TOKENS, newFormat)\n      this.secondType = this.checkAcceptingType(CONFIG.SECOND_TOKENS, newFormat)\n      this.apmType = this.checkAcceptingType(CONFIG.APM_TOKENS, newFormat)\n\n      // Failsafe checking\n      if (!hourType && !minuteType && !this.secondType && !this.apmType) {\n        if (this.debugMode && this.format) {\n          this.debugLog(`No valid tokens found in your defined \"format\" string \"${this.format}\". Fallback to the default \"HH:mm\" format.`)\n        }\n        hourType = 'HH'\n        minuteType = 'mm'\n      }\n      this.hourType = hourType\n      this.minuteType = minuteType\n\n      this.hourType ? this.renderHoursList() : this.hours = []\n      this.minuteType ? this.renderList('minute') : this.minutes = []\n      this.secondType ? this.renderList('second') : this.seconds = []\n      this.apmType ? this.renderApmList() : this.apms = []\n\n      nextTick(() => {\n        this.readValues()\n      })\n    },\n\n    renderHoursList () {\n      const hoursCount = this.baseOn12Hours ? 12 : 24\n      const hours = []\n      for (let i = 0; i < hoursCount; i++) {\n        if (this.hourType === 'k' || this.hourType === 'kk') {\n          hours.push(this.formatValue(this.hourType, i + 1))\n        } else {\n          hours.push(this.formatValue(this.hourType, i))\n        }\n      }\n      this.hours = hours\n    },\n\n    renderList (listType, interval) {\n      if (!this.isMinuteOrSecond(listType)) { return }\n\n      const isMinute = listType === 'minute'\n      interval = interval || (isMinute ? (this.opts.minuteInterval || DEFAULT_OPTIONS.minuteInterval) : (this.opts.secondInterval || DEFAULT_OPTIONS.secondInterval))\n\n      const result = []\n      for (let i = 0; i < 60; i += interval) {\n        result.push(this.formatValue(isMinute ? this.minuteType : this.secondType, i))\n      }\n      isMinute ? this.minutes = result : this.seconds = result\n    },\n\n    renderApmList () {\n      this.apms = this.apmType === 'A' ? ['AM', 'PM'] : ['am', 'pm']\n    },\n\n    readValues () {\n      if (this.useStringValue) {\n        if (this.debugMode) {\n          this.debugLog(`Received a string value: \"${this.modelValue}\"`)\n        }\n        this.readStringValues(this.modelValue)\n      } else {\n        if (this.debugMode) {\n          this.debugLog(`Received an object value: \"${JSON.stringify(this.modelValue || {})}\"`)\n        }\n        this.readObjectValues(this.modelValue)\n      }\n    },\n\n    readObjectValues (objValue) {\n      const timeValue = JSON.parse(JSON.stringify(objValue || {}))\n      const values = Object.keys(timeValue)\n\n      // Failsafe for empty `v-model` object\n      if (values.length === 0) {\n        this.addFallbackValues()\n        return\n      }\n\n      CONFIG.BASIC_TYPES.forEach(type => {\n        const token = this.getTokenByType(type)\n        if (values.indexOf(token) > -1) {\n          const sanitizedValue = this.sanitizedValue(token, timeValue[token])\n          this[type] = sanitizedValue\n          timeValue[token] = sanitizedValue\n        } else {\n          this[type] = ''\n        }\n      })\n      this.timeValue = timeValue\n    },\n\n    getMatchAllByRegex (testString, regexString) {\n      const str = 'polyfillTest'\n      const needsPolyfill = Boolean(!str.matchAll || typeof str.matchAll !== 'function')\n      return needsPolyfill ? this.polyfillMatchAll(testString, regexString) : testString.matchAll(new RegExp(regexString, 'g'))\n    },\n\n    readStringValues (stringValue) {\n      // Failsafe for empty `v-model` string\n      if (!stringValue || !stringValue.length) {\n        this.addFallbackValues()\n        return\n      }\n\n      const formatString = String(this.formatString)\n      const tokensRegxStr = `(${this.tokenRegexBase})+?`\n      const othersRegxStr = `[^(${this.tokenRegexBase})]+`\n\n      const tokensMatchAll = this.getMatchAllByRegex(formatString, tokensRegxStr)\n      const othersMatchAll = this.getMatchAllByRegex(formatString, othersRegxStr)\n\n      const chunks = []\n      const tokenChunks = []\n\n      for (let tkMatch of tokensMatchAll) {\n        const tokenMatchItem = {\n          index: tkMatch.index,\n          token: tkMatch[0],\n          isValueToken: true\n        }\n        chunks.push(tokenMatchItem)\n        tokenChunks.push(tokenMatchItem)\n      }\n\n      for (let otMatch of othersMatchAll) {\n        chunks.push({\n          index: otMatch.index,\n          token: otMatch[0]\n        })\n      }\n\n      chunks.sort((l, r) => l.index < r.index ? -1 : 1)\n\n      let regexCombo = ''\n      chunks.forEach(chunk => {\n        if (chunk.isValueToken) {\n          const tokenRegex = this.getTokenRegex(chunk.token) || ''\n          regexCombo += tokenRegex\n        } else {\n          const safeChars = chunk.token.replace(/\\\\{0}(\\*|\\?|\\.|\\+)/g, '\\\\$1')\n          regexCombo += `(?:${safeChars})`\n        }\n      })\n\n      const comboReg = new RegExp(regexCombo)\n\n      // Do test before match\n      if (comboReg.test(stringValue)) {\n        const matchResults = stringValue.match(new RegExp(regexCombo))\n        const valueResults = matchResults.slice(1, tokenChunks.length + 1)\n        const timeValue = {}\n        valueResults.forEach((value, vrIndex) => {\n          if (tokenChunks[vrIndex]) {\n            const targetToken = tokenChunks[vrIndex].token\n            timeValue[targetToken] = this.setValueFromString(value, targetToken)\n          }\n        })\n        this.timeValue = timeValue\n\n        if (this.debugMode) {\n          const tokenChunksForLog = tokenChunks.map(tChunk => tChunk && tChunk.token)\n          this.debugLog(`Successfully parsed values ${JSON.stringify(valueResults)}\\nfor ${JSON.stringify(tokenChunksForLog)}\\nin format pattern '${this.formatString}'`)\n        }\n      } else {\n        if (this.debugMode) {\n          this.debugLog(`The input string in \"v-model\" does NOT match the \"format\" pattern\\nformat: ${this.formatString}\\nv-model: ${stringValue}`)\n        }\n      }\n    },\n\n    polyfillMatchAll (targetString, regxStr) {\n      const matchesList = targetString.match(new RegExp(regxStr, 'g'))\n      const result = []\n      const indicesReg = []\n      if (matchesList && matchesList.length) {\n        matchesList.forEach(matchedItem => {\n          const existIndex = indicesReg.findIndex(idxItem => idxItem.str === matchedItem)\n          let index\n          if (existIndex >= 0) {\n            if (indicesReg[existIndex] && indicesReg[existIndex].regex) {\n              index = indicesReg[existIndex].regex.exec(targetString).index\n            }\n          } else {\n            const itemIndicesRegex = new RegExp(matchedItem, 'g')\n            index = itemIndicesRegex.exec(targetString).index\n            indicesReg.push({\n              str: String(matchedItem),\n              regex: itemIndicesRegex\n            })\n          }\n          result.push({\n            0: String(matchedItem),\n            index: index\n          })\n        })\n      }\n      return result\n    },\n\n    addFallbackValues () {\n      const timeValue = {}\n      this.inUse.types.forEach(type => {\n        timeValue[this.getTokenByType(type)] = ''\n      })\n      this.timeValue = timeValue\n    },\n\n    setValueFromString (parsedValue, token) {\n      if (!token || !parsedValue) { return '' }\n      const tokenType = this.getTokenType(token)\n      if (!tokenType || !tokenType.length) { return '' }\n      const stdValue = (parsedValue !== this.getTokenByType(tokenType)) ? parsedValue : ''\n      this[tokenType] = stdValue\n      return stdValue\n    },\n\n    fillValues (forceEmit) {\n      const fullValues = {}\n\n      const baseHour = this.hour\n      const baseHourType = this.hourType\n\n      let apmValue\n\n      // Hour type or hour value is NOT set in the \"format\" string\n      if (!baseHourType || !this.isNumber(baseHour)) {\n        CONFIG.HOUR_TOKENS.forEach(token => fullValues[token] = '')\n        apmValue = this.lowerCasedApm(this.apm || '')\n        fullValues.a = apmValue\n        fullValues.A = apmValue.toUpperCase()\n\n      // Both Hour type and value are set\n      } else {\n        const hourValue = +baseHour\n        const apmValue = (this.baseOn12Hours && this.apm) ? this.lowerCasedApm(this.apm) : false\n\n        CONFIG.HOUR_TOKENS.forEach((token) => {\n          if (token === baseHourType) {\n            fullValues[token] = baseHour\n            return\n          }\n\n          let value\n          let apm\n          switch (token) {\n            case 'H':\n            case 'HH':\n            case 'k':\n            case 'kk':\n              if (this.baseOn12Hours) {\n                if (apmValue === 'pm') {\n                  value = hourValue < 12 ? hourValue + 12 : hourValue\n                } else if (['k', 'kk'].includes(token)) {\n                  value = hourValue === 12 ? 24 : hourValue\n                } else {\n                  value = hourValue % 12\n                }\n              } else {\n                if (['k', 'kk'].includes(token)) {\n                  value = hourValue === 0 ? 24 : hourValue\n                } else {\n                  value = hourValue % 24\n                }\n              }\n              fullValues[token] = this.formatValue(token, value)\n              break\n            case 'h':\n            case 'hh':\n              // h <-> hh\n              if (this.baseOn12Hours) {\n                value = hourValue\n                apm = apmValue || ''\n              // Read from other hour formats\n              } else {\n                if (hourValue > 11 && hourValue < 24) {\n                  apm = 'pm'\n                  value = hourValue === 12 ? 12 : hourValue % 12\n                } else {\n                  apm = 'am'\n                  value = hourValue % 12 === 0 ? 12 : hourValue\n                }\n              }\n              fullValues[token] = this.formatValue(token, value)\n              fullValues.a = apm\n              fullValues.A = apm.toUpperCase()\n              break\n          }\n        })\n      }\n\n      fullValues.m = this.formatValue('m', this.minute)\n      fullValues.mm = this.formatValue('mm', this.minute)\n      fullValues.s = this.formatValue('s', this.second)\n      fullValues.ss = this.formatValue('ss', this.second)\n\n      this.fullValues = fullValues\n\n      // On lazy mode, emit `input` and `change` events only when:\n      // - The user pick a new value and then close the dropdown\n      // - The user click the (\"x\") clear button\n      if (!this.lazy || forceEmit) {\n        this.emitTimeValue()\n      }\n\n      if (this.closeOnComplete && this.allValueSelected && this.showDropdown) {\n        this.toggleActive()\n      }\n    },\n\n    emitTimeValue () {\n      if (!this.fullValues) { return }\n\n      if (this.lazy && this.bakDisplayTime === this.displayTime) {\n        if (this.debugMode) {\n          this.debugLog('The value does not change on `lazy` mode. Skip the emitting `input` and `change` event.')\n        }\n        return\n      }\n\n      const fullValues = JSON.parse(JSON.stringify(this.fullValues))\n\n      if (this.useStringValue) {\n        this.$emit('update:modelValue', this.inputIsEmpty ? '' : String(this.displayTime))\n      } else {\n        const tokensInUse = this.inUse.tokens || []\n        const timeValue = {}\n        tokensInUse.forEach((token) => {\n          timeValue[token] = fullValues[token] || ''\n        })\n        this.$emit('update:modelValue', JSON.parse(JSON.stringify(timeValue)))\n      }\n\n      this.$emit('change', {\n        data: fullValues,\n        displayTime: this.inputIsEmpty ? '' : String(this.displayTime)\n      })\n    },\n\n    translate12hRange (value) {\n      const valueT = this.match12hRange(value)\n      if (+valueT[1] === 12) {\n        return +valueT[1] + (valueT[2].toLowerCase() === 'p' ? 0 : 12)\n      }\n      return +valueT[1] + (valueT[2].toLowerCase() === 'p' ? 12 : 0)\n    },\n\n    isDisabled (type, value) {\n      if (!this.isBasicType(type) || !this.inUse[type]) { return true }\n      switch (type) {\n        case 'hour':\n          return this.isDisabledHour(value)\n        case 'minute':\n        case 'second':\n          if (!this[`${type}RangeList`]) {\n            return false\n          }\n          return !this[`${type}RangeList`].includes(value)\n        case 'apm':\n          if (!this.restrictedHourRange) {\n            return false\n          }\n          return !this.has[this.lowerCasedApm(value)]\n        default:\n          return true\n      }\n    },\n\n    isDisabledHour (value) {\n      if (!this.restrictedHourRange) { return false }\n      if (this.baseOn12Hours) {\n        if (!this.apm || !this.apm.length) {\n          return false\n        } else {\n          const token = this.apm.toLowerCase() === 'am' ? 'a' : 'p'\n          return !this.restrictedHourRange.includes(`${+value}${token}`)\n        }\n      }\n      // Fallback for 'HH' and 'H hour format with a `hour-range` in a 12-hour form\n      if (\n        (this.hourType === 'HH' || this.hourType === 'H') &&\n        +value === 0 && this.restrictedHourRange.includes(24)\n      ) {\n        return false\n      }\n      return !this.restrictedHourRange.includes(+value)\n    },\n\n    notInInterval (section, value) {\n      if (!section || !this.isMinuteOrSecond(section)) { return }\n      if (this.opts[`${section}Interval`] === 1) { return false }\n      return +value % this.opts[`${section}Interval`] !== 0\n    },\n\n    renderRangeList (rawRange, section) {\n      if (!rawRange || !section || !this.isMinuteOrSecond(section)) { return [] }\n      const range = []\n      let formatedValue\n      rawRange.forEach(value => {\n        if (value instanceof Array) {\n          if (value.length > 2 && this.debugMode) {\n            this.debugLog(`Nested array within \"${section}-range\" must contain no more than two items. Only the first two items of ${JSON.stringify(value)} will be taken into account.`)\n          }\n          const start = value[0]\n          const end = value[1] || value[0]\n          for (let i = +start; i <= +end; i++) {\n            if (i < 0 || i > 59) { continue }\n            formatedValue = this.formatValue(this.getTokenByType(section), i)\n            if (!range.includes(formatedValue)) {\n              range.push(formatedValue)\n            }\n          }\n        } else {\n          if (+value < 0 || +value > 59) { return }\n          formatedValue = this.formatValue(this.getTokenByType(section), value)\n          if (!range.includes(formatedValue)) {\n            range.push(formatedValue)\n          }\n        }\n      })\n      range.sort((l, r) => { return l - r })\n      // Debug Mode\n      if (this.debugMode) {\n        const fullList = (section === 'minute' ? this.minutes : this.seconds) || []\n        const validItems = fullList.filter(item => range.includes(item))\n        if (!validItems || !validItems.length) {\n          if (section === 'minute') {\n            this.debugLog(`The minute list is empty due to the \"minute-range\" config\\nminute-range: ${JSON.stringify(this.minuteRange)}\\nminute-interval: ${this.opts.minuteInterval}`)\n          } else {\n            this.debugLog(`The second list is empty due to the \"second-range\" config\\nsecond-range: ${JSON.stringify(this.secondRange)}\\nsecond-interval: ${this.opts.secondInterval}`)\n          }\n        }\n      }\n      return range\n    },\n\n    forceApmSelection () {\n      if (this.manualInput) {\n        // Skip this to allow users to paste a string value from the clipboard in Manual Input mode\n        return\n      }\n      if (this.apmType && !this.apm) {\n        if (this.has.am || this.has.pm) {\n          this.doClearApmChecking = true\n          const apmValue = this.has.am ? 'am' : 'pm'\n          this.apm = this.apmType === 'A' ? apmValue.toUpperCase() : apmValue\n        }\n      }\n    },\n\n    emptyApmSelection () {\n      if (this.doClearApmChecking && this.hour === '' && this.minute === '' && this.second === '') {\n        this.apm = ''\n      }\n      this.doClearApmChecking = false\n    },\n\n    apmDisplayText (apmValue) {\n      if (this.amText && this.lowerCasedApm(apmValue) === 'am') {\n        return this.amText\n      }\n      if (this.pmText && this.lowerCasedApm(apmValue) === 'pm') {\n        return this.pmText\n      }\n      return apmValue\n    },\n\n    toggleActive () {\n      if (this.disabled) { return }\n      this.isActive = !this.isActive\n\n      if (this.isActive) {\n        this.isFocusing = true\n        if (this.manualInput) {\n          this.$emit('focus')\n        }\n        if (!this.opts.hideDropdown) {\n          this.setDropdownState(true)\n        }\n        // Record to check if value did change in the later phase\n        if (this.lazy) {\n          this.bakDisplayTime = String(this.displayTime || '')\n        }\n        if (this.manualInput && !this.inputIsEmpty) {\n          nextTick(() => {\n            if (this.$refs.input && this.$refs.input.selectionStart === 0 && this.$refs.input.selectionEnd === this.displayTime.length) {\n              // Select the first slot instead of the whole value string when tabbed in\n              this.selectFirstSlot()\n            }\n          })\n        }\n      } else {\n        if (this.showDropdown) {\n          this.setDropdownState(false)\n        } else if (this.manualInput) {\n          this.$emit('blur')\n        }\n        this.isFocusing = false\n        if (this.lazy) {\n          this.fillValues(true)\n          this.bakDisplayTime = undefined\n        }\n      }\n\n      if (this.restrictedHourRange && this.baseOn12Hours) {\n        this.showDropdown ? this.forceApmSelection() : this.emptyApmSelection()\n      }\n      if (this.showDropdown) {\n        this.checkForAutoScroll()\n      }\n    },\n\n    setDropdownState (toShow, fromUserClick = false) {\n      if (toShow) {\n        this.keepFocusing()\n        if (this.autoDirectionEnabled) {\n          this.checkDropDirection()\n        }\n        this.showDropdown = true\n        this.$emit('open') \n        if (fromUserClick) {\n          if (this.fixedDropdownButton) {\n            this.isActive = true\n          }\n          this.$emit('blur')\n          this.checkForAutoScroll()\n        }\n      } else {\n        this.showDropdown = false\n        this.$emit('close')\n      }\n    },\n\n    blurEvent () {\n      if (this.manualInput && !this.opts.hideDropdown) {\n        // hideDropdown's `blur` event is handled somewhere else\n        this.$emit('blur')\n      }\n    },\n\n    select (type, value) {\n      if (this.isBasicType(type) && !this.isDisabled(type, value)) {\n        this[type] = value\n        if (this.doClearApmChecking) {\n          this.doClearApmChecking = false\n        }\n      }\n    },\n\n    clearTime () {\n      if (this.disabled) { return }\n      this.hour = ''\n      this.minute = ''\n      this.second = ''\n      this.apm = ''\n\n      if (this.manualInput && this.$refs && this.$refs.input && this.$refs.input.value.length) {\n        this.$refs.input.value = ''\n      }\n\n      if (this.lazy) {\n        this.fillValues(true)\n      }\n    },\n\n    //\n    // Auto-Scroll\n    //\n\n    checkForAutoScroll () {\n      if (this.inputIsEmpty) { return }\n      if (this.autoScroll) {\n        nextTick(() => {\n          this.scrollToSelectedValues()\n        })\n      } else if (this.advancedKeyboard) {\n        // Auto-focus on selected value in the first column for advanced-keyboard\n        nextTick(() => {\n          const firstColumn = this.inUse.types[0]\n          this.scrollToSelected(firstColumn, true)\n        })\n      }\n    },\n\n    scrollToSelected (column, allowFallback = false) {\n      if (!this.timeValue || this.inputIsEmpty) { return }\n      const targetList = this.$el.querySelectorAll(`ul.${column}s`)[0]\n      let targetValue = this.activeItemInCol(column)[0]\n      if (!targetValue && allowFallback) {\n        // No value selected in the target column, fallback to the first found valid item\n        targetValue = this.validItemsInCol(column)[0]\n      }\n      if (targetList && targetValue) {\n        targetList.scrollTop = targetValue.offsetTop || 0\n        if (this.advancedKeyboard) {\n          targetValue.focus()\n        }\n      }\n    },\n\n    scrollToSelectedValues () {\n      if (!this.timeValue || this.inputIsEmpty) { return }\n      this.inUse.types.forEach(section => {\n        this.scrollToSelected(section)\n      })\n    },\n\n    //\n    // Additional Keyboard Navigation\n    //\n\n    onFocus () {\n      if (this.disabled) { return }\n      if (!this.isFocusing) {\n        this.isFocusing = true\n      }\n      if (!this.isActive) {\n        this.toggleActive()\n      }\n    },\n\n    escBlur () {\n      if (this.disabled) { return }\n      window.clearTimeout(this.debounceTimer)\n      this.isFocusing = false\n      const inputBox = this.$el.querySelectorAll('input.vue__time-picker-input')[0]\n      if (inputBox) {\n        inputBox.blur()\n      }\n    },\n\n    debounceBlur () {\n      if (this.disabled) { return }\n      this.isFocusing = false\n      window.clearTimeout(this.debounceTimer)\n      this.debounceTimer = window.setTimeout(() => {\n        window.clearTimeout(this.debounceTimer)\n        this.onBlur()\n      }, this.opts.blurDelay)\n    },\n\n    onBlur () {\n      if (!this.disabled && !this.isFocusing && this.isActive) {\n        this.toggleActive()\n      }\n    },\n\n    keepFocusing () {\n      if (this.disabled) { return }\n      window.clearTimeout(this.debounceTimer)\n      if (!this.isFocusing) {\n        this.isFocusing = true\n      }\n    },\n\n    validItemsInCol (column) {\n      const columnClass = `${column}s`\n      return this.$el.querySelectorAll(`ul.${columnClass} > li:not(.hint):not([disabled])`)\n    },\n\n    activeItemInCol (column) {\n      const columnClass = `${column}s`\n      return this.$el.querySelectorAll(`ul.${columnClass} > li.active:not(.hint)`)\n    },\n\n    getClosestSibling (column, dataKey, getPrevious = false) {\n      const siblingsInCol = this.validItemsInCol(column)\n      const selfIndex = Array.prototype.findIndex.call(siblingsInCol, (sbl) => {\n        return sbl.getAttribute('data-key') === dataKey\n      })\n\n      // Already the first item\n      if (getPrevious && selfIndex === 0) {\n        return siblingsInCol[siblingsInCol.length - 1]\n      }\n      // Already the last item\n      if (!getPrevious && selfIndex === siblingsInCol.length - 1) {\n        return siblingsInCol[0]\n      }\n      // Selected value not in the valid values list\n      if (selfIndex < 0) {\n        return siblingsInCol[0]\n      }\n\n      if (getPrevious) {\n        return siblingsInCol[selfIndex - 1]\n      }\n      return siblingsInCol[selfIndex + 1]\n    },\n\n    prevItem (column, dataKey, isManualInput = false) {\n      const targetItem = this.getClosestSibling(column, dataKey, true)\n      if (targetItem) {\n        return isManualInput ? targetItem : targetItem.focus()\n      }\n    },\n\n    nextItem (column, dataKey, isManualInput = false) {\n      const targetItem = this.getClosestSibling(column, dataKey, false)\n      if (targetItem) {\n        return isManualInput ? targetItem : targetItem.focus()\n      }\n    },\n\n    getSideColumnName (currentColumn, toLeft = false) {\n      const currentColumnIndex = this.inUse.types.indexOf(currentColumn)\n      if (toLeft && currentColumnIndex <= 0) {\n        if (this.debugMode) {\n          this.debugLog('You\\'re in the leftmost list already')\n        }\n        return\n      } else if (!toLeft && currentColumnIndex === (this.inUse.types.length - 1)) {\n        if (this.debugMode) {\n          this.debugLog('You\\'re in the rightmost list already')\n        }\n        return\n      }\n      return this.inUse.types[toLeft ? currentColumnIndex - 1 : currentColumnIndex + 1]\n    },\n\n    getFirstItemInSideColumn (currentColumn, toLeft = false) {\n      const targetColumn = this.getSideColumnName(currentColumn, toLeft)\n      if (!targetColumn) { return }\n      const listItems = this.validItemsInCol(targetColumn)\n      if (listItems && listItems[0]) {\n        return listItems[0]\n      }\n    },\n\n    getActiveItemInSideColumn (currentColumn, toLeft = false) {\n      const targetColumn = this.getSideColumnName(currentColumn, toLeft)\n      if (!targetColumn) { return }\n      const activeItems = this.activeItemInCol(targetColumn)\n      if (activeItems && activeItems[0]) {\n        return activeItems[0]\n      }\n    },\n\n    toLeftColumn (currentColumn) {\n      const targetItem = this.getActiveItemInSideColumn(currentColumn, true) || this.getFirstItemInSideColumn(currentColumn, true)\n      if (targetItem) {\n        targetItem.focus()\n      }\n    },\n\n    toRightColumn (currentColumn) {\n      const targetItem = this.getActiveItemInSideColumn(currentColumn, false) || this.getFirstItemInSideColumn(currentColumn, false)\n      if (targetItem) {\n        targetItem.focus()\n      }\n    },\n\n    //\n    // Manual Input\n    //\n\n    onMouseDown () {\n      if (!this.manualInput) { return }\n      window.clearTimeout(this.selectionTimer)\n      this.selectionTimer = window.setTimeout(() => {\n        window.clearTimeout(this.selectionTimer)\n        if (this.$refs && this.$refs.input) {\n          const nearestSlot = this.getNearestChunkByPos(this.$refs.input.selectionStart || 0)\n          this.debounceSetInputSelection(nearestSlot)\n        }\n      }, 50)\n    },\n\n    keyDownHandler (evt) {\n      if (evt.isComposing || evt.keyCode === 229) {\n        // Skip IME inputs\n        evt.preventDefault()\n        evt.stopPropagation()\n        return false\n      }\n      // Numbers\n      if ((evt.keyCode >= 48 && evt.keyCode <= 57) || (evt.keyCode >= 96 && evt.keyCode <= 105)) {\n        evt.preventDefault()\n        this.keyboardInput(evt.key)\n      // A|P|M\n      } else if ([65, 80, 77].includes(evt.keyCode)) {\n        evt.preventDefault()\n        this.keyboardInput(evt.key, true)\n      // Arrow keys\n      } else if (evt.keyCode >= 37 && evt.keyCode <= 40) {\n        evt.preventDefault()\n        this.clearKbInputLog()\n        this.arrowHandler(evt)\n      // Delete|Backspace\n      } else if (evt.keyCode === 8 || evt.keyCode === 46) {\n        evt.preventDefault()\n        this.clearKbInputLog()\n        this.clearTime()\n      // Tab\n      } else if (evt.keyCode === 9) {\n        this.clearKbInputLog()\n        this.tabHandler(evt)\n      // Prevent any Non-ESC and non-pasting inputs\n      } else if (evt.keyCode !== 27 && !(evt.metaKey || evt.ctrlKey)) {\n        evt.preventDefault()\n      }\n    },\n\n    onCompostionStart (evt) {\n      evt.preventDefault()\n      evt.stopPropagation()\n      this.bakCurrentPos = this.getCurrentTokenChunk()\n      return false\n    },\n\n    onCompostionEnd (evt) {\n      evt.preventDefault()\n      evt.stopPropagation()\n\n      const cpsData = evt.data\n      let inputIsCustomApmText = false\n      if (this.has.customApmText) {\n        inputIsCustomApmText = this.isCustomApmText(cpsData)\n      }\n      if (inputIsCustomApmText) {\n        this.setSanitizedValueToSection('apm', inputIsCustomApmText)\n      }\n\n      this.$refs.input.value = this.has.customApmText ? this.customDisplayTime : this.displayTime\n\n      nextTick(() => {\n        if (this.bakCurrentPos) {\n          const bakPos = JSON.parse(JSON.stringify(this.bakCurrentPos))\n          if (inputIsCustomApmText) {\n            bakPos.end = (bakPos.start + cpsData.length)\n          }\n          this.debounceSetInputSelection(bakPos)\n          this.bakCurrentPos = null\n        }\n      })\n      return false\n    },\n\n    pasteHandler (evt) {\n      evt.preventDefault()\n      let pastingText = (evt.clipboardData || window.clipboardData).getData('text')\n      if (this.debugMode) {\n        this.debugLog(`Pasting value \"${pastingText}\" from clipboard`)\n      }\n      if (!pastingText || !pastingText.length) { return }\n\n      // Replace custom AM/PM text (if any)\n      if (this.has.customApmText) {\n        pastingText = this.replaceCustomApmText(pastingText)\n      }\n\n      if (this.inputIsEmpty) {\n        this.readStringValues(pastingText)\n      } else {\n        this.kbInputLog = pastingText.substr(-2, 2)\n        this.setKbInput()\n        this.debounceClearKbLog()\n      }\n    },\n\n    arrowHandler (evt) {\n      const direction = { 37: 'L', 38: 'U', 39: 'R', 40: 'D' }[evt.keyCode]\n      if (direction === 'U' || direction === 'D') {\n        if (this.inputIsEmpty) {\n          this.selectFirstValidValue()\n        } else {\n          const currentChunk = this.getCurrentTokenChunk()\n          if (!currentChunk) {\n            this.selectFirstValidValue()\n            return\n          }\n          const tokenType = currentChunk.type\n          this.getClosestValidItemInCol(tokenType, this[tokenType], direction)\n          const newChunkPos = this.getCurrentTokenChunk()\n          this.debounceSetInputSelection(newChunkPos)\n        }\n      } else if (direction === 'R') {\n        this.toLateralToken(false)\n      } else if (direction === 'L') {\n        this.toLateralToken(true)\n      }\n    },\n\n    tabHandler (evt) {      \n      if (!this.inputIsEmpty && this.tokenChunksPos && this.tokenChunksPos.length) {\n        const currentChunk = this.getCurrentTokenChunk()\n        if (!currentChunk) { return }\n        const firstChunk = this.tokenChunksPos[0]\n        const lastChunk = this.tokenChunksPos[this.tokenChunksPos.length - 1]\n        if ((evt.shiftKey && currentChunk.token !== firstChunk.token) || (!evt.shiftKey && currentChunk.token !== lastChunk.token)) {\n          evt.preventDefault()\n          this.toLateralToken(evt.shiftKey)\n        }\n      }\n    },\n\n    keyboardInput (newChar, isApm = false) {\n      const currentChunk = this.getCurrentTokenChunk()\n      if (!currentChunk || (currentChunk.type !== 'apm' && isApm) || (currentChunk.type === 'apm' && !isApm)) { return }\n      this.kbInputLog = `${this.kbInputLog.substr(-1)}${newChar}`\n      this.setKbInput()\n      this.debounceClearKbLog()\n    },\n\n    clearKbInputLog () {\n      window.clearTimeout(this.kbInputTimer)\n      this.kbInputLog = ''\n    },\n\n    debounceClearKbLog () {\n      window.clearTimeout(this.kbInputTimer)\n      this.kbInputTimer = window.setTimeout(() => {\n        this.clearKbInputLog()\n      }, this.opts.manualInputTimeout)\n    },\n\n    setKbInput (value) {\n      value = value || this.kbInputLog\n      const currentChunk = this.getCurrentTokenChunk()\n      if (!currentChunk || !value || !value.length) { return }\n      const chunkType = currentChunk.type\n      const chunkToken = currentChunk.token\n\n      let validValue\n      if (chunkType === 'apm') {\n        if (this.lowerCasedApm(value).includes('a')) {\n          validValue = 'am'\n        } else if (this.lowerCasedApm(value).includes('p')) {\n          validValue = 'pm'\n        }\n        if (validValue) {\n          validValue = chunkToken === 'A' ? validValue.toUpperCase() : validValue\n        }\n      } else {\n        if (this.isValidValue(chunkToken, value)) {\n          validValue = value\n        } else {\n          const lastInputValue = this.formatValue(chunkToken, value.substr(-1))\n          if (this.isValidValue(chunkToken, lastInputValue)) {\n            validValue = lastInputValue\n          }\n        }\n      }\n\n      if (validValue) {\n        this.setSanitizedValueToSection(chunkType, validValue)\n        const newChunkPos = this.getCurrentTokenChunk()\n        this.debounceSetInputSelection(newChunkPos)      \n      }\n      if (this.debugMode) {\n        if (validValue) {\n          this.debugLog(`Successfully set value \"${validValue}\" from latest input \"${value}\" for the \"${chunkType}\" slot`)\n        } else {\n          this.debugLog(`Value \"${value}\" is invalid in the \"${chunkType}\" slot`)\n        }\n      }\n    },\n\n    // Form Autofill\n    onChange () {\n      if (!this.manualInput || !this.$refs || !this.$refs.input) { return }\n      const autoFillValue = this.$refs.input.value || ''\n      if (autoFillValue && autoFillValue.length) {\n        this.readStringValues(autoFillValue)\n      }\n    },\n\n    getNearestChunkByPos (startPos) {\n      if (!this.tokenChunksPos || !this.tokenChunksPos.length) { return }\n      let nearest\n      let nearestDelta = -1\n      for (let i = 0; i < this.tokenChunksPos.length; i++) {\n        const chunk = JSON.parse(JSON.stringify(this.tokenChunksPos[i]))\n        if (chunk.start === startPos) {\n          return chunk\n        }\n        const delta = Math.abs(chunk.start - startPos)\n        if (nearestDelta < 0) {\n          nearest = chunk\n          nearestDelta = delta\n        } else {\n          if (nearestDelta <= delta) {\n            return nearest\n          }\n          nearestDelta = delta\n          nearest = chunk\n        }\n      }\n      return nearest\n    },\n\n    selectFirstValidValue () {\n      if (!this.tokenChunksPos || !this.tokenChunksPos.length) { return }\n      const firstSlotType = this.tokenChunksPos[0].type\n      if (firstSlotType === 'hour') {\n        this.getClosestHourItem()\n      } else {\n        this.getClosestValidItemInCol(firstSlotType, this[firstSlotType])\n      }\n      this.selectFirstSlot()\n    },\n\n    getClosestHourItem (currentValue, direction = 'U') {\n      if (!this.validHoursList || !this.validHoursList.length) {\n        if (this.debugMode) {\n          this.debugLog(`No valid hour values found, please check your \"hour-range\" config\\nhour-range: ${JSON.stringify(this.hourRange)}`)\n        }\n        return\n      }\n      if (!currentValue) {\n        this.setManualHour(this.validHoursList[0])\n        return\n      }\n      const currentIndex = this.validHoursList.findIndex(item => {\n        if (!this.baseOn12Hours) {\n          return item === currentValue\n        } else {\n          const valueKey = `${currentValue}${this.lowerCasedApm(this.apm) === 'pm' ? 'p' : 'a'}` \n          return item === valueKey\n        }\n      })\n      let nextIndex\n      if (currentIndex === -1) {\n        nextIndex = 0\n      } else if (direction === 'D') {\n        nextIndex = currentIndex === 0 ? this.validHoursList.length - 1 : currentIndex - 1\n      } else {\n        nextIndex = (currentIndex + 1) % this.validHoursList.length\n      }\n      const nextItem = this.validHoursList[nextIndex]\n      this.setManualHour(nextItem)\n    },\n\n    getClosestValidItemInCol (column, currentValue, direction = 'U') {\n      if (column === 'hour') {\n        this.getClosestHourItem(currentValue, direction)\n      } else {\n        const nextItem = direction === 'D' ? this.prevItem(column, this[column], true) : this.nextItem(column, this[column], true)\n        if (nextItem) {\n          this.select(column, nextItem.getAttribute('data-key'))\n        }\n      }\n    },\n\n    setSanitizedValueToSection (section, inputValue) {\n      if (!section || !this.getTokenByType(section)) { return }\n      // NOTE: Disabled values are allowed here, followed by an 'error' event, though\n      const sanitizedValue = this.sanitizedValue(this.getTokenByType(section), inputValue)\n      this[section] = sanitizedValue\n    },\n\n    setManualHour (nextItem) {\n      if (this.is12hRange(nextItem)) {\n        const hourT = this.match12hRange(nextItem)\n        const apmValue = hourT[2] === 'a' ? 'AM' : 'PM'\n        this.setSanitizedValueToSection('apm', this.apmType === 'a' ? apmValue.toLowerCase() : apmValue)\n        this.setSanitizedValueToSection('hour', hourT[1])\n      } else {\n        this.setSanitizedValueToSection('hour', nextItem)\n      }\n    },\n\n    debounceSetInputSelection ({start = 0, end = 0 }) {\n      nextTick(() => {\n        this.setInputSelectionRange(start, end)\n      })\n      window.clearTimeout(this.selectionTimer)\n      this.selectionTimer = window.setTimeout(() => {\n        window.clearTimeout(this.selectionTimer)\n        // Double-check selection for 12hr format\n        if (this.$refs.input && (this.$refs.input.selectionStart !== start || this.$refs.input.selectionEnd !== end)) {\n          this.setInputSelectionRange(start, end)\n        }\n      }, 30)\n    },\n\n    setInputSelectionRange (start, end) {\n      if (this.$refs && this.$refs.input) {\n        this.$refs.input.setSelectionRange(start, end)\n      }\n    },\n\n    getCurrentTokenChunk () {\n      return this.getNearestChunkByPos((this.$refs.input && this.$refs.input.selectionStart) || 0)\n    },\n\n    selectFirstSlot () {\n      const firstChunkPos = this.getNearestChunkByPos(0)\n      this.debounceSetInputSelection(firstChunkPos)\n    },\n\n    toLateralToken (toLeft) {\n      const currentChunk = this.getCurrentTokenChunk()\n      if (!currentChunk) {\n        this.selectFirstValidValue()\n        return\n      }\n      const currentChunkIndex = this.tokenChunksPos.findIndex(chk => chk.token === currentChunk.token)\n      if ((!toLeft && currentChunkIndex >= this.tokenChunksPos.length - 1) || (toLeft && currentChunkIndex === 0)) {\n        if (this.debugMode) {\n          if (toLeft) {\n            this.debugLog('You\\'re in the leftmost slot already')\n          } else {\n            this.debugLog('You\\'re in the rightmost slot already')\n          }\n        }\n        return\n      }\n      const targetSlotPos = toLeft ? this.tokenChunksPos[currentChunkIndex - 1] : this.tokenChunksPos[currentChunkIndex + 1]\n      this.debounceSetInputSelection(targetSlotPos)\n    },\n\n    isCustomApmText (inputData) {\n      if (!inputData || !inputData.length) { return false }\n      if (this.amText && this.amText === inputData) {\n        return this.apmType === 'A' ? 'AM' : 'am'\n      }\n      if (this.pmText && this.pmText === inputData) {\n        return this.apmType === 'A' ? 'PM' : 'pm'\n      }\n      return false\n    },\n\n    replaceCustomApmText (inputString) {\n      if (this.amText && this.amText.length && inputString.includes(this.amText)) {\n        return inputString.replace(new RegExp(this.amText, 'g'), this.apmType === 'A' ? 'AM' : 'am')\n      } else if (this.pmText && this.pmText.length && inputString.includes(this.pmText)) {\n        return inputString.replace(new RegExp(this.pmText, 'g'), this.apmType === 'A' ? 'PM' : 'pm')\n      }\n      return inputString\n    },\n\n    checkDropDirection () {\n      if (!this.$el) { return }\n      let container\n      if (this.containerId && this.containerId.length) {\n        container = document.getElementById(this.containerId)\n        if (!container && this.debugMode) {\n          this.debugLog(`Container with id \"${this.containerId}\" not found. Fallback to document body.`)\n        }\n      }\n      const el = this.$el\n      let spaceDown\n      if (container && container.offsetHeight) {\n        // Valid container found\n        spaceDown = (container.offsetTop + container.offsetHeight) - (el.offsetTop + el.offsetHeight)\n      } else {\n        // Fallback to document body\n        const docHeight = Math.max(document.body.scrollHeight, document.documentElement.scrollHeight, document.body.offsetHeight, document.documentElement.offsetHeight, document.body.clientHeight, document.documentElement.clientHeight)\n        spaceDown = docHeight - (el.offsetTop + el.offsetHeight)\n      }\n      this.forceDropOnTop = this.opts.dropOffsetHeight > spaceDown\n    },\n\n    //\n    // Helpers\n    //\n\n    is12hRange (value) {\n      return /^\\d{1,2}(a|p|A|P)$/.test(value)\n    },\n\n    match12hRange (value) {\n      return value.match(/^(\\d{1,2})(a|p|A|P)$/)\n    },\n\n    isNumber (value) {\n      return !isNaN(parseFloat(value)) && isFinite(value)\n    },\n\n    isBasicType (type) {\n      return CONFIG.BASIC_TYPES.includes(type)\n    },\n\n    lowerCasedApm (apmValue) {\n      return (apmValue || '').toLowerCase()\n    },\n\n    getTokenRegex (token) {\n      switch (token) {\n        case 'HH':\n          return '([01][0-9]|2[0-3]|H{2})'\n        case 'H':\n          return '([0-9]{1}|1[0-9]|2[0-3]|H{1})'\n        case 'hh':\n          return '(0[1-9]|1[0-2]|h{2})'\n        case 'h':\n          return '([1-9]{1}|1[0-2]|h{1})'\n        case 'kk':\n          return '(0[1-9]|1[0-9]|2[0-4]|k{2})'\n        case 'k':\n          return '([1-9]{1}|1[0-9]|2[0-4]|k{1})'\n        case 'mm':\n          return '([0-5][0-9]|m{2})'\n        case 'ss':\n          return '([0-5][0-9]|s{2})'\n        case 'm':\n          return '([0-9]{1}|[1-5][0-9]|m{1})'\n        case 's':\n          return '([0-9]{1}|[1-5][0-9]|s{1})'\n        case 'A':\n          return '(AM|PM|A{1})'\n        case 'a':\n          return '(am|pm|a{1})'\n        default:\n          return ''\n      }\n    },\n\n    isEmptyValue (targetToken, testValue) {\n      return (!testValue || !testValue.length) || (testValue && testValue === targetToken)\n    },\n\n    isValidValue (targetToken, testValue) {\n      if (!targetToken || this.isEmptyValue(targetToken, testValue)) { return false }\n      const tokenRegexStr = this.getTokenRegex(targetToken)\n      if (!tokenRegexStr || !tokenRegexStr.length) { return false }\n      return (new RegExp(`^${tokenRegexStr}$`)).test(testValue)\n    },\n\n    sanitizedValue (targetToken, inputValue) {\n      if (this.isValidValue(targetToken, inputValue)) {\n        return inputValue\n      }\n      return ''\n    },\n\n    getTokenType (token) {\n      return this.inUse.types[this.inUse.tokens.indexOf(token)] || ''\n    },\n\n    getTokenByType (type) {\n      return this[`${type}Type`] || ''\n    },\n\n    isMinuteOrSecond (type) {\n      return ['minute', 'second'].includes(type)\n    },\n\n    // Breaking attribution coercion changes in Vue 3\n    // > https://v3.vuejs.org/guide/migration/attribute-coercion.html#overview\n    booleanAttr (isTrue = false) {\n      return isTrue ? true : null\n    },\n\n    debugLog (logText) {\n      if (!logText || !logText.length) { return }\n      let identifier = ''\n      if (this.id) {\n        identifier += `#${this.id}`\n      }\n      if (this.name) {\n        identifier += `[name=${this.name}]`\n      }\n      if (this.inputClass) {\n        let inputClasses = []\n        if (typeof this.inputClass === 'string') {\n          inputClasses = this.inputClass.split(/\\s/g)\n        } else if (Array.isArray(this.inputClass)) {\n          inputClasses = [].concat([], this.inputClass)\n        } else if (typeof this.inputClass === 'object') {\n          Object.keys(this.inputClass).forEach(clsName => {\n            if (this.inputClass[clsName]) {\n              inputClasses.push(clsName)\n            }\n          })\n        }\n        for (let inputClass of inputClasses) {\n          if (inputClass && inputClass.trim().length) {\n            identifier += `.${inputClass.trim()}`\n          }\n        }\n      }\n      const finalLogText = `DEBUG: ${logText}${identifier ? `\\n\\t(${identifier})` : '' }`\n      if (window.console.debug && typeof window.console.debug === 'function') {\n        window.console.debug(finalLogText)\n      } else {\n        window.console.log(finalLogText)\n      }\n    }\n  },\n\n  mounted () {\n    window.clearTimeout(this.debounceTimer)\n    window.clearTimeout(this.selectionTimer)\n    window.clearTimeout(this.kbInputTimer)\n    this.renderFormat()\n  },\n\n  beforeUnmount () {\n    window.clearTimeout(this.debounceTimer)\n    window.clearTimeout(this.selectionTimer)\n    window.clearTimeout(this.kbInputTimer)\n  }\n}\n</script>\n\n<template>\n<span class=\"vue__time-picker\" :style=\"inputWidthStyle\">\n  <input type=\"text\" class=\"vue__time-picker-input\" ref=\"input\"\n         :class=\"[inputClass, {'is-empty': inputIsEmpty, 'invalid': hasInvalidInput, 'all-selected': allValueSelected, 'disabled': disabled, 'has-custom-icon': $slots && $slots.icon }]\"\n         :style=\"inputWidthStyle\"\n         :id=\"id\"\n         :name=\"name\"\n         :value=\"inputIsEmpty ? null : customDisplayTime\"\n         :placeholder=\"placeholder ? placeholder : formatString\"\n         :tabindex=\"disabled ? -1 : tabindex\"\n         :disabled=\"booleanAttr(disabled)\"\n         :readonly=\"booleanAttr(!manualInput)\"\n         :autocomplete=\"autocomplete\"\n         @focus=\"onFocus\"\n         @change=\"onChange\"\n         @blur=\"debounceBlur(); blurEvent()\"\n         @mousedown=\"onMouseDown\"\n         @keydown=\"keyDownHandler\"\n         @compositionstart=\"onCompostionStart\"\n         @compositionend=\"onCompostionEnd\"\n         @paste=\"pasteHandler\"\n         @keydown.esc.exact=\"escBlur\" />\n  <div class=\"controls\" v-if=\"showClearBtn || showDropdownBtn\" tabindex=\"-1\">\n    <span v-if=\"!isActive && showClearBtn\" class=\"clear-btn\" tabindex=\"-1\"\n          :class=\"{'has-custom-btn': $slots && $slots.clearButton }\"\n          @click=\"clearTime\">\n      <slot name=\"clearButton\"><span class=\"char\">&times;</span></slot>\n    </span>\n    <span v-if=\"showDropdownBtn\" class=\"dropdown-btn\" tabindex=\"-1\"\n          :class=\"{'has-custom-btn': $slots && $slots.dropdownButton }\"\n          @click=\"setDropdownState(fixedDropdownButton ? !showDropdown : true, true)\"\n          @mousedown=\"keepFocusing\">\n      <slot name=\"dropdownButton\"><span class=\"char\">&dtrif;</span></slot>\n    </span>\n  </div>\n  <div class=\"custom-icon\" v-if=\"$slots && $slots.icon\"><slot name=\"icon\"></slot></div>\n  <div class=\"time-picker-overlay\" v-if=\"showDropdown\" @click=\"toggleActive\" tabindex=\"-1\"></div>\n  <div class=\"dropdown\" v-show=\"showDropdown\" tabindex=\"-1\"\n       :class=\"[dropdownDirClass]\" :style=\"inputWidthStyle\"\n       @mouseup=\"keepFocusing\" @click.stop=\"\">\n    <div class=\"select-list\" :style=\"inputWidthStyle\" tabindex=\"-1\">\n      <!-- Common Keyboard Support: less event listeners -->\n      <template v-if=\"!advancedKeyboard\">\n        <template v-for=\"column in columnsSequence\" :key=\"column\">\n          <ul v-if=\"column === 'hour'\" class=\"hours\" @scroll=\"keepFocusing\">\n            <li class=\"hint\" v-text=\"hourLabelText\"></li>\n            <template v-for=\"(hr, hIndex) in hours\" :key=\"hIndex\">\n              <li v-if=\"!opts.hideDisabledHours || (opts.hideDisabledHours && !isDisabled('hour', hr))\"\n                  :class=\"{active: hour === hr}\"\n                  :disabled=\"booleanAttr(isDisabled('hour', hr))\"\n                  :data-key=\"hr\"\n                  v-text=\"hr\"\n                  @click=\"select('hour', hr)\"></li>\n            </template>\n          </ul>\n          <ul v-if=\"column === 'minute'\" class=\"minutes\" @scroll=\"keepFocusing\">\n            <li class=\"hint\" v-text=\"minuteLabelText\"></li>\n            <template v-for=\"(m, mIndex) in minutes\" :key=\"mIndex\">\n              <li v-if=\"!opts.hideDisabledMinutes || (opts.hideDisabledMinutes && !isDisabled('minute', m))\"\n                  :class=\"{active: minute === m}\"\n                  :disabled=\"booleanAttr(isDisabled('minute', m))\"\n                  :data-key=\"m\"\n                  v-text=\"m\"\n                  @click=\"select('minute', m)\"></li>\n            </template>\n          </ul>\n          <ul v-if=\"column === 'second'\" class=\"seconds\" @scroll=\"keepFocusing\">\n            <li class=\"hint\" v-text=\"secondLabelText\"></li>\n            <template v-for=\"(s, sIndex) in seconds\" :key=\"sIndex\">\n              <li v-if=\"!opts.hideDisabledSeconds || (opts.hideDisabledSeconds && !isDisabled('second', s))\"\n                  :class=\"{active: second === s}\"\n                  :disabled=\"booleanAttr(isDisabled('second', s))\"\n                  :data-key=\"s\"\n                  v-text=\"s\"\n                  @click=\"select('second', s)\"></li>\n            </template>\n          </ul>\n          <ul v-if=\"column === 'apm'\" class=\"apms\" @scroll=\"keepFocusing\">\n            <li class=\"hint\" v-text=\"apmLabelText\"></li>\n            <template v-for=\"(a, aIndex) in apms\" :key=\"aIndex\">\n              <li v-if=\"!opts.hideDisabledHours || (opts.hideDisabledHours && !isDisabled('apm', a))\"\n                  :class=\"{active: apm === a}\"\n                  :disabled=\"booleanAttr(isDisabled('apm', a))\"\n                  :data-key=\"a\"\n                  v-text=\"apmDisplayText(a)\"\n                  @click=\"select('apm', a)\"></li>\n            </template>\n          </ul>\n        </template>\n      </template><!-- / Common Keyboard Support -->\n\n      <!--\n        Advanced Keyboard Support\n        Addeds hundreds of additional event lisenters\n      -->\n      <template v-if=\"advancedKeyboard\">\n        <template v-for=\"column in columnsSequence\" :key=\"column\">\n          <ul v-if=\"column === 'hour'\" class=\"hours\" tabindex=\"-1\" @scroll=\"keepFocusing\">\n            <li class=\"hint\" v-text=\"hourLabelText\" tabindex=\"-1\"></li>\n            <template v-for=\"(hr, hIndex) in hours\" :key=\"hIndex\">\n              <li v-if=\"!opts.hideDisabledHours || (opts.hideDisabledHours && !isDisabled('hour', hr))\"\n                  :class=\"{active: hour === hr}\"\n                  :tabindex=\"isDisabled('hour', hr) ? -1 : tabindex\"\n                  :data-key=\"hr\"\n                  :disabled=\"booleanAttr(isDisabled('hour', hr))\"\n                  v-text=\"hr\"\n                  @click=\"select('hour', hr)\"\n                  @keydown.space.prevent=\"select('hour', hr)\"\n                  @keydown.enter.prevent=\"select('hour', hr)\"\n                  @keydown.up.prevent=\"prevItem('hour', hr)\"\n                  @keydown.down.prevent=\"nextItem('hour', hr)\"\n                  @keydown.left.prevent=\"toLeftColumn('hour')\"\n                  @keydown.right.prevent=\"toRightColumn('hour')\"\n                  @keydown.esc.exact=\"debounceBlur\"\n                  @blur=\"debounceBlur\"\n                  @focus=\"keepFocusing\"></li>\n            </template>\n          </ul>\n          <ul v-if=\"column === 'minute'\" class=\"minutes\" tabindex=\"-1\" @scroll=\"keepFocusing\">\n            <li class=\"hint\" v-text=\"minuteLabelText\" tabindex=\"-1\"></li>\n            <template v-for=\"(m, mIndex) in minutes\" :key=\"mIndex\">\n              <li v-if=\"!opts.hideDisabledMinutes || (opts.hideDisabledMinutes && !isDisabled('minute', m))\"\n                  :class=\"{active: minute === m}\"\n                  :tabindex=\"isDisabled('minute', m) ? -1 : tabindex\"\n                  :data-key=\"m\"\n                  :disabled=\"booleanAttr(isDisabled('minute', m))\"\n                  v-text=\"m\"\n                  @click=\"select('minute', m)\"\n                  @keydown.space.prevent=\"select('minute', m)\"\n                  @keydown.enter.prevent=\"select('minute', m)\"\n                  @keydown.up.prevent=\"prevItem('minute', m)\"\n                  @keydown.down.prevent=\"nextItem('minute', m)\"\n                  @keydown.left.prevent=\"toLeftColumn('minute')\"\n                  @keydown.right.prevent=\"toRightColumn('minute')\"\n                  @keydown.esc.exact=\"debounceBlur\"\n                  @blur=\"debounceBlur\"\n                  @focus=\"keepFocusing\"></li>\n            </template>\n          </ul>\n          <ul v-if=\"column === 'second'\" class=\"seconds\" tabindex=\"-1\" @scroll=\"keepFocusing\">\n            <li class=\"hint\" v-text=\"secondLabelText\" tabindex=\"-1\"></li>\n            <template v-for=\"(s, sIndex) in seconds\" :key=\"sIndex\">\n              <li v-if=\"!opts.hideDisabledSeconds || (opts.hideDisabledSeconds && !isDisabled('second', s))\"\n                  :class=\"{active: second === s}\"\n                  :tabindex=\"isDisabled('second', s) ? -1 : tabindex\"\n                  :data-key=\"s\"\n                  :disabled=\"booleanAttr(isDisabled('second', s))\"\n                  v-text=\"s\"\n                  @click=\"select('second', s)\"\n                  @keydown.space.prevent=\"select('second', s)\"\n                  @keydown.enter.prevent=\"select('second', s)\"\n                  @keydown.up.prevent=\"prevItem('second', s)\"\n                  @keydown.down.prevent=\"nextItem('second', s)\"\n                  @keydown.left.prevent=\"toLeftColumn('second')\"\n                  @keydown.right.prevent=\"toRightColumn('second')\"\n                  @keydown.esc.exact=\"debounceBlur\"\n                  @blur=\"debounceBlur\"\n                  @focus=\"keepFocusing\"></li>\n            </template>\n          </ul>\n          <ul v-if=\"column === 'apm'\" class=\"apms\" tabindex=\"-1\" @scroll=\"keepFocusing\">\n            <li class=\"hint\" v-text=\"apmLabelText\" tabindex=\"-1\"></li>\n            <template v-for=\"(a, aIndex) in apms\" :key=\"aIndex\">\n              <li v-if=\"!opts.hideDisabledHours || (opts.hideDisabledHours && !isDisabled('apm', a))\"\n                  :class=\"{active: apm === a}\"\n                  :tabindex=\"isDisabled('apm', a) ? -1 : tabindex\"\n                  :data-key=\"a\"\n                  :disabled=\"booleanAttr(isDisabled('apm', a))\"\n                  v-text=\"apmDisplayText(a)\"\n                  @click=\"select('apm', a)\"\n                  @keydown.space.prevent=\"select('apm', a)\"\n                  @keydown.enter.prevent=\"select('apm', a)\"\n                  @keydown.up.prevent=\"prevItem('apm', a)\"\n                  @keydown.down.prevent=\"nextItem('apm', a)\"\n                  @keydown.left.prevent=\"toLeftColumn('apm')\"\n                  @keydown.right.prevent=\"toRightColumn('apm')\"\n                  @keydown.esc.exact=\"debounceBlur\"\n                  @blur=\"debounceBlur\"\n                  @focus=\"keepFocusing\"></li>\n            </template>\n          </ul>\n        </template>\n      </template><!-- / Advanced Keyboard Support -->\n    </div>\n  </div>\n</span>\n</template>\n\n<style lang=\"css\">\n.vue__time-picker {\n  display: inline-block;\n  position: relative;\n  font-size: 1em;\n  width: 10em;\n  font-family: sans-serif;\n  vertical-align: middle;\n}\n\n.vue__time-picker * {\n  box-sizing: border-box;\n}\n\n.vue__time-picker input.vue__time-picker-input {\n  border: 1px solid #d2d2d2;\n  width: 10em;\n  height: 2.2em;\n  padding: 0.3em 0.5em;\n  font-size: 1em;\n}\n\n.vue__time-picker input.has-custom-icon {\n  padding-left: 1.8em;\n}\n\n.vue__time-picker input.vue__time-picker-input.invalid:not(.skip-error-style) {\n  border-color: #cc0033;\n  outline-color: #cc0033;\n}\n\n.vue__time-picker input.vue__time-picker-input:disabled,\n.vue__time-picker input.vue__time-picker-input.disabled {\n  color: #d2d2d2;\n}\n\n.vue__time-picker .controls {\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  z-index: 3;\n\n  display: flex;\n  flex-flow: row nowrap;\n  justify-content: flex-end;\n  align-items: stretch;\n\n  /* Prevent browser focusing on the controls layer */\n  pointer-events: none;\n}\n\n.vue__time-picker .controls > * {\n  cursor: pointer;\n  \n  width: auto;\n  display: flex;\n  flex-flow: column nowrap;\n  justify-content: center;\n  align-items: center;\n\n  padding: 0 0.35em;\n\n  color: #d2d2d2;\n  line-height: 100%;\n  font-style: normal;\n\n  /* Resume pointer-events on children components */\n  pointer-events: initial;\n\n  transition: color .2s, opacity .2s;\n}\n\n.vue__time-picker .controls > *:hover {\n  color: #797979;\n}\n\n.vue__time-picker .controls > *:focus,\n.vue__time-picker .controls > *:active {\n  outline: 0;\n}\n\n.vue__time-picker .controls .char {\n  font-size: 1.1em;\n  line-height: 100%;\n\n  /* Vertical align fixes for webkit browsers only */\n  -webkit-margin-before: -0.15em;\n}\n\n.vue__time-picker .custom-icon {\n  z-index: 2;\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  width: 1.8em;\n\n  display: flex;\n  flex-flow: column nowrap;\n  justify-content: center;\n  align-items: center;\n\n  /* pass down mouse events to the <input> underneath */\n  pointer-events: none;\n}\n\n.vue__time-picker .custom-icon img,\n.vue__time-picker .custom-icon svg,\n.vue__time-picker .controls img,\n.vue__time-picker .controls svg {\n  display: inline-block;\n  vertical-align: middle;\n  margin: 0;\n  border: 0;\n  outline: 0;\n  max-width: 1em;\n  height: auto;\n}\n\n.vue__time-picker .time-picker-overlay {\n  z-index: 4;\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n}\n\n.vue__time-picker .dropdown {\n  position: absolute;\n  z-index: 5;\n  top: calc(2.2em + 2px);\n  left: 0;\n  background: #fff;\n  box-shadow: 0 1px 6px rgba(0,0,0,0.15);\n  width: 10em;\n  height: 10em;\n  font-weight: normal;\n}\n\n.vue__time-picker .dropdown.drop-up {\n  top: auto;\n  bottom: calc(2.2em + 1px);\n}\n\n.vue__time-picker .dropdown .select-list {\n  width: 10em;\n  height: 10em;\n  overflow: hidden;\n  display: flex;\n  flex-flow: row nowrap;\n  align-items: stretch;\n  justify-content: space-between;\n}\n\n.vue__time-picker .dropdown .select-list:focus,\n.vue__time-picker .dropdown .select-list:active {\n  outline: 0;\n}\n\n.vue__time-picker .dropdown ul {\n  padding: 0;\n  margin: 0;\n  list-style: none;\n  outline: 0;\n\n  flex: 1 1 0.00001px;\n  overflow-x: hidden;\n  overflow-y: auto;\n}\n\n.vue__time-picker .dropdown ul.minutes,\n.vue__time-picker .dropdown ul.seconds,\n.vue__time-picker .dropdown ul.apms{\n  border-left: 1px solid #fff;\n}\n\n.vue__time-picker .dropdown ul li {\n  list-style: none;\n  text-align: center;\n  padding: 0.3em 0;\n  color: #161616;\n}\n\n.vue__time-picker .dropdown ul li:not(.hint):not([disabled]):hover,\n.vue__time-picker .dropdown ul li:not(.hint):not([disabled]):focus {\n  background: rgba(0,0,0,.08);\n  color: #161616;\n  cursor: pointer;\n}\n\n.vue__time-picker .dropdown ul li:not([disabled]).active,\n.vue__time-picker .dropdown ul li:not([disabled]).active:hover,\n.vue__time-picker .dropdown ul li:not([disabled]).active:focus {\n  background: #41B883;\n  color: #fff;\n}\n\n.vue__time-picker .dropdown ul li[disabled],\n.vue__time-picker .dropdown ul li[disabled]:hover {\n  background: transparent;\n  opacity: 0.3;\n  cursor: not-allowed;\n}\n\n.vue__time-picker .dropdown .hint {\n  color: #a5a5a5;\n  cursor: default;\n  font-size: 0.8em;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,IAAM,SAAS;EACb,aAAa,CAAC,MAAM,KAAK,MAAM,KAAK,MAAM,GAAG;EAC7C,eAAe,CAAC,MAAM,GAAG;EACzB,eAAe,CAAC,MAAM,GAAG;EACzB,YAAY,CAAC,KAAK,GAAG;EACrB,aAAa,CAAC,QAAQ,UAAU,UAAU,KAAK;AACjD;AAEA,IAAM,kBAAkB;EACtB,QAAQ;EACR,gBAAgB;EAChB,gBAAgB;EAChB,WAAW;EACX,aAAa;EACb,aAAa;EACb,mBAAmB;EACnB,qBAAqB;EACrB,qBAAqB;EACrB,mBAAmB;EACnB,kBAAkB;EAClB,cAAc;EACd,WAAW;EACX,oBAAoB;EACpB,kBAAkB;AACpB;AAEA,IAAA,SAAe;EACb,MAAM;EAEN,OAAO;IACL,YAAY,EAAE,MAAM,CAAE,QAAQ,MAAA,EAAA;IAC9B,QAAQ,EAAE,MAAM,OAAA;IAChB,gBAAgB,EAAE,MAAM,CAAE,QAAQ,MAAA,EAAA;IAClC,gBAAgB,EAAE,MAAM,CAAE,QAAQ,MAAA,EAAA;IAElC,WAAW,EAAE,MAAM,MAAA;IACnB,aAAa,EAAE,MAAM,MAAA;IACrB,aAAa,EAAE,MAAM,MAAA;IAErB,mBAAmB,EAAE,MAAM,SAAS,SAAS,MAAA;IAC7C,qBAAqB,EAAE,MAAM,SAAS,SAAS,MAAA;IAC/C,qBAAqB,EAAE,MAAM,SAAS,SAAS,MAAA;IAC/C,mBAAmB,EAAE,MAAM,SAAS,SAAS,MAAA;IAE7C,iBAAiB,EAAE,MAAM,SAAS,SAAS,MAAA;IAC3C,UAAU,EAAE,MAAM,SAAS,SAAS,MAAA;IACpC,iBAAiB,EAAE,MAAM,SAAS,SAAS,MAAA;IAE3C,IAAI,EAAE,MAAM,OAAA;IACZ,MAAM,EAAE,MAAM,OAAA;IACd,YAAY,EAAE,MAAM,CAAE,QAAQ,QAAQ,KAAA,EAAA;IACtC,aAAa,EAAE,MAAM,OAAA;IACrB,UAAU,EAAE,MAAM,CAAE,QAAQ,MAAA,GAAU,SAAS,EAAA;IAC/C,YAAY,EAAE,MAAM,OAAA;IACpB,cAAc,EAAE,MAAM,QAAQ,SAAS,MAAA;IAEvC,WAAW,EAAE,MAAM,OAAA;IACnB,aAAa,EAAE,MAAM,OAAA;IACrB,aAAa,EAAE,MAAM,OAAA;IACrB,UAAU,EAAE,MAAM,OAAA;IAClB,QAAQ,EAAE,MAAM,OAAA;IAChB,QAAQ,EAAE,MAAM,OAAA;IAEhB,WAAW,EAAE,MAAM,CAAE,QAAQ,MAAA,EAAA;IAC7B,kBAAkB,EAAE,MAAM,SAAS,SAAS,MAAA;IAE5C,MAAM,EAAE,MAAM,SAAS,SAAS,MAAA;IAChC,YAAY,EAAE,MAAM,SAAS,SAAS,MAAA;IAEtC,eAAe,EAAE,MAAM,QAAQ,SAAS,OAAA;IACxC,kBAAkB,EAAE,MAAM,CAAE,QAAQ,MAAA,EAAA;IACpC,aAAa,EAAE,MAAM,OAAA;IAErB,aAAa,EAAE,MAAM,SAAS,SAAS,MAAA;IACvC,oBAAoB,EAAE,MAAM,CAAE,QAAQ,MAAA,EAAA;IACtC,cAAc,EAAE,MAAM,SAAS,SAAS,MAAA;IACxC,qBAAqB,EAAE,MAAM,SAAS,SAAS,MAAA;IAE/C,WAAW,EAAE,MAAM,SAAS,SAAS,MAAA;;EAGvC,OAAQ;AACN,WAAO;MACL,WAAW,CAAA;MAEX,OAAO,CAAA;MACP,SAAS,CAAA;MACT,SAAS,CAAA;MACT,MAAM,CAAA;MAEN,UAAU;MACV,cAAc;MACd,YAAY;MACZ,eAAe;MAEf,UAAU;MACV,YAAY;MACZ,YAAY;MACZ,SAAS;MACT,MAAM;MACN,QAAQ;MACR,QAAQ;MACR,KAAK;MACL,YAAY;MACZ,gBAAgB;MAChB,oBAAoB;MAEpB,gBAAgB;MAChB,cAAc;MACd,YAAY;MACZ,eAAe;MACf,gBAAgB;;;EAIpB,OAAO,CAAC,qBAAqB,UAAU,QAAQ,SAAS,SAAS,QAAQ,OAAO;EAEhF,UAAU;IACR,OAAQ;AACN,YAAM,UAAU,OAAO,OAAO,CAAA,GAAI,eAAe;AAEjD,UAAI,KAAK,UAAU,KAAK,OAAO,QAAQ;AACrC,gBAAQ,SAAS,OAAO,KAAK,MAAM;;AAGrC,UAAI,KAAK,SAAS,KAAK,cAAc,GAAG;AACtC,gBAAQ,iBAAiB,CAAC,KAAK;;AAGjC,UAAI,CAAC,QAAQ,kBAAkB,QAAQ,iBAAiB,KAAK,QAAQ,iBAAiB,IAAI;AACxF,YAAI,KAAK,WAAW;AAClB,cAAI,QAAQ,iBAAiB,IAAI;AAC/B,iBAAK,SAAS,8DAA8D,KAAK,cAAc,EAAE;qBACxF,QAAQ,mBAAmB,KAAK,QAAQ,iBAAiB,GAAG;AACrE,iBAAK,SAAS,gEAAgE,KAAK,cAAc,EAAE;;;AAGvG,YAAI,QAAQ,mBAAmB,GAAG;AAChC,kBAAQ,iBAAiB;eACpB;AACL,kBAAQ,iBAAiB;;;AAI7B,UAAI,KAAK,SAAS,KAAK,cAAc,GAAG;AACtC,gBAAQ,iBAAiB,CAAC,KAAK;;AAGjC,UAAI,CAAC,QAAQ,kBAAkB,QAAQ,iBAAiB,KAAK,QAAQ,iBAAiB,IAAI;AACxF,YAAI,KAAK,WAAW;AAClB,cAAI,QAAQ,iBAAiB,IAAI;AAC/B,iBAAK,SAAS,8DAA8D,KAAK,cAAc,EAAE;qBACxF,QAAQ,mBAAmB,KAAK,QAAQ,iBAAiB,GAAG;AACrE,iBAAK,SAAS,gEAAgE,KAAK,cAAc,EAAE;;;AAGvG,YAAI,QAAQ,mBAAmB,GAAG;AAChC,kBAAQ,iBAAiB;eACpB;AACL,kBAAQ,iBAAiB;;;AAI7B,UAAI,KAAK,aAAa,MAAM,QAAQ,KAAK,SAAS,GAAG;AACnD,gBAAQ,YAAY,KAAK,MAAM,KAAK,UAAU,KAAK,SAAS,CAAC;AAC7D,YAAI,CAAC,KAAK,UAAU,UAAU,KAAK,WAAW;AAC5C,eAAK,SAAS,gDAAgD;;;AAIlE,UAAI,KAAK,eAAe,MAAM,QAAQ,KAAK,WAAW,GAAG;AACvD,gBAAQ,cAAc,KAAK,MAAM,KAAK,UAAU,KAAK,WAAW,CAAC;AACjE,YAAI,CAAC,KAAK,YAAY,UAAU,KAAK,WAAW;AAC9C,eAAK,SAAS,kDAAkD;;;AAIpE,UAAI,KAAK,eAAe,MAAM,QAAQ,KAAK,WAAW,GAAG;AACvD,gBAAQ,cAAc,KAAK,MAAM,KAAK,UAAU,KAAK,WAAW,CAAC;AACjE,YAAI,CAAC,KAAK,YAAY,UAAU,KAAK,WAAW;AAC9C,eAAK,SAAS,kDAAkD;;;AAIpE,UAAI,KAAK,mBAAmB;AAC1B,gBAAQ,oBAAoB;;AAG9B,UAAI,KAAK,qBAAqB,KAAK,mBAAmB;AACpD,gBAAQ,oBAAoB;;AAE9B,UAAI,KAAK,uBAAuB,KAAK,mBAAmB;AACtD,gBAAQ,sBAAsB;;AAEhC,UAAI,KAAK,uBAAuB,KAAK,mBAAmB;AACtD,gBAAQ,sBAAsB;;AAGhC,UAAI,KAAK,cAAc;AACrB,YAAI,KAAK,aAAa;AACpB,kBAAQ,eAAe;mBACd,KAAK,WAAW;AACzB,eAAK,SAAS,qDAAqD;;;AAIvE,UAAI,KAAK,aAAa,CAAC,KAAK,YAAY,GAAG;AACzC,gBAAQ,YAAY,CAAC,KAAK;;AAG5B,UAAI,KAAK,sBAAsB,CAAC,KAAK,qBAAqB,GAAG;AAC3D,gBAAQ,qBAAqB,CAAC,KAAK;;AAGrC,UAAI,KAAK,oBAAoB,CAAC,KAAK,mBAAmB,GAAG;AACvD,gBAAQ,mBAAmB,CAAC,KAAK;;AAGnC,aAAO;;IAGT,iBAAkB;AAChB,aAAO,OAAO,KAAK,eAAe;;IAGpC,eAAgB;AACd,aAAO,KAAK,KAAK,UAAU,gBAAgB;;IAG7C,QAAS;AACP,YAAM,aAAa,OAAO,YAAY,OAAO,UAAQ,KAAK,eAAe,IAAI,CAAC;AAE9E,iBAAW,KAAK,CAAC,GAAG,MAAM;AACxB,eAAO,KAAK,aAAa,QAAQ,KAAK,eAAe,CAAC,KAAK,IAAI,IAAI,KAAK,aAAa,QAAQ,KAAK,eAAe,CAAC,KAAK,IAAI;OAC5H;AACD,YAAM,cAAc,WAAW,IAAI,UAAQ,KAAK,eAAe,IAAI,CAAC;AACpE,aAAO;QACL,MAAM,CAAC,CAAC,KAAK;QACb,QAAQ,CAAC,CAAC,KAAK;QACf,QAAQ,CAAC,CAAC,KAAK;QACf,KAAK,CAAC,CAAC,KAAK;QACZ,OAAO,cAAc,CAAA;QACrB,QAAQ,eAAe,CAAA;;;IAI3B,cAAe;AACb,UAAI,eAAe,OAAO,KAAK,YAAY;AAC3C,UAAI,KAAK,MAAM;AACb,uBAAe,aAAa,QAAQ,IAAI,OAAO,KAAK,UAAU,GAAG,GAAG,KAAK,IAAI;;AAE/E,UAAI,KAAK,QAAQ;AACf,uBAAe,aAAa,QAAQ,IAAI,OAAO,KAAK,YAAY,GAAG,GAAG,KAAK,MAAM;;AAEnF,UAAI,KAAK,UAAU,KAAK,YAAY;AAClC,uBAAe,aAAa,QAAQ,IAAI,OAAO,KAAK,YAAY,GAAG,GAAG,KAAK,MAAM;;AAEnF,UAAI,KAAK,OAAO,KAAK,SAAS;AAC5B,uBAAe,aAAa,QAAQ,IAAI,OAAO,KAAK,SAAS,GAAG,GAAG,KAAK,GAAG;;AAE7E,aAAO;;IAGT,oBAAqB;AACnB,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,QAAQ;AAChC,eAAO,KAAK;;AAEd,aAAO,KAAK,YAAY,QAAQ,IAAI,OAAO,KAAK,KAAK,GAAG,GAAG,KAAK,eAAe,KAAK,GAAG,CAAC;;IAG1F,eAAgB;AACd,aAAO,KAAK,iBAAiB,KAAK;;IAGpC,mBAAoB;AAClB,UACG,KAAK,MAAM,QAAQ,CAAC,KAAK,QACzB,KAAK,MAAM,UAAU,CAAC,KAAK,UAC3B,KAAK,MAAM,UAAU,CAAC,KAAK,UAC3B,KAAK,MAAM,OAAO,CAAC,KAAK,KACzB;AACA,eAAO;;AAET,aAAO;;IAGT,kBAAmB;AACjB,aAAO,KAAK,MAAM,MAAM,IAAI,UAAQ,IAAI,KAAK,CAAA;;IAG/C,eAAgB;AACd,UAAI,KAAK,mBAAmB,KAAK,UAAU;AACzC,eAAO;;AAET,aAAO,CAAC,KAAK;;IAGf,kBAAmB;AACjB,UAAI,KAAK,qBAAqB;AAAE,eAAO;MAAA;AACvC,UAAI,KAAK,KAAK,gBAAgB,KAAK,YAAY,CAAC,KAAK,cAAc;AACjE,eAAO;;AAET,aAAO;;IAGT,gBAAiB;AACf,aAAO,KAAK,aAAa,OAAO,KAAK,aAAa;;IAGpD,wBAAyB;AACvB,UAAI,CAAC,KAAK,YAAY,CAAC,KAAK,KAAK,WAAW;AAAE,eAAO;MAAA;AACrD,UAAI,CAAC,KAAK,KAAK,UAAU,QAAQ;AAAE,eAAO,CAAA;MAAA;AAE1C,YAAM,QAAQ,CAAA;AACd,WAAK,KAAK,UAAU,QAAQ,WAAS;AACnC,YAAI,iBAAiB,OAAO;AAC1B,cAAI,MAAM,SAAS,KAAK,KAAK,WAAW;AACtC,iBAAK,SAAS,qGAAqG,KAAK,UAAU,KAAK,CAAC,8BAA8B;;AAGxK,cAAI,QAAQ,MAAM,CAAC;AACnB,cAAI,MAAM,MAAM,CAAC,KAAK,MAAM,CAAC;AAE7B,cAAI,KAAK,WAAW,KAAK,GAAG;AAC1B,oBAAQ,KAAK,kBAAkB,KAAK;;AAEtC,cAAI,KAAK,WAAW,GAAG,GAAG;AACxB,kBAAM,KAAK,kBAAkB,GAAG;;AAGlC,mBAAS,IAAI,CAAC,OAAO,KAAK,CAAC,KAAK,KAAK;AACnC,gBAAI,IAAI,KAAK,IAAI,IAAI;AAAE;YAAA;AACvB,gBAAI,CAAC,MAAM,SAAS,CAAC,GAAG;AACtB,oBAAM,KAAK,CAAC;;;eAGX;AACL,cAAI,KAAK,WAAW,KAAK,GAAG;AAC1B,oBAAQ,KAAK,kBAAkB,KAAK;iBAC/B;AACL,oBAAQ,CAAC;;AAEX,cAAI,QAAQ,KAAK,QAAQ,IAAI;AAAE;UAAA;AAC/B,cAAI,CAAC,MAAM,SAAS,KAAK,GAAG;AAC1B,kBAAM,KAAK,KAAK;;;OAGrB;AACD,YAAM,KAAK,CAAC,GAAG,MAAM;AAAE,eAAO,IAAI;MAAA,CAAG;AACrC,aAAO;;IAGT,sBAAuB;AAErB,UAAI,CAAC,KAAK,uBAAuB;AAAE,eAAO;MAAA;AAE1C,UAAI,KAAK,eAAe;AACtB,cAAM,QAAQ,KAAK,sBAAsB,IAAI,CAAC,UAAU;AACtD,cAAI,UAAU,IAAI;AAChB,mBAAO;qBACE,UAAU,MAAM,UAAU,GAAG;AACtC,mBAAO;;AAET,iBAAO,QAAQ,KAAK,GAAG,QAAQ,EAAE,MAAM,GAAG,KAAK;SAChD;AACD,eAAO;;AAGT,aAAO,KAAK;;IAGd,iBAAkB;AAChB,UAAI,CAAC,KAAK,aAAa;AAAE,eAAO;MAAA;AAChC,UAAI,KAAK,qBAAqB;AAC5B,YAAI,OAAO,CAAA;AACX,YAAI,KAAK,eAAe;AACtB,iBAAO,KAAK,oBAAoB,IAAI,QAAM;AACxC,kBAAM,IAAI,GAAG,OAAO,GAAG,GAAG,SAAS,CAAC;AACpC,kBAAM,IAAI,GAAG,OAAO,EAAE;AACtB,mBAAO,GAAG,KAAK,YAAY,KAAK,UAAU,CAAC,CAAC,GAAG,CAAC;WACjD;AACD,gBAAM,YAAY,KAAK,QAAQ,KAAK;AACpC,cAAI,YAAY,GAAG;AAEjB,iBAAK,QAAQ,KAAK,OAAO,WAAW,CAAC,EAAE,CAAC,CAAC;;AAE3C,iBAAO;;AAET,eAAO,KAAK,oBAAoB,IAAI,QAAM;AACxC,iBAAO,KAAK,YAAY,KAAK,UAAU,EAAE;SAC1C;AACD,YAAI,KAAK,SAAS,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,MAAM,MAAM;AAElD,eAAK,KAAK,KAAK,MAAK,CAAE;;AAExB,eAAO;;AAET,UAAI,KAAK,eAAe;AACtB,eAAO,CAAA,EAAG,OAAO,CAAA,GAAI,KAAK,MAAM,IAAI,QAAM,GAAG,EAAE,GAAG,GAAG,KAAK,MAAM,IAAI,QAAM,GAAG,EAAE,GAAG,CAAC;;AAErF,aAAO,KAAK;;IAGd,MAAO;AACL,YAAM,SAAS;QACb,eAAe;;AAEjB,YAAM,aAAa,CAAC,CAAC,KAAK;AAE1B,UAAI,cAAc,KAAK,yBAAyB,KAAK,sBAAsB,QAAQ;AACjF,cAAM,QAAQ,CAAA,EAAG,OAAO,CAAA,GAAI,KAAK,qBAAqB;AACtD,eAAO,KAAK,MAAM,KAAK,WAAS,QAAQ,MAAM,UAAU,EAAE;AAC1D,eAAO,KAAK,MAAM,KAAK,WAAS,SAAS,MAAM,QAAQ,EAAE;aACpD;AACL,eAAO,KAAK;AACZ,eAAO,KAAK;;AAEd,UAAK,KAAK,UAAU,KAAK,OAAO,UAAY,KAAK,UAAU,KAAK,OAAO,QAAS;AAC9E,eAAO,gBAAgB;;AAEzB,aAAO;;IAGT,kBAAmB;AACjB,UAAI,CAAC,KAAK,cAAc,CAAC,KAAK,KAAK,aAAa;AAAE,eAAO;MAAA;AACzD,UAAI,CAAC,KAAK,KAAK,YAAY,QAAQ;AAAE,eAAO,CAAA;MAAA;AAC5C,aAAO,KAAK,gBAAgB,KAAK,KAAK,aAAa,QAAQ;;IAG7D,kBAAmB;AACjB,UAAI,CAAC,KAAK,cAAc,CAAC,KAAK,KAAK,aAAa;AAAE,eAAO;MAAA;AACzD,UAAI,CAAC,KAAK,KAAK,YAAY,QAAQ;AAAE,eAAO,CAAA;MAAA;AAC5C,aAAO,KAAK,gBAAgB,KAAK,KAAK,aAAa,QAAQ;;IAG7D,gBAAiB;AACf,aAAO,KAAK,aAAa,KAAK;;IAEhC,kBAAmB;AACjB,aAAO,KAAK,eAAe,KAAK;;IAElC,kBAAkB;AAChB,aAAO,KAAK,eAAe,KAAK;;IAElC,eAAgB;AACd,aAAO,KAAK,YAAY,KAAK;;IAG/B,kBAAmB;AACjB,UAAI,CAAC,KAAK,cAAc,CAAC,KAAK,WAAW,QAAQ;AAAE;MAAA;AACnD,aAAO;QACL,OAAO,KAAK;;;IAIhB,iBAAkB;AAChB,aAAO,KAAK,MAAM,OAAO,KAAK,GAAG;;IAGnC,cAAe;AACb,UAAI,CAAC,KAAK,eAAe,CAAC,KAAK,gBAAgB;AAAE,eAAO;MAAA;AAExD,YAAM,eAAe,OAAO,KAAK,YAAY;AAC7C,YAAM,gBAAgB,IAAI,KAAK,cAAc;AAC7C,YAAM,iBAAiB,KAAK,mBAAmB,cAAc,aAAa;AAE1E,YAAM,cAAc,CAAA;AACpB,eAAS,WAAW,gBAAgB;AAClC,cAAM,WAAW,QAAQ,CAAC;AAC1B,cAAM,iBAAiB;UACrB,OAAO,QAAQ;UACf,OAAO;UACP,MAAM,KAAK,aAAa,QAAQ;UAChC,gBAAgB,SAAS,SAAS;UAClC,MAAM,YAAY,IAAI;;AAExB,oBAAY,KAAK,cAAc;;AAEjC,aAAO;;IAGT,oBAAqB;AACnB,UAAI,CAAC,KAAK,aAAa;AAAE,eAAO;MAAA;AAChC,aAAO,KAAK,YAAY,KAAK,SAAO,IAAI,cAAc;;IAGxD,iBAAkB;AAChB,UAAI,CAAC,KAAK,aAAa;AAAE,eAAO;MAAA;AAChC,UAAI,CAAC,KAAK,mBAAmB;AAC3B,eAAO,KAAK,YAAY,IAAI,SAAO;AACjC,iBAAO;YACL,OAAO,IAAI;YACX,MAAM,IAAI;YACV,OAAO,IAAI;YACX,KAAK,IAAI,QAAQ,IAAI;;SAExB;;AAEH,YAAM,OAAO,CAAA;AACb,UAAI,eAAe;AACnB,WAAK,YAAY,QAAQ,SAAO;AAC9B,YAAI;AAEJ,YAAI,IAAI,SAAS,SAAS,KAAK,IAAI,eAAe;AAChD,cAAI,KAAK,OAAO,KAAK,IAAI,QAAQ;AAC/B,kBAAM,gBAAgB,KAAK,IAAI,YAAW,MAAO,OAAO,KAAK,SAAS,KAAK;AAC3E,8BAAmB,iBAAiB,cAAc,SAAU,cAAc,SAAS,IAAI;iBAClF;AACL,8BAAkB,IAAI;;eAGnB;AACL,4BAAkB,KAAK,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,EAAE,SAAS,KAAK,IAAI,IAAI,EAAE,SAAS,IAAI;;AAE1F,aAAK,KAAK;UACR,OAAO,IAAI;UACX,MAAM,IAAI;UACV,OAAO,IAAI,QAAQ;UACnB,KAAK,IAAI,QAAQ,eAAe;SACjC;AACD,YAAI,IAAI,kBAAkB,kBAAkB,IAAI,KAAK;AACnD,0BAAiB,kBAAkB,IAAI;;OAE1C;AACD,aAAO;;IAGT,gBAAiB;AACf,UAAI,KAAK,cAAc;AAAE,eAAO,CAAA;MAAA;AAChC,UAAI,CAAC,KAAK,uBAAuB,CAAC,KAAK,mBAAmB,CAAC,KAAK,mBAAmB,KAAK,KAAK,mBAAmB,KAAK,KAAK,KAAK,mBAAmB,GAAG;AAAE,eAAO,CAAA;MAAA;AAE9J,YAAM,SAAS,CAAA;AACf,UAAI,KAAK,MAAM,QAAQ,CAAC,KAAK,aAAa,KAAK,UAAU,KAAK,IAAI,MAAM,CAAC,KAAK,aAAa,KAAK,UAAU,KAAK,IAAI,KAAK,KAAK,WAAW,QAAQ,KAAK,IAAI,IAAI;AAC3J,eAAO,KAAK,MAAM;;AAEpB,UAAI,KAAK,MAAM,UAAU,CAAC,KAAK,aAAa,KAAK,YAAY,KAAK,MAAM,MAAM,CAAC,KAAK,aAAa,KAAK,YAAY,KAAK,MAAM,KAAK,KAAK,WAAW,UAAU,KAAK,MAAM,KAAK,KAAK,cAAc,UAAU,KAAK,MAAM,IAAI;AACtN,eAAO,KAAK,QAAQ;;AAEtB,UAAI,KAAK,MAAM,UAAU,CAAC,KAAK,aAAa,KAAK,YAAY,KAAK,MAAM,MAAM,CAAC,KAAK,aAAa,KAAK,YAAY,KAAK,MAAM,KAAK,KAAK,WAAW,UAAU,KAAK,MAAM,KAAK,KAAK,cAAc,UAAU,KAAK,MAAM,IAAI;AACtN,eAAO,KAAK,QAAQ;;AAEtB,UAAI,KAAK,MAAM,OAAO,CAAC,KAAK,aAAa,KAAK,SAAS,KAAK,GAAG,MAAM,CAAC,KAAK,aAAa,KAAK,SAAS,KAAK,GAAG,KAAK,KAAK,WAAW,OAAO,KAAK,GAAG,IAAI;AACpJ,eAAO,KAAK,KAAK;;AAEnB,UAAI,OAAO,QAAQ;AACjB,eAAO;;AAET,aAAO,CAAA;;IAGT,kBAAmB;AACjB,aAAO,QAAQ,KAAK,iBAAiB,KAAK,cAAc,MAAM;;IAGhE,uBAAwB;AACtB,aAAO,KAAK,kBAAkB;;IAGhC,mBAAoB;AAClB,UAAI,KAAK,sBAAsB;AAC7B,eAAO,KAAK,iBAAiB,YAAY;;AAE3C,aAAO,KAAK,kBAAkB,OAAO,YAAY;;;EAIrD,OAAO;IACL,cAAe,UAAU;AACvB,WAAK,aAAa,QAAQ;;IAE5B,sBAAuB,YAAY;AACjC,WAAK,WAAW,UAAU,UAAU;;IAEtC,sBAAuB,YAAY;AACjC,WAAK,WAAW,UAAU,UAAU;;IAEtC,OAAO;MACL,MAAM;MACN,UAAW;AACT,aAAK,WAAU;;;IAGnB,cAAe;AACb,WAAK,WAAU;;IAEjB,SAAU,YAAY;AACpB,UAAI,YAAY;AAEd,YAAI,KAAK,UAAU;AACjB,eAAK,WAAW;;AAElB,YAAI,KAAK,cAAc;AACrB,eAAK,eAAe;;;;IAI1B,uBAAwB,WAAW,WAAW;AAC5C,UAAI,aAAa,aAAa,GAAG;AAC/B,aAAK,MAAM,SAAS,KAAK,aAAa;iBAC7B,aAAa,aAAa,GAAG;AACtC,aAAK,MAAM,SAAS,CAAA,CAAE;;;;EAK5B,SAAS;IACP,YAAa,OAAO,GAAG;AACrB,UAAI,CAAC,KAAK,SAAS,CAAC,GAAG;AAAE,eAAO;MAAA;AAChC,UAAI,CAAC;AACL,cAAQ,OAAK;QACX,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;AACH,cAAI,CAAC,KAAK,GAAG,EAAE,SAAS,KAAK,KAAK,MAAM,GAAG;AACzC,mBAAO,UAAU,MAAM,OAAO;;AAEhC,iBAAO,OAAO,CAAC;QACjB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;AACH,cAAI,CAAC,MAAM,IAAI,EAAE,SAAS,KAAK,KAAK,MAAM,GAAG;AAC3C,mBAAO,UAAU,OAAO,OAAO;;AAEjC,iBAAO,IAAI,KAAK,IAAI,CAAC,KAAK,OAAO,CAAC;QACpC;AACE,iBAAO;;;IAIb,mBAAoB,aAAa,cAAc;AAC7C,UAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,aAAa,QAAQ;AAAE,eAAO;MAAA;AACpE,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAI,aAAa,QAAQ,YAAY,CAAC,CAAC,IAAI,IAAI;AAC7C,iBAAO,YAAY,CAAC;;;AAGxB,aAAO;;IAGT,aAAc,WAAW;AACvB,kBAAY,aAAa,KAAK,KAAK,UAAU,gBAAgB;AAE7D,UAAI,WAAW,KAAK,mBAAmB,OAAO,aAAa,SAAS;AACpE,UAAI,aAAa,KAAK,mBAAmB,OAAO,eAAe,SAAS;AACxE,WAAK,aAAa,KAAK,mBAAmB,OAAO,eAAe,SAAS;AACzE,WAAK,UAAU,KAAK,mBAAmB,OAAO,YAAY,SAAS;AAGnE,UAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,cAAc,CAAC,KAAK,SAAS;AACjE,YAAI,KAAK,aAAa,KAAK,QAAQ;AACjC,eAAK,SAAS,0DAA0D,KAAK,MAAM,4CAA4C;;AAEjI,mBAAW;AACX,qBAAa;;AAEf,WAAK,WAAW;AAChB,WAAK,aAAa;AAElB,WAAK,WAAW,KAAK,gBAAe,IAAK,KAAK,QAAQ,CAAA;AACtD,WAAK,aAAa,KAAK,WAAW,QAAQ,IAAI,KAAK,UAAU,CAAA;AAC7D,WAAK,aAAa,KAAK,WAAW,QAAQ,IAAI,KAAK,UAAU,CAAA;AAC7D,WAAK,UAAU,KAAK,cAAa,IAAK,KAAK,OAAO,CAAA;AAElD,eAAS,MAAM;AACb,aAAK,WAAU;OAChB;;IAGH,kBAAmB;AACjB,YAAM,aAAa,KAAK,gBAAgB,KAAK;AAC7C,YAAM,QAAQ,CAAA;AACd,eAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACnC,YAAI,KAAK,aAAa,OAAO,KAAK,aAAa,MAAM;AACnD,gBAAM,KAAK,KAAK,YAAY,KAAK,UAAU,IAAI,CAAC,CAAC;eAC5C;AACL,gBAAM,KAAK,KAAK,YAAY,KAAK,UAAU,CAAC,CAAC;;;AAGjD,WAAK,QAAQ;;IAGf,WAAY,UAAU,UAAU;AAC9B,UAAI,CAAC,KAAK,iBAAiB,QAAQ,GAAG;AAAE;MAAA;AAExC,YAAM,WAAW,aAAa;AAC9B,iBAAW,aAAa,WAAY,KAAK,KAAK,kBAAkB,gBAAgB,iBAAmB,KAAK,KAAK,kBAAkB,gBAAgB;AAE/I,YAAM,SAAS,CAAA;AACf,eAAS,IAAI,GAAG,IAAI,IAAI,KAAK,UAAU;AACrC,eAAO,KAAK,KAAK,YAAY,WAAW,KAAK,aAAa,KAAK,YAAY,CAAC,CAAC;;AAE/E,iBAAW,KAAK,UAAU,SAAS,KAAK,UAAU;;IAGpD,gBAAiB;AACf,WAAK,OAAO,KAAK,YAAY,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI;;IAG/D,aAAc;AACZ,UAAI,KAAK,gBAAgB;AACvB,YAAI,KAAK,WAAW;AAClB,eAAK,SAAS,6BAA6B,KAAK,UAAU,GAAG;;AAE/D,aAAK,iBAAiB,KAAK,UAAU;aAChC;AACL,YAAI,KAAK,WAAW;AAClB,eAAK,SAAS,8BAA8B,KAAK,UAAU,KAAK,cAAc,CAAA,CAAE,CAAC,GAAG;;AAEtF,aAAK,iBAAiB,KAAK,UAAU;;;IAIzC,iBAAkB,UAAU;AAC1B,YAAM,YAAY,KAAK,MAAM,KAAK,UAAU,YAAY,CAAA,CAAE,CAAC;AAC3D,YAAM,SAAS,OAAO,KAAK,SAAS;AAGpC,UAAI,OAAO,WAAW,GAAG;AACvB,aAAK,kBAAiB;AACtB;;AAGF,aAAO,YAAY,QAAQ,UAAQ;AACjC,cAAM,QAAQ,KAAK,eAAe,IAAI;AACtC,YAAI,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC9B,gBAAM,iBAAiB,KAAK,eAAe,OAAO,UAAU,KAAK,CAAC;AAClE,eAAK,IAAI,IAAI;AACb,oBAAU,KAAK,IAAI;eACd;AACL,eAAK,IAAI,IAAI;;OAEhB;AACD,WAAK,YAAY;;IAGnB,mBAAoB,YAAY,aAAa;AAC3C,YAAM,MAAM;AACZ,YAAM,gBAAgB,QAAQ,CAAC,IAAI,YAAY,OAAO,IAAI,aAAa,UAAU;AACjF,aAAO,gBAAgB,KAAK,iBAAiB,YAAY,WAAW,IAAI,WAAW,SAAS,IAAI,OAAO,aAAa,GAAG,CAAC;;IAG1H,iBAAkB,aAAa;AAE7B,UAAI,CAAC,eAAe,CAAC,YAAY,QAAQ;AACvC,aAAK,kBAAiB;AACtB;;AAGF,YAAM,eAAe,OAAO,KAAK,YAAY;AAC7C,YAAM,gBAAgB,IAAI,KAAK,cAAc;AAC7C,YAAM,gBAAgB,MAAM,KAAK,cAAc;AAE/C,YAAM,iBAAiB,KAAK,mBAAmB,cAAc,aAAa;AAC1E,YAAM,iBAAiB,KAAK,mBAAmB,cAAc,aAAa;AAE1E,YAAM,SAAS,CAAA;AACf,YAAM,cAAc,CAAA;AAEpB,eAAS,WAAW,gBAAgB;AAClC,cAAM,iBAAiB;UACrB,OAAO,QAAQ;UACf,OAAO,QAAQ,CAAC;UAChB,cAAc;;AAEhB,eAAO,KAAK,cAAc;AAC1B,oBAAY,KAAK,cAAc;;AAGjC,eAAS,WAAW,gBAAgB;AAClC,eAAO,KAAK;UACV,OAAO,QAAQ;UACf,OAAO,QAAQ,CAAC;SACjB;;AAGH,aAAO,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,QAAQ,KAAK,CAAC;AAEhD,UAAI,aAAa;AACjB,aAAO,QAAQ,WAAS;AACtB,YAAI,MAAM,cAAc;AACtB,gBAAM,aAAa,KAAK,cAAc,MAAM,KAAK,KAAK;AACtD,wBAAc;eACT;AACL,gBAAM,YAAY,MAAM,MAAM,QAAQ,uBAAuB,MAAM;AACnE,wBAAc,MAAM,SAAS;;OAEhC;AAED,YAAM,WAAW,IAAI,OAAO,UAAU;AAGtC,UAAI,SAAS,KAAK,WAAW,GAAG;AAC9B,cAAM,eAAe,YAAY,MAAM,IAAI,OAAO,UAAU,CAAC;AAC7D,cAAM,eAAe,aAAa,MAAM,GAAG,YAAY,SAAS,CAAC;AACjE,cAAM,YAAY,CAAA;AAClB,qBAAa,QAAQ,CAAC,OAAO,YAAY;AACvC,cAAI,YAAY,OAAO,GAAG;AACxB,kBAAM,cAAc,YAAY,OAAO,EAAE;AACzC,sBAAU,WAAW,IAAI,KAAK,mBAAmB,OAAO,WAAW;;SAEtE;AACD,aAAK,YAAY;AAEjB,YAAI,KAAK,WAAW;AAClB,gBAAM,oBAAoB,YAAY,IAAI,YAAU,UAAU,OAAO,KAAK;AAC1E,eAAK,SAAS,8BAA8B,KAAK,UAAU,YAAY,CAAC;MAAS,KAAK,UAAU,iBAAiB,CAAC;qBAAwB,KAAK,YAAY,GAAG;;aAE3J;AACL,YAAI,KAAK,WAAW;AAClB,eAAK,SAAS;UAA8E,KAAK,YAAY;WAAc,WAAW,EAAE;;;;IAK9I,iBAAkB,cAAc,SAAS;AACvC,YAAM,cAAc,aAAa,MAAM,IAAI,OAAO,SAAS,GAAG,CAAC;AAC/D,YAAM,SAAS,CAAA;AACf,YAAM,aAAa,CAAA;AACnB,UAAI,eAAe,YAAY,QAAQ;AACrC,oBAAY,QAAQ,iBAAe;AACjC,gBAAM,aAAa,WAAW,UAAU,aAAW,QAAQ,QAAQ,WAAW;AAC9E,cAAI;AACJ,cAAI,cAAc,GAAG;AACnB,gBAAI,WAAW,UAAU,KAAK,WAAW,UAAU,EAAE,OAAO;AAC1D,sBAAQ,WAAW,UAAU,EAAE,MAAM,KAAK,YAAY,EAAE;;iBAErD;AACL,kBAAM,mBAAmB,IAAI,OAAO,aAAa,GAAG;AACpD,oBAAQ,iBAAiB,KAAK,YAAY,EAAE;AAC5C,uBAAW,KAAK;cACd,KAAK,OAAO,WAAW;cACvB,OAAO;aACR;;AAEH,iBAAO,KAAK;YACV,GAAG,OAAO,WAAW;YACrB;WACD;SACF;;AAEH,aAAO;;IAGT,oBAAqB;AACnB,YAAM,YAAY,CAAA;AAClB,WAAK,MAAM,MAAM,QAAQ,UAAQ;AAC/B,kBAAU,KAAK,eAAe,IAAI,CAAC,IAAI;OACxC;AACD,WAAK,YAAY;;IAGnB,mBAAoB,aAAa,OAAO;AACtC,UAAI,CAAC,SAAS,CAAC,aAAa;AAAE,eAAO;MAAA;AACrC,YAAM,YAAY,KAAK,aAAa,KAAK;AACzC,UAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AAAE,eAAO;MAAA;AAC9C,YAAM,WAAY,gBAAgB,KAAK,eAAe,SAAS,IAAK,cAAc;AAClF,WAAK,SAAS,IAAI;AAClB,aAAO;;IAGT,WAAY,WAAW;AACrB,YAAM,aAAa,CAAA;AAEnB,YAAM,WAAW,KAAK;AACtB,YAAM,eAAe,KAAK;AAE1B,UAAI;AAGJ,UAAI,CAAC,gBAAgB,CAAC,KAAK,SAAS,QAAQ,GAAG;AAC7C,eAAO,YAAY,QAAQ,WAAS,WAAW,KAAK,IAAI,EAAE;AAC1D,mBAAW,KAAK,cAAc,KAAK,OAAO,EAAE;AAC5C,mBAAW,IAAI;AACf,mBAAW,IAAI,SAAS,YAAW;aAG9B;AACL,cAAM,YAAY,CAAC;AACnB,cAAMA,YAAY,KAAK,iBAAiB,KAAK,MAAO,KAAK,cAAc,KAAK,GAAG,IAAI;AAEnF,eAAO,YAAY,QAAQ,CAAC,UAAU;AACpC,cAAI,UAAU,cAAc;AAC1B,uBAAW,KAAK,IAAI;AACpB;;AAGF,cAAI;AACJ,cAAI;AACJ,kBAAQ,OAAK;YACX,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;AACH,kBAAI,KAAK,eAAe;AACtB,oBAAIA,cAAa,MAAM;AACrB,0BAAQ,YAAY,KAAK,YAAY,KAAK;2BACjC,CAAC,KAAK,IAAI,EAAE,SAAS,KAAK,GAAG;AACtC,0BAAQ,cAAc,KAAK,KAAK;uBAC3B;AACL,0BAAQ,YAAY;;qBAEjB;AACL,oBAAI,CAAC,KAAK,IAAI,EAAE,SAAS,KAAK,GAAG;AAC/B,0BAAQ,cAAc,IAAI,KAAK;uBAC1B;AACL,0BAAQ,YAAY;;;AAGxB,yBAAW,KAAK,IAAI,KAAK,YAAY,OAAO,KAAK;AACjD;YACF,KAAK;YACL,KAAK;AAEH,kBAAI,KAAK,eAAe;AACtB,wBAAQ;AACR,sBAAMA,aAAY;qBAEb;AACL,oBAAI,YAAY,MAAM,YAAY,IAAI;AACpC,wBAAM;AACN,0BAAQ,cAAc,KAAK,KAAK,YAAY;uBACvC;AACL,wBAAM;AACN,0BAAQ,YAAY,OAAO,IAAI,KAAK;;;AAGxC,yBAAW,KAAK,IAAI,KAAK,YAAY,OAAO,KAAK;AACjD,yBAAW,IAAI;AACf,yBAAW,IAAI,IAAI,YAAW;AAC9B;;SAEL;;AAGH,iBAAW,IAAI,KAAK,YAAY,KAAK,KAAK,MAAM;AAChD,iBAAW,KAAK,KAAK,YAAY,MAAM,KAAK,MAAM;AAClD,iBAAW,IAAI,KAAK,YAAY,KAAK,KAAK,MAAM;AAChD,iBAAW,KAAK,KAAK,YAAY,MAAM,KAAK,MAAM;AAElD,WAAK,aAAa;AAKlB,UAAI,CAAC,KAAK,QAAQ,WAAW;AAC3B,aAAK,cAAa;;AAGpB,UAAI,KAAK,mBAAmB,KAAK,oBAAoB,KAAK,cAAc;AACtE,aAAK,aAAY;;;IAIrB,gBAAiB;AACf,UAAI,CAAC,KAAK,YAAY;AAAE;MAAA;AAExB,UAAI,KAAK,QAAQ,KAAK,mBAAmB,KAAK,aAAa;AACzD,YAAI,KAAK,WAAW;AAClB,eAAK,SAAS,yFAAyF;;AAEzG;;AAGF,YAAM,aAAa,KAAK,MAAM,KAAK,UAAU,KAAK,UAAU,CAAC;AAE7D,UAAI,KAAK,gBAAgB;AACvB,aAAK,MAAM,qBAAqB,KAAK,eAAe,KAAK,OAAO,KAAK,WAAW,CAAC;aAC5E;AACL,cAAM,cAAc,KAAK,MAAM,UAAU,CAAA;AACzC,cAAM,YAAY,CAAA;AAClB,oBAAY,QAAQ,CAAC,UAAU;AAC7B,oBAAU,KAAK,IAAI,WAAW,KAAK,KAAK;SACzC;AACD,aAAK,MAAM,qBAAqB,KAAK,MAAM,KAAK,UAAU,SAAS,CAAC,CAAC;;AAGvE,WAAK,MAAM,UAAU;QACnB,MAAM;QACN,aAAa,KAAK,eAAe,KAAK,OAAO,KAAK,WAAW;OAC9D;;IAGH,kBAAmB,OAAO;AACxB,YAAM,SAAS,KAAK,cAAc,KAAK;AACvC,UAAI,CAAC,OAAO,CAAC,MAAM,IAAI;AACrB,eAAO,CAAC,OAAO,CAAC,KAAK,OAAO,CAAC,EAAE,YAAW,MAAO,MAAM,IAAI;;AAE7D,aAAO,CAAC,OAAO,CAAC,KAAK,OAAO,CAAC,EAAE,YAAW,MAAO,MAAM,KAAK;;IAG9D,WAAY,MAAM,OAAO;AACvB,UAAI,CAAC,KAAK,YAAY,IAAI,KAAK,CAAC,KAAK,MAAM,IAAI,GAAG;AAAE,eAAO;MAAA;AAC3D,cAAQ,MAAI;QACV,KAAK;AACH,iBAAO,KAAK,eAAe,KAAK;QAClC,KAAK;QACL,KAAK;AACH,cAAI,CAAC,KAAK,GAAG,IAAI,WAAW,GAAG;AAC7B,mBAAO;;AAET,iBAAO,CAAC,KAAK,GAAG,IAAI,WAAW,EAAE,SAAS,KAAK;QACjD,KAAK;AACH,cAAI,CAAC,KAAK,qBAAqB;AAC7B,mBAAO;;AAET,iBAAO,CAAC,KAAK,IAAI,KAAK,cAAc,KAAK,CAAC;QAC5C;AACE,iBAAO;;;IAIb,eAAgB,OAAO;AACrB,UAAI,CAAC,KAAK,qBAAqB;AAAE,eAAO;MAAA;AACxC,UAAI,KAAK,eAAe;AACtB,YAAI,CAAC,KAAK,OAAO,CAAC,KAAK,IAAI,QAAQ;AACjC,iBAAO;eACF;AACL,gBAAM,QAAQ,KAAK,IAAI,YAAW,MAAO,OAAO,MAAM;AACtD,iBAAO,CAAC,KAAK,oBAAoB,SAAS,GAAG,CAAC,KAAK,GAAG,KAAK,EAAE;;;AAIjE,WACG,KAAK,aAAa,QAAQ,KAAK,aAAa,QAC7C,CAAC,UAAU,KAAK,KAAK,oBAAoB,SAAS,EAAE,GACpD;AACA,eAAO;;AAET,aAAO,CAAC,KAAK,oBAAoB,SAAS,CAAC,KAAK;;IAGlD,cAAe,SAAS,OAAO;AAC7B,UAAI,CAAC,WAAW,CAAC,KAAK,iBAAiB,OAAO,GAAG;AAAE;MAAA;AACnD,UAAI,KAAK,KAAK,GAAG,OAAO,UAAU,MAAM,GAAG;AAAE,eAAO;MAAA;AACpD,aAAO,CAAC,QAAQ,KAAK,KAAK,GAAG,OAAO,UAAU,MAAM;;IAGtD,gBAAiB,UAAU,SAAS;AAClC,UAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,iBAAiB,OAAO,GAAG;AAAE,eAAO,CAAA;MAAA;AACvE,YAAM,QAAQ,CAAA;AACd,UAAI;AACJ,eAAS,QAAQ,WAAS;AACxB,YAAI,iBAAiB,OAAO;AAC1B,cAAI,MAAM,SAAS,KAAK,KAAK,WAAW;AACtC,iBAAK,SAAS,wBAAwB,OAAO,4EAA4E,KAAK,UAAU,KAAK,CAAC,8BAA8B;;AAE9K,gBAAM,QAAQ,MAAM,CAAC;AACrB,gBAAM,MAAM,MAAM,CAAC,KAAK,MAAM,CAAC;AAC/B,mBAAS,IAAI,CAAC,OAAO,KAAK,CAAC,KAAK,KAAK;AACnC,gBAAI,IAAI,KAAK,IAAI,IAAI;AAAE;YAAA;AACvB,4BAAgB,KAAK,YAAY,KAAK,eAAe,OAAO,GAAG,CAAC;AAChE,gBAAI,CAAC,MAAM,SAAS,aAAa,GAAG;AAClC,oBAAM,KAAK,aAAa;;;eAGvB;AACL,cAAI,CAAC,QAAQ,KAAK,CAAC,QAAQ,IAAI;AAAE;UAAA;AACjC,0BAAgB,KAAK,YAAY,KAAK,eAAe,OAAO,GAAG,KAAK;AACpE,cAAI,CAAC,MAAM,SAAS,aAAa,GAAG;AAClC,kBAAM,KAAK,aAAa;;;OAG7B;AACD,YAAM,KAAK,CAAC,GAAG,MAAM;AAAE,eAAO,IAAI;MAAA,CAAG;AAErC,UAAI,KAAK,WAAW;AAClB,cAAM,YAAY,YAAY,WAAW,KAAK,UAAU,KAAK,YAAY,CAAA;AACzE,cAAM,aAAa,SAAS,OAAO,UAAQ,MAAM,SAAS,IAAI,CAAC;AAC/D,YAAI,CAAC,cAAc,CAAC,WAAW,QAAQ;AACrC,cAAI,YAAY,UAAU;AACxB,iBAAK,SAAS;gBAA4E,KAAK,UAAU,KAAK,WAAW,CAAC;mBAAsB,KAAK,KAAK,cAAc,EAAE;iBACrK;AACL,iBAAK,SAAS;gBAA4E,KAAK,UAAU,KAAK,WAAW,CAAC;mBAAsB,KAAK,KAAK,cAAc,EAAE;;;;AAIhL,aAAO;;IAGT,oBAAqB;AACnB,UAAI,KAAK,aAAa;AAEpB;;AAEF,UAAI,KAAK,WAAW,CAAC,KAAK,KAAK;AAC7B,YAAI,KAAK,IAAI,MAAM,KAAK,IAAI,IAAI;AAC9B,eAAK,qBAAqB;AAC1B,gBAAM,WAAW,KAAK,IAAI,KAAK,OAAO;AACtC,eAAK,MAAM,KAAK,YAAY,MAAM,SAAS,YAAW,IAAK;;;;IAKjE,oBAAqB;AACnB,UAAI,KAAK,sBAAsB,KAAK,SAAS,MAAM,KAAK,WAAW,MAAM,KAAK,WAAW,IAAI;AAC3F,aAAK,MAAM;;AAEb,WAAK,qBAAqB;;IAG5B,eAAgB,UAAU;AACxB,UAAI,KAAK,UAAU,KAAK,cAAc,QAAQ,MAAM,MAAM;AACxD,eAAO,KAAK;;AAEd,UAAI,KAAK,UAAU,KAAK,cAAc,QAAQ,MAAM,MAAM;AACxD,eAAO,KAAK;;AAEd,aAAO;;IAGT,eAAgB;AACd,UAAI,KAAK,UAAU;AAAE;MAAA;AACrB,WAAK,WAAW,CAAC,KAAK;AAEtB,UAAI,KAAK,UAAU;AACjB,aAAK,aAAa;AAClB,YAAI,KAAK,aAAa;AACpB,eAAK,MAAM,OAAO;;AAEpB,YAAI,CAAC,KAAK,KAAK,cAAc;AAC3B,eAAK,iBAAiB,IAAI;;AAG5B,YAAI,KAAK,MAAM;AACb,eAAK,iBAAiB,OAAO,KAAK,eAAe,EAAE;;AAErD,YAAI,KAAK,eAAe,CAAC,KAAK,cAAc;AAC1C,mBAAS,MAAM;AACb,gBAAI,KAAK,MAAM,SAAS,KAAK,MAAM,MAAM,mBAAmB,KAAK,KAAK,MAAM,MAAM,iBAAiB,KAAK,YAAY,QAAQ;AAE1H,mBAAK,gBAAe;;WAEvB;;aAEE;AACL,YAAI,KAAK,cAAc;AACrB,eAAK,iBAAiB,KAAK;mBAClB,KAAK,aAAa;AAC3B,eAAK,MAAM,MAAM;;AAEnB,aAAK,aAAa;AAClB,YAAI,KAAK,MAAM;AACb,eAAK,WAAW,IAAI;AACpB,eAAK,iBAAiB;;;AAI1B,UAAI,KAAK,uBAAuB,KAAK,eAAe;AAClD,aAAK,eAAe,KAAK,kBAAiB,IAAK,KAAK,kBAAiB;;AAEvE,UAAI,KAAK,cAAc;AACrB,aAAK,mBAAkB;;;IAI3B,iBAAkB,QAAQ,gBAAgB,OAAO;AAC/C,UAAI,QAAQ;AACV,aAAK,aAAY;AACjB,YAAI,KAAK,sBAAsB;AAC7B,eAAK,mBAAkB;;AAEzB,aAAK,eAAe;AACpB,aAAK,MAAM,MAAM;AACjB,YAAI,eAAe;AACjB,cAAI,KAAK,qBAAqB;AAC5B,iBAAK,WAAW;;AAElB,eAAK,MAAM,MAAM;AACjB,eAAK,mBAAkB;;aAEpB;AACL,aAAK,eAAe;AACpB,aAAK,MAAM,OAAO;;;IAItB,YAAa;AACX,UAAI,KAAK,eAAe,CAAC,KAAK,KAAK,cAAc;AAE/C,aAAK,MAAM,MAAM;;;IAIrB,OAAQ,MAAM,OAAO;AACnB,UAAI,KAAK,YAAY,IAAI,KAAK,CAAC,KAAK,WAAW,MAAM,KAAK,GAAG;AAC3D,aAAK,IAAI,IAAI;AACb,YAAI,KAAK,oBAAoB;AAC3B,eAAK,qBAAqB;;;;IAKhC,YAAa;AACX,UAAI,KAAK,UAAU;AAAE;MAAA;AACrB,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,SAAS;AACd,WAAK,MAAM;AAEX,UAAI,KAAK,eAAe,KAAK,SAAS,KAAK,MAAM,SAAS,KAAK,MAAM,MAAM,MAAM,QAAQ;AACvF,aAAK,MAAM,MAAM,QAAQ;;AAG3B,UAAI,KAAK,MAAM;AACb,aAAK,WAAW,IAAI;;;;;;IAQxB,qBAAsB;AACpB,UAAI,KAAK,cAAc;AAAE;MAAA;AACzB,UAAI,KAAK,YAAY;AACnB,iBAAS,MAAM;AACb,eAAK,uBAAsB;SAC5B;iBACQ,KAAK,kBAAkB;AAEhC,iBAAS,MAAM;AACb,gBAAM,cAAc,KAAK,MAAM,MAAM,CAAC;AACtC,eAAK,iBAAiB,aAAa,IAAI;SACxC;;;IAIL,iBAAkB,QAAQ,gBAAgB,OAAO;AAC/C,UAAI,CAAC,KAAK,aAAa,KAAK,cAAc;AAAE;MAAA;AAC5C,YAAM,aAAa,KAAK,IAAI,iBAAiB,MAAM,MAAM,GAAG,EAAE,CAAC;AAC/D,UAAI,cAAc,KAAK,gBAAgB,MAAM,EAAE,CAAC;AAChD,UAAI,CAAC,eAAe,eAAe;AAEjC,sBAAc,KAAK,gBAAgB,MAAM,EAAE,CAAC;;AAE9C,UAAI,cAAc,aAAa;AAC7B,mBAAW,YAAY,YAAY,aAAa;AAChD,YAAI,KAAK,kBAAkB;AACzB,sBAAY,MAAK;;;;IAKvB,yBAA0B;AACxB,UAAI,CAAC,KAAK,aAAa,KAAK,cAAc;AAAE;MAAA;AAC5C,WAAK,MAAM,MAAM,QAAQ,aAAW;AAClC,aAAK,iBAAiB,OAAO;OAC9B;;;;;IAOH,UAAW;AACT,UAAI,KAAK,UAAU;AAAE;MAAA;AACrB,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa;;AAEpB,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,aAAY;;;IAIrB,UAAW;AACT,UAAI,KAAK,UAAU;AAAE;MAAA;AACrB,aAAO,aAAa,KAAK,aAAa;AACtC,WAAK,aAAa;AAClB,YAAM,WAAW,KAAK,IAAI,iBAAiB,8BAA8B,EAAE,CAAC;AAC5E,UAAI,UAAU;AACZ,iBAAS,KAAI;;;IAIjB,eAAgB;AACd,UAAI,KAAK,UAAU;AAAE;MAAA;AACrB,WAAK,aAAa;AAClB,aAAO,aAAa,KAAK,aAAa;AACtC,WAAK,gBAAgB,OAAO,WAAW,MAAM;AAC3C,eAAO,aAAa,KAAK,aAAa;AACtC,aAAK,OAAM;SACV,KAAK,KAAK,SAAS;;IAGxB,SAAU;AACR,UAAI,CAAC,KAAK,YAAY,CAAC,KAAK,cAAc,KAAK,UAAU;AACvD,aAAK,aAAY;;;IAIrB,eAAgB;AACd,UAAI,KAAK,UAAU;AAAE;MAAA;AACrB,aAAO,aAAa,KAAK,aAAa;AACtC,UAAI,CAAC,KAAK,YAAY;AACpB,aAAK,aAAa;;;IAItB,gBAAiB,QAAQ;AACvB,YAAM,cAAc,GAAG,MAAM;AAC7B,aAAO,KAAK,IAAI,iBAAiB,MAAM,WAAW,kCAAkC;;IAGtF,gBAAiB,QAAQ;AACvB,YAAM,cAAc,GAAG,MAAM;AAC7B,aAAO,KAAK,IAAI,iBAAiB,MAAM,WAAW,yBAAyB;;IAG7E,kBAAmB,QAAQ,SAAS,cAAc,OAAO;AACvD,YAAM,gBAAgB,KAAK,gBAAgB,MAAM;AACjD,YAAM,YAAY,MAAM,UAAU,UAAU,KAAK,eAAe,CAAC,QAAQ;AACvE,eAAO,IAAI,aAAa,UAAU,MAAM;OACzC;AAGD,UAAI,eAAe,cAAc,GAAG;AAClC,eAAO,cAAc,cAAc,SAAS,CAAC;;AAG/C,UAAI,CAAC,eAAe,cAAc,cAAc,SAAS,GAAG;AAC1D,eAAO,cAAc,CAAC;;AAGxB,UAAI,YAAY,GAAG;AACjB,eAAO,cAAc,CAAC;;AAGxB,UAAI,aAAa;AACf,eAAO,cAAc,YAAY,CAAC;;AAEpC,aAAO,cAAc,YAAY,CAAC;;IAGpC,SAAU,QAAQ,SAAS,gBAAgB,OAAO;AAChD,YAAM,aAAa,KAAK,kBAAkB,QAAQ,SAAS,IAAI;AAC/D,UAAI,YAAY;AACd,eAAO,gBAAgB,aAAa,WAAW,MAAK;;;IAIxD,SAAU,QAAQ,SAAS,gBAAgB,OAAO;AAChD,YAAM,aAAa,KAAK,kBAAkB,QAAQ,SAAS,KAAK;AAChE,UAAI,YAAY;AACd,eAAO,gBAAgB,aAAa,WAAW,MAAK;;;IAIxD,kBAAmB,eAAe,SAAS,OAAO;AAChD,YAAM,qBAAqB,KAAK,MAAM,MAAM,QAAQ,aAAa;AACjE,UAAI,UAAU,sBAAsB,GAAG;AACrC,YAAI,KAAK,WAAW;AAClB,eAAK,SAAS,qCAAsC;;AAEtD;iBACS,CAAC,UAAU,uBAAwB,KAAK,MAAM,MAAM,SAAS,GAAI;AAC1E,YAAI,KAAK,WAAW;AAClB,eAAK,SAAS,sCAAuC;;AAEvD;;AAEF,aAAO,KAAK,MAAM,MAAM,SAAS,qBAAqB,IAAI,qBAAqB,CAAC;;IAGlF,yBAA0B,eAAe,SAAS,OAAO;AACvD,YAAM,eAAe,KAAK,kBAAkB,eAAe,MAAM;AACjE,UAAI,CAAC,cAAc;AAAE;MAAA;AACrB,YAAM,YAAY,KAAK,gBAAgB,YAAY;AACnD,UAAI,aAAa,UAAU,CAAC,GAAG;AAC7B,eAAO,UAAU,CAAC;;;IAItB,0BAA2B,eAAe,SAAS,OAAO;AACxD,YAAM,eAAe,KAAK,kBAAkB,eAAe,MAAM;AACjE,UAAI,CAAC,cAAc;AAAE;MAAA;AACrB,YAAM,cAAc,KAAK,gBAAgB,YAAY;AACrD,UAAI,eAAe,YAAY,CAAC,GAAG;AACjC,eAAO,YAAY,CAAC;;;IAIxB,aAAc,eAAe;AAC3B,YAAM,aAAa,KAAK,0BAA0B,eAAe,IAAI,KAAK,KAAK,yBAAyB,eAAe,IAAI;AAC3H,UAAI,YAAY;AACd,mBAAW,MAAK;;;IAIpB,cAAe,eAAe;AAC5B,YAAM,aAAa,KAAK,0BAA0B,eAAe,KAAK,KAAK,KAAK,yBAAyB,eAAe,KAAK;AAC7H,UAAI,YAAY;AACd,mBAAW,MAAK;;;;;;IAQpB,cAAe;AACb,UAAI,CAAC,KAAK,aAAa;AAAE;MAAA;AACzB,aAAO,aAAa,KAAK,cAAc;AACvC,WAAK,iBAAiB,OAAO,WAAW,MAAM;AAC5C,eAAO,aAAa,KAAK,cAAc;AACvC,YAAI,KAAK,SAAS,KAAK,MAAM,OAAO;AAClC,gBAAM,cAAc,KAAK,qBAAqB,KAAK,MAAM,MAAM,kBAAkB,CAAC;AAClF,eAAK,0BAA0B,WAAW;;SAE3C,EAAE;;IAGP,eAAgB,KAAK;AACnB,UAAI,IAAI,eAAe,IAAI,YAAY,KAAK;AAE1C,YAAI,eAAc;AAClB,YAAI,gBAAe;AACnB,eAAO;;AAGT,UAAK,IAAI,WAAW,MAAM,IAAI,WAAW,MAAQ,IAAI,WAAW,MAAM,IAAI,WAAW,KAAM;AACzF,YAAI,eAAc;AAClB,aAAK,cAAc,IAAI,GAAG;iBAEjB,CAAC,IAAI,IAAI,EAAE,EAAE,SAAS,IAAI,OAAO,GAAG;AAC7C,YAAI,eAAc;AAClB,aAAK,cAAc,IAAI,KAAK,IAAI;iBAEvB,IAAI,WAAW,MAAM,IAAI,WAAW,IAAI;AACjD,YAAI,eAAc;AAClB,aAAK,gBAAe;AACpB,aAAK,aAAa,GAAG;iBAEZ,IAAI,YAAY,KAAK,IAAI,YAAY,IAAI;AAClD,YAAI,eAAc;AAClB,aAAK,gBAAe;AACpB,aAAK,UAAS;iBAEL,IAAI,YAAY,GAAG;AAC5B,aAAK,gBAAe;AACpB,aAAK,WAAW,GAAG;iBAEV,IAAI,YAAY,MAAM,EAAE,IAAI,WAAW,IAAI,UAAU;AAC9D,YAAI,eAAc;;;IAItB,kBAAmB,KAAK;AACtB,UAAI,eAAc;AAClB,UAAI,gBAAe;AACnB,WAAK,gBAAgB,KAAK,qBAAoB;AAC9C,aAAO;;IAGT,gBAAiB,KAAK;AACpB,UAAI,eAAc;AAClB,UAAI,gBAAe;AAEnB,YAAM,UAAU,IAAI;AACpB,UAAI,uBAAuB;AAC3B,UAAI,KAAK,IAAI,eAAe;AAC1B,+BAAuB,KAAK,gBAAgB,OAAO;;AAErD,UAAI,sBAAsB;AACxB,aAAK,2BAA2B,OAAO,oBAAoB;;AAG7D,WAAK,MAAM,MAAM,QAAQ,KAAK,IAAI,gBAAgB,KAAK,oBAAoB,KAAK;AAEhF,eAAS,MAAM;AACb,YAAI,KAAK,eAAe;AACtB,gBAAM,SAAS,KAAK,MAAM,KAAK,UAAU,KAAK,aAAa,CAAC;AAC5D,cAAI,sBAAsB;AACxB,mBAAO,MAAO,OAAO,QAAQ,QAAQ;;AAEvC,eAAK,0BAA0B,MAAM;AACrC,eAAK,gBAAgB;;OAExB;AACD,aAAO;;IAGT,aAAc,KAAK;AACjB,UAAI,eAAc;AAClB,UAAI,eAAe,IAAI,iBAAiB,OAAO,eAAe,QAAQ,MAAM;AAC5E,UAAI,KAAK,WAAW;AAClB,aAAK,SAAS,kBAAkB,WAAW,kBAAkB;;AAE/D,UAAI,CAAC,eAAe,CAAC,YAAY,QAAQ;AAAE;MAAA;AAG3C,UAAI,KAAK,IAAI,eAAe;AAC1B,sBAAc,KAAK,qBAAqB,WAAW;;AAGrD,UAAI,KAAK,cAAc;AACrB,aAAK,iBAAiB,WAAW;aAC5B;AACL,aAAK,aAAa,YAAY,OAAO,IAAI,CAAC;AAC1C,aAAK,WAAU;AACf,aAAK,mBAAkB;;;IAI3B,aAAc,KAAK;AACjB,YAAM,YAAY,EAAE,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAA,EAAM,IAAI,OAAO;AACpE,UAAI,cAAc,OAAO,cAAc,KAAK;AAC1C,YAAI,KAAK,cAAc;AACrB,eAAK,sBAAqB;eACrB;AACL,gBAAM,eAAe,KAAK,qBAAoB;AAC9C,cAAI,CAAC,cAAc;AACjB,iBAAK,sBAAqB;AAC1B;;AAEF,gBAAM,YAAY,aAAa;AAC/B,eAAK,yBAAyB,WAAW,KAAK,SAAS,GAAG,SAAS;AACnE,gBAAM,cAAc,KAAK,qBAAoB;AAC7C,eAAK,0BAA0B,WAAW;;iBAEnC,cAAc,KAAK;AAC5B,aAAK,eAAe,KAAK;iBAChB,cAAc,KAAK;AAC5B,aAAK,eAAe,IAAI;;;IAI5B,WAAY,KAAK;AACf,UAAI,CAAC,KAAK,gBAAgB,KAAK,kBAAkB,KAAK,eAAe,QAAQ;AAC3E,cAAM,eAAe,KAAK,qBAAoB;AAC9C,YAAI,CAAC,cAAc;AAAE;QAAA;AACrB,cAAM,aAAa,KAAK,eAAe,CAAC;AACxC,cAAM,YAAY,KAAK,eAAe,KAAK,eAAe,SAAS,CAAC;AACpE,YAAK,IAAI,YAAY,aAAa,UAAU,WAAW,SAAW,CAAC,IAAI,YAAY,aAAa,UAAU,UAAU,OAAQ;AAC1H,cAAI,eAAc;AAClB,eAAK,eAAe,IAAI,QAAQ;;;;IAKtC,cAAe,SAAS,QAAQ,OAAO;AACrC,YAAM,eAAe,KAAK,qBAAoB;AAC9C,UAAI,CAAC,gBAAiB,aAAa,SAAS,SAAS,SAAW,aAAa,SAAS,SAAS,CAAC,OAAQ;AAAE;MAAA;AAC1G,WAAK,aAAa,GAAG,KAAK,WAAW,OAAO,EAAE,CAAC,GAAG,OAAO;AACzD,WAAK,WAAU;AACf,WAAK,mBAAkB;;IAGzB,kBAAmB;AACjB,aAAO,aAAa,KAAK,YAAY;AACrC,WAAK,aAAa;;IAGpB,qBAAsB;AACpB,aAAO,aAAa,KAAK,YAAY;AACrC,WAAK,eAAe,OAAO,WAAW,MAAM;AAC1C,aAAK,gBAAe;SACnB,KAAK,KAAK,kBAAkB;;IAGjC,WAAY,OAAO;AACjB,cAAQ,SAAS,KAAK;AACtB,YAAM,eAAe,KAAK,qBAAoB;AAC9C,UAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,MAAM,QAAQ;AAAE;MAAA;AAChD,YAAM,YAAY,aAAa;AAC/B,YAAM,aAAa,aAAa;AAEhC,UAAI;AACJ,UAAI,cAAc,OAAO;AACvB,YAAI,KAAK,cAAc,KAAK,EAAE,SAAS,GAAG,GAAG;AAC3C,uBAAa;mBACJ,KAAK,cAAc,KAAK,EAAE,SAAS,GAAG,GAAG;AAClD,uBAAa;;AAEf,YAAI,YAAY;AACd,uBAAa,eAAe,MAAM,WAAW,YAAW,IAAK;;aAE1D;AACL,YAAI,KAAK,aAAa,YAAY,KAAK,GAAG;AACxC,uBAAa;eACR;AACL,gBAAM,iBAAiB,KAAK,YAAY,YAAY,MAAM,OAAO,EAAE,CAAC;AACpE,cAAI,KAAK,aAAa,YAAY,cAAc,GAAG;AACjD,yBAAa;;;;AAKnB,UAAI,YAAY;AACd,aAAK,2BAA2B,WAAW,UAAU;AACrD,cAAM,cAAc,KAAK,qBAAoB;AAC7C,aAAK,0BAA0B,WAAW;;AAE5C,UAAI,KAAK,WAAW;AAClB,YAAI,YAAY;AACd,eAAK,SAAS,2BAA2B,UAAU,wBAAwB,KAAK,cAAc,SAAS,QAAQ;eAC1G;AACL,eAAK,SAAS,UAAU,KAAK,wBAAwB,SAAS,QAAQ;;;;;IAM5E,WAAY;AACV,UAAI,CAAC,KAAK,eAAe,CAAC,KAAK,SAAS,CAAC,KAAK,MAAM,OAAO;AAAE;MAAA;AAC7D,YAAM,gBAAgB,KAAK,MAAM,MAAM,SAAS;AAChD,UAAI,iBAAiB,cAAc,QAAQ;AACzC,aAAK,iBAAiB,aAAa;;;IAIvC,qBAAsB,UAAU;AAC9B,UAAI,CAAC,KAAK,kBAAkB,CAAC,KAAK,eAAe,QAAQ;AAAE;MAAA;AAC3D,UAAI;AACJ,UAAI,eAAe;AACnB,eAAS,IAAI,GAAG,IAAI,KAAK,eAAe,QAAQ,KAAK;AACnD,cAAM,QAAQ,KAAK,MAAM,KAAK,UAAU,KAAK,eAAe,CAAC,CAAC,CAAC;AAC/D,YAAI,MAAM,UAAU,UAAU;AAC5B,iBAAO;;AAET,cAAM,QAAQ,KAAK,IAAI,MAAM,QAAQ,QAAQ;AAC7C,YAAI,eAAe,GAAG;AACpB,oBAAU;AACV,yBAAe;eACV;AACL,cAAI,gBAAgB,OAAO;AACzB,mBAAO;;AAET,yBAAe;AACf,oBAAU;;;AAGd,aAAO;;IAGT,wBAAyB;AACvB,UAAI,CAAC,KAAK,kBAAkB,CAAC,KAAK,eAAe,QAAQ;AAAE;MAAA;AAC3D,YAAM,gBAAgB,KAAK,eAAe,CAAC,EAAE;AAC7C,UAAI,kBAAkB,QAAQ;AAC5B,aAAK,mBAAkB;aAClB;AACL,aAAK,yBAAyB,eAAe,KAAK,aAAa,CAAC;;AAElE,WAAK,gBAAe;;IAGtB,mBAAoB,cAAc,YAAY,KAAK;AACjD,UAAI,CAAC,KAAK,kBAAkB,CAAC,KAAK,eAAe,QAAQ;AACvD,YAAI,KAAK,WAAW;AAClB,eAAK,SAAS;cAAkF,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE;;AAElI;;AAEF,UAAI,CAAC,cAAc;AACjB,aAAK,cAAc,KAAK,eAAe,CAAC,CAAC;AACzC;;AAEF,YAAM,eAAe,KAAK,eAAe,UAAU,UAAQ;AACzD,YAAI,CAAC,KAAK,eAAe;AACvB,iBAAO,SAAS;eACX;AACL,gBAAM,WAAW,GAAG,YAAY,GAAG,KAAK,cAAc,KAAK,GAAG,MAAM,OAAO,MAAM,GAAG;AACpF,iBAAO,SAAS;;OAEnB;AACD,UAAI;AACJ,UAAI,iBAAiB,IAAI;AACvB,oBAAY;iBACH,cAAc,KAAK;AAC5B,oBAAY,iBAAiB,IAAI,KAAK,eAAe,SAAS,IAAI,eAAe;aAC5E;AACL,qBAAa,eAAe,KAAK,KAAK,eAAe;;AAEvD,YAAM,WAAW,KAAK,eAAe,SAAS;AAC9C,WAAK,cAAc,QAAQ;;IAG7B,yBAA0B,QAAQ,cAAc,YAAY,KAAK;AAC/D,UAAI,WAAW,QAAQ;AACrB,aAAK,mBAAmB,cAAc,SAAS;aAC1C;AACL,cAAM,WAAW,cAAc,MAAM,KAAK,SAAS,QAAQ,KAAK,MAAM,GAAG,IAAI,IAAI,KAAK,SAAS,QAAQ,KAAK,MAAM,GAAG,IAAI;AACzH,YAAI,UAAU;AACZ,eAAK,OAAO,QAAQ,SAAS,aAAa,UAAU,CAAC;;;;IAK3D,2BAA4B,SAAS,YAAY;AAC/C,UAAI,CAAC,WAAW,CAAC,KAAK,eAAe,OAAO,GAAG;AAAE;MAAA;AAEjD,YAAM,iBAAiB,KAAK,eAAe,KAAK,eAAe,OAAO,GAAG,UAAU;AACnF,WAAK,OAAO,IAAI;;IAGlB,cAAe,UAAU;AACvB,UAAI,KAAK,WAAW,QAAQ,GAAG;AAC7B,cAAM,QAAQ,KAAK,cAAc,QAAQ;AACzC,cAAM,WAAW,MAAM,CAAC,MAAM,MAAM,OAAO;AAC3C,aAAK,2BAA2B,OAAO,KAAK,YAAY,MAAM,SAAS,YAAW,IAAK,QAAQ;AAC/F,aAAK,2BAA2B,QAAQ,MAAM,CAAC,CAAC;aAC3C;AACL,aAAK,2BAA2B,QAAQ,QAAQ;;;IAIpD,0BAA2B,EAAC,QAAQ,GAAG,MAAM,EAAA,GAAK;AAChD,eAAS,MAAM;AACb,aAAK,uBAAuB,OAAO,GAAG;OACvC;AACD,aAAO,aAAa,KAAK,cAAc;AACvC,WAAK,iBAAiB,OAAO,WAAW,MAAM;AAC5C,eAAO,aAAa,KAAK,cAAc;AAEvC,YAAI,KAAK,MAAM,UAAU,KAAK,MAAM,MAAM,mBAAmB,SAAS,KAAK,MAAM,MAAM,iBAAiB,MAAM;AAC5G,eAAK,uBAAuB,OAAO,GAAG;;SAEvC,EAAE;;IAGP,uBAAwB,OAAO,KAAK;AAClC,UAAI,KAAK,SAAS,KAAK,MAAM,OAAO;AAClC,aAAK,MAAM,MAAM,kBAAkB,OAAO,GAAG;;;IAIjD,uBAAwB;AACtB,aAAO,KAAK,qBAAsB,KAAK,MAAM,SAAS,KAAK,MAAM,MAAM,kBAAmB,CAAC;;IAG7F,kBAAmB;AACjB,YAAM,gBAAgB,KAAK,qBAAqB,CAAC;AACjD,WAAK,0BAA0B,aAAa;;IAG9C,eAAgB,QAAQ;AACtB,YAAM,eAAe,KAAK,qBAAoB;AAC9C,UAAI,CAAC,cAAc;AACjB,aAAK,sBAAqB;AAC1B;;AAEF,YAAM,oBAAoB,KAAK,eAAe,UAAU,SAAO,IAAI,UAAU,aAAa,KAAK;AAC/F,UAAK,CAAC,UAAU,qBAAqB,KAAK,eAAe,SAAS,KAAO,UAAU,sBAAsB,GAAI;AAC3G,YAAI,KAAK,WAAW;AAClB,cAAI,QAAQ;AACV,iBAAK,SAAS,qCAAsC;iBAC/C;AACL,iBAAK,SAAS,sCAAuC;;;AAGzD;;AAEF,YAAM,gBAAgB,SAAS,KAAK,eAAe,oBAAoB,CAAC,IAAI,KAAK,eAAe,oBAAoB,CAAC;AACrH,WAAK,0BAA0B,aAAa;;IAG9C,gBAAiB,WAAW;AAC1B,UAAI,CAAC,aAAa,CAAC,UAAU,QAAQ;AAAE,eAAO;MAAA;AAC9C,UAAI,KAAK,UAAU,KAAK,WAAW,WAAW;AAC5C,eAAO,KAAK,YAAY,MAAM,OAAO;;AAEvC,UAAI,KAAK,UAAU,KAAK,WAAW,WAAW;AAC5C,eAAO,KAAK,YAAY,MAAM,OAAO;;AAEvC,aAAO;;IAGT,qBAAsB,aAAa;AACjC,UAAI,KAAK,UAAU,KAAK,OAAO,UAAU,YAAY,SAAS,KAAK,MAAM,GAAG;AAC1E,eAAO,YAAY,QAAQ,IAAI,OAAO,KAAK,QAAQ,GAAG,GAAG,KAAK,YAAY,MAAM,OAAO,IAAI;iBAClF,KAAK,UAAU,KAAK,OAAO,UAAU,YAAY,SAAS,KAAK,MAAM,GAAG;AACjF,eAAO,YAAY,QAAQ,IAAI,OAAO,KAAK,QAAQ,GAAG,GAAG,KAAK,YAAY,MAAM,OAAO,IAAI;;AAE7F,aAAO;;IAGT,qBAAsB;AACpB,UAAI,CAAC,KAAK,KAAK;AAAE;MAAA;AACjB,UAAI;AACJ,UAAI,KAAK,eAAe,KAAK,YAAY,QAAQ;AAC/C,oBAAY,SAAS,eAAe,KAAK,WAAW;AACpD,YAAI,CAAC,aAAa,KAAK,WAAW;AAChC,eAAK,SAAS,sBAAsB,KAAK,WAAW,yCAAyC;;;AAGjG,YAAM,KAAK,KAAK;AAChB,UAAI;AACJ,UAAI,aAAa,UAAU,cAAc;AAEvC,oBAAa,UAAU,YAAY,UAAU,gBAAiB,GAAG,YAAY,GAAG;aAC3E;AAEL,cAAM,YAAY,KAAK,IAAI,SAAS,KAAK,cAAc,SAAS,gBAAgB,cAAc,SAAS,KAAK,cAAc,SAAS,gBAAgB,cAAc,SAAS,KAAK,cAAc,SAAS,gBAAgB,YAAY;AAClO,oBAAY,aAAa,GAAG,YAAY,GAAG;;AAE7C,WAAK,iBAAiB,KAAK,KAAK,mBAAmB;;;;;IAOrD,WAAY,OAAO;AACjB,aAAO,qBAAqB,KAAK,KAAK;;IAGxC,cAAe,OAAO;AACpB,aAAO,MAAM,MAAM,sBAAsB;;IAG3C,SAAU,OAAO;AACf,aAAO,CAAC,MAAM,WAAW,KAAK,CAAC,KAAK,SAAS,KAAK;;IAGpD,YAAa,MAAM;AACjB,aAAO,OAAO,YAAY,SAAS,IAAI;;IAGzC,cAAe,UAAU;AACvB,cAAQ,YAAY,IAAI,YAAW;;IAGrC,cAAe,OAAO;AACpB,cAAQ,OAAK;QACX,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO;QACT,KAAK;AACH,iBAAO;QACT;AACE,iBAAO;;;IAIb,aAAc,aAAa,WAAW;AACpC,aAAQ,CAAC,aAAa,CAAC,UAAU,UAAY,aAAa,cAAc;;IAG1E,aAAc,aAAa,WAAW;AACpC,UAAI,CAAC,eAAe,KAAK,aAAa,aAAa,SAAS,GAAG;AAAE,eAAO;MAAA;AACxE,YAAM,gBAAgB,KAAK,cAAc,WAAW;AACpD,UAAI,CAAC,iBAAiB,CAAC,cAAc,QAAQ;AAAE,eAAO;MAAA;AACtD,aAAQ,IAAI,OAAO,IAAI,aAAa,GAAG,EAAG,KAAK,SAAS;;IAG1D,eAAgB,aAAa,YAAY;AACvC,UAAI,KAAK,aAAa,aAAa,UAAU,GAAG;AAC9C,eAAO;;AAET,aAAO;;IAGT,aAAc,OAAO;AACnB,aAAO,KAAK,MAAM,MAAM,KAAK,MAAM,OAAO,QAAQ,KAAK,CAAC,KAAK;;IAG/D,eAAgB,MAAM;AACpB,aAAO,KAAK,GAAG,IAAI,MAAM,KAAK;;IAGhC,iBAAkB,MAAM;AACtB,aAAO,CAAC,UAAU,QAAQ,EAAE,SAAS,IAAI;;;;IAK3C,YAAa,SAAS,OAAO;AAC3B,aAAO,SAAS,OAAO;;IAGzB,SAAU,SAAS;AACjB,UAAI,CAAC,WAAW,CAAC,QAAQ,QAAQ;AAAE;MAAA;AACnC,UAAI,aAAa;AACjB,UAAI,KAAK,IAAI;AACX,sBAAc,IAAI,KAAK,EAAE;;AAE3B,UAAI,KAAK,MAAM;AACb,sBAAc,SAAS,KAAK,IAAI;;AAElC,UAAI,KAAK,YAAY;AACnB,YAAI,eAAe,CAAA;AACnB,YAAI,OAAO,KAAK,eAAe,UAAU;AACvC,yBAAe,KAAK,WAAW,MAAM,KAAK;mBACjC,MAAM,QAAQ,KAAK,UAAU,GAAG;AACzC,yBAAe,CAAA,EAAG,OAAO,CAAA,GAAI,KAAK,UAAU;mBACnC,OAAO,KAAK,eAAe,UAAU;AAC9C,iBAAO,KAAK,KAAK,UAAU,EAAE,QAAQ,aAAW;AAC9C,gBAAI,KAAK,WAAW,OAAO,GAAG;AAC5B,2BAAa,KAAK,OAAO;;WAE5B;;AAEH,iBAAS,cAAc,cAAc;AACnC,cAAI,cAAc,WAAW,KAAI,EAAG,QAAQ;AAC1C,0BAAc,IAAI,WAAW,KAAI,CAAE;;;;AAIzC,YAAM,eAAe,UAAU,OAAO,GAAG,aAAa;IAAQ,UAAU,MAAM,EAAA;AAC9E,UAAI,OAAO,QAAQ,SAAS,OAAO,OAAO,QAAQ,UAAU,YAAY;AACtE,eAAO,QAAQ,MAAM,YAAY;aAC5B;AACL,eAAO,QAAQ,IAAI,YAAY;;;;EAKrC,UAAW;AACT,WAAO,aAAa,KAAK,aAAa;AACtC,WAAO,aAAa,KAAK,cAAc;AACvC,WAAO,aAAa,KAAK,YAAY;AACrC,SAAK,aAAY;;EAGnB,gBAAiB;AACf,WAAO,aAAa,KAAK,aAAa;AACtC,WAAO,aAAa,KAAK,cAAc;AACvC,WAAO,aAAa,KAAK,YAAY;;AAEzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;", "names": ["apmValue"]}