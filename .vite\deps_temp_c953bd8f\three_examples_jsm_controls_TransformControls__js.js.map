{"version": 3, "sources": ["../../node_modules/three/examples/jsm/controls/TransformControls.js"], "sourcesContent": ["import {\n\tBoxGeometry,\n\tBufferGeometry,\n\tCylinderGeometry,\n\tDoubleSide,\n\tEuler,\n\tFloat32BufferAttribute,\n\tLine,\n\tLineBasicMaterial,\n\tMatrix4,\n\tMesh,\n\tMeshBasicMaterial,\n\tObject3D,\n\tOctahedronGeometry,\n\tPlaneGeometry,\n\tQuaternion,\n\tRaycaster,\n\tSphereGeometry,\n\tTorusGeometry,\n\tVector3\n} from 'three';\n\nconst _raycaster = new Raycaster();\n\nconst _tempVector = new Vector3();\nconst _tempVector2 = new Vector3();\nconst _tempQuaternion = new Quaternion();\nconst _unit = {\n\tX: new Vector3( 1, 0, 0 ),\n\tY: new Vector3( 0, 1, 0 ),\n\tZ: new Vector3( 0, 0, 1 )\n};\n\nconst _changeEvent = { type: 'change' };\nconst _mouseDownEvent = { type: 'mouseDown' };\nconst _mouseUpEvent = { type: 'mouseUp', mode: null };\nconst _objectChangeEvent = { type: 'objectChange' };\n\nclass TransformControls extends Object3D {\n\n\tconstructor( camera, domElement ) {\n\n\t\tsuper();\n\n\t\tif ( domElement === undefined ) {\n\n\t\t\tconsole.warn( 'THREE.TransformControls: The second parameter \"domElement\" is now mandatory.' );\n\t\t\tdomElement = document;\n\n\t\t}\n\n\t\tthis.isTransformControls = true;\n\n\t\tthis.visible = false;\n\t\tthis.domElement = domElement;\n\t\tthis.domElement.style.touchAction = 'none'; // disable touch scroll\n\n\t\tconst _gizmo = new TransformControlsGizmo();\n\t\tthis._gizmo = _gizmo;\n\t\tthis.add( _gizmo );\n\n\t\tconst _plane = new TransformControlsPlane();\n\t\tthis._plane = _plane;\n\t\tthis.add( _plane );\n\n\t\tconst scope = this;\n\n\t\t// Defined getter, setter and store for a property\n\t\tfunction defineProperty( propName, defaultValue ) {\n\n\t\t\tlet propValue = defaultValue;\n\n\t\t\tObject.defineProperty( scope, propName, {\n\n\t\t\t\tget: function () {\n\n\t\t\t\t\treturn propValue !== undefined ? propValue : defaultValue;\n\n\t\t\t\t},\n\n\t\t\t\tset: function ( value ) {\n\n\t\t\t\t\tif ( propValue !== value ) {\n\n\t\t\t\t\t\tpropValue = value;\n\t\t\t\t\t\t_plane[ propName ] = value;\n\t\t\t\t\t\t_gizmo[ propName ] = value;\n\n\t\t\t\t\t\tscope.dispatchEvent( { type: propName + '-changed', value: value } );\n\t\t\t\t\t\tscope.dispatchEvent( _changeEvent );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t\tscope[ propName ] = defaultValue;\n\t\t\t_plane[ propName ] = defaultValue;\n\t\t\t_gizmo[ propName ] = defaultValue;\n\n\t\t}\n\n\t\t// Define properties with getters/setter\n\t\t// Setting the defined property will automatically trigger change event\n\t\t// Defined properties are passed down to gizmo and plane\n\n\t\tdefineProperty( 'camera', camera );\n\t\tdefineProperty( 'object', undefined );\n\t\tdefineProperty( 'enabled', true );\n\t\tdefineProperty( 'axis', null );\n\t\tdefineProperty( 'mode', 'translate' );\n\t\tdefineProperty( 'translationSnap', null );\n\t\tdefineProperty( 'rotationSnap', null );\n\t\tdefineProperty( 'scaleSnap', null );\n\t\tdefineProperty( 'space', 'world' );\n\t\tdefineProperty( 'size', 1 );\n\t\tdefineProperty( 'dragging', false );\n\t\tdefineProperty( 'showX', true );\n\t\tdefineProperty( 'showY', true );\n\t\tdefineProperty( 'showZ', true );\n\n\t\t// Reusable utility variables\n\n\t\tconst worldPosition = new Vector3();\n\t\tconst worldPositionStart = new Vector3();\n\t\tconst worldQuaternion = new Quaternion();\n\t\tconst worldQuaternionStart = new Quaternion();\n\t\tconst cameraPosition = new Vector3();\n\t\tconst cameraQuaternion = new Quaternion();\n\t\tconst pointStart = new Vector3();\n\t\tconst pointEnd = new Vector3();\n\t\tconst rotationAxis = new Vector3();\n\t\tconst rotationAngle = 0;\n\t\tconst eye = new Vector3();\n\n\t\t// TODO: remove properties unused in plane and gizmo\n\n\t\tdefineProperty( 'worldPosition', worldPosition );\n\t\tdefineProperty( 'worldPositionStart', worldPositionStart );\n\t\tdefineProperty( 'worldQuaternion', worldQuaternion );\n\t\tdefineProperty( 'worldQuaternionStart', worldQuaternionStart );\n\t\tdefineProperty( 'cameraPosition', cameraPosition );\n\t\tdefineProperty( 'cameraQuaternion', cameraQuaternion );\n\t\tdefineProperty( 'pointStart', pointStart );\n\t\tdefineProperty( 'pointEnd', pointEnd );\n\t\tdefineProperty( 'rotationAxis', rotationAxis );\n\t\tdefineProperty( 'rotationAngle', rotationAngle );\n\t\tdefineProperty( 'eye', eye );\n\n\t\tthis._offset = new Vector3();\n\t\tthis._startNorm = new Vector3();\n\t\tthis._endNorm = new Vector3();\n\t\tthis._cameraScale = new Vector3();\n\n\t\tthis._parentPosition = new Vector3();\n\t\tthis._parentQuaternion = new Quaternion();\n\t\tthis._parentQuaternionInv = new Quaternion();\n\t\tthis._parentScale = new Vector3();\n\n\t\tthis._worldScaleStart = new Vector3();\n\t\tthis._worldQuaternionInv = new Quaternion();\n\t\tthis._worldScale = new Vector3();\n\n\t\tthis._positionStart = new Vector3();\n\t\tthis._quaternionStart = new Quaternion();\n\t\tthis._scaleStart = new Vector3();\n\n\t\tthis._getPointer = getPointer.bind( this );\n\t\tthis._onPointerDown = onPointerDown.bind( this );\n\t\tthis._onPointerHover = onPointerHover.bind( this );\n\t\tthis._onPointerMove = onPointerMove.bind( this );\n\t\tthis._onPointerUp = onPointerUp.bind( this );\n\n\t\tthis.domElement.addEventListener( 'pointerdown', this._onPointerDown );\n\t\tthis.domElement.addEventListener( 'pointermove', this._onPointerHover );\n\t\tthis.domElement.addEventListener( 'pointerup', this._onPointerUp );\n\n\t}\n\n\t// updateMatrixWorld  updates key transformation variables\n\tupdateMatrixWorld() {\n\n\t\tif ( this.object !== undefined ) {\n\n\t\t\tthis.object.updateMatrixWorld();\n\n\t\t\tif ( this.object.parent === null ) {\n\n\t\t\t\tconsole.error( 'TransformControls: The attached 3D object must be a part of the scene graph.' );\n\n\t\t\t} else {\n\n\t\t\t\tthis.object.parent.matrixWorld.decompose( this._parentPosition, this._parentQuaternion, this._parentScale );\n\n\t\t\t}\n\n\t\t\tthis.object.matrixWorld.decompose( this.worldPosition, this.worldQuaternion, this._worldScale );\n\n\t\t\tthis._parentQuaternionInv.copy( this._parentQuaternion ).invert();\n\t\t\tthis._worldQuaternionInv.copy( this.worldQuaternion ).invert();\n\n\t\t}\n\n\t\tthis.camera.updateMatrixWorld();\n\t\tthis.camera.matrixWorld.decompose( this.cameraPosition, this.cameraQuaternion, this._cameraScale );\n\n\t\tif ( this.camera.isOrthographicCamera ) {\n\n\t\t\tthis.camera.getWorldDirection( this.eye ).negate();\n\n\t\t} else {\n\n\t\t\tthis.eye.copy( this.cameraPosition ).sub( this.worldPosition ).normalize();\n\n\t\t}\n\n\t\tsuper.updateMatrixWorld( this );\n\n\t}\n\n\tpointerHover( pointer ) {\n\n\t\tif ( this.object === undefined || this.dragging === true ) return;\n\n\t\t_raycaster.setFromCamera( pointer, this.camera );\n\n\t\tconst intersect = intersectObjectWithRay( this._gizmo.picker[ this.mode ], _raycaster );\n\n\t\tif ( intersect ) {\n\n\t\t\tthis.axis = intersect.object.name;\n\n\t\t} else {\n\n\t\t\tthis.axis = null;\n\n\t\t}\n\n\t}\n\n\tpointerDown( pointer ) {\n\n\t\tif ( this.object === undefined || this.dragging === true || pointer.button !== 0 ) return;\n\n\t\tif ( this.axis !== null ) {\n\n\t\t\t_raycaster.setFromCamera( pointer, this.camera );\n\n\t\t\tconst planeIntersect = intersectObjectWithRay( this._plane, _raycaster, true );\n\n\t\t\tif ( planeIntersect ) {\n\n\t\t\t\tthis.object.updateMatrixWorld();\n\t\t\t\tthis.object.parent.updateMatrixWorld();\n\n\t\t\t\tthis._positionStart.copy( this.object.position );\n\t\t\t\tthis._quaternionStart.copy( this.object.quaternion );\n\t\t\t\tthis._scaleStart.copy( this.object.scale );\n\n\t\t\t\tthis.object.matrixWorld.decompose( this.worldPositionStart, this.worldQuaternionStart, this._worldScaleStart );\n\n\t\t\t\tthis.pointStart.copy( planeIntersect.point ).sub( this.worldPositionStart );\n\n\t\t\t}\n\n\t\t\tthis.dragging = true;\n\t\t\t_mouseDownEvent.mode = this.mode;\n\t\t\tthis.dispatchEvent( _mouseDownEvent );\n\n\t\t}\n\n\t}\n\n\tpointerMove( pointer ) {\n\n\t\tconst axis = this.axis;\n\t\tconst mode = this.mode;\n\t\tconst object = this.object;\n\t\tlet space = this.space;\n\n\t\tif ( mode === 'scale' ) {\n\n\t\t\tspace = 'local';\n\n\t\t} else if ( axis === 'E' || axis === 'XYZE' || axis === 'XYZ' ) {\n\n\t\t\tspace = 'world';\n\n\t\t}\n\n\t\tif ( object === undefined || axis === null || this.dragging === false || pointer.button !== - 1 ) return;\n\n\t\t_raycaster.setFromCamera( pointer, this.camera );\n\n\t\tconst planeIntersect = intersectObjectWithRay( this._plane, _raycaster, true );\n\n\t\tif ( ! planeIntersect ) return;\n\n\t\tthis.pointEnd.copy( planeIntersect.point ).sub( this.worldPositionStart );\n\n\t\tif ( mode === 'translate' ) {\n\n\t\t\t// Apply translate\n\n\t\t\tthis._offset.copy( this.pointEnd ).sub( this.pointStart );\n\n\t\t\tif ( space === 'local' && axis !== 'XYZ' ) {\n\n\t\t\t\tthis._offset.applyQuaternion( this._worldQuaternionInv );\n\n\t\t\t}\n\n\t\t\tif ( axis.indexOf( 'X' ) === - 1 ) this._offset.x = 0;\n\t\t\tif ( axis.indexOf( 'Y' ) === - 1 ) this._offset.y = 0;\n\t\t\tif ( axis.indexOf( 'Z' ) === - 1 ) this._offset.z = 0;\n\n\t\t\tif ( space === 'local' && axis !== 'XYZ' ) {\n\n\t\t\t\tthis._offset.applyQuaternion( this._quaternionStart ).divide( this._parentScale );\n\n\t\t\t} else {\n\n\t\t\t\tthis._offset.applyQuaternion( this._parentQuaternionInv ).divide( this._parentScale );\n\n\t\t\t}\n\n\t\t\tobject.position.copy( this._offset ).add( this._positionStart );\n\n\t\t\t// Apply translation snap\n\n\t\t\tif ( this.translationSnap ) {\n\n\t\t\t\tif ( space === 'local' ) {\n\n\t\t\t\t\tobject.position.applyQuaternion( _tempQuaternion.copy( this._quaternionStart ).invert() );\n\n\t\t\t\t\tif ( axis.search( 'X' ) !== - 1 ) {\n\n\t\t\t\t\t\tobject.position.x = Math.round( object.position.x / this.translationSnap ) * this.translationSnap;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( axis.search( 'Y' ) !== - 1 ) {\n\n\t\t\t\t\t\tobject.position.y = Math.round( object.position.y / this.translationSnap ) * this.translationSnap;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( axis.search( 'Z' ) !== - 1 ) {\n\n\t\t\t\t\t\tobject.position.z = Math.round( object.position.z / this.translationSnap ) * this.translationSnap;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tobject.position.applyQuaternion( this._quaternionStart );\n\n\t\t\t\t}\n\n\t\t\t\tif ( space === 'world' ) {\n\n\t\t\t\t\tif ( object.parent ) {\n\n\t\t\t\t\t\tobject.position.add( _tempVector.setFromMatrixPosition( object.parent.matrixWorld ) );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( axis.search( 'X' ) !== - 1 ) {\n\n\t\t\t\t\t\tobject.position.x = Math.round( object.position.x / this.translationSnap ) * this.translationSnap;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( axis.search( 'Y' ) !== - 1 ) {\n\n\t\t\t\t\t\tobject.position.y = Math.round( object.position.y / this.translationSnap ) * this.translationSnap;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( axis.search( 'Z' ) !== - 1 ) {\n\n\t\t\t\t\t\tobject.position.z = Math.round( object.position.z / this.translationSnap ) * this.translationSnap;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( object.parent ) {\n\n\t\t\t\t\t\tobject.position.sub( _tempVector.setFromMatrixPosition( object.parent.matrixWorld ) );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else if ( mode === 'scale' ) {\n\n\t\t\tif ( axis.search( 'XYZ' ) !== - 1 ) {\n\n\t\t\t\tlet d = this.pointEnd.length() / this.pointStart.length();\n\n\t\t\t\tif ( this.pointEnd.dot( this.pointStart ) < 0 ) d *= - 1;\n\n\t\t\t\t_tempVector2.set( d, d, d );\n\n\t\t\t} else {\n\n\t\t\t\t_tempVector.copy( this.pointStart );\n\t\t\t\t_tempVector2.copy( this.pointEnd );\n\n\t\t\t\t_tempVector.applyQuaternion( this._worldQuaternionInv );\n\t\t\t\t_tempVector2.applyQuaternion( this._worldQuaternionInv );\n\n\t\t\t\t_tempVector2.divide( _tempVector );\n\n\t\t\t\tif ( axis.search( 'X' ) === - 1 ) {\n\n\t\t\t\t\t_tempVector2.x = 1;\n\n\t\t\t\t}\n\n\t\t\t\tif ( axis.search( 'Y' ) === - 1 ) {\n\n\t\t\t\t\t_tempVector2.y = 1;\n\n\t\t\t\t}\n\n\t\t\t\tif ( axis.search( 'Z' ) === - 1 ) {\n\n\t\t\t\t\t_tempVector2.z = 1;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// Apply scale\n\n\t\t\tobject.scale.copy( this._scaleStart ).multiply( _tempVector2 );\n\n\t\t\tif ( this.scaleSnap ) {\n\n\t\t\t\tif ( axis.search( 'X' ) !== - 1 ) {\n\n\t\t\t\t\tobject.scale.x = Math.round( object.scale.x / this.scaleSnap ) * this.scaleSnap || this.scaleSnap;\n\n\t\t\t\t}\n\n\t\t\t\tif ( axis.search( 'Y' ) !== - 1 ) {\n\n\t\t\t\t\tobject.scale.y = Math.round( object.scale.y / this.scaleSnap ) * this.scaleSnap || this.scaleSnap;\n\n\t\t\t\t}\n\n\t\t\t\tif ( axis.search( 'Z' ) !== - 1 ) {\n\n\t\t\t\t\tobject.scale.z = Math.round( object.scale.z / this.scaleSnap ) * this.scaleSnap || this.scaleSnap;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t} else if ( mode === 'rotate' ) {\n\n\t\t\tthis._offset.copy( this.pointEnd ).sub( this.pointStart );\n\n\t\t\tconst ROTATION_SPEED = 20 / this.worldPosition.distanceTo( _tempVector.setFromMatrixPosition( this.camera.matrixWorld ) );\n\n\t\t\tif ( axis === 'E' ) {\n\n\t\t\t\tthis.rotationAxis.copy( this.eye );\n\t\t\t\tthis.rotationAngle = this.pointEnd.angleTo( this.pointStart );\n\n\t\t\t\tthis._startNorm.copy( this.pointStart ).normalize();\n\t\t\t\tthis._endNorm.copy( this.pointEnd ).normalize();\n\n\t\t\t\tthis.rotationAngle *= ( this._endNorm.cross( this._startNorm ).dot( this.eye ) < 0 ? 1 : - 1 );\n\n\t\t\t} else if ( axis === 'XYZE' ) {\n\n\t\t\t\tthis.rotationAxis.copy( this._offset ).cross( this.eye ).normalize();\n\t\t\t\tthis.rotationAngle = this._offset.dot( _tempVector.copy( this.rotationAxis ).cross( this.eye ) ) * ROTATION_SPEED;\n\n\t\t\t} else if ( axis === 'X' || axis === 'Y' || axis === 'Z' ) {\n\n\t\t\t\tthis.rotationAxis.copy( _unit[ axis ] );\n\n\t\t\t\t_tempVector.copy( _unit[ axis ] );\n\n\t\t\t\tif ( space === 'local' ) {\n\n\t\t\t\t\t_tempVector.applyQuaternion( this.worldQuaternion );\n\n\t\t\t\t}\n\n\t\t\t\tthis.rotationAngle = this._offset.dot( _tempVector.cross( this.eye ).normalize() ) * ROTATION_SPEED;\n\n\t\t\t}\n\n\t\t\t// Apply rotation snap\n\n\t\t\tif ( this.rotationSnap ) this.rotationAngle = Math.round( this.rotationAngle / this.rotationSnap ) * this.rotationSnap;\n\n\t\t\t// Apply rotate\n\t\t\tif ( space === 'local' && axis !== 'E' && axis !== 'XYZE' ) {\n\n\t\t\t\tobject.quaternion.copy( this._quaternionStart );\n\t\t\t\tobject.quaternion.multiply( _tempQuaternion.setFromAxisAngle( this.rotationAxis, this.rotationAngle ) ).normalize();\n\n\t\t\t} else {\n\n\t\t\t\tthis.rotationAxis.applyQuaternion( this._parentQuaternionInv );\n\t\t\t\tobject.quaternion.copy( _tempQuaternion.setFromAxisAngle( this.rotationAxis, this.rotationAngle ) );\n\t\t\t\tobject.quaternion.multiply( this._quaternionStart ).normalize();\n\n\t\t\t}\n\n\t\t}\n\n\t\tthis.dispatchEvent( _changeEvent );\n\t\tthis.dispatchEvent( _objectChangeEvent );\n\n\t}\n\n\tpointerUp( pointer ) {\n\n\t\tif ( pointer.button !== 0 ) return;\n\n\t\tif ( this.dragging && ( this.axis !== null ) ) {\n\n\t\t\t_mouseUpEvent.mode = this.mode;\n\t\t\tthis.dispatchEvent( _mouseUpEvent );\n\n\t\t}\n\n\t\tthis.dragging = false;\n\t\tthis.axis = null;\n\n\t}\n\n\tdispose() {\n\n\t\tthis.domElement.removeEventListener( 'pointerdown', this._onPointerDown );\n\t\tthis.domElement.removeEventListener( 'pointermove', this._onPointerHover );\n\t\tthis.domElement.removeEventListener( 'pointermove', this._onPointerMove );\n\t\tthis.domElement.removeEventListener( 'pointerup', this._onPointerUp );\n\n\t\tthis.traverse( function ( child ) {\n\n\t\t\tif ( child.geometry ) child.geometry.dispose();\n\t\t\tif ( child.material ) child.material.dispose();\n\n\t\t} );\n\n\t}\n\n\t// Set current object\n\tattach( object ) {\n\n\t\tthis.object = object;\n\t\tthis.visible = true;\n\n\t\treturn this;\n\n\t}\n\n\t// Detach from object\n\tdetach() {\n\n\t\tthis.object = undefined;\n\t\tthis.visible = false;\n\t\tthis.axis = null;\n\n\t\treturn this;\n\n\t}\n\n\treset() {\n\n\t\tif ( ! this.enabled ) return;\n\n\t\tif ( this.dragging ) {\n\n\t\t\tthis.object.position.copy( this._positionStart );\n\t\t\tthis.object.quaternion.copy( this._quaternionStart );\n\t\t\tthis.object.scale.copy( this._scaleStart );\n\n\t\t\tthis.dispatchEvent( _changeEvent );\n\t\t\tthis.dispatchEvent( _objectChangeEvent );\n\n\t\t\tthis.pointStart.copy( this.pointEnd );\n\n\t\t}\n\n\t}\n\n\tgetRaycaster() {\n\n\t\treturn _raycaster;\n\n\t}\n\n\t// TODO: deprecate\n\n\tgetMode() {\n\n\t\treturn this.mode;\n\n\t}\n\n\tsetMode( mode ) {\n\n\t\tthis.mode = mode;\n\n\t}\n\n\tsetTranslationSnap( translationSnap ) {\n\n\t\tthis.translationSnap = translationSnap;\n\n\t}\n\n\tsetRotationSnap( rotationSnap ) {\n\n\t\tthis.rotationSnap = rotationSnap;\n\n\t}\n\n\tsetScaleSnap( scaleSnap ) {\n\n\t\tthis.scaleSnap = scaleSnap;\n\n\t}\n\n\tsetSize( size ) {\n\n\t\tthis.size = size;\n\n\t}\n\n\tsetSpace( space ) {\n\n\t\tthis.space = space;\n\n\t}\n\n}\n\n// mouse / touch event handlers\n\nfunction getPointer( event ) {\n\n\tif ( this.domElement.ownerDocument.pointerLockElement ) {\n\n\t\treturn {\n\t\t\tx: 0,\n\t\t\ty: 0,\n\t\t\tbutton: event.button\n\t\t};\n\n\t} else {\n\n\t\tconst rect = this.domElement.getBoundingClientRect();\n\n\t\treturn {\n\t\t\tx: ( event.clientX - rect.left ) / rect.width * 2 - 1,\n\t\t\ty: - ( event.clientY - rect.top ) / rect.height * 2 + 1,\n\t\t\tbutton: event.button\n\t\t};\n\n\t}\n\n}\n\nfunction onPointerHover( event ) {\n\n\tif ( ! this.enabled ) return;\n\n\tswitch ( event.pointerType ) {\n\n\t\tcase 'mouse':\n\t\tcase 'pen':\n\t\t\tthis.pointerHover( this._getPointer( event ) );\n\t\t\tbreak;\n\n\t}\n\n}\n\nfunction onPointerDown( event ) {\n\n\tif ( ! this.enabled ) return;\n\n\tif ( ! document.pointerLockElement ) {\n\n\t\tthis.domElement.setPointerCapture( event.pointerId );\n\n\t}\n\n\tthis.domElement.addEventListener( 'pointermove', this._onPointerMove );\n\n\tthis.pointerHover( this._getPointer( event ) );\n\tthis.pointerDown( this._getPointer( event ) );\n\n}\n\nfunction onPointerMove( event ) {\n\n\tif ( ! this.enabled ) return;\n\n\tthis.pointerMove( this._getPointer( event ) );\n\n}\n\nfunction onPointerUp( event ) {\n\n\tif ( ! this.enabled ) return;\n\n\tthis.domElement.releasePointerCapture( event.pointerId );\n\n\tthis.domElement.removeEventListener( 'pointermove', this._onPointerMove );\n\n\tthis.pointerUp( this._getPointer( event ) );\n\n}\n\nfunction intersectObjectWithRay( object, raycaster, includeInvisible ) {\n\n\tconst allIntersections = raycaster.intersectObject( object, true );\n\n\tfor ( let i = 0; i < allIntersections.length; i ++ ) {\n\n\t\tif ( allIntersections[ i ].object.visible || includeInvisible ) {\n\n\t\t\treturn allIntersections[ i ];\n\n\t\t}\n\n\t}\n\n\treturn false;\n\n}\n\n//\n\n// Reusable utility variables\n\nconst _tempEuler = new Euler();\nconst _alignVector = new Vector3( 0, 1, 0 );\nconst _zeroVector = new Vector3( 0, 0, 0 );\nconst _lookAtMatrix = new Matrix4();\nconst _tempQuaternion2 = new Quaternion();\nconst _identityQuaternion = new Quaternion();\nconst _dirVector = new Vector3();\nconst _tempMatrix = new Matrix4();\n\nconst _unitX = new Vector3( 1, 0, 0 );\nconst _unitY = new Vector3( 0, 1, 0 );\nconst _unitZ = new Vector3( 0, 0, 1 );\n\nconst _v1 = new Vector3();\nconst _v2 = new Vector3();\nconst _v3 = new Vector3();\n\nclass TransformControlsGizmo extends Object3D {\n\n\tconstructor() {\n\n\t\tsuper();\n\n\t\tthis.isTransformControlsGizmo = true;\n\n\t\tthis.type = 'TransformControlsGizmo';\n\n\t\t// shared materials\n\n\t\tconst gizmoMaterial = new MeshBasicMaterial( {\n\t\t\tdepthTest: false,\n\t\t\tdepthWrite: false,\n\t\t\tfog: false,\n\t\t\ttoneMapped: false,\n\t\t\ttransparent: true\n\t\t} );\n\n\t\tconst gizmoLineMaterial = new LineBasicMaterial( {\n\t\t\tdepthTest: false,\n\t\t\tdepthWrite: false,\n\t\t\tfog: false,\n\t\t\ttoneMapped: false,\n\t\t\ttransparent: true\n\t\t} );\n\n\t\t// Make unique material for each axis/color\n\n\t\tconst matInvisible = gizmoMaterial.clone();\n\t\tmatInvisible.opacity = 0.15;\n\n\t\tconst matHelper = gizmoLineMaterial.clone();\n\t\tmatHelper.opacity = 0.5;\n\n\t\tconst matRed = gizmoMaterial.clone();\n\t\tmatRed.color.setHex( 0xff0000 );\n\n\t\tconst matGreen = gizmoMaterial.clone();\n\t\tmatGreen.color.setHex( 0x00ff00 );\n\n\t\tconst matBlue = gizmoMaterial.clone();\n\t\tmatBlue.color.setHex( 0x0000ff );\n\n\t\tconst matRedTransparent = gizmoMaterial.clone();\n\t\tmatRedTransparent.color.setHex( 0xff0000 );\n\t\tmatRedTransparent.opacity = 0.5;\n\n\t\tconst matGreenTransparent = gizmoMaterial.clone();\n\t\tmatGreenTransparent.color.setHex( 0x00ff00 );\n\t\tmatGreenTransparent.opacity = 0.5;\n\n\t\tconst matBlueTransparent = gizmoMaterial.clone();\n\t\tmatBlueTransparent.color.setHex( 0x0000ff );\n\t\tmatBlueTransparent.opacity = 0.5;\n\n\t\tconst matWhiteTransparent = gizmoMaterial.clone();\n\t\tmatWhiteTransparent.opacity = 0.25;\n\n\t\tconst matYellowTransparent = gizmoMaterial.clone();\n\t\tmatYellowTransparent.color.setHex( 0xffff00 );\n\t\tmatYellowTransparent.opacity = 0.25;\n\n\t\tconst matYellow = gizmoMaterial.clone();\n\t\tmatYellow.color.setHex( 0xffff00 );\n\n\t\tconst matGray = gizmoMaterial.clone();\n\t\tmatGray.color.setHex( 0x787878 );\n\n\t\t// reusable geometry\n\n\t\tconst arrowGeometry = new CylinderGeometry( 0, 0.04, 0.1, 12 );\n\t\tarrowGeometry.translate( 0, 0.05, 0 );\n\n\t\tconst scaleHandleGeometry = new BoxGeometry( 0.08, 0.08, 0.08 );\n\t\tscaleHandleGeometry.translate( 0, 0.04, 0 );\n\n\t\tconst lineGeometry = new BufferGeometry();\n\t\tlineGeometry.setAttribute( 'position', new Float32BufferAttribute( [ 0, 0, 0,\t1, 0, 0 ], 3 ) );\n\n\t\tconst lineGeometry2 = new CylinderGeometry( 0.0075, 0.0075, 0.5, 3 );\n\t\tlineGeometry2.translate( 0, 0.25, 0 );\n\n\t\tfunction CircleGeometry( radius, arc ) {\n\n\t\t\tconst geometry = new TorusGeometry( radius, 0.0075, 3, 64, arc * Math.PI * 2 );\n\t\t\tgeometry.rotateY( Math.PI / 2 );\n\t\t\tgeometry.rotateX( Math.PI / 2 );\n\t\t\treturn geometry;\n\n\t\t}\n\n\t\t// Special geometry for transform helper. If scaled with position vector it spans from [0,0,0] to position\n\n\t\tfunction TranslateHelperGeometry() {\n\n\t\t\tconst geometry = new BufferGeometry();\n\n\t\t\tgeometry.setAttribute( 'position', new Float32BufferAttribute( [ 0, 0, 0, 1, 1, 1 ], 3 ) );\n\n\t\t\treturn geometry;\n\n\t\t}\n\n\t\t// Gizmo definitions - custom hierarchy definitions for setupGizmo() function\n\n\t\tconst gizmoTranslate = {\n\t\t\tX: [\n\t\t\t\t[ new Mesh( arrowGeometry, matRed ), [ 0.5, 0, 0 ], [ 0, 0, - Math.PI / 2 ]],\n\t\t\t\t[ new Mesh( arrowGeometry, matRed ), [ - 0.5, 0, 0 ], [ 0, 0, Math.PI / 2 ]],\n\t\t\t\t[ new Mesh( lineGeometry2, matRed ), [ 0, 0, 0 ], [ 0, 0, - Math.PI / 2 ]]\n\t\t\t],\n\t\t\tY: [\n\t\t\t\t[ new Mesh( arrowGeometry, matGreen ), [ 0, 0.5, 0 ]],\n\t\t\t\t[ new Mesh( arrowGeometry, matGreen ), [ 0, - 0.5, 0 ], [ Math.PI, 0, 0 ]],\n\t\t\t\t[ new Mesh( lineGeometry2, matGreen ) ]\n\t\t\t],\n\t\t\tZ: [\n\t\t\t\t[ new Mesh( arrowGeometry, matBlue ), [ 0, 0, 0.5 ], [ Math.PI / 2, 0, 0 ]],\n\t\t\t\t[ new Mesh( arrowGeometry, matBlue ), [ 0, 0, - 0.5 ], [ - Math.PI / 2, 0, 0 ]],\n\t\t\t\t[ new Mesh( lineGeometry2, matBlue ), null, [ Math.PI / 2, 0, 0 ]]\n\t\t\t],\n\t\t\tXYZ: [\n\t\t\t\t[ new Mesh( new OctahedronGeometry( 0.1, 0 ), matWhiteTransparent.clone() ), [ 0, 0, 0 ]]\n\t\t\t],\n\t\t\tXY: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.15, 0.15, 0.01 ), matBlueTransparent.clone() ), [ 0.15, 0.15, 0 ]]\n\t\t\t],\n\t\t\tYZ: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.15, 0.15, 0.01 ), matRedTransparent.clone() ), [ 0, 0.15, 0.15 ], [ 0, Math.PI / 2, 0 ]]\n\t\t\t],\n\t\t\tXZ: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.15, 0.15, 0.01 ), matGreenTransparent.clone() ), [ 0.15, 0, 0.15 ], [ - Math.PI / 2, 0, 0 ]]\n\t\t\t]\n\t\t};\n\n\t\tconst pickerTranslate = {\n\t\t\tX: [\n\t\t\t\t[ new Mesh( new CylinderGeometry( 0.2, 0, 0.6, 4 ), matInvisible ), [ 0.3, 0, 0 ], [ 0, 0, - Math.PI / 2 ]],\n\t\t\t\t[ new Mesh( new CylinderGeometry( 0.2, 0, 0.6, 4 ), matInvisible ), [ - 0.3, 0, 0 ], [ 0, 0, Math.PI / 2 ]]\n\t\t\t],\n\t\t\tY: [\n\t\t\t\t[ new Mesh( new CylinderGeometry( 0.2, 0, 0.6, 4 ), matInvisible ), [ 0, 0.3, 0 ]],\n\t\t\t\t[ new Mesh( new CylinderGeometry( 0.2, 0, 0.6, 4 ), matInvisible ), [ 0, - 0.3, 0 ], [ 0, 0, Math.PI ]]\n\t\t\t],\n\t\t\tZ: [\n\t\t\t\t[ new Mesh( new CylinderGeometry( 0.2, 0, 0.6, 4 ), matInvisible ), [ 0, 0, 0.3 ], [ Math.PI / 2, 0, 0 ]],\n\t\t\t\t[ new Mesh( new CylinderGeometry( 0.2, 0, 0.6, 4 ), matInvisible ), [ 0, 0, - 0.3 ], [ - Math.PI / 2, 0, 0 ]]\n\t\t\t],\n\t\t\tXYZ: [\n\t\t\t\t[ new Mesh( new OctahedronGeometry( 0.2, 0 ), matInvisible ) ]\n\t\t\t],\n\t\t\tXY: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.2, 0.2, 0.01 ), matInvisible ), [ 0.15, 0.15, 0 ]]\n\t\t\t],\n\t\t\tYZ: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.2, 0.2, 0.01 ), matInvisible ), [ 0, 0.15, 0.15 ], [ 0, Math.PI / 2, 0 ]]\n\t\t\t],\n\t\t\tXZ: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.2, 0.2, 0.01 ), matInvisible ), [ 0.15, 0, 0.15 ], [ - Math.PI / 2, 0, 0 ]]\n\t\t\t]\n\t\t};\n\n\t\tconst helperTranslate = {\n\t\t\tSTART: [\n\t\t\t\t[ new Mesh( new OctahedronGeometry( 0.01, 2 ), matHelper ), null, null, null, 'helper' ]\n\t\t\t],\n\t\t\tEND: [\n\t\t\t\t[ new Mesh( new OctahedronGeometry( 0.01, 2 ), matHelper ), null, null, null, 'helper' ]\n\t\t\t],\n\t\t\tDELTA: [\n\t\t\t\t[ new Line( TranslateHelperGeometry(), matHelper ), null, null, null, 'helper' ]\n\t\t\t],\n\t\t\tX: [\n\t\t\t\t[ new Line( lineGeometry, matHelper.clone() ), [ - 1e3, 0, 0 ], null, [ 1e6, 1, 1 ], 'helper' ]\n\t\t\t],\n\t\t\tY: [\n\t\t\t\t[ new Line( lineGeometry, matHelper.clone() ), [ 0, - 1e3, 0 ], [ 0, 0, Math.PI / 2 ], [ 1e6, 1, 1 ], 'helper' ]\n\t\t\t],\n\t\t\tZ: [\n\t\t\t\t[ new Line( lineGeometry, matHelper.clone() ), [ 0, 0, - 1e3 ], [ 0, - Math.PI / 2, 0 ], [ 1e6, 1, 1 ], 'helper' ]\n\t\t\t]\n\t\t};\n\n\t\tconst gizmoRotate = {\n\t\t\tXYZE: [\n\t\t\t\t[ new Mesh( CircleGeometry( 0.5, 1 ), matGray ), null, [ 0, Math.PI / 2, 0 ]]\n\t\t\t],\n\t\t\tX: [\n\t\t\t\t[ new Mesh( CircleGeometry( 0.5, 0.5 ), matRed ) ]\n\t\t\t],\n\t\t\tY: [\n\t\t\t\t[ new Mesh( CircleGeometry( 0.5, 0.5 ), matGreen ), null, [ 0, 0, - Math.PI / 2 ]]\n\t\t\t],\n\t\t\tZ: [\n\t\t\t\t[ new Mesh( CircleGeometry( 0.5, 0.5 ), matBlue ), null, [ 0, Math.PI / 2, 0 ]]\n\t\t\t],\n\t\t\tE: [\n\t\t\t\t[ new Mesh( CircleGeometry( 0.75, 1 ), matYellowTransparent ), null, [ 0, Math.PI / 2, 0 ]]\n\t\t\t]\n\t\t};\n\n\t\tconst helperRotate = {\n\t\t\tAXIS: [\n\t\t\t\t[ new Line( lineGeometry, matHelper.clone() ), [ - 1e3, 0, 0 ], null, [ 1e6, 1, 1 ], 'helper' ]\n\t\t\t]\n\t\t};\n\n\t\tconst pickerRotate = {\n\t\t\tXYZE: [\n\t\t\t\t[ new Mesh( new SphereGeometry( 0.25, 10, 8 ), matInvisible ) ]\n\t\t\t],\n\t\t\tX: [\n\t\t\t\t[ new Mesh( new TorusGeometry( 0.5, 0.1, 4, 24 ), matInvisible ), [ 0, 0, 0 ], [ 0, - Math.PI / 2, - Math.PI / 2 ]],\n\t\t\t],\n\t\t\tY: [\n\t\t\t\t[ new Mesh( new TorusGeometry( 0.5, 0.1, 4, 24 ), matInvisible ), [ 0, 0, 0 ], [ Math.PI / 2, 0, 0 ]],\n\t\t\t],\n\t\t\tZ: [\n\t\t\t\t[ new Mesh( new TorusGeometry( 0.5, 0.1, 4, 24 ), matInvisible ), [ 0, 0, 0 ], [ 0, 0, - Math.PI / 2 ]],\n\t\t\t],\n\t\t\tE: [\n\t\t\t\t[ new Mesh( new TorusGeometry( 0.75, 0.1, 2, 24 ), matInvisible ) ]\n\t\t\t]\n\t\t};\n\n\t\tconst gizmoScale = {\n\t\t\tX: [\n\t\t\t\t[ new Mesh( scaleHandleGeometry, matRed ), [ 0.5, 0, 0 ], [ 0, 0, - Math.PI / 2 ]],\n\t\t\t\t[ new Mesh( lineGeometry2, matRed ), [ 0, 0, 0 ], [ 0, 0, - Math.PI / 2 ]],\n\t\t\t\t[ new Mesh( scaleHandleGeometry, matRed ), [ - 0.5, 0, 0 ], [ 0, 0, Math.PI / 2 ]],\n\t\t\t],\n\t\t\tY: [\n\t\t\t\t[ new Mesh( scaleHandleGeometry, matGreen ), [ 0, 0.5, 0 ]],\n\t\t\t\t[ new Mesh( lineGeometry2, matGreen ) ],\n\t\t\t\t[ new Mesh( scaleHandleGeometry, matGreen ), [ 0, - 0.5, 0 ], [ 0, 0, Math.PI ]],\n\t\t\t],\n\t\t\tZ: [\n\t\t\t\t[ new Mesh( scaleHandleGeometry, matBlue ), [ 0, 0, 0.5 ], [ Math.PI / 2, 0, 0 ]],\n\t\t\t\t[ new Mesh( lineGeometry2, matBlue ), [ 0, 0, 0 ], [ Math.PI / 2, 0, 0 ]],\n\t\t\t\t[ new Mesh( scaleHandleGeometry, matBlue ), [ 0, 0, - 0.5 ], [ - Math.PI / 2, 0, 0 ]]\n\t\t\t],\n\t\t\tXY: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.15, 0.15, 0.01 ), matBlueTransparent ), [ 0.15, 0.15, 0 ]]\n\t\t\t],\n\t\t\tYZ: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.15, 0.15, 0.01 ), matRedTransparent ), [ 0, 0.15, 0.15 ], [ 0, Math.PI / 2, 0 ]]\n\t\t\t],\n\t\t\tXZ: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.15, 0.15, 0.01 ), matGreenTransparent ), [ 0.15, 0, 0.15 ], [ - Math.PI / 2, 0, 0 ]]\n\t\t\t],\n\t\t\tXYZ: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.1, 0.1, 0.1 ), matWhiteTransparent.clone() ) ],\n\t\t\t]\n\t\t};\n\n\t\tconst pickerScale = {\n\t\t\tX: [\n\t\t\t\t[ new Mesh( new CylinderGeometry( 0.2, 0, 0.6, 4 ), matInvisible ), [ 0.3, 0, 0 ], [ 0, 0, - Math.PI / 2 ]],\n\t\t\t\t[ new Mesh( new CylinderGeometry( 0.2, 0, 0.6, 4 ), matInvisible ), [ - 0.3, 0, 0 ], [ 0, 0, Math.PI / 2 ]]\n\t\t\t],\n\t\t\tY: [\n\t\t\t\t[ new Mesh( new CylinderGeometry( 0.2, 0, 0.6, 4 ), matInvisible ), [ 0, 0.3, 0 ]],\n\t\t\t\t[ new Mesh( new CylinderGeometry( 0.2, 0, 0.6, 4 ), matInvisible ), [ 0, - 0.3, 0 ], [ 0, 0, Math.PI ]]\n\t\t\t],\n\t\t\tZ: [\n\t\t\t\t[ new Mesh( new CylinderGeometry( 0.2, 0, 0.6, 4 ), matInvisible ), [ 0, 0, 0.3 ], [ Math.PI / 2, 0, 0 ]],\n\t\t\t\t[ new Mesh( new CylinderGeometry( 0.2, 0, 0.6, 4 ), matInvisible ), [ 0, 0, - 0.3 ], [ - Math.PI / 2, 0, 0 ]]\n\t\t\t],\n\t\t\tXY: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.2, 0.2, 0.01 ), matInvisible ), [ 0.15, 0.15, 0 ]],\n\t\t\t],\n\t\t\tYZ: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.2, 0.2, 0.01 ), matInvisible ), [ 0, 0.15, 0.15 ], [ 0, Math.PI / 2, 0 ]],\n\t\t\t],\n\t\t\tXZ: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.2, 0.2, 0.01 ), matInvisible ), [ 0.15, 0, 0.15 ], [ - Math.PI / 2, 0, 0 ]],\n\t\t\t],\n\t\t\tXYZ: [\n\t\t\t\t[ new Mesh( new BoxGeometry( 0.2, 0.2, 0.2 ), matInvisible ), [ 0, 0, 0 ]],\n\t\t\t]\n\t\t};\n\n\t\tconst helperScale = {\n\t\t\tX: [\n\t\t\t\t[ new Line( lineGeometry, matHelper.clone() ), [ - 1e3, 0, 0 ], null, [ 1e6, 1, 1 ], 'helper' ]\n\t\t\t],\n\t\t\tY: [\n\t\t\t\t[ new Line( lineGeometry, matHelper.clone() ), [ 0, - 1e3, 0 ], [ 0, 0, Math.PI / 2 ], [ 1e6, 1, 1 ], 'helper' ]\n\t\t\t],\n\t\t\tZ: [\n\t\t\t\t[ new Line( lineGeometry, matHelper.clone() ), [ 0, 0, - 1e3 ], [ 0, - Math.PI / 2, 0 ], [ 1e6, 1, 1 ], 'helper' ]\n\t\t\t]\n\t\t};\n\n\t\t// Creates an Object3D with gizmos described in custom hierarchy definition.\n\n\t\tfunction setupGizmo( gizmoMap ) {\n\n\t\t\tconst gizmo = new Object3D();\n\n\t\t\tfor ( const name in gizmoMap ) {\n\n\t\t\t\tfor ( let i = gizmoMap[ name ].length; i --; ) {\n\n\t\t\t\t\tconst object = gizmoMap[ name ][ i ][ 0 ].clone();\n\t\t\t\t\tconst position = gizmoMap[ name ][ i ][ 1 ];\n\t\t\t\t\tconst rotation = gizmoMap[ name ][ i ][ 2 ];\n\t\t\t\t\tconst scale = gizmoMap[ name ][ i ][ 3 ];\n\t\t\t\t\tconst tag = gizmoMap[ name ][ i ][ 4 ];\n\n\t\t\t\t\t// name and tag properties are essential for picking and updating logic.\n\t\t\t\t\tobject.name = name;\n\t\t\t\t\tobject.tag = tag;\n\n\t\t\t\t\tif ( position ) {\n\n\t\t\t\t\t\tobject.position.set( position[ 0 ], position[ 1 ], position[ 2 ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( rotation ) {\n\n\t\t\t\t\t\tobject.rotation.set( rotation[ 0 ], rotation[ 1 ], rotation[ 2 ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( scale ) {\n\n\t\t\t\t\t\tobject.scale.set( scale[ 0 ], scale[ 1 ], scale[ 2 ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tobject.updateMatrix();\n\n\t\t\t\t\tconst tempGeometry = object.geometry.clone();\n\t\t\t\t\ttempGeometry.applyMatrix4( object.matrix );\n\t\t\t\t\tobject.geometry = tempGeometry;\n\t\t\t\t\tobject.renderOrder = Infinity;\n\n\t\t\t\t\tobject.position.set( 0, 0, 0 );\n\t\t\t\t\tobject.rotation.set( 0, 0, 0 );\n\t\t\t\t\tobject.scale.set( 1, 1, 1 );\n\n\t\t\t\t\tgizmo.add( object );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn gizmo;\n\n\t\t}\n\n\t\t// Gizmo creation\n\n\t\tthis.gizmo = {};\n\t\tthis.picker = {};\n\t\tthis.helper = {};\n\n\t\tthis.add( this.gizmo[ 'translate' ] = setupGizmo( gizmoTranslate ) );\n\t\tthis.add( this.gizmo[ 'rotate' ] = setupGizmo( gizmoRotate ) );\n\t\tthis.add( this.gizmo[ 'scale' ] = setupGizmo( gizmoScale ) );\n\t\tthis.add( this.picker[ 'translate' ] = setupGizmo( pickerTranslate ) );\n\t\tthis.add( this.picker[ 'rotate' ] = setupGizmo( pickerRotate ) );\n\t\tthis.add( this.picker[ 'scale' ] = setupGizmo( pickerScale ) );\n\t\tthis.add( this.helper[ 'translate' ] = setupGizmo( helperTranslate ) );\n\t\tthis.add( this.helper[ 'rotate' ] = setupGizmo( helperRotate ) );\n\t\tthis.add( this.helper[ 'scale' ] = setupGizmo( helperScale ) );\n\n\t\t// Pickers should be hidden always\n\n\t\tthis.picker[ 'translate' ].visible = false;\n\t\tthis.picker[ 'rotate' ].visible = false;\n\t\tthis.picker[ 'scale' ].visible = false;\n\n\t}\n\n\t// updateMatrixWorld will update transformations and appearance of individual handles\n\n\tupdateMatrixWorld( force ) {\n\n\t\tconst space = ( this.mode === 'scale' ) ? 'local' : this.space; // scale always oriented to local rotation\n\n\t\tconst quaternion = ( space === 'local' ) ? this.worldQuaternion : _identityQuaternion;\n\n\t\t// Show only gizmos for current transform mode\n\n\t\tthis.gizmo[ 'translate' ].visible = this.mode === 'translate';\n\t\tthis.gizmo[ 'rotate' ].visible = this.mode === 'rotate';\n\t\tthis.gizmo[ 'scale' ].visible = this.mode === 'scale';\n\n\t\tthis.helper[ 'translate' ].visible = this.mode === 'translate';\n\t\tthis.helper[ 'rotate' ].visible = this.mode === 'rotate';\n\t\tthis.helper[ 'scale' ].visible = this.mode === 'scale';\n\n\n\t\tlet handles = [];\n\t\thandles = handles.concat( this.picker[ this.mode ].children );\n\t\thandles = handles.concat( this.gizmo[ this.mode ].children );\n\t\thandles = handles.concat( this.helper[ this.mode ].children );\n\n\t\tfor ( let i = 0; i < handles.length; i ++ ) {\n\n\t\t\tconst handle = handles[ i ];\n\n\t\t\t// hide aligned to camera\n\n\t\t\thandle.visible = true;\n\t\t\thandle.rotation.set( 0, 0, 0 );\n\t\t\thandle.position.copy( this.worldPosition );\n\n\t\t\tlet factor;\n\n\t\t\tif ( this.camera.isOrthographicCamera ) {\n\n\t\t\t\tfactor = ( this.camera.top - this.camera.bottom ) / this.camera.zoom;\n\n\t\t\t} else {\n\n\t\t\t\tfactor = this.worldPosition.distanceTo( this.cameraPosition ) * Math.min( 1.9 * Math.tan( Math.PI * this.camera.fov / 360 ) / this.camera.zoom, 7 );\n\n\t\t\t}\n\n\t\t\thandle.scale.set( 1, 1, 1 ).multiplyScalar( factor * this.size / 4 );\n\n\t\t\t// TODO: simplify helpers and consider decoupling from gizmo\n\n\t\t\tif ( handle.tag === 'helper' ) {\n\n\t\t\t\thandle.visible = false;\n\n\t\t\t\tif ( handle.name === 'AXIS' ) {\n\n\t\t\t\t\thandle.visible = !! this.axis;\n\n\t\t\t\t\tif ( this.axis === 'X' ) {\n\n\t\t\t\t\t\t_tempQuaternion.setFromEuler( _tempEuler.set( 0, 0, 0 ) );\n\t\t\t\t\t\thandle.quaternion.copy( quaternion ).multiply( _tempQuaternion );\n\n\t\t\t\t\t\tif ( Math.abs( _alignVector.copy( _unitX ).applyQuaternion( quaternion ).dot( this.eye ) ) > 0.9 ) {\n\n\t\t\t\t\t\t\thandle.visible = false;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( this.axis === 'Y' ) {\n\n\t\t\t\t\t\t_tempQuaternion.setFromEuler( _tempEuler.set( 0, 0, Math.PI / 2 ) );\n\t\t\t\t\t\thandle.quaternion.copy( quaternion ).multiply( _tempQuaternion );\n\n\t\t\t\t\t\tif ( Math.abs( _alignVector.copy( _unitY ).applyQuaternion( quaternion ).dot( this.eye ) ) > 0.9 ) {\n\n\t\t\t\t\t\t\thandle.visible = false;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( this.axis === 'Z' ) {\n\n\t\t\t\t\t\t_tempQuaternion.setFromEuler( _tempEuler.set( 0, Math.PI / 2, 0 ) );\n\t\t\t\t\t\thandle.quaternion.copy( quaternion ).multiply( _tempQuaternion );\n\n\t\t\t\t\t\tif ( Math.abs( _alignVector.copy( _unitZ ).applyQuaternion( quaternion ).dot( this.eye ) ) > 0.9 ) {\n\n\t\t\t\t\t\t\thandle.visible = false;\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( this.axis === 'XYZE' ) {\n\n\t\t\t\t\t\t_tempQuaternion.setFromEuler( _tempEuler.set( 0, Math.PI / 2, 0 ) );\n\t\t\t\t\t\t_alignVector.copy( this.rotationAxis );\n\t\t\t\t\t\thandle.quaternion.setFromRotationMatrix( _lookAtMatrix.lookAt( _zeroVector, _alignVector, _unitY ) );\n\t\t\t\t\t\thandle.quaternion.multiply( _tempQuaternion );\n\t\t\t\t\t\thandle.visible = this.dragging;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( this.axis === 'E' ) {\n\n\t\t\t\t\t\thandle.visible = false;\n\n\t\t\t\t\t}\n\n\n\t\t\t\t} else if ( handle.name === 'START' ) {\n\n\t\t\t\t\thandle.position.copy( this.worldPositionStart );\n\t\t\t\t\thandle.visible = this.dragging;\n\n\t\t\t\t} else if ( handle.name === 'END' ) {\n\n\t\t\t\t\thandle.position.copy( this.worldPosition );\n\t\t\t\t\thandle.visible = this.dragging;\n\n\t\t\t\t} else if ( handle.name === 'DELTA' ) {\n\n\t\t\t\t\thandle.position.copy( this.worldPositionStart );\n\t\t\t\t\thandle.quaternion.copy( this.worldQuaternionStart );\n\t\t\t\t\t_tempVector.set( 1e-10, 1e-10, 1e-10 ).add( this.worldPositionStart ).sub( this.worldPosition ).multiplyScalar( - 1 );\n\t\t\t\t\t_tempVector.applyQuaternion( this.worldQuaternionStart.clone().invert() );\n\t\t\t\t\thandle.scale.copy( _tempVector );\n\t\t\t\t\thandle.visible = this.dragging;\n\n\t\t\t\t} else {\n\n\t\t\t\t\thandle.quaternion.copy( quaternion );\n\n\t\t\t\t\tif ( this.dragging ) {\n\n\t\t\t\t\t\thandle.position.copy( this.worldPositionStart );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\thandle.position.copy( this.worldPosition );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( this.axis ) {\n\n\t\t\t\t\t\thandle.visible = this.axis.search( handle.name ) !== - 1;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\t// If updating helper, skip rest of the loop\n\t\t\t\tcontinue;\n\n\t\t\t}\n\n\t\t\t// Align handles to current local or world rotation\n\n\t\t\thandle.quaternion.copy( quaternion );\n\n\t\t\tif ( this.mode === 'translate' || this.mode === 'scale' ) {\n\n\t\t\t\t// Hide translate and scale axis facing the camera\n\n\t\t\t\tconst AXIS_HIDE_THRESHOLD = 0.99;\n\t\t\t\tconst PLANE_HIDE_THRESHOLD = 0.2;\n\n\t\t\t\tif ( handle.name === 'X' ) {\n\n\t\t\t\t\tif ( Math.abs( _alignVector.copy( _unitX ).applyQuaternion( quaternion ).dot( this.eye ) ) > AXIS_HIDE_THRESHOLD ) {\n\n\t\t\t\t\t\thandle.scale.set( 1e-10, 1e-10, 1e-10 );\n\t\t\t\t\t\thandle.visible = false;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tif ( handle.name === 'Y' ) {\n\n\t\t\t\t\tif ( Math.abs( _alignVector.copy( _unitY ).applyQuaternion( quaternion ).dot( this.eye ) ) > AXIS_HIDE_THRESHOLD ) {\n\n\t\t\t\t\t\thandle.scale.set( 1e-10, 1e-10, 1e-10 );\n\t\t\t\t\t\thandle.visible = false;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tif ( handle.name === 'Z' ) {\n\n\t\t\t\t\tif ( Math.abs( _alignVector.copy( _unitZ ).applyQuaternion( quaternion ).dot( this.eye ) ) > AXIS_HIDE_THRESHOLD ) {\n\n\t\t\t\t\t\thandle.scale.set( 1e-10, 1e-10, 1e-10 );\n\t\t\t\t\t\thandle.visible = false;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tif ( handle.name === 'XY' ) {\n\n\t\t\t\t\tif ( Math.abs( _alignVector.copy( _unitZ ).applyQuaternion( quaternion ).dot( this.eye ) ) < PLANE_HIDE_THRESHOLD ) {\n\n\t\t\t\t\t\thandle.scale.set( 1e-10, 1e-10, 1e-10 );\n\t\t\t\t\t\thandle.visible = false;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tif ( handle.name === 'YZ' ) {\n\n\t\t\t\t\tif ( Math.abs( _alignVector.copy( _unitX ).applyQuaternion( quaternion ).dot( this.eye ) ) < PLANE_HIDE_THRESHOLD ) {\n\n\t\t\t\t\t\thandle.scale.set( 1e-10, 1e-10, 1e-10 );\n\t\t\t\t\t\thandle.visible = false;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t\tif ( handle.name === 'XZ' ) {\n\n\t\t\t\t\tif ( Math.abs( _alignVector.copy( _unitY ).applyQuaternion( quaternion ).dot( this.eye ) ) < PLANE_HIDE_THRESHOLD ) {\n\n\t\t\t\t\t\thandle.scale.set( 1e-10, 1e-10, 1e-10 );\n\t\t\t\t\t\thandle.visible = false;\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t} else if ( this.mode === 'rotate' ) {\n\n\t\t\t\t// Align handles to current local or world rotation\n\n\t\t\t\t_tempQuaternion2.copy( quaternion );\n\t\t\t\t_alignVector.copy( this.eye ).applyQuaternion( _tempQuaternion.copy( quaternion ).invert() );\n\n\t\t\t\tif ( handle.name.search( 'E' ) !== - 1 ) {\n\n\t\t\t\t\thandle.quaternion.setFromRotationMatrix( _lookAtMatrix.lookAt( this.eye, _zeroVector, _unitY ) );\n\n\t\t\t\t}\n\n\t\t\t\tif ( handle.name === 'X' ) {\n\n\t\t\t\t\t_tempQuaternion.setFromAxisAngle( _unitX, Math.atan2( - _alignVector.y, _alignVector.z ) );\n\t\t\t\t\t_tempQuaternion.multiplyQuaternions( _tempQuaternion2, _tempQuaternion );\n\t\t\t\t\thandle.quaternion.copy( _tempQuaternion );\n\n\t\t\t\t}\n\n\t\t\t\tif ( handle.name === 'Y' ) {\n\n\t\t\t\t\t_tempQuaternion.setFromAxisAngle( _unitY, Math.atan2( _alignVector.x, _alignVector.z ) );\n\t\t\t\t\t_tempQuaternion.multiplyQuaternions( _tempQuaternion2, _tempQuaternion );\n\t\t\t\t\thandle.quaternion.copy( _tempQuaternion );\n\n\t\t\t\t}\n\n\t\t\t\tif ( handle.name === 'Z' ) {\n\n\t\t\t\t\t_tempQuaternion.setFromAxisAngle( _unitZ, Math.atan2( _alignVector.y, _alignVector.x ) );\n\t\t\t\t\t_tempQuaternion.multiplyQuaternions( _tempQuaternion2, _tempQuaternion );\n\t\t\t\t\thandle.quaternion.copy( _tempQuaternion );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// Hide disabled axes\n\t\t\thandle.visible = handle.visible && ( handle.name.indexOf( 'X' ) === - 1 || this.showX );\n\t\t\thandle.visible = handle.visible && ( handle.name.indexOf( 'Y' ) === - 1 || this.showY );\n\t\t\thandle.visible = handle.visible && ( handle.name.indexOf( 'Z' ) === - 1 || this.showZ );\n\t\t\thandle.visible = handle.visible && ( handle.name.indexOf( 'E' ) === - 1 || ( this.showX && this.showY && this.showZ ) );\n\n\t\t\t// highlight selected axis\n\n\t\t\thandle.material._color = handle.material._color || handle.material.color.clone();\n\t\t\thandle.material._opacity = handle.material._opacity || handle.material.opacity;\n\n\t\t\thandle.material.color.copy( handle.material._color );\n\t\t\thandle.material.opacity = handle.material._opacity;\n\n\t\t\tif ( this.enabled && this.axis ) {\n\n\t\t\t\tif ( handle.name === this.axis ) {\n\n\t\t\t\t\thandle.material.color.setHex( 0xffff00 );\n\t\t\t\t\thandle.material.opacity = 1.0;\n\n\t\t\t\t} else if ( this.axis.split( '' ).some( function ( a ) {\n\n\t\t\t\t\treturn handle.name === a;\n\n\t\t\t\t} ) ) {\n\n\t\t\t\t\thandle.material.color.setHex( 0xffff00 );\n\t\t\t\t\thandle.material.opacity = 1.0;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tsuper.updateMatrixWorld( force );\n\n\t}\n\n}\n\n//\n\nclass TransformControlsPlane extends Mesh {\n\n\tconstructor() {\n\n\t\tsuper(\n\t\t\tnew PlaneGeometry( 100000, 100000, 2, 2 ),\n\t\t\tnew MeshBasicMaterial( { visible: false, wireframe: true, side: DoubleSide, transparent: true, opacity: 0.1, toneMapped: false } )\n\t\t);\n\n\t\tthis.isTransformControlsPlane = true;\n\n\t\tthis.type = 'TransformControlsPlane';\n\n\t}\n\n\tupdateMatrixWorld( force ) {\n\n\t\tlet space = this.space;\n\n\t\tthis.position.copy( this.worldPosition );\n\n\t\tif ( this.mode === 'scale' ) space = 'local'; // scale always oriented to local rotation\n\n\t\t_v1.copy( _unitX ).applyQuaternion( space === 'local' ? this.worldQuaternion : _identityQuaternion );\n\t\t_v2.copy( _unitY ).applyQuaternion( space === 'local' ? this.worldQuaternion : _identityQuaternion );\n\t\t_v3.copy( _unitZ ).applyQuaternion( space === 'local' ? this.worldQuaternion : _identityQuaternion );\n\n\t\t// Align the plane for current transform mode, axis and space.\n\n\t\t_alignVector.copy( _v2 );\n\n\t\tswitch ( this.mode ) {\n\n\t\t\tcase 'translate':\n\t\t\tcase 'scale':\n\t\t\t\tswitch ( this.axis ) {\n\n\t\t\t\t\tcase 'X':\n\t\t\t\t\t\t_alignVector.copy( this.eye ).cross( _v1 );\n\t\t\t\t\t\t_dirVector.copy( _v1 ).cross( _alignVector );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'Y':\n\t\t\t\t\t\t_alignVector.copy( this.eye ).cross( _v2 );\n\t\t\t\t\t\t_dirVector.copy( _v2 ).cross( _alignVector );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'Z':\n\t\t\t\t\t\t_alignVector.copy( this.eye ).cross( _v3 );\n\t\t\t\t\t\t_dirVector.copy( _v3 ).cross( _alignVector );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'XY':\n\t\t\t\t\t\t_dirVector.copy( _v3 );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'YZ':\n\t\t\t\t\t\t_dirVector.copy( _v1 );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'XZ':\n\t\t\t\t\t\t_alignVector.copy( _v3 );\n\t\t\t\t\t\t_dirVector.copy( _v2 );\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'XYZ':\n\t\t\t\t\tcase 'E':\n\t\t\t\t\t\t_dirVector.set( 0, 0, 0 );\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\tcase 'rotate':\n\t\t\tdefault:\n\t\t\t\t// special case for rotate\n\t\t\t\t_dirVector.set( 0, 0, 0 );\n\n\t\t}\n\n\t\tif ( _dirVector.length() === 0 ) {\n\n\t\t\t// If in rotate mode, make the plane parallel to camera\n\t\t\tthis.quaternion.copy( this.cameraQuaternion );\n\n\t\t} else {\n\n\t\t\t_tempMatrix.lookAt( _tempVector.set( 0, 0, 0 ), _dirVector, _alignVector );\n\n\t\t\tthis.quaternion.setFromRotationMatrix( _tempMatrix );\n\n\t\t}\n\n\t\tsuper.updateMatrixWorld( force );\n\n\t}\n\n}\n\nexport { TransformControls, TransformControlsGizmo, TransformControlsPlane };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAsBA,IAAM,aAAa,IAAI,UAAU;AAEjC,IAAM,cAAc,IAAI,QAAQ;AAChC,IAAM,eAAe,IAAI,QAAQ;AACjC,IAAM,kBAAkB,IAAI,WAAW;AACvC,IAAM,QAAQ;AAAA,EACb,GAAG,IAAI,QAAS,GAAG,GAAG,CAAE;AAAA,EACxB,GAAG,IAAI,QAAS,GAAG,GAAG,CAAE;AAAA,EACxB,GAAG,IAAI,QAAS,GAAG,GAAG,CAAE;AACzB;AAEA,IAAM,eAAe,EAAE,MAAM,SAAS;AACtC,IAAM,kBAAkB,EAAE,MAAM,YAAY;AAC5C,IAAM,gBAAgB,EAAE,MAAM,WAAW,MAAM,KAAK;AACpD,IAAM,qBAAqB,EAAE,MAAM,eAAe;AAElD,IAAM,oBAAN,cAAgC,SAAS;AAAA,EAExC,YAAa,QAAQ,YAAa;AAEjC,UAAM;AAEN,QAAK,eAAe,QAAY;AAE/B,cAAQ,KAAM,8EAA+E;AAC7F,mBAAa;AAAA,IAEd;AAEA,SAAK,sBAAsB;AAE3B,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,WAAW,MAAM,cAAc;AAEpC,UAAM,SAAS,IAAI,uBAAuB;AAC1C,SAAK,SAAS;AACd,SAAK,IAAK,MAAO;AAEjB,UAAM,SAAS,IAAI,uBAAuB;AAC1C,SAAK,SAAS;AACd,SAAK,IAAK,MAAO;AAEjB,UAAM,QAAQ;AAGd,aAAS,eAAgB,UAAU,cAAe;AAEjD,UAAI,YAAY;AAEhB,aAAO,eAAgB,OAAO,UAAU;AAAA,QAEvC,KAAK,WAAY;AAEhB,iBAAO,cAAc,SAAY,YAAY;AAAA,QAE9C;AAAA,QAEA,KAAK,SAAW,OAAQ;AAEvB,cAAK,cAAc,OAAQ;AAE1B,wBAAY;AACZ,mBAAQ,QAAS,IAAI;AACrB,mBAAQ,QAAS,IAAI;AAErB,kBAAM,cAAe,EAAE,MAAM,WAAW,YAAY,MAAa,CAAE;AACnE,kBAAM,cAAe,YAAa;AAAA,UAEnC;AAAA,QAED;AAAA,MAED,CAAE;AAEF,YAAO,QAAS,IAAI;AACpB,aAAQ,QAAS,IAAI;AACrB,aAAQ,QAAS,IAAI;AAAA,IAEtB;AAMA,mBAAgB,UAAU,MAAO;AACjC,mBAAgB,UAAU,MAAU;AACpC,mBAAgB,WAAW,IAAK;AAChC,mBAAgB,QAAQ,IAAK;AAC7B,mBAAgB,QAAQ,WAAY;AACpC,mBAAgB,mBAAmB,IAAK;AACxC,mBAAgB,gBAAgB,IAAK;AACrC,mBAAgB,aAAa,IAAK;AAClC,mBAAgB,SAAS,OAAQ;AACjC,mBAAgB,QAAQ,CAAE;AAC1B,mBAAgB,YAAY,KAAM;AAClC,mBAAgB,SAAS,IAAK;AAC9B,mBAAgB,SAAS,IAAK;AAC9B,mBAAgB,SAAS,IAAK;AAI9B,UAAM,gBAAgB,IAAI,QAAQ;AAClC,UAAM,qBAAqB,IAAI,QAAQ;AACvC,UAAM,kBAAkB,IAAI,WAAW;AACvC,UAAM,uBAAuB,IAAI,WAAW;AAC5C,UAAM,iBAAiB,IAAI,QAAQ;AACnC,UAAM,mBAAmB,IAAI,WAAW;AACxC,UAAM,aAAa,IAAI,QAAQ;AAC/B,UAAM,WAAW,IAAI,QAAQ;AAC7B,UAAM,eAAe,IAAI,QAAQ;AACjC,UAAM,gBAAgB;AACtB,UAAM,MAAM,IAAI,QAAQ;AAIxB,mBAAgB,iBAAiB,aAAc;AAC/C,mBAAgB,sBAAsB,kBAAmB;AACzD,mBAAgB,mBAAmB,eAAgB;AACnD,mBAAgB,wBAAwB,oBAAqB;AAC7D,mBAAgB,kBAAkB,cAAe;AACjD,mBAAgB,oBAAoB,gBAAiB;AACrD,mBAAgB,cAAc,UAAW;AACzC,mBAAgB,YAAY,QAAS;AACrC,mBAAgB,gBAAgB,YAAa;AAC7C,mBAAgB,iBAAiB,aAAc;AAC/C,mBAAgB,OAAO,GAAI;AAE3B,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,eAAe,IAAI,QAAQ;AAEhC,SAAK,kBAAkB,IAAI,QAAQ;AACnC,SAAK,oBAAoB,IAAI,WAAW;AACxC,SAAK,uBAAuB,IAAI,WAAW;AAC3C,SAAK,eAAe,IAAI,QAAQ;AAEhC,SAAK,mBAAmB,IAAI,QAAQ;AACpC,SAAK,sBAAsB,IAAI,WAAW;AAC1C,SAAK,cAAc,IAAI,QAAQ;AAE/B,SAAK,iBAAiB,IAAI,QAAQ;AAClC,SAAK,mBAAmB,IAAI,WAAW;AACvC,SAAK,cAAc,IAAI,QAAQ;AAE/B,SAAK,cAAc,WAAW,KAAM,IAAK;AACzC,SAAK,iBAAiB,cAAc,KAAM,IAAK;AAC/C,SAAK,kBAAkB,eAAe,KAAM,IAAK;AACjD,SAAK,iBAAiB,cAAc,KAAM,IAAK;AAC/C,SAAK,eAAe,YAAY,KAAM,IAAK;AAE3C,SAAK,WAAW,iBAAkB,eAAe,KAAK,cAAe;AACrE,SAAK,WAAW,iBAAkB,eAAe,KAAK,eAAgB;AACtE,SAAK,WAAW,iBAAkB,aAAa,KAAK,YAAa;AAAA,EAElE;AAAA;AAAA,EAGA,oBAAoB;AAEnB,QAAK,KAAK,WAAW,QAAY;AAEhC,WAAK,OAAO,kBAAkB;AAE9B,UAAK,KAAK,OAAO,WAAW,MAAO;AAElC,gBAAQ,MAAO,8EAA+E;AAAA,MAE/F,OAAO;AAEN,aAAK,OAAO,OAAO,YAAY,UAAW,KAAK,iBAAiB,KAAK,mBAAmB,KAAK,YAAa;AAAA,MAE3G;AAEA,WAAK,OAAO,YAAY,UAAW,KAAK,eAAe,KAAK,iBAAiB,KAAK,WAAY;AAE9F,WAAK,qBAAqB,KAAM,KAAK,iBAAkB,EAAE,OAAO;AAChE,WAAK,oBAAoB,KAAM,KAAK,eAAgB,EAAE,OAAO;AAAA,IAE9D;AAEA,SAAK,OAAO,kBAAkB;AAC9B,SAAK,OAAO,YAAY,UAAW,KAAK,gBAAgB,KAAK,kBAAkB,KAAK,YAAa;AAEjG,QAAK,KAAK,OAAO,sBAAuB;AAEvC,WAAK,OAAO,kBAAmB,KAAK,GAAI,EAAE,OAAO;AAAA,IAElD,OAAO;AAEN,WAAK,IAAI,KAAM,KAAK,cAAe,EAAE,IAAK,KAAK,aAAc,EAAE,UAAU;AAAA,IAE1E;AAEA,UAAM,kBAAmB,IAAK;AAAA,EAE/B;AAAA,EAEA,aAAc,SAAU;AAEvB,QAAK,KAAK,WAAW,UAAa,KAAK,aAAa,KAAO;AAE3D,eAAW,cAAe,SAAS,KAAK,MAAO;AAE/C,UAAM,YAAY,uBAAwB,KAAK,OAAO,OAAQ,KAAK,IAAK,GAAG,UAAW;AAEtF,QAAK,WAAY;AAEhB,WAAK,OAAO,UAAU,OAAO;AAAA,IAE9B,OAAO;AAEN,WAAK,OAAO;AAAA,IAEb;AAAA,EAED;AAAA,EAEA,YAAa,SAAU;AAEtB,QAAK,KAAK,WAAW,UAAa,KAAK,aAAa,QAAQ,QAAQ,WAAW,EAAI;AAEnF,QAAK,KAAK,SAAS,MAAO;AAEzB,iBAAW,cAAe,SAAS,KAAK,MAAO;AAE/C,YAAM,iBAAiB,uBAAwB,KAAK,QAAQ,YAAY,IAAK;AAE7E,UAAK,gBAAiB;AAErB,aAAK,OAAO,kBAAkB;AAC9B,aAAK,OAAO,OAAO,kBAAkB;AAErC,aAAK,eAAe,KAAM,KAAK,OAAO,QAAS;AAC/C,aAAK,iBAAiB,KAAM,KAAK,OAAO,UAAW;AACnD,aAAK,YAAY,KAAM,KAAK,OAAO,KAAM;AAEzC,aAAK,OAAO,YAAY,UAAW,KAAK,oBAAoB,KAAK,sBAAsB,KAAK,gBAAiB;AAE7G,aAAK,WAAW,KAAM,eAAe,KAAM,EAAE,IAAK,KAAK,kBAAmB;AAAA,MAE3E;AAEA,WAAK,WAAW;AAChB,sBAAgB,OAAO,KAAK;AAC5B,WAAK,cAAe,eAAgB;AAAA,IAErC;AAAA,EAED;AAAA,EAEA,YAAa,SAAU;AAEtB,UAAM,OAAO,KAAK;AAClB,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,KAAK;AACpB,QAAI,QAAQ,KAAK;AAEjB,QAAK,SAAS,SAAU;AAEvB,cAAQ;AAAA,IAET,WAAY,SAAS,OAAO,SAAS,UAAU,SAAS,OAAQ;AAE/D,cAAQ;AAAA,IAET;AAEA,QAAK,WAAW,UAAa,SAAS,QAAQ,KAAK,aAAa,SAAS,QAAQ,WAAW,GAAM;AAElG,eAAW,cAAe,SAAS,KAAK,MAAO;AAE/C,UAAM,iBAAiB,uBAAwB,KAAK,QAAQ,YAAY,IAAK;AAE7E,QAAK,CAAE,eAAiB;AAExB,SAAK,SAAS,KAAM,eAAe,KAAM,EAAE,IAAK,KAAK,kBAAmB;AAExE,QAAK,SAAS,aAAc;AAI3B,WAAK,QAAQ,KAAM,KAAK,QAAS,EAAE,IAAK,KAAK,UAAW;AAExD,UAAK,UAAU,WAAW,SAAS,OAAQ;AAE1C,aAAK,QAAQ,gBAAiB,KAAK,mBAAoB;AAAA,MAExD;AAEA,UAAK,KAAK,QAAS,GAAI,MAAM,GAAM,MAAK,QAAQ,IAAI;AACpD,UAAK,KAAK,QAAS,GAAI,MAAM,GAAM,MAAK,QAAQ,IAAI;AACpD,UAAK,KAAK,QAAS,GAAI,MAAM,GAAM,MAAK,QAAQ,IAAI;AAEpD,UAAK,UAAU,WAAW,SAAS,OAAQ;AAE1C,aAAK,QAAQ,gBAAiB,KAAK,gBAAiB,EAAE,OAAQ,KAAK,YAAa;AAAA,MAEjF,OAAO;AAEN,aAAK,QAAQ,gBAAiB,KAAK,oBAAqB,EAAE,OAAQ,KAAK,YAAa;AAAA,MAErF;AAEA,aAAO,SAAS,KAAM,KAAK,OAAQ,EAAE,IAAK,KAAK,cAAe;AAI9D,UAAK,KAAK,iBAAkB;AAE3B,YAAK,UAAU,SAAU;AAExB,iBAAO,SAAS,gBAAiB,gBAAgB,KAAM,KAAK,gBAAiB,EAAE,OAAO,CAAE;AAExF,cAAK,KAAK,OAAQ,GAAI,MAAM,IAAM;AAEjC,mBAAO,SAAS,IAAI,KAAK,MAAO,OAAO,SAAS,IAAI,KAAK,eAAgB,IAAI,KAAK;AAAA,UAEnF;AAEA,cAAK,KAAK,OAAQ,GAAI,MAAM,IAAM;AAEjC,mBAAO,SAAS,IAAI,KAAK,MAAO,OAAO,SAAS,IAAI,KAAK,eAAgB,IAAI,KAAK;AAAA,UAEnF;AAEA,cAAK,KAAK,OAAQ,GAAI,MAAM,IAAM;AAEjC,mBAAO,SAAS,IAAI,KAAK,MAAO,OAAO,SAAS,IAAI,KAAK,eAAgB,IAAI,KAAK;AAAA,UAEnF;AAEA,iBAAO,SAAS,gBAAiB,KAAK,gBAAiB;AAAA,QAExD;AAEA,YAAK,UAAU,SAAU;AAExB,cAAK,OAAO,QAAS;AAEpB,mBAAO,SAAS,IAAK,YAAY,sBAAuB,OAAO,OAAO,WAAY,CAAE;AAAA,UAErF;AAEA,cAAK,KAAK,OAAQ,GAAI,MAAM,IAAM;AAEjC,mBAAO,SAAS,IAAI,KAAK,MAAO,OAAO,SAAS,IAAI,KAAK,eAAgB,IAAI,KAAK;AAAA,UAEnF;AAEA,cAAK,KAAK,OAAQ,GAAI,MAAM,IAAM;AAEjC,mBAAO,SAAS,IAAI,KAAK,MAAO,OAAO,SAAS,IAAI,KAAK,eAAgB,IAAI,KAAK;AAAA,UAEnF;AAEA,cAAK,KAAK,OAAQ,GAAI,MAAM,IAAM;AAEjC,mBAAO,SAAS,IAAI,KAAK,MAAO,OAAO,SAAS,IAAI,KAAK,eAAgB,IAAI,KAAK;AAAA,UAEnF;AAEA,cAAK,OAAO,QAAS;AAEpB,mBAAO,SAAS,IAAK,YAAY,sBAAuB,OAAO,OAAO,WAAY,CAAE;AAAA,UAErF;AAAA,QAED;AAAA,MAED;AAAA,IAED,WAAY,SAAS,SAAU;AAE9B,UAAK,KAAK,OAAQ,KAAM,MAAM,IAAM;AAEnC,YAAI,IAAI,KAAK,SAAS,OAAO,IAAI,KAAK,WAAW,OAAO;AAExD,YAAK,KAAK,SAAS,IAAK,KAAK,UAAW,IAAI,EAAI,MAAK;AAErD,qBAAa,IAAK,GAAG,GAAG,CAAE;AAAA,MAE3B,OAAO;AAEN,oBAAY,KAAM,KAAK,UAAW;AAClC,qBAAa,KAAM,KAAK,QAAS;AAEjC,oBAAY,gBAAiB,KAAK,mBAAoB;AACtD,qBAAa,gBAAiB,KAAK,mBAAoB;AAEvD,qBAAa,OAAQ,WAAY;AAEjC,YAAK,KAAK,OAAQ,GAAI,MAAM,IAAM;AAEjC,uBAAa,IAAI;AAAA,QAElB;AAEA,YAAK,KAAK,OAAQ,GAAI,MAAM,IAAM;AAEjC,uBAAa,IAAI;AAAA,QAElB;AAEA,YAAK,KAAK,OAAQ,GAAI,MAAM,IAAM;AAEjC,uBAAa,IAAI;AAAA,QAElB;AAAA,MAED;AAIA,aAAO,MAAM,KAAM,KAAK,WAAY,EAAE,SAAU,YAAa;AAE7D,UAAK,KAAK,WAAY;AAErB,YAAK,KAAK,OAAQ,GAAI,MAAM,IAAM;AAEjC,iBAAO,MAAM,IAAI,KAAK,MAAO,OAAO,MAAM,IAAI,KAAK,SAAU,IAAI,KAAK,aAAa,KAAK;AAAA,QAEzF;AAEA,YAAK,KAAK,OAAQ,GAAI,MAAM,IAAM;AAEjC,iBAAO,MAAM,IAAI,KAAK,MAAO,OAAO,MAAM,IAAI,KAAK,SAAU,IAAI,KAAK,aAAa,KAAK;AAAA,QAEzF;AAEA,YAAK,KAAK,OAAQ,GAAI,MAAM,IAAM;AAEjC,iBAAO,MAAM,IAAI,KAAK,MAAO,OAAO,MAAM,IAAI,KAAK,SAAU,IAAI,KAAK,aAAa,KAAK;AAAA,QAEzF;AAAA,MAED;AAAA,IAED,WAAY,SAAS,UAAW;AAE/B,WAAK,QAAQ,KAAM,KAAK,QAAS,EAAE,IAAK,KAAK,UAAW;AAExD,YAAM,iBAAiB,KAAK,KAAK,cAAc,WAAY,YAAY,sBAAuB,KAAK,OAAO,WAAY,CAAE;AAExH,UAAK,SAAS,KAAM;AAEnB,aAAK,aAAa,KAAM,KAAK,GAAI;AACjC,aAAK,gBAAgB,KAAK,SAAS,QAAS,KAAK,UAAW;AAE5D,aAAK,WAAW,KAAM,KAAK,UAAW,EAAE,UAAU;AAClD,aAAK,SAAS,KAAM,KAAK,QAAS,EAAE,UAAU;AAE9C,aAAK,iBAAmB,KAAK,SAAS,MAAO,KAAK,UAAW,EAAE,IAAK,KAAK,GAAI,IAAI,IAAI,IAAI;AAAA,MAE1F,WAAY,SAAS,QAAS;AAE7B,aAAK,aAAa,KAAM,KAAK,OAAQ,EAAE,MAAO,KAAK,GAAI,EAAE,UAAU;AACnE,aAAK,gBAAgB,KAAK,QAAQ,IAAK,YAAY,KAAM,KAAK,YAAa,EAAE,MAAO,KAAK,GAAI,CAAE,IAAI;AAAA,MAEpG,WAAY,SAAS,OAAO,SAAS,OAAO,SAAS,KAAM;AAE1D,aAAK,aAAa,KAAM,MAAO,IAAK,CAAE;AAEtC,oBAAY,KAAM,MAAO,IAAK,CAAE;AAEhC,YAAK,UAAU,SAAU;AAExB,sBAAY,gBAAiB,KAAK,eAAgB;AAAA,QAEnD;AAEA,aAAK,gBAAgB,KAAK,QAAQ,IAAK,YAAY,MAAO,KAAK,GAAI,EAAE,UAAU,CAAE,IAAI;AAAA,MAEtF;AAIA,UAAK,KAAK,aAAe,MAAK,gBAAgB,KAAK,MAAO,KAAK,gBAAgB,KAAK,YAAa,IAAI,KAAK;AAG1G,UAAK,UAAU,WAAW,SAAS,OAAO,SAAS,QAAS;AAE3D,eAAO,WAAW,KAAM,KAAK,gBAAiB;AAC9C,eAAO,WAAW,SAAU,gBAAgB,iBAAkB,KAAK,cAAc,KAAK,aAAc,CAAE,EAAE,UAAU;AAAA,MAEnH,OAAO;AAEN,aAAK,aAAa,gBAAiB,KAAK,oBAAqB;AAC7D,eAAO,WAAW,KAAM,gBAAgB,iBAAkB,KAAK,cAAc,KAAK,aAAc,CAAE;AAClG,eAAO,WAAW,SAAU,KAAK,gBAAiB,EAAE,UAAU;AAAA,MAE/D;AAAA,IAED;AAEA,SAAK,cAAe,YAAa;AACjC,SAAK,cAAe,kBAAmB;AAAA,EAExC;AAAA,EAEA,UAAW,SAAU;AAEpB,QAAK,QAAQ,WAAW,EAAI;AAE5B,QAAK,KAAK,YAAc,KAAK,SAAS,MAAS;AAE9C,oBAAc,OAAO,KAAK;AAC1B,WAAK,cAAe,aAAc;AAAA,IAEnC;AAEA,SAAK,WAAW;AAChB,SAAK,OAAO;AAAA,EAEb;AAAA,EAEA,UAAU;AAET,SAAK,WAAW,oBAAqB,eAAe,KAAK,cAAe;AACxE,SAAK,WAAW,oBAAqB,eAAe,KAAK,eAAgB;AACzE,SAAK,WAAW,oBAAqB,eAAe,KAAK,cAAe;AACxE,SAAK,WAAW,oBAAqB,aAAa,KAAK,YAAa;AAEpE,SAAK,SAAU,SAAW,OAAQ;AAEjC,UAAK,MAAM,SAAW,OAAM,SAAS,QAAQ;AAC7C,UAAK,MAAM,SAAW,OAAM,SAAS,QAAQ;AAAA,IAE9C,CAAE;AAAA,EAEH;AAAA;AAAA,EAGA,OAAQ,QAAS;AAEhB,SAAK,SAAS;AACd,SAAK,UAAU;AAEf,WAAO;AAAA,EAER;AAAA;AAAA,EAGA,SAAS;AAER,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,OAAO;AAEZ,WAAO;AAAA,EAER;AAAA,EAEA,QAAQ;AAEP,QAAK,CAAE,KAAK,QAAU;AAEtB,QAAK,KAAK,UAAW;AAEpB,WAAK,OAAO,SAAS,KAAM,KAAK,cAAe;AAC/C,WAAK,OAAO,WAAW,KAAM,KAAK,gBAAiB;AACnD,WAAK,OAAO,MAAM,KAAM,KAAK,WAAY;AAEzC,WAAK,cAAe,YAAa;AACjC,WAAK,cAAe,kBAAmB;AAEvC,WAAK,WAAW,KAAM,KAAK,QAAS;AAAA,IAErC;AAAA,EAED;AAAA,EAEA,eAAe;AAEd,WAAO;AAAA,EAER;AAAA;AAAA,EAIA,UAAU;AAET,WAAO,KAAK;AAAA,EAEb;AAAA,EAEA,QAAS,MAAO;AAEf,SAAK,OAAO;AAAA,EAEb;AAAA,EAEA,mBAAoB,iBAAkB;AAErC,SAAK,kBAAkB;AAAA,EAExB;AAAA,EAEA,gBAAiB,cAAe;AAE/B,SAAK,eAAe;AAAA,EAErB;AAAA,EAEA,aAAc,WAAY;AAEzB,SAAK,YAAY;AAAA,EAElB;AAAA,EAEA,QAAS,MAAO;AAEf,SAAK,OAAO;AAAA,EAEb;AAAA,EAEA,SAAU,OAAQ;AAEjB,SAAK,QAAQ;AAAA,EAEd;AAED;AAIA,SAAS,WAAY,OAAQ;AAE5B,MAAK,KAAK,WAAW,cAAc,oBAAqB;AAEvD,WAAO;AAAA,MACN,GAAG;AAAA,MACH,GAAG;AAAA,MACH,QAAQ,MAAM;AAAA,IACf;AAAA,EAED,OAAO;AAEN,UAAM,OAAO,KAAK,WAAW,sBAAsB;AAEnD,WAAO;AAAA,MACN,IAAK,MAAM,UAAU,KAAK,QAAS,KAAK,QAAQ,IAAI;AAAA,MACpD,GAAG,EAAI,MAAM,UAAU,KAAK,OAAQ,KAAK,SAAS,IAAI;AAAA,MACtD,QAAQ,MAAM;AAAA,IACf;AAAA,EAED;AAED;AAEA,SAAS,eAAgB,OAAQ;AAEhC,MAAK,CAAE,KAAK,QAAU;AAEtB,UAAS,MAAM,aAAc;AAAA,IAE5B,KAAK;AAAA,IACL,KAAK;AACJ,WAAK,aAAc,KAAK,YAAa,KAAM,CAAE;AAC7C;AAAA,EAEF;AAED;AAEA,SAAS,cAAe,OAAQ;AAE/B,MAAK,CAAE,KAAK,QAAU;AAEtB,MAAK,CAAE,SAAS,oBAAqB;AAEpC,SAAK,WAAW,kBAAmB,MAAM,SAAU;AAAA,EAEpD;AAEA,OAAK,WAAW,iBAAkB,eAAe,KAAK,cAAe;AAErE,OAAK,aAAc,KAAK,YAAa,KAAM,CAAE;AAC7C,OAAK,YAAa,KAAK,YAAa,KAAM,CAAE;AAE7C;AAEA,SAAS,cAAe,OAAQ;AAE/B,MAAK,CAAE,KAAK,QAAU;AAEtB,OAAK,YAAa,KAAK,YAAa,KAAM,CAAE;AAE7C;AAEA,SAAS,YAAa,OAAQ;AAE7B,MAAK,CAAE,KAAK,QAAU;AAEtB,OAAK,WAAW,sBAAuB,MAAM,SAAU;AAEvD,OAAK,WAAW,oBAAqB,eAAe,KAAK,cAAe;AAExE,OAAK,UAAW,KAAK,YAAa,KAAM,CAAE;AAE3C;AAEA,SAAS,uBAAwB,QAAQ,WAAW,kBAAmB;AAEtE,QAAM,mBAAmB,UAAU,gBAAiB,QAAQ,IAAK;AAEjE,WAAU,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAO;AAEpD,QAAK,iBAAkB,CAAE,EAAE,OAAO,WAAW,kBAAmB;AAE/D,aAAO,iBAAkB,CAAE;AAAA,IAE5B;AAAA,EAED;AAEA,SAAO;AAER;AAMA,IAAM,aAAa,IAAI,MAAM;AAC7B,IAAM,eAAe,IAAI,QAAS,GAAG,GAAG,CAAE;AAC1C,IAAM,cAAc,IAAI,QAAS,GAAG,GAAG,CAAE;AACzC,IAAM,gBAAgB,IAAI,QAAQ;AAClC,IAAM,mBAAmB,IAAI,WAAW;AACxC,IAAM,sBAAsB,IAAI,WAAW;AAC3C,IAAM,aAAa,IAAI,QAAQ;AAC/B,IAAM,cAAc,IAAI,QAAQ;AAEhC,IAAM,SAAS,IAAI,QAAS,GAAG,GAAG,CAAE;AACpC,IAAM,SAAS,IAAI,QAAS,GAAG,GAAG,CAAE;AACpC,IAAM,SAAS,IAAI,QAAS,GAAG,GAAG,CAAE;AAEpC,IAAM,MAAM,IAAI,QAAQ;AACxB,IAAM,MAAM,IAAI,QAAQ;AACxB,IAAM,MAAM,IAAI,QAAQ;AAExB,IAAM,yBAAN,cAAqC,SAAS;AAAA,EAE7C,cAAc;AAEb,UAAM;AAEN,SAAK,2BAA2B;AAEhC,SAAK,OAAO;AAIZ,UAAM,gBAAgB,IAAI,kBAAmB;AAAA,MAC5C,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,KAAK;AAAA,MACL,YAAY;AAAA,MACZ,aAAa;AAAA,IACd,CAAE;AAEF,UAAM,oBAAoB,IAAI,kBAAmB;AAAA,MAChD,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,KAAK;AAAA,MACL,YAAY;AAAA,MACZ,aAAa;AAAA,IACd,CAAE;AAIF,UAAM,eAAe,cAAc,MAAM;AACzC,iBAAa,UAAU;AAEvB,UAAM,YAAY,kBAAkB,MAAM;AAC1C,cAAU,UAAU;AAEpB,UAAM,SAAS,cAAc,MAAM;AACnC,WAAO,MAAM,OAAQ,QAAS;AAE9B,UAAM,WAAW,cAAc,MAAM;AACrC,aAAS,MAAM,OAAQ,KAAS;AAEhC,UAAM,UAAU,cAAc,MAAM;AACpC,YAAQ,MAAM,OAAQ,GAAS;AAE/B,UAAM,oBAAoB,cAAc,MAAM;AAC9C,sBAAkB,MAAM,OAAQ,QAAS;AACzC,sBAAkB,UAAU;AAE5B,UAAM,sBAAsB,cAAc,MAAM;AAChD,wBAAoB,MAAM,OAAQ,KAAS;AAC3C,wBAAoB,UAAU;AAE9B,UAAM,qBAAqB,cAAc,MAAM;AAC/C,uBAAmB,MAAM,OAAQ,GAAS;AAC1C,uBAAmB,UAAU;AAE7B,UAAM,sBAAsB,cAAc,MAAM;AAChD,wBAAoB,UAAU;AAE9B,UAAM,uBAAuB,cAAc,MAAM;AACjD,yBAAqB,MAAM,OAAQ,QAAS;AAC5C,yBAAqB,UAAU;AAE/B,UAAM,YAAY,cAAc,MAAM;AACtC,cAAU,MAAM,OAAQ,QAAS;AAEjC,UAAM,UAAU,cAAc,MAAM;AACpC,YAAQ,MAAM,OAAQ,OAAS;AAI/B,UAAM,gBAAgB,IAAI,iBAAkB,GAAG,MAAM,KAAK,EAAG;AAC7D,kBAAc,UAAW,GAAG,MAAM,CAAE;AAEpC,UAAM,sBAAsB,IAAI,YAAa,MAAM,MAAM,IAAK;AAC9D,wBAAoB,UAAW,GAAG,MAAM,CAAE;AAE1C,UAAM,eAAe,IAAI,eAAe;AACxC,iBAAa,aAAc,YAAY,IAAI,uBAAwB,CAAE,GAAG,GAAG,GAAG,GAAG,GAAG,CAAE,GAAG,CAAE,CAAE;AAE7F,UAAM,gBAAgB,IAAI,iBAAkB,OAAQ,OAAQ,KAAK,CAAE;AACnE,kBAAc,UAAW,GAAG,MAAM,CAAE;AAEpC,aAAS,eAAgB,QAAQ,KAAM;AAEtC,YAAM,WAAW,IAAI,cAAe,QAAQ,OAAQ,GAAG,IAAI,MAAM,KAAK,KAAK,CAAE;AAC7E,eAAS,QAAS,KAAK,KAAK,CAAE;AAC9B,eAAS,QAAS,KAAK,KAAK,CAAE;AAC9B,aAAO;AAAA,IAER;AAIA,aAAS,0BAA0B;AAElC,YAAM,WAAW,IAAI,eAAe;AAEpC,eAAS,aAAc,YAAY,IAAI,uBAAwB,CAAE,GAAG,GAAG,GAAG,GAAG,GAAG,CAAE,GAAG,CAAE,CAAE;AAEzF,aAAO;AAAA,IAER;AAIA,UAAM,iBAAiB;AAAA,MACtB,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,eAAe,MAAO,GAAG,CAAE,KAAK,GAAG,CAAE,GAAG,CAAE,GAAG,GAAG,CAAE,KAAK,KAAK,CAAE,CAAC;AAAA,QAC3E,CAAE,IAAI,KAAM,eAAe,MAAO,GAAG,CAAE,MAAO,GAAG,CAAE,GAAG,CAAE,GAAG,GAAG,KAAK,KAAK,CAAE,CAAC;AAAA,QAC3E,CAAE,IAAI,KAAM,eAAe,MAAO,GAAG,CAAE,GAAG,GAAG,CAAE,GAAG,CAAE,GAAG,GAAG,CAAE,KAAK,KAAK,CAAE,CAAC;AAAA,MAC1E;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,eAAe,QAAS,GAAG,CAAE,GAAG,KAAK,CAAE,CAAC;AAAA,QACpD,CAAE,IAAI,KAAM,eAAe,QAAS,GAAG,CAAE,GAAG,MAAO,CAAE,GAAG,CAAE,KAAK,IAAI,GAAG,CAAE,CAAC;AAAA,QACzE,CAAE,IAAI,KAAM,eAAe,QAAS,CAAE;AAAA,MACvC;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,eAAe,OAAQ,GAAG,CAAE,GAAG,GAAG,GAAI,GAAG,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,QAC1E,CAAE,IAAI,KAAM,eAAe,OAAQ,GAAG,CAAE,GAAG,GAAG,IAAM,GAAG,CAAE,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,QAC9E,CAAE,IAAI,KAAM,eAAe,OAAQ,GAAG,MAAM,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,MAClE;AAAA,MACA,KAAK;AAAA,QACJ,CAAE,IAAI,KAAM,IAAI,mBAAoB,KAAK,CAAE,GAAG,oBAAoB,MAAM,CAAE,GAAG,CAAE,GAAG,GAAG,CAAE,CAAC;AAAA,MACzF;AAAA,MACA,IAAI;AAAA,QACH,CAAE,IAAI,KAAM,IAAI,YAAa,MAAM,MAAM,IAAK,GAAG,mBAAmB,MAAM,CAAE,GAAG,CAAE,MAAM,MAAM,CAAE,CAAC;AAAA,MACjG;AAAA,MACA,IAAI;AAAA,QACH,CAAE,IAAI,KAAM,IAAI,YAAa,MAAM,MAAM,IAAK,GAAG,kBAAkB,MAAM,CAAE,GAAG,CAAE,GAAG,MAAM,IAAK,GAAG,CAAE,GAAG,KAAK,KAAK,GAAG,CAAE,CAAC;AAAA,MACvH;AAAA,MACA,IAAI;AAAA,QACH,CAAE,IAAI,KAAM,IAAI,YAAa,MAAM,MAAM,IAAK,GAAG,oBAAoB,MAAM,CAAE,GAAG,CAAE,MAAM,GAAG,IAAK,GAAG,CAAE,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,MAC3H;AAAA,IACD;AAEA,UAAM,kBAAkB;AAAA,MACvB,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,IAAI,iBAAkB,KAAK,GAAG,KAAK,CAAE,GAAG,YAAa,GAAG,CAAE,KAAK,GAAG,CAAE,GAAG,CAAE,GAAG,GAAG,CAAE,KAAK,KAAK,CAAE,CAAC;AAAA,QAC1G,CAAE,IAAI,KAAM,IAAI,iBAAkB,KAAK,GAAG,KAAK,CAAE,GAAG,YAAa,GAAG,CAAE,MAAO,GAAG,CAAE,GAAG,CAAE,GAAG,GAAG,KAAK,KAAK,CAAE,CAAC;AAAA,MAC3G;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,IAAI,iBAAkB,KAAK,GAAG,KAAK,CAAE,GAAG,YAAa,GAAG,CAAE,GAAG,KAAK,CAAE,CAAC;AAAA,QACjF,CAAE,IAAI,KAAM,IAAI,iBAAkB,KAAK,GAAG,KAAK,CAAE,GAAG,YAAa,GAAG,CAAE,GAAG,MAAO,CAAE,GAAG,CAAE,GAAG,GAAG,KAAK,EAAG,CAAC;AAAA,MACvG;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,IAAI,iBAAkB,KAAK,GAAG,KAAK,CAAE,GAAG,YAAa,GAAG,CAAE,GAAG,GAAG,GAAI,GAAG,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,QACxG,CAAE,IAAI,KAAM,IAAI,iBAAkB,KAAK,GAAG,KAAK,CAAE,GAAG,YAAa,GAAG,CAAE,GAAG,GAAG,IAAM,GAAG,CAAE,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,MAC7G;AAAA,MACA,KAAK;AAAA,QACJ,CAAE,IAAI,KAAM,IAAI,mBAAoB,KAAK,CAAE,GAAG,YAAa,CAAE;AAAA,MAC9D;AAAA,MACA,IAAI;AAAA,QACH,CAAE,IAAI,KAAM,IAAI,YAAa,KAAK,KAAK,IAAK,GAAG,YAAa,GAAG,CAAE,MAAM,MAAM,CAAE,CAAC;AAAA,MACjF;AAAA,MACA,IAAI;AAAA,QACH,CAAE,IAAI,KAAM,IAAI,YAAa,KAAK,KAAK,IAAK,GAAG,YAAa,GAAG,CAAE,GAAG,MAAM,IAAK,GAAG,CAAE,GAAG,KAAK,KAAK,GAAG,CAAE,CAAC;AAAA,MACxG;AAAA,MACA,IAAI;AAAA,QACH,CAAE,IAAI,KAAM,IAAI,YAAa,KAAK,KAAK,IAAK,GAAG,YAAa,GAAG,CAAE,MAAM,GAAG,IAAK,GAAG,CAAE,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,MAC1G;AAAA,IACD;AAEA,UAAM,kBAAkB;AAAA,MACvB,OAAO;AAAA,QACN,CAAE,IAAI,KAAM,IAAI,mBAAoB,MAAM,CAAE,GAAG,SAAU,GAAG,MAAM,MAAM,MAAM,QAAS;AAAA,MACxF;AAAA,MACA,KAAK;AAAA,QACJ,CAAE,IAAI,KAAM,IAAI,mBAAoB,MAAM,CAAE,GAAG,SAAU,GAAG,MAAM,MAAM,MAAM,QAAS;AAAA,MACxF;AAAA,MACA,OAAO;AAAA,QACN,CAAE,IAAI,KAAM,wBAAwB,GAAG,SAAU,GAAG,MAAM,MAAM,MAAM,QAAS;AAAA,MAChF;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,cAAc,UAAU,MAAM,CAAE,GAAG,CAAE,MAAO,GAAG,CAAE,GAAG,MAAM,CAAE,KAAK,GAAG,CAAE,GAAG,QAAS;AAAA,MAC/F;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,cAAc,UAAU,MAAM,CAAE,GAAG,CAAE,GAAG,MAAO,CAAE,GAAG,CAAE,GAAG,GAAG,KAAK,KAAK,CAAE,GAAG,CAAE,KAAK,GAAG,CAAE,GAAG,QAAS;AAAA,MAChH;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,cAAc,UAAU,MAAM,CAAE,GAAG,CAAE,GAAG,GAAG,IAAM,GAAG,CAAE,GAAG,CAAE,KAAK,KAAK,GAAG,CAAE,GAAG,CAAE,KAAK,GAAG,CAAE,GAAG,QAAS;AAAA,MAClH;AAAA,IACD;AAEA,UAAM,cAAc;AAAA,MACnB,MAAM;AAAA,QACL,CAAE,IAAI,KAAM,eAAgB,KAAK,CAAE,GAAG,OAAQ,GAAG,MAAM,CAAE,GAAG,KAAK,KAAK,GAAG,CAAE,CAAC;AAAA,MAC7E;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,eAAgB,KAAK,GAAI,GAAG,MAAO,CAAE;AAAA,MAClD;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,eAAgB,KAAK,GAAI,GAAG,QAAS,GAAG,MAAM,CAAE,GAAG,GAAG,CAAE,KAAK,KAAK,CAAE,CAAC;AAAA,MAClF;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,eAAgB,KAAK,GAAI,GAAG,OAAQ,GAAG,MAAM,CAAE,GAAG,KAAK,KAAK,GAAG,CAAE,CAAC;AAAA,MAC/E;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,eAAgB,MAAM,CAAE,GAAG,oBAAqB,GAAG,MAAM,CAAE,GAAG,KAAK,KAAK,GAAG,CAAE,CAAC;AAAA,MAC3F;AAAA,IACD;AAEA,UAAM,eAAe;AAAA,MACpB,MAAM;AAAA,QACL,CAAE,IAAI,KAAM,cAAc,UAAU,MAAM,CAAE,GAAG,CAAE,MAAO,GAAG,CAAE,GAAG,MAAM,CAAE,KAAK,GAAG,CAAE,GAAG,QAAS;AAAA,MAC/F;AAAA,IACD;AAEA,UAAM,eAAe;AAAA,MACpB,MAAM;AAAA,QACL,CAAE,IAAI,KAAM,IAAI,eAAgB,MAAM,IAAI,CAAE,GAAG,YAAa,CAAE;AAAA,MAC/D;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,IAAI,cAAe,KAAK,KAAK,GAAG,EAAG,GAAG,YAAa,GAAG,CAAE,GAAG,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,KAAK,KAAK,GAAG,CAAE,KAAK,KAAK,CAAE,CAAC;AAAA,MACnH;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,IAAI,cAAe,KAAK,KAAK,GAAG,EAAG,GAAG,YAAa,GAAG,CAAE,GAAG,GAAG,CAAE,GAAG,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,MACrG;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,IAAI,cAAe,KAAK,KAAK,GAAG,EAAG,GAAG,YAAa,GAAG,CAAE,GAAG,GAAG,CAAE,GAAG,CAAE,GAAG,GAAG,CAAE,KAAK,KAAK,CAAE,CAAC;AAAA,MACvG;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,IAAI,cAAe,MAAM,KAAK,GAAG,EAAG,GAAG,YAAa,CAAE;AAAA,MACnE;AAAA,IACD;AAEA,UAAM,aAAa;AAAA,MAClB,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,qBAAqB,MAAO,GAAG,CAAE,KAAK,GAAG,CAAE,GAAG,CAAE,GAAG,GAAG,CAAE,KAAK,KAAK,CAAE,CAAC;AAAA,QACjF,CAAE,IAAI,KAAM,eAAe,MAAO,GAAG,CAAE,GAAG,GAAG,CAAE,GAAG,CAAE,GAAG,GAAG,CAAE,KAAK,KAAK,CAAE,CAAC;AAAA,QACzE,CAAE,IAAI,KAAM,qBAAqB,MAAO,GAAG,CAAE,MAAO,GAAG,CAAE,GAAG,CAAE,GAAG,GAAG,KAAK,KAAK,CAAE,CAAC;AAAA,MAClF;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,qBAAqB,QAAS,GAAG,CAAE,GAAG,KAAK,CAAE,CAAC;AAAA,QAC1D,CAAE,IAAI,KAAM,eAAe,QAAS,CAAE;AAAA,QACtC,CAAE,IAAI,KAAM,qBAAqB,QAAS,GAAG,CAAE,GAAG,MAAO,CAAE,GAAG,CAAE,GAAG,GAAG,KAAK,EAAG,CAAC;AAAA,MAChF;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,qBAAqB,OAAQ,GAAG,CAAE,GAAG,GAAG,GAAI,GAAG,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,QAChF,CAAE,IAAI,KAAM,eAAe,OAAQ,GAAG,CAAE,GAAG,GAAG,CAAE,GAAG,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,QACxE,CAAE,IAAI,KAAM,qBAAqB,OAAQ,GAAG,CAAE,GAAG,GAAG,IAAM,GAAG,CAAE,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,MACrF;AAAA,MACA,IAAI;AAAA,QACH,CAAE,IAAI,KAAM,IAAI,YAAa,MAAM,MAAM,IAAK,GAAG,kBAAmB,GAAG,CAAE,MAAM,MAAM,CAAE,CAAC;AAAA,MACzF;AAAA,MACA,IAAI;AAAA,QACH,CAAE,IAAI,KAAM,IAAI,YAAa,MAAM,MAAM,IAAK,GAAG,iBAAkB,GAAG,CAAE,GAAG,MAAM,IAAK,GAAG,CAAE,GAAG,KAAK,KAAK,GAAG,CAAE,CAAC;AAAA,MAC/G;AAAA,MACA,IAAI;AAAA,QACH,CAAE,IAAI,KAAM,IAAI,YAAa,MAAM,MAAM,IAAK,GAAG,mBAAoB,GAAG,CAAE,MAAM,GAAG,IAAK,GAAG,CAAE,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,MACnH;AAAA,MACA,KAAK;AAAA,QACJ,CAAE,IAAI,KAAM,IAAI,YAAa,KAAK,KAAK,GAAI,GAAG,oBAAoB,MAAM,CAAE,CAAE;AAAA,MAC7E;AAAA,IACD;AAEA,UAAM,cAAc;AAAA,MACnB,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,IAAI,iBAAkB,KAAK,GAAG,KAAK,CAAE,GAAG,YAAa,GAAG,CAAE,KAAK,GAAG,CAAE,GAAG,CAAE,GAAG,GAAG,CAAE,KAAK,KAAK,CAAE,CAAC;AAAA,QAC1G,CAAE,IAAI,KAAM,IAAI,iBAAkB,KAAK,GAAG,KAAK,CAAE,GAAG,YAAa,GAAG,CAAE,MAAO,GAAG,CAAE,GAAG,CAAE,GAAG,GAAG,KAAK,KAAK,CAAE,CAAC;AAAA,MAC3G;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,IAAI,iBAAkB,KAAK,GAAG,KAAK,CAAE,GAAG,YAAa,GAAG,CAAE,GAAG,KAAK,CAAE,CAAC;AAAA,QACjF,CAAE,IAAI,KAAM,IAAI,iBAAkB,KAAK,GAAG,KAAK,CAAE,GAAG,YAAa,GAAG,CAAE,GAAG,MAAO,CAAE,GAAG,CAAE,GAAG,GAAG,KAAK,EAAG,CAAC;AAAA,MACvG;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,IAAI,iBAAkB,KAAK,GAAG,KAAK,CAAE,GAAG,YAAa,GAAG,CAAE,GAAG,GAAG,GAAI,GAAG,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,QACxG,CAAE,IAAI,KAAM,IAAI,iBAAkB,KAAK,GAAG,KAAK,CAAE,GAAG,YAAa,GAAG,CAAE,GAAG,GAAG,IAAM,GAAG,CAAE,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,MAC7G;AAAA,MACA,IAAI;AAAA,QACH,CAAE,IAAI,KAAM,IAAI,YAAa,KAAK,KAAK,IAAK,GAAG,YAAa,GAAG,CAAE,MAAM,MAAM,CAAE,CAAC;AAAA,MACjF;AAAA,MACA,IAAI;AAAA,QACH,CAAE,IAAI,KAAM,IAAI,YAAa,KAAK,KAAK,IAAK,GAAG,YAAa,GAAG,CAAE,GAAG,MAAM,IAAK,GAAG,CAAE,GAAG,KAAK,KAAK,GAAG,CAAE,CAAC;AAAA,MACxG;AAAA,MACA,IAAI;AAAA,QACH,CAAE,IAAI,KAAM,IAAI,YAAa,KAAK,KAAK,IAAK,GAAG,YAAa,GAAG,CAAE,MAAM,GAAG,IAAK,GAAG,CAAE,CAAE,KAAK,KAAK,GAAG,GAAG,CAAE,CAAC;AAAA,MAC1G;AAAA,MACA,KAAK;AAAA,QACJ,CAAE,IAAI,KAAM,IAAI,YAAa,KAAK,KAAK,GAAI,GAAG,YAAa,GAAG,CAAE,GAAG,GAAG,CAAE,CAAC;AAAA,MAC1E;AAAA,IACD;AAEA,UAAM,cAAc;AAAA,MACnB,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,cAAc,UAAU,MAAM,CAAE,GAAG,CAAE,MAAO,GAAG,CAAE,GAAG,MAAM,CAAE,KAAK,GAAG,CAAE,GAAG,QAAS;AAAA,MAC/F;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,cAAc,UAAU,MAAM,CAAE,GAAG,CAAE,GAAG,MAAO,CAAE,GAAG,CAAE,GAAG,GAAG,KAAK,KAAK,CAAE,GAAG,CAAE,KAAK,GAAG,CAAE,GAAG,QAAS;AAAA,MAChH;AAAA,MACA,GAAG;AAAA,QACF,CAAE,IAAI,KAAM,cAAc,UAAU,MAAM,CAAE,GAAG,CAAE,GAAG,GAAG,IAAM,GAAG,CAAE,GAAG,CAAE,KAAK,KAAK,GAAG,CAAE,GAAG,CAAE,KAAK,GAAG,CAAE,GAAG,QAAS;AAAA,MAClH;AAAA,IACD;AAIA,aAAS,WAAY,UAAW;AAE/B,YAAM,QAAQ,IAAI,SAAS;AAE3B,iBAAY,QAAQ,UAAW;AAE9B,iBAAU,IAAI,SAAU,IAAK,EAAE,QAAQ,OAAQ;AAE9C,gBAAM,SAAS,SAAU,IAAK,EAAG,CAAE,EAAG,CAAE,EAAE,MAAM;AAChD,gBAAM,WAAW,SAAU,IAAK,EAAG,CAAE,EAAG,CAAE;AAC1C,gBAAM,WAAW,SAAU,IAAK,EAAG,CAAE,EAAG,CAAE;AAC1C,gBAAM,QAAQ,SAAU,IAAK,EAAG,CAAE,EAAG,CAAE;AACvC,gBAAM,MAAM,SAAU,IAAK,EAAG,CAAE,EAAG,CAAE;AAGrC,iBAAO,OAAO;AACd,iBAAO,MAAM;AAEb,cAAK,UAAW;AAEf,mBAAO,SAAS,IAAK,SAAU,CAAE,GAAG,SAAU,CAAE,GAAG,SAAU,CAAE,CAAE;AAAA,UAElE;AAEA,cAAK,UAAW;AAEf,mBAAO,SAAS,IAAK,SAAU,CAAE,GAAG,SAAU,CAAE,GAAG,SAAU,CAAE,CAAE;AAAA,UAElE;AAEA,cAAK,OAAQ;AAEZ,mBAAO,MAAM,IAAK,MAAO,CAAE,GAAG,MAAO,CAAE,GAAG,MAAO,CAAE,CAAE;AAAA,UAEtD;AAEA,iBAAO,aAAa;AAEpB,gBAAM,eAAe,OAAO,SAAS,MAAM;AAC3C,uBAAa,aAAc,OAAO,MAAO;AACzC,iBAAO,WAAW;AAClB,iBAAO,cAAc;AAErB,iBAAO,SAAS,IAAK,GAAG,GAAG,CAAE;AAC7B,iBAAO,SAAS,IAAK,GAAG,GAAG,CAAE;AAC7B,iBAAO,MAAM,IAAK,GAAG,GAAG,CAAE;AAE1B,gBAAM,IAAK,MAAO;AAAA,QAEnB;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAIA,SAAK,QAAQ,CAAC;AACd,SAAK,SAAS,CAAC;AACf,SAAK,SAAS,CAAC;AAEf,SAAK,IAAK,KAAK,MAAO,WAAY,IAAI,WAAY,cAAe,CAAE;AACnE,SAAK,IAAK,KAAK,MAAO,QAAS,IAAI,WAAY,WAAY,CAAE;AAC7D,SAAK,IAAK,KAAK,MAAO,OAAQ,IAAI,WAAY,UAAW,CAAE;AAC3D,SAAK,IAAK,KAAK,OAAQ,WAAY,IAAI,WAAY,eAAgB,CAAE;AACrE,SAAK,IAAK,KAAK,OAAQ,QAAS,IAAI,WAAY,YAAa,CAAE;AAC/D,SAAK,IAAK,KAAK,OAAQ,OAAQ,IAAI,WAAY,WAAY,CAAE;AAC7D,SAAK,IAAK,KAAK,OAAQ,WAAY,IAAI,WAAY,eAAgB,CAAE;AACrE,SAAK,IAAK,KAAK,OAAQ,QAAS,IAAI,WAAY,YAAa,CAAE;AAC/D,SAAK,IAAK,KAAK,OAAQ,OAAQ,IAAI,WAAY,WAAY,CAAE;AAI7D,SAAK,OAAQ,WAAY,EAAE,UAAU;AACrC,SAAK,OAAQ,QAAS,EAAE,UAAU;AAClC,SAAK,OAAQ,OAAQ,EAAE,UAAU;AAAA,EAElC;AAAA;AAAA,EAIA,kBAAmB,OAAQ;AAE1B,UAAM,QAAU,KAAK,SAAS,UAAY,UAAU,KAAK;AAEzD,UAAM,aAAe,UAAU,UAAY,KAAK,kBAAkB;AAIlE,SAAK,MAAO,WAAY,EAAE,UAAU,KAAK,SAAS;AAClD,SAAK,MAAO,QAAS,EAAE,UAAU,KAAK,SAAS;AAC/C,SAAK,MAAO,OAAQ,EAAE,UAAU,KAAK,SAAS;AAE9C,SAAK,OAAQ,WAAY,EAAE,UAAU,KAAK,SAAS;AACnD,SAAK,OAAQ,QAAS,EAAE,UAAU,KAAK,SAAS;AAChD,SAAK,OAAQ,OAAQ,EAAE,UAAU,KAAK,SAAS;AAG/C,QAAI,UAAU,CAAC;AACf,cAAU,QAAQ,OAAQ,KAAK,OAAQ,KAAK,IAAK,EAAE,QAAS;AAC5D,cAAU,QAAQ,OAAQ,KAAK,MAAO,KAAK,IAAK,EAAE,QAAS;AAC3D,cAAU,QAAQ,OAAQ,KAAK,OAAQ,KAAK,IAAK,EAAE,QAAS;AAE5D,aAAU,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAO;AAE3C,YAAM,SAAS,QAAS,CAAE;AAI1B,aAAO,UAAU;AACjB,aAAO,SAAS,IAAK,GAAG,GAAG,CAAE;AAC7B,aAAO,SAAS,KAAM,KAAK,aAAc;AAEzC,UAAI;AAEJ,UAAK,KAAK,OAAO,sBAAuB;AAEvC,kBAAW,KAAK,OAAO,MAAM,KAAK,OAAO,UAAW,KAAK,OAAO;AAAA,MAEjE,OAAO;AAEN,iBAAS,KAAK,cAAc,WAAY,KAAK,cAAe,IAAI,KAAK,IAAK,MAAM,KAAK,IAAK,KAAK,KAAK,KAAK,OAAO,MAAM,GAAI,IAAI,KAAK,OAAO,MAAM,CAAE;AAAA,MAEnJ;AAEA,aAAO,MAAM,IAAK,GAAG,GAAG,CAAE,EAAE,eAAgB,SAAS,KAAK,OAAO,CAAE;AAInE,UAAK,OAAO,QAAQ,UAAW;AAE9B,eAAO,UAAU;AAEjB,YAAK,OAAO,SAAS,QAAS;AAE7B,iBAAO,UAAU,CAAC,CAAE,KAAK;AAEzB,cAAK,KAAK,SAAS,KAAM;AAExB,4BAAgB,aAAc,WAAW,IAAK,GAAG,GAAG,CAAE,CAAE;AACxD,mBAAO,WAAW,KAAM,UAAW,EAAE,SAAU,eAAgB;AAE/D,gBAAK,KAAK,IAAK,aAAa,KAAM,MAAO,EAAE,gBAAiB,UAAW,EAAE,IAAK,KAAK,GAAI,CAAE,IAAI,KAAM;AAElG,qBAAO,UAAU;AAAA,YAElB;AAAA,UAED;AAEA,cAAK,KAAK,SAAS,KAAM;AAExB,4BAAgB,aAAc,WAAW,IAAK,GAAG,GAAG,KAAK,KAAK,CAAE,CAAE;AAClE,mBAAO,WAAW,KAAM,UAAW,EAAE,SAAU,eAAgB;AAE/D,gBAAK,KAAK,IAAK,aAAa,KAAM,MAAO,EAAE,gBAAiB,UAAW,EAAE,IAAK,KAAK,GAAI,CAAE,IAAI,KAAM;AAElG,qBAAO,UAAU;AAAA,YAElB;AAAA,UAED;AAEA,cAAK,KAAK,SAAS,KAAM;AAExB,4BAAgB,aAAc,WAAW,IAAK,GAAG,KAAK,KAAK,GAAG,CAAE,CAAE;AAClE,mBAAO,WAAW,KAAM,UAAW,EAAE,SAAU,eAAgB;AAE/D,gBAAK,KAAK,IAAK,aAAa,KAAM,MAAO,EAAE,gBAAiB,UAAW,EAAE,IAAK,KAAK,GAAI,CAAE,IAAI,KAAM;AAElG,qBAAO,UAAU;AAAA,YAElB;AAAA,UAED;AAEA,cAAK,KAAK,SAAS,QAAS;AAE3B,4BAAgB,aAAc,WAAW,IAAK,GAAG,KAAK,KAAK,GAAG,CAAE,CAAE;AAClE,yBAAa,KAAM,KAAK,YAAa;AACrC,mBAAO,WAAW,sBAAuB,cAAc,OAAQ,aAAa,cAAc,MAAO,CAAE;AACnG,mBAAO,WAAW,SAAU,eAAgB;AAC5C,mBAAO,UAAU,KAAK;AAAA,UAEvB;AAEA,cAAK,KAAK,SAAS,KAAM;AAExB,mBAAO,UAAU;AAAA,UAElB;AAAA,QAGD,WAAY,OAAO,SAAS,SAAU;AAErC,iBAAO,SAAS,KAAM,KAAK,kBAAmB;AAC9C,iBAAO,UAAU,KAAK;AAAA,QAEvB,WAAY,OAAO,SAAS,OAAQ;AAEnC,iBAAO,SAAS,KAAM,KAAK,aAAc;AACzC,iBAAO,UAAU,KAAK;AAAA,QAEvB,WAAY,OAAO,SAAS,SAAU;AAErC,iBAAO,SAAS,KAAM,KAAK,kBAAmB;AAC9C,iBAAO,WAAW,KAAM,KAAK,oBAAqB;AAClD,sBAAY,IAAK,OAAO,OAAO,KAAM,EAAE,IAAK,KAAK,kBAAmB,EAAE,IAAK,KAAK,aAAc,EAAE,eAAgB,EAAI;AACpH,sBAAY,gBAAiB,KAAK,qBAAqB,MAAM,EAAE,OAAO,CAAE;AACxE,iBAAO,MAAM,KAAM,WAAY;AAC/B,iBAAO,UAAU,KAAK;AAAA,QAEvB,OAAO;AAEN,iBAAO,WAAW,KAAM,UAAW;AAEnC,cAAK,KAAK,UAAW;AAEpB,mBAAO,SAAS,KAAM,KAAK,kBAAmB;AAAA,UAE/C,OAAO;AAEN,mBAAO,SAAS,KAAM,KAAK,aAAc;AAAA,UAE1C;AAEA,cAAK,KAAK,MAAO;AAEhB,mBAAO,UAAU,KAAK,KAAK,OAAQ,OAAO,IAAK,MAAM;AAAA,UAEtD;AAAA,QAED;AAGA;AAAA,MAED;AAIA,aAAO,WAAW,KAAM,UAAW;AAEnC,UAAK,KAAK,SAAS,eAAe,KAAK,SAAS,SAAU;AAIzD,cAAM,sBAAsB;AAC5B,cAAM,uBAAuB;AAE7B,YAAK,OAAO,SAAS,KAAM;AAE1B,cAAK,KAAK,IAAK,aAAa,KAAM,MAAO,EAAE,gBAAiB,UAAW,EAAE,IAAK,KAAK,GAAI,CAAE,IAAI,qBAAsB;AAElH,mBAAO,MAAM,IAAK,OAAO,OAAO,KAAM;AACtC,mBAAO,UAAU;AAAA,UAElB;AAAA,QAED;AAEA,YAAK,OAAO,SAAS,KAAM;AAE1B,cAAK,KAAK,IAAK,aAAa,KAAM,MAAO,EAAE,gBAAiB,UAAW,EAAE,IAAK,KAAK,GAAI,CAAE,IAAI,qBAAsB;AAElH,mBAAO,MAAM,IAAK,OAAO,OAAO,KAAM;AACtC,mBAAO,UAAU;AAAA,UAElB;AAAA,QAED;AAEA,YAAK,OAAO,SAAS,KAAM;AAE1B,cAAK,KAAK,IAAK,aAAa,KAAM,MAAO,EAAE,gBAAiB,UAAW,EAAE,IAAK,KAAK,GAAI,CAAE,IAAI,qBAAsB;AAElH,mBAAO,MAAM,IAAK,OAAO,OAAO,KAAM;AACtC,mBAAO,UAAU;AAAA,UAElB;AAAA,QAED;AAEA,YAAK,OAAO,SAAS,MAAO;AAE3B,cAAK,KAAK,IAAK,aAAa,KAAM,MAAO,EAAE,gBAAiB,UAAW,EAAE,IAAK,KAAK,GAAI,CAAE,IAAI,sBAAuB;AAEnH,mBAAO,MAAM,IAAK,OAAO,OAAO,KAAM;AACtC,mBAAO,UAAU;AAAA,UAElB;AAAA,QAED;AAEA,YAAK,OAAO,SAAS,MAAO;AAE3B,cAAK,KAAK,IAAK,aAAa,KAAM,MAAO,EAAE,gBAAiB,UAAW,EAAE,IAAK,KAAK,GAAI,CAAE,IAAI,sBAAuB;AAEnH,mBAAO,MAAM,IAAK,OAAO,OAAO,KAAM;AACtC,mBAAO,UAAU;AAAA,UAElB;AAAA,QAED;AAEA,YAAK,OAAO,SAAS,MAAO;AAE3B,cAAK,KAAK,IAAK,aAAa,KAAM,MAAO,EAAE,gBAAiB,UAAW,EAAE,IAAK,KAAK,GAAI,CAAE,IAAI,sBAAuB;AAEnH,mBAAO,MAAM,IAAK,OAAO,OAAO,KAAM;AACtC,mBAAO,UAAU;AAAA,UAElB;AAAA,QAED;AAAA,MAED,WAAY,KAAK,SAAS,UAAW;AAIpC,yBAAiB,KAAM,UAAW;AAClC,qBAAa,KAAM,KAAK,GAAI,EAAE,gBAAiB,gBAAgB,KAAM,UAAW,EAAE,OAAO,CAAE;AAE3F,YAAK,OAAO,KAAK,OAAQ,GAAI,MAAM,IAAM;AAExC,iBAAO,WAAW,sBAAuB,cAAc,OAAQ,KAAK,KAAK,aAAa,MAAO,CAAE;AAAA,QAEhG;AAEA,YAAK,OAAO,SAAS,KAAM;AAE1B,0BAAgB,iBAAkB,QAAQ,KAAK,MAAO,CAAE,aAAa,GAAG,aAAa,CAAE,CAAE;AACzF,0BAAgB,oBAAqB,kBAAkB,eAAgB;AACvE,iBAAO,WAAW,KAAM,eAAgB;AAAA,QAEzC;AAEA,YAAK,OAAO,SAAS,KAAM;AAE1B,0BAAgB,iBAAkB,QAAQ,KAAK,MAAO,aAAa,GAAG,aAAa,CAAE,CAAE;AACvF,0BAAgB,oBAAqB,kBAAkB,eAAgB;AACvE,iBAAO,WAAW,KAAM,eAAgB;AAAA,QAEzC;AAEA,YAAK,OAAO,SAAS,KAAM;AAE1B,0BAAgB,iBAAkB,QAAQ,KAAK,MAAO,aAAa,GAAG,aAAa,CAAE,CAAE;AACvF,0BAAgB,oBAAqB,kBAAkB,eAAgB;AACvE,iBAAO,WAAW,KAAM,eAAgB;AAAA,QAEzC;AAAA,MAED;AAGA,aAAO,UAAU,OAAO,YAAa,OAAO,KAAK,QAAS,GAAI,MAAM,MAAO,KAAK;AAChF,aAAO,UAAU,OAAO,YAAa,OAAO,KAAK,QAAS,GAAI,MAAM,MAAO,KAAK;AAChF,aAAO,UAAU,OAAO,YAAa,OAAO,KAAK,QAAS,GAAI,MAAM,MAAO,KAAK;AAChF,aAAO,UAAU,OAAO,YAAa,OAAO,KAAK,QAAS,GAAI,MAAM,MAAS,KAAK,SAAS,KAAK,SAAS,KAAK;AAI9G,aAAO,SAAS,SAAS,OAAO,SAAS,UAAU,OAAO,SAAS,MAAM,MAAM;AAC/E,aAAO,SAAS,WAAW,OAAO,SAAS,YAAY,OAAO,SAAS;AAEvE,aAAO,SAAS,MAAM,KAAM,OAAO,SAAS,MAAO;AACnD,aAAO,SAAS,UAAU,OAAO,SAAS;AAE1C,UAAK,KAAK,WAAW,KAAK,MAAO;AAEhC,YAAK,OAAO,SAAS,KAAK,MAAO;AAEhC,iBAAO,SAAS,MAAM,OAAQ,QAAS;AACvC,iBAAO,SAAS,UAAU;AAAA,QAE3B,WAAY,KAAK,KAAK,MAAO,EAAG,EAAE,KAAM,SAAW,GAAI;AAEtD,iBAAO,OAAO,SAAS;AAAA,QAExB,CAAE,GAAI;AAEL,iBAAO,SAAS,MAAM,OAAQ,QAAS;AACvC,iBAAO,SAAS,UAAU;AAAA,QAE3B;AAAA,MAED;AAAA,IAED;AAEA,UAAM,kBAAmB,KAAM;AAAA,EAEhC;AAED;AAIA,IAAM,yBAAN,cAAqC,KAAK;AAAA,EAEzC,cAAc;AAEb;AAAA,MACC,IAAI,cAAe,KAAQ,KAAQ,GAAG,CAAE;AAAA,MACxC,IAAI,kBAAmB,EAAE,SAAS,OAAO,WAAW,MAAM,MAAM,YAAY,aAAa,MAAM,SAAS,KAAK,YAAY,MAAM,CAAE;AAAA,IAClI;AAEA,SAAK,2BAA2B;AAEhC,SAAK,OAAO;AAAA,EAEb;AAAA,EAEA,kBAAmB,OAAQ;AAE1B,QAAI,QAAQ,KAAK;AAEjB,SAAK,SAAS,KAAM,KAAK,aAAc;AAEvC,QAAK,KAAK,SAAS,QAAU,SAAQ;AAErC,QAAI,KAAM,MAAO,EAAE,gBAAiB,UAAU,UAAU,KAAK,kBAAkB,mBAAoB;AACnG,QAAI,KAAM,MAAO,EAAE,gBAAiB,UAAU,UAAU,KAAK,kBAAkB,mBAAoB;AACnG,QAAI,KAAM,MAAO,EAAE,gBAAiB,UAAU,UAAU,KAAK,kBAAkB,mBAAoB;AAInG,iBAAa,KAAM,GAAI;AAEvB,YAAS,KAAK,MAAO;AAAA,MAEpB,KAAK;AAAA,MACL,KAAK;AACJ,gBAAS,KAAK,MAAO;AAAA,UAEpB,KAAK;AACJ,yBAAa,KAAM,KAAK,GAAI,EAAE,MAAO,GAAI;AACzC,uBAAW,KAAM,GAAI,EAAE,MAAO,YAAa;AAC3C;AAAA,UACD,KAAK;AACJ,yBAAa,KAAM,KAAK,GAAI,EAAE,MAAO,GAAI;AACzC,uBAAW,KAAM,GAAI,EAAE,MAAO,YAAa;AAC3C;AAAA,UACD,KAAK;AACJ,yBAAa,KAAM,KAAK,GAAI,EAAE,MAAO,GAAI;AACzC,uBAAW,KAAM,GAAI,EAAE,MAAO,YAAa;AAC3C;AAAA,UACD,KAAK;AACJ,uBAAW,KAAM,GAAI;AACrB;AAAA,UACD,KAAK;AACJ,uBAAW,KAAM,GAAI;AACrB;AAAA,UACD,KAAK;AACJ,yBAAa,KAAM,GAAI;AACvB,uBAAW,KAAM,GAAI;AACrB;AAAA,UACD,KAAK;AAAA,UACL,KAAK;AACJ,uBAAW,IAAK,GAAG,GAAG,CAAE;AACxB;AAAA,QAEF;AAEA;AAAA,MACD,KAAK;AAAA,MACL;AAEC,mBAAW,IAAK,GAAG,GAAG,CAAE;AAAA,IAE1B;AAEA,QAAK,WAAW,OAAO,MAAM,GAAI;AAGhC,WAAK,WAAW,KAAM,KAAK,gBAAiB;AAAA,IAE7C,OAAO;AAEN,kBAAY,OAAQ,YAAY,IAAK,GAAG,GAAG,CAAE,GAAG,YAAY,YAAa;AAEzE,WAAK,WAAW,sBAAuB,WAAY;AAAA,IAEpD;AAEA,UAAM,kBAAmB,KAAM;AAAA,EAEhC;AAED;", "names": []}