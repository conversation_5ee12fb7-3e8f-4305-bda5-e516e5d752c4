{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/ca.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"Dilluns\",\n  \"Di<PERSON>s\",\n  \"<PERSON>mecre<PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"Divendres\",\n  \"<PERSON><PERSON>bte\",\n  \"<PERSON><PERSON><PERSON>\"\n];\nconst weekDaysShort = [\n  \"Dl\",\n  \"Dt\",\n  \"Dc\",\n  \"Dj\",\n  \"Dv\",\n  \"Ds\",\n  \"Dg\"\n];\nconst months = [\n  \"Gener\",\n  \"Febrer\",\n  \"Març\",\n  \"Abril\",\n  \"Maig\",\n  \"Juny\",\n  \"<PERSON>l\",\n  \"Agost\",\n  \"Setembre\",\n  \"Octubre\",\n  \"Novembre\",\n  \"Desembre\"\n];\nconst years = \"Anys\";\nconst year = \"Any\";\nconst month = \"Mes\";\nconst week = \"Setmana\";\nconst day = \"Dia\";\nconst today = \"Avui\";\nconst noEvent = \"No hi ha esdeveniments\";\nconst allDay = \"Tot el dia\";\nconst deleteEvent = \"Eliminar\";\nconst createEvent = \"Crear un esdeveniment\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst ca = {\n  weekDays,\n  weekDaysShort,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  ca as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  weekDaysShort,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}