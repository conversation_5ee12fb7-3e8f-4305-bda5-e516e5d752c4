import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/sr.es.js
var weekDays = [
  "Ponedeljak",
  "Utorak",
  "Sr<PERSON>",
  "Četvrtak",
  "Pet<PERSON>",
  "Sub<PERSON>",
  "<PERSON><PERSON><PERSON>"
];
var months = [
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "April",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "Avgust",
  "Septembar",
  "Oktobar",
  "Novembar",
  "Decembar"
];
var years = "Godine";
var year = "Godina";
var month = "Mesec";
var week = "Sedmica";
var day = "Dan";
var today = "Danas";
var noEvent = "Nema događaja";
var allDay = "<PERSON>li dan";
var deleteEvent = "Obriši";
var createEvent = "Kreiraj događaj";
var dateFormat = "dddd D MMMM YYYY";
var sr = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  sr as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/sr.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=sr.es-IUTKD42N.js.map
