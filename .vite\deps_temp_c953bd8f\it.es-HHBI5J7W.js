import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/it.es.js
var weekDays = [
  "Lunedì",
  "Martedì",
  "Mercoledì",
  "Giovedì",
  "Venerdì",
  "Sabato",
  "Domenica"
];
var months = [
  "Gennai<PERSON>",
  "Feb<PERSON><PERSON>",
  "Mar<PERSON>",
  "<PERSON><PERSON>",
  "Maggio",
  "Giugno",
  "Luglio",
  "Agosto",
  "Settembre",
  "Ottobre",
  "Novembre",
  "Dicembre"
];
var years = "Anni";
var year = "Anno";
var month = "Mese";
var week = "Settimana";
var day = "Giorno";
var today = "Oggi";
var noEvent = "Nessun evento";
var allDay = "Tutto il giorno";
var deleteEvent = "Cancella";
var createEvent = "Crea evento";
var dateFormat = "dddd D MMMM YYYY";
var it = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  it as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/it.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=it.es-HHBI5J7W.js.map
