{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/is.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"M<PERSON><PERSON>gu<PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  \"Laugardagur\",\n  \"Sunnudagur\"\n];\nconst months = [\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"Ág<PERSON><PERSON>\",\n  \"September\",\n  \"Október\",\n  \"Nóvember\",\n  \"Desember\"\n];\nconst years = \"Ár\";\nconst year = \"Ár\";\nconst month = \"Mánuður\";\nconst week = \"Vika\";\nconst day = \"Dagur\";\nconst today = \"Í dag\";\nconst noEvent = \"Enginn atburður\";\nconst allDay = \"Allan daginn\";\nconst deleteEvent = \"Eyða\";\nconst createEvent = \"Búðu til viðburð\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst is = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  is as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}