{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/da.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"Mandag\",\n  \"Tirsdag\",\n  \"Onsdag\",\n  \"Torsdag\",\n  \"Fredag\",\n  \"Lørdag\",\n  \"Søndag\"\n];\nconst months = [\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"April\",\n  \"<PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"August\",\n  \"September\",\n  \"Oktober\",\n  \"November\",\n  \"December\"\n];\nconst years = \"År (flertal)\";\nconst year = \"År\";\nconst month = \"Måned\";\nconst week = \"Uge\";\nconst day = \"Dag\";\nconst today = \"I dag\";\nconst noEvent = \"Ingen begivenhed\";\nconst allDay = \"Hele dagen\";\nconst deleteEvent = \"Slet\";\nconst createEvent = \"Opret et event\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst da = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  da as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}