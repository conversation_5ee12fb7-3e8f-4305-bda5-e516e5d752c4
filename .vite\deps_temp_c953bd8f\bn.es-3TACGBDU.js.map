{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/bn.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"সোম\",\n  \"মঙ্গল\",\n  \"বুধ\",\n  \"বৃহস্পতি\",\n  \"শুক্র\",\n  \"শনি\",\n  \"রবি\"\n];\nconst months = [\n  \"জানুয়ারি\",\n  \"ফেব্ুয়ারী\",\n  \"মার্চ\",\n  \"এপ্রিল\",\n  \"মে\",\n  \"জুন\",\n  \"জুলাই\",\n  \"অগাস্ট\",\n  \"সেপ্টেম্বর\",\n  \"অক্টোবর\",\n  \"নভেম্বর\",\n  \"ডিসেম্বর\"\n];\nconst years = \"বছর\";\nconst year = \"বছর\";\nconst month = \"মাস\";\nconst week = \"সপ্তাহ\";\nconst day = \"দিন\";\nconst today = \"আজ\";\nconst noEvent = \"কার্যসূচী\";\nconst allDay = \"সারাদিন\";\nconst deleteEvent = \"মুছুন\";\nconst createEvent = \"কার্যসূচী তৈরি করুন\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst bn = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  bn as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}