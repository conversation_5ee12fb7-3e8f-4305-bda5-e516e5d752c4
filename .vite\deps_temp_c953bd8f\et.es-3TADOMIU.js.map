{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/et.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"\n];\nconst months = [\n  \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"August\",\n  \"September\",\n  \"Oktoober\",\n  \"November\",\n  \"Detsember\"\n];\nconst years = \"Aastad\";\nconst year = \"Aasta\";\nconst month = \"Kuu\";\nconst week = \"Nädal\";\nconst day = \"Päev\";\nconst today = \"Täna\";\nconst noEvent = \"Sündmus puudub\";\nconst allDay = \"Terve päev\";\nconst deleteEvent = \"Kustuta\";\nconst createEvent = \"Loo sündmus\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst et = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  et as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}