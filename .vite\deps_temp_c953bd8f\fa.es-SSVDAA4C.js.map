{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/fa.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"دوشنبه\",\n  \"سه شنبه\",\n  \"چهار شنبه\",\n  \"پنج شنبه\",\n  \"جمعه\",\n  \"شنبه\",\n  \"یک شنبه\"\n];\nconst months = [\n  \"ژانویه\",\n  \"فوریه\",\n  \"مارس\",\n  \"آوریل\",\n  \"می\",\n  \"ژوئن\",\n  \"ژوئیه\",\n  \"اوت\",\n  \"سپتامبر\",\n  \"اکتبر\",\n  \"نوامبر\",\n  \"دسامبر\"\n];\nconst years = \"سالها\";\nconst year = \"سال\";\nconst month = \"ماه\";\nconst week = \"هفته\";\nconst day = \"روز\";\nconst today = \"امروز\";\nconst noEvent = \"رویدادی نیست\";\nconst allDay = \"تمام روز\";\nconst deleteEvent = \"حذف\";\nconst createEvent = \"ایجاد یک رویداد\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst fa = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  fa as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}