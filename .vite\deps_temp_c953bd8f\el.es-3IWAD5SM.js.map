{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/el.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"Δευτέρα\",\n  \"Τρίτη\",\n  \"Τετάρτη\",\n  \"Πέμπτη\",\n  \"Παρα<PERSON>κευ<PERSON>\",\n  \"Σάββατο\",\n  \"Κυριακ<PERSON>\"\n];\nconst months = [\n  \"Ιανουάριος\",\n  \"Φεβρουάριος\",\n  \"Μάρτι<PERSON>\",\n  \"Απρίλιος\",\n  \"Μάι<PERSON>\",\n  \"Ιούνιος\",\n  \"Ιούλιος\",\n  \"Αύγουστος\",\n  \"Σεπτέμβριος\",\n  \"Οκτώβριος\",\n  \"Νοέμβριος\",\n  \"Δεκέμβριος\"\n];\nconst monthsGenitive = [\n  \"Ιανουαρίου\",\n  \"Φεβρουαρίου\",\n  \"Μαρτίου\",\n  \"Απριλίου\",\n  \"Μαΐου\",\n  \"Ιουνίου\",\n  \"Ιουλίου\",\n  \"Αυγούστου\",\n  \"Σεπτεμβρίου\",\n  \"Οκτωβρίου\",\n  \"Νοεμβρίου\",\n  \"Δεκεμβρίου\"\n];\nconst years = \"Έτη\";\nconst year = \"Έτος\";\nconst month = \"Μήνα\";\nconst week = \"Εβδομάδα\";\nconst day = \"Ημέρα\";\nconst today = \"Σήμερα\";\nconst noEvent = \"Κανένα συμβάν\";\nconst allDay = \"Ημερήσιο συμβάν\";\nconst deleteEvent = \"Διαγραφή\";\nconst createEvent = \"Δημιουργία συμβάντος\";\nconst dateFormat = \"dddd D MMMMG YYYY\";\nconst am = \"π.μ.\";\nconst pm = \"μ.μ.\";\nconst el = {\n  weekDays,\n  months,\n  monthsGenitive,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat,\n  am,\n  pm\n};\nexport {\n  allDay,\n  am,\n  createEvent,\n  dateFormat,\n  day,\n  el as default,\n  deleteEvent,\n  month,\n  months,\n  monthsGenitive,\n  noEvent,\n  pm,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,iBAAiB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,KAAK;AACX,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}