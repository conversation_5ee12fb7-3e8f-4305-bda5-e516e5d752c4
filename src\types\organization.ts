export enum UserRole {
  ADMIN = 'admin',
  READER = 'reader',
  EDITOR = 'editor',
  SUPPORTER = 'supporter',
}
export enum Theme {
  LIGHT = 'light',
  DARK = 'dark',
  CUSTOM = 'custom'
}
export type User = {
  user_id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  organization_id?: string[];
};
export type Role = {
  user_id: string;
  created_time: string;
  role: UserRole;
  email: string | undefined;
};

export type RoleResponse = {
  userrole: Role,
  organizationId:string
}

export type Organization = {
  _id: string;
  name: string;
  founding_date: Date;
  contact_email: string;
  phone_number: string;
  address: string;
  website: string;
  max_users: number;
  roles: Role[];
  thumbnail: string;
  theme: Theme,
  primary:string,
  secondary:string,
};

export type unique_org_id={
  exists:boolean;
  message:string;
}
export type OrgThumbnail = {
  _id: string;
  thumbnail: string;
};
export type CreateOrganizationInput = {
  name: string;
  founding_date: Date;
  contact_email: string;
  phone_number: string;
  address: string;
  website: string;
  max_users: number;
  is_public: boolean;
  thumbnail: string;
  organizationId: string;
  theme: Theme,
  primary:string,
  secondary:string,
};

export type UpdateOrganizationInput = {
  name?: string;
  founding_date?: Date;
  contact_email?: string;
  contact_phone_number?: string;
  address?: string;
  website?: string;
  thumbnail? : string;
  max_users?: number;
  theme?: Theme,
  primary?:string,
  secondary?:string,
  measurement_id?:string,
};
