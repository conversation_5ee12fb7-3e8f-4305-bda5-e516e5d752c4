{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/tr.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON>arş<PERSON><PERSON>\",\n  \"<PERSON>şem<PERSON>\",\n  \"<PERSON>uma\",\n  \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON>\"\n];\nconst months = [\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"Mart\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"Haziran\",\n  \"Temmuz\",\n  \"Ağustos\",\n  \"Eylül\",\n  \"Ekim\",\n  \"Kasım\",\n  \"Aralık\"\n];\nconst years = \"Yıllar\";\nconst year = \"Yıl\";\nconst month = \"Ay\";\nconst week = \"Hafta\";\nconst day = \"Gün\";\nconst today = \"Bugün\";\nconst noEvent = \"Etkinlik Yok\";\nconst allDay = \"Tüm gün\";\nconst deleteEvent = \"Sil\";\nconst createEvent = \"Etkinlik ekle\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst tr = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  tr as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}