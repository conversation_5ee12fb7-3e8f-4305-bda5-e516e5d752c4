import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/es.es.js
var weekDays = [
  "Lunes",
  "<PERSON><PERSON>",
  "Miércoles",
  "Jueves",
  "Viernes",
  "Sábado",
  "Domingo"
];
var months = [
  "En<PERSON>",
  "Febrero",
  "Mar<PERSON>",
  "Abri<PERSON>",
  "Mayo",
  "Jun<PERSON>",
  "Julio",
  "Agosto",
  "Septiembre",
  "Octubre",
  "Noviembre",
  "Diciembre"
];
var years = "Años";
var year = "Año";
var month = "Mes";
var week = "Semana";
var day = "Día";
var today = "Hoy";
var noEvent = "No hay evento";
var allDay = "Todo el día";
var deleteEvent = "Borrar";
var createEvent = "Crear un evento";
var dateFormat = "dddd D MMMM YYYY";
var es = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  es as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/es.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=es.es-VGQ756GN.js.map
