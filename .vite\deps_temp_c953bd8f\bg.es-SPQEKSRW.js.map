{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/bg.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"Понеделник\",\n  \"Вторник\",\n  \"Сряда\",\n  \"Четвъртък\",\n  \"Петък\",\n  \"Събота\",\n  \"Неделя\"\n];\nconst months = [\n  \"Януари\",\n  \"Февруари\",\n  \"Март\",\n  \"А<PERSON>рил\",\n  \"Май\",\n  \"Юни\",\n  \"Юли\",\n  \"Август\",\n  \"Септември\",\n  \"Октомври\",\n  \"Ноември\",\n  \"Декември\"\n];\nconst years = \"Години\";\nconst year = \"Година\";\nconst month = \"Месец\";\nconst week = \"Седмица\";\nconst day = \"Ден\";\nconst today = \"Днес\";\nconst noEvent = \"Няма събития\";\nconst allDay = \"Цял ден\";\nconst deleteEvent = \"Изтрий\";\nconst createEvent = \"Създай събитие\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst bg = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  bg as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}