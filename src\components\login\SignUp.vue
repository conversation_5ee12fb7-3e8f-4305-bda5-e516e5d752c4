<script setup>
import { createUserWithEmailAndPassword, fetchSignInMethodsForEmail, onAuthStateChanged } from 'firebase/auth';
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { doc, setDoc, serverTimestamp, getFirestore } from "firebase/firestore";
const db = getFirestore();
import AuthInput from '../../components/common/AuthInput.vue';
import Button from '../../components/common/Button.vue';
import StepIndicator from '../../components/common/StepIndicator.vue';
import { auth } from '../../firebaseConfig';
import { UserStore } from '../../store';
import { getCookie, setCookie } from '../../helpers/domhelper';
import { useRouter } from 'vue-router';
import { Form } from 'vee-validate';
import { emailSchema, passwordSchema, roleSchema, nameSchema } from '@/validationSchema/user';
const emit = defineEmits(['handleToggleForms']);
const props = defineProps({
  isVerificationPending: {
    type: Boolean,
    default: false,
  },
});
const store = UserStore();
const router = useRouter();
const stepCount = ref(1);
const subStepCount = ref(1);
const isEmailVerified = ref(false);
const isVerificationInProgress = ref(false);
const resendTimer = ref(0);
const canResend = ref(true);
const verificationSent = ref(false);

// Loading states for button
const isSubmitting = ref(false);

// Form data storage
const formData = ref({
  first_name: '',
  last_name: '',
  email: '',
  organizationRole: '',
  password: '',
  confirmPassword: '',
});

const Errormsg = ref({
  isShow: false,
  message: null,
});

const stepStatus = ref({
  personalInfo: false,
  setupPassword: false,
  verify: false,
});

// Step configuration for StepIndicator component
const steps = [
  { key: 'personalInfo', label: 'Personal Info' },
  { key: 'setupPassword', label: 'Setup Password' },
  { key: 'verify', label: 'Verify' },
];

let unsubscribe = () => { };
let intervalId = null;

// Helper function to get current validation schema
const getCurrentSchema = () => {
  if (stepCount.value === 1) {
    switch (subStepCount.value) {
      case 1: return nameSchema;
      case 2: return emailSchema;
      case 3: return roleSchema;
      default: return nameSchema;
    }
  }
  return passwordSchema;
};

// Helper function to get current initial values for forms
const getCurrentInitialValues = () => {
  if (stepCount.value === 1) {
    switch (subStepCount.value) {
      case 1:
        return {
          first_name: formData.value.first_name,
          last_name: formData.value.last_name,
        };
      case 2:
        return {
          email: formData.value.email,
        };
      case 3:
        return {
          organizationRole: formData.value.organizationRole,
        };
      default:
        return {
          first_name: formData.value.first_name,
          last_name: formData.value.last_name,
        };
    }
  }
  // Step 2: Password setup
  return {
    password: formData.value.password,
    confirmPassword: formData.value.confirmPassword,
  };
};

const handleToggleForms = () => {
  emit('handleToggleForms');
};

// Handle back button navigation
const handleBackButton = () => {
  Errormsg.value.isShow = false;

  if (stepCount.value === 1) {
    if (subStepCount.value > 1) {
      subStepCount.value--;
    } else {
      // If on first sub-step of first step, go back to login
      handleToggleForms();
    }
  } else if (stepCount.value === 2) {
    // Go back to last sub-step of step 1
    stepCount.value = 1;
    subStepCount.value = 3;
    stepStatus.value.personalInfo = false;
  }
  // Note: No back button on verification step (stepCount === 3)
};

const checkIfUserExists = async (email) => {
  try {
    const signInMethods = await fetchSignInMethodsForEmail(auth, email);
    if (signInMethods.length > 0) {
      // User already exists
      return true;
    }
    // User does not exist
    return false;

  } catch (error) {
    console.error("Error checking user existence:", error);
    throw error;
  }
};

const startResendTimer = () => {
  resendTimer.value = 60;
  canResend.value = false;
  const timer = setInterval(() => {
    resendTimer.value--;
    if (resendTimer.value <= 0) {
      canResend.value = true;
      clearInterval(timer);
    }
  }, 1000);
};
const listenForVerification = () => {
  if (intervalId) {
    clearInterval(intervalId);
  }
  intervalId = setInterval(() => {
    if (auth.currentUser) {
      auth.currentUser.reload().then(() => {
        if (auth.currentUser.emailVerified !== isEmailVerified.value) {
          isEmailVerified.value = auth.currentUser.emailVerified;
          stepStatus.value.verify = isEmailVerified.value;
          if (isEmailVerified.value) {
            clearInterval(intervalId);
          }
        }
      });
    }
  }, 5000);
};
const completeSignup = async () => {
  try {
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      formData.value.email,
      formData.value.password,
    );
    const user = userCredential.user;
    if (!user) {
      throw new Error("No authenticated user found");
    }
    // Ensure user token is refreshed before proceeding
    const idToken = await user.getIdToken(true);
    setCookie("accessToken", idToken, 30);
    console.log("access token" + idToken);
    console.log("Token retrieved from cookies:", getCookie("accessToken"));
    // Store in Firestore
    const userRef = doc(db, "users", user.uid);
    await setDoc(userRef, {
      uid: user.uid,
      email: user.email,
      displayName: `${formData.value.first_name} ${formData.value.last_name}`,
      firstName: formData.value.first_name,
      lastName: formData.value.last_name,
      role: formData.value.organizationRole,
      createdAt: serverTimestamp(),
    });
    const userData = {
      email: user.email,
      first_name: formData.value.first_name,
      last_name: formData.value.last_name,
      uid: user.uid,
      role: formData.value.organizationRole,
    };
    await store.CreateUser(userData);

  } catch (error) {
    if (auth.currentUser) {
      auth.currentUser.delete();
    }
    console.error("Signup completion error:", error);
    throw error;
  }
};
const sendVerificationEmail = async () => {
  const user = auth.currentUser;
  if (user) {
    if (user.emailVerified) {
      router.push('/projects');
      return;
    }
    Errormsg.value = {
      isShow: false,
      message: null,
    };
    resendTimer.value = 0;
    canResend.value = false;
    await store.sendVerificationEmail(user).then(() => {
      stepCount.value = 3;
      isVerificationInProgress.value = true;
      verificationSent.value = true;
      startResendTimer();
      listenForVerification();
      console.log("Verification email sent to:", user.email);
    }).catch((error) => {
      console.error("Error sending verification email:", error);
      Errormsg.value = {
        isShow: true,
        message: error.error?.includes('TOO_MANY_ATTEMPTS_TRY_LATER')
          ? "Too many verification attempts. Please try again later."
          : "Failed to send verification email",
      };
      startResendTimer();
    });
  }
};
const handlePasswordSetup = async () => {
  try {
    const userExists = await checkIfUserExists(formData.value.email);
    if (userExists) {
      throw new Error("User already exists. Please use a different email.");
    }
    await completeSignup().then(async () => {
      await sendVerificationEmail();
    });
    stepStatus.value.setupPassword = true;
  } catch (error) {
    verificationSent.value = false;
    console.error("Password setup error:", error);
    stepCount.value = 2;
    Errormsg.value = {
      isShow: true,
      message: "Failed to create account. Please try again.",
    };
  }
};
const handleStepSubmit = async (values) => {
  Errormsg.value.isShow = false;
  isSubmitting.value = true;

  try {
    if (stepCount.value === 1) {
      if (subStepCount.value === 1) {
        formData.value.first_name = values.first_name;
        formData.value.last_name = values.last_name;
        subStepCount.value++;
        return;
      }

      if (subStepCount.value === 2) {
        const userExists = await checkIfUserExists(values.email);
        if (userExists) {
          Errormsg.value = {
            isShow: true,
            message: "User already exists. Please use a different email.",
          };
          return;
        }
        formData.value.email = values.email;
        subStepCount.value++;
        return;
      }

      if (subStepCount.value === 3) {
        formData.value.organizationRole = values.organizationRole;
        stepStatus.value.personalInfo = true;
        stepCount.value = 2;
        subStepCount.value = 1;
        return;
      }
    }

    if (stepCount.value === 2) {
      formData.value.password = values.password;
      formData.value.confirmPassword = values.confirmPassword;
      await handlePasswordSetup();
    }
  } catch (error) {
    console.error('Step submission error:', error);
    Errormsg.value = {
      isShow: true,
      message: error.message || 'An error occurred. Please try again.',
    };
  } finally {
    isSubmitting.value = false;
  }
};

const handleExplorePlatform = async () => {
  const user = auth.currentUser;
  if (user && isEmailVerified.value) {
    const accessToken = await user.getIdToken();
    setCookie('accessToken', accessToken);
    const urlParams = new URLSearchParams(window.location.search);
    const redirectUri = urlParams.get('redirect_uri');
    if (redirectUri) {
      const decodedRedirectUri = decodeURIComponent(redirectUri);
      if (decodedRedirectUri.startsWith('/')) {
        window.location.href = decodedRedirectUri;
      } else {
        router.push('/projects');
      }
    } else {
      router.push('/projects');
    }
  }
};

onMounted(() => {
  unsubscribe = onAuthStateChanged(auth, async (user) => {
    if (user) {
      isEmailVerified.value = user.emailVerified;
      if (isEmailVerified.value && stepCount.value === 3) {
        isVerificationInProgress.value = false;
      }
    }
  });

});
// move user to verification step if email is not verified
watch(() => props.isVerificationPending, async (newValue) => {
  if (newValue === true && !auth.currentUser.emailVerified) {
    // Set verification in progress state
    isEmailVerified.value = false;
    stepCount.value = 3;
    isVerificationInProgress.value = true;
    stepStatus.value = {
      personalInfo: true,
      setupPassword: true,
      verify: false,
    };
    // Send verification email if user exists
    await sendVerificationEmail();
  } else {
    stepCount.value = 1;
    subStepCount.value = 1;
    isVerificationInProgress.value = false;
    verificationSent.value = false;
    resendTimer.value = 0;
    canResend.value = true;
  }
}, { immediate: true });

onUnmounted(() => {
  unsubscribe();
  if (intervalId) {
    clearInterval(intervalId);
  }
});

</script>
<template>
  <div>
    <!-- Step Progress Indicator -->
    <StepIndicator :steps="steps" :step-status="stepStatus" />

    <!-- Step 1: Personal Info Sub-steps -->
    <template v-if="stepCount === 1">
      <!-- Personal Info Form -->
      <Form
        v-slot="{ errorBag }"
        :validation-schema="getCurrentSchema()"
        :initial-values="getCurrentInitialValues()"
        @submit="handleStepSubmit"
        class="w-full mb-0"
      >
        <!-- Sub-step 1: Name -->
        <div v-if="subStepCount === 1" class="space-y-4">
          <AuthInput
            name="first_name"
            label="What is your first name?"
            placeholder="eg, Mohammed"
            variant="signup"
            :has-error="!!(errorBag?.first_name) || Errormsg.isShow"
          />
          <AuthInput
            name="last_name"
            label="What is your last name?"
            placeholder="eg, Omar"
            variant="signup"
            :has-error="!!(errorBag?.last_name) || Errormsg.isShow"
          />
        </div>

        <!-- Sub-step 2: Email -->
        <AuthInput
          v-if="subStepCount === 2"
          name="email"
          type="email"
          label="What is your Email?"
          placeholder="eg, <EMAIL>"
          variant="signup"
          :has-error="!!(errorBag?.email) || Errormsg.isShow"
        />

        <!-- Sub-step 3: Organization Role -->
        <AuthInput
          v-if="subStepCount === 3"
          name="organizationRole"
          label="What is your organization role?"
          placeholder="eg, Software Developer, Manager, etc."
          variant="signup"
          :has-error="!!(errorBag?.organizationRole) || Errormsg.isShow"
        />

        <!-- Submit Button -->
        <Button
          title="Next"
          type="submit"
          :loading="isSubmitting"
          :disabled="isSubmitting"
          theme="primary"
          extra-classes="w-full mt-4"
        />
      </Form>
    </template>

    <!-- Step 2: Setup Password -->
    <template v-if="stepCount === 2">
      <Form
        v-slot="{ errorBag }"
        :validation-schema="passwordSchema"
        :initial-values="getCurrentInitialValues()"
        @submit="handleStepSubmit"
        class="w-full mb-0 pt-2 space-y-4"
      >
        <!-- Password Fields -->
        <AuthInput
          name="password"
          type="password"
          label="Enter Password"
          placeholder="enter password"
          variant="signup"
          :has-error="!!(errorBag?.password) || Errormsg.isShow"
        />

        <AuthInput
          name="confirmPassword"
          type="password"
          label="Confirm Password"
          placeholder="reenter password"
          variant="signup"
          :has-error="!!(errorBag?.confirmPassword) || Errormsg.isShow"
        />

        <!-- Submit Button -->
        <Button
          title="Next"
          type="submit"
          :loading="isSubmitting"
          :disabled="isSubmitting"
          theme="primary"
          extra-classes="w-full mt-4"
        />
      </Form>
    </template>

    <!-- Step 3: Verification -->
    <template v-if="stepCount === 3">
      <div class="text-start pt-8">
        <!-- Pending Verification -->
        <div v-if="!isEmailVerified" class="space-y-4">
          <div>
            <h3 class="text-gray-900 text-2xl font-extrabold mb-2">
              You're Almost There!
            </h3>
            <p class="text-lg text-gray-900 font-medium">
              We've sent an invitation, Check your inbox to verify.
            </p>
          </div>

          <div class="mt-4">
            <button
              v-if="canResend"
              @click="sendVerificationEmail"
              class="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Resend verification email
            </button>
            <p v-else class="text-sm font-medium text-gray-500">
              Resend
              <span class="text-blue-700 text-sm font-medium">
                (00:{{ resendTimer }})
              </span>
            </p>
          </div>
        </div>

        <!-- Verification Success -->
        <div v-else class="space-y-4">
          <div>
            <h3 class="text-gray-900 text-2xl font-extrabold mb-2">
              Verified successfully!
            </h3>
            <p class="text-lg font-medium text-gray-900">
              Account Verified, Explore the Platforms
            </p>
          </div>

          <Button
            title="Explore the platform"
            theme="primary"
            extra-classes="w-full mt-4"
            @handleClick="handleExplorePlatform"
          />
        </div>
      </div>
    </template>
    <Button
          v-if="!(stepCount === 3 || (stepCount === 1 && subStepCount === 1))"
          title="Back"
          type="button"
          @handleClick="handleBackButton"
          theme="secondary"
          extra-classes="w-full mt-3"></Button>
    <!-- Global Error Message -->
    <div v-if="Errormsg.isShow" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
      <p class="text-sm text-red-600 flex items-center">
        <i class="fa fa-exclamation-circle mr-2" aria-hidden="true"></i>
        {{ Errormsg.message }}
      </p>
    </div>

    <!-- Google Sign In (Only on first step) -->
    <template v-if="stepCount === 1 && subStepCount === 1">
      <div class="flex items-center my-3">
        <div class="flex-grow h-px bg-gray-200"></div>
        <span class="px-3 text-sm text-gray-500">or</span>
        <div class="flex-grow h-px bg-gray-200"></div>
      </div>
        <slot name="googleSignIn"></slot>
    </template>

    <!-- Login Link -->
    <p v-if="!isEmailVerified" class="text-left mt-4">
      <span class="text-gray-900 text-sm">Already have an account?</span>
      <button
        @click="handleToggleForms()"
        class="text-blue-600 hover:text-blue-800 text-sm font-medium ml-1"
      >
        Login here
      </button>
    </p>
  </div>

</template>

<style scoped></style>
