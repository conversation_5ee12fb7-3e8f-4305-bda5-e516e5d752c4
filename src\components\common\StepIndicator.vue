<script setup>
const props = defineProps({
  steps: {
    type: Array,
    required: true,
    // Expected format: [{ key: 'personalInfo', label: 'Personal Info' }, ...]
  },
  stepStatus: {
    type: Object,
    required: true,
    // Expected format: { personalInfo: true, setupPassword: false, verify: false }
  },
});
</script>

<template>
  <div class="mb-6">
    <div class="flex items-center justify-between mb-4 flex-nowrap overflow-x-auto">
      <!-- Step indicators -->
      <template v-for="(step, index) in props.steps" :key="index">
        <div class="flex flex-col items-center">
          <!-- Step icon/number -->
          <span v-if="props.stepStatus[step.key]" class="text-blue-600">
            <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M19.467 8.39384L18.5754 7.50102C18.3867 7.31332 18.2834 7.06305 18.2834 6.79789V5.53464C18.2834 3.89201 16.9469 2.55527 15.3046 2.55527H14.0416C13.7804 2.55527 13.5242 2.449 13.3395 2.26428L12.4469 1.37147C11.2851 0.209512 9.39654 0.209512 8.23479 1.37147L7.34014 2.26428C7.15545 2.449 6.89927 2.55527 6.63813 2.55527H5.37509C3.73275 2.55527 2.39624 3.89201 2.39624 5.53464V6.79789C2.39624 7.06305 2.29298 7.31332 2.10531 7.50102L1.21265 8.39284C0.649644 8.95594 0.339844 9.70476 0.339844 10.5002C0.339844 11.2957 0.650637 12.0446 1.21265 12.6067L2.10432 13.4995C2.29298 13.6872 2.39624 13.9374 2.39624 14.2026V15.4659C2.39624 17.1085 3.73275 18.4452 5.37509 18.4452H6.63813C6.89927 18.4452 7.15545 18.5515 7.34014 18.7362L8.2328 19.63C8.81368 20.21 9.57627 20.5 10.3389 20.5C11.1014 20.5 11.864 20.21 12.4449 19.629L13.3376 18.7362C13.5242 18.5515 13.7804 18.4452 14.0416 18.4452H15.3046C16.9469 18.4452 18.2834 17.1085 18.2834 15.4659V14.2026C18.2834 13.9374 18.3867 13.6872 18.5754 13.4995L19.467 12.6077C20.0291 12.0446 20.3398 11.2967 20.3398 10.5002C20.3398 9.70376 20.03 8.95594 19.467 8.39384ZM14.8627 9.34028L8.90503 13.3128C8.73722 13.425 8.54459 13.4796 8.35394 13.4796C8.09776 13.4796 7.84357 13.3803 7.65193 13.1886L5.66603 11.2024C5.27779 10.8141 5.27779 10.1864 5.66603 9.79811C6.05427 9.4098 6.68182 9.4098 7.07006 9.79811L8.48005 11.2083L13.7606 7.68773C14.2183 7.38284 14.8339 7.50598 15.1378 7.96282C15.4426 8.41966 15.3195 9.03639 14.8627 9.34028Z"
                fill="#1A56DB" />
            </svg>
          </span>
          <span v-else class="text-sm font-medium text-gray-500">{{ index + 1 }}</span>

          <!-- Step label -->
          <span class="text-sm font-medium text-center mt-2"
            :class="props.stepStatus[step.key] ? 'text-blue-600' : 'text-gray-500'">
            {{ step.label }}
          </span>
        </div>

        <!-- Connector line (except after last step) -->
        <div v-if="index < steps.length - 1" class="mx-2 h-0.5 w-10 sm:w-auto sm:flex-1"
          :class="props.stepStatus[step.key] ? 'bg-blue-600' : 'bg-gray-200'">
        </div>
      </template>
    </div>
  </div>
</template>
