import {
  createNormalizer,
  mergeProps,
  snapshot,
  subscribe
} from "./chunk-5BVUQFI6.js";
import {
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  shallowRef,
  unref,
  watch,
  watchEffect
} from "./chunk-IJV5NOMV.js";
import "./chunk-LK32TJAX.js";

// node_modules/@zag-js/vue/dist/index.mjs
function toCase(txt) {
  return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
}
var propMap = {
  htmlFor: "for",
  className: "class",
  onDoubleClick: "onDblclick",
  onChange: "onInput",
  onFocus: "onFocusin",
  onBlur: "onFocusout",
  defaultValue: "value",
  defaultChecked: "checked"
};
function toVueProp(prop) {
  if (prop in propMap) return propMap[prop];
  if (prop.startsWith("on")) {
    return `on${toCase(prop.substr(2))}`;
  }
  return prop.toLowerCase();
}
var normalizeProps = createNormalizer((props) => {
  const normalized = {};
  for (const key in props) {
    const value = props[key];
    if (key === "children") {
      if (typeof value === "string") {
        normalized["innerHTML"] = value;
      } else if (value != null) {
        console.warn("[Vue Normalize Prop] : avoid passing non-primitive value as `children`");
      }
    } else {
      normalized[toVueProp(key)] = props[key];
    }
  }
  return normalized;
});
function useSnapshot(service, options) {
  const { actions, context } = options ?? {};
  const state = shallowRef(service.state);
  const unsubscribe = subscribe(service.state, () => {
    state.value = snapshot(service.state);
  });
  onUnmounted(() => {
    unsubscribe == null ? void 0 : unsubscribe();
  });
  watchEffect(() => {
    service.setOptions({ actions });
  });
  if (context) {
    watch(
      context,
      (ctx) => {
        service.setContext(unref(ctx));
      },
      { deep: true }
    );
  }
  return state;
}
function useActor(service) {
  const state = useSnapshot(service);
  return [state, service.send];
}
function useService(machine, options) {
  const { state: hydratedState, context } = options ?? {};
  const service = typeof machine === "function" ? machine() : machine;
  if (context) service.setContext(unref(context));
  service._created();
  onMounted(() => {
    service.start(hydratedState);
    onBeforeUnmount(() => {
      service.stop();
    });
  });
  return service;
}
function useMachine(machine, options) {
  const service = useService(machine, options);
  const state = useSnapshot(service, options);
  return [state, service.send, service];
}
export {
  mergeProps,
  normalizeProps,
  useActor,
  useMachine,
  useSnapshot
};
//# sourceMappingURL=@zag-js_vue.js.map
