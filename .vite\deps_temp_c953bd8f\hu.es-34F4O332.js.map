{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/hu.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"<PERSON><PERSON>t<PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"Csütörtök\",\n  \"Péntek\",\n  \"<PERSON><PERSON><PERSON>t\",\n  \"Vas<PERSON>rna<PERSON>\"\n];\nconst months = [\n  \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON>usz<PERSON>\",\n  \"Szeptember\",\n  \"Október\",\n  \"November\",\n  \"December\"\n];\nconst years = \"Évek\";\nconst year = \"Év\";\nconst month = \"Hónap\";\nconst week = \"Hét\";\nconst day = \"Nap\";\nconst today = \"Mai nap\";\nconst noEvent = \"Nincs esemény\";\nconst allDay = \"Egész nap\";\nconst deleteEvent = \"Esemény törlese\";\nconst createEvent = \"Esemény létrehozása\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst hu = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  hu as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}