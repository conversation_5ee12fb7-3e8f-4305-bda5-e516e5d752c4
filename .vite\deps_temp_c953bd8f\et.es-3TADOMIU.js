import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/et.es.js
var weekDays = [
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
];
var months = [
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "April<PERSON>",
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "August",
  "September",
  "Oktoober",
  "November",
  "Detsember"
];
var years = "Aastad";
var year = "Aasta";
var month = "Kuu";
var week = "Nädal";
var day = "Päev";
var today = "Täna";
var noEvent = "Sündmus puudub";
var allDay = "Terve päev";
var deleteEvent = "Kustuta";
var createEvent = "Loo sündmus";
var dateFormat = "dddd D MMMM YYYY";
var et = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  et as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/et.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=et.es-3TADOMIU.js.map
