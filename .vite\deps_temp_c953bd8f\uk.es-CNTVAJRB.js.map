{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/uk.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"Понеділок\",\n  \"Вівторок\",\n  \"Середа\",\n  \"Четвер\",\n  \"П'ятниця\",\n  \"Субота\",\n  \"Неділя\"\n];\nconst months = [\n  \"Січень\",\n  \"Лютий\",\n  \"Березень\",\n  \"Квітень\",\n  \"Травень\",\n  \"Червень\",\n  \"Липень\",\n  \"Серпень\",\n  \"Вересень\",\n  \"Жовтень\",\n  \"Листопад\",\n  \"Грудень\"\n];\nconst weekDaysShort = [\n  \"Пн\",\n  \"Вт\",\n  \"Ср\",\n  \"Чт\",\n  \"Пт\",\n  \"Сб\",\n  \"Нд\"\n];\nconst years = \"Роки\";\nconst year = \"Рік\";\nconst month = \"Місяць\";\nconst week = \"Тиждень\";\nconst day = \"День\";\nconst today = \"Сьогодні\";\nconst noEvent = \"Немає подій\";\nconst allDay = \"Весь день\";\nconst deleteEvent = \"Видалити\";\nconst createEvent = \"Створити подію\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst uk = {\n  weekDays,\n  months,\n  weekDaysShort,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  uk as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  weekDaysShort,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}