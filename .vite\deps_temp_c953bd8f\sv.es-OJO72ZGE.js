import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/sv.es.js
var weekDays = [
  "Måndag",
  "Tisdag",
  "Onsdag",
  "Torsdag",
  "Fredag",
  "Lördag",
  "Söndag"
];
var months = [
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "April",
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "August<PERSON>",
  "September",
  "Oktober",
  "November",
  "December"
];
var years = "År";
var year = "År";
var month = "Månad";
var week = "Vecka";
var day = "Dag";
var today = "Idag";
var noEvent = "Ingen händelse";
var allDay = "Heldag";
var deleteEvent = "Ta bort";
var createEvent = "Skapa händelse";
var dateFormat = "dddd den D MMMM YYYY";
var sv = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  sv as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/sv.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=sv.es-OJO72ZGE.js.map
