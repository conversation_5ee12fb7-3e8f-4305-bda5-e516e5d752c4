import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/zh-cn.es.js
var weekDays = [
  "星期一",
  "星期二",
  "星期三",
  "星期四",
  "星期五",
  "星期六",
  "星期日"
];
var weekDaysShort = [
  "一",
  "二",
  "三",
  "四",
  "五",
  "六",
  "日"
];
var months = [
  "一月",
  "二月",
  "三月",
  "四月",
  "五月",
  "六月",
  "七月",
  "八月",
  "九月",
  "十月",
  "十一月",
  "十二月"
];
var years = "年";
var year = "本年";
var month = "月";
var week = "周";
var day = "日";
var today = "今日";
var noEvent = "暂无活动";
var allDay = "整天";
var deleteEvent = "删除";
var createEvent = "新建活动";
var dateFormat = "YYYY MMMM D dddd";
var zhCn = {
  weekDays,
  weekDaysShort,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  zhCn as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  weekDaysShort,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/zh-cn.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=zh-cn.es-NNHWGFQ7.js.map
