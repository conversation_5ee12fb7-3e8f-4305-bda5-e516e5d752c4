{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/it.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"Lunedì\",\n  \"Martedì\",\n  \"Mercoledì\",\n  \"Giovedì\",\n  \"Venerdì\",\n  \"Sabato\",\n  \"Domenica\"\n];\nconst months = [\n  \"Gennai<PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"Ma<PERSON>\",\n  \"Giu<PERSON>\",\n  \"Luglio\",\n  \"Agosto\",\n  \"Settembre\",\n  \"Ottobre\",\n  \"Novembre\",\n  \"Dicembre\"\n];\nconst years = \"Anni\";\nconst year = \"Anno\";\nconst month = \"Mese\";\nconst week = \"Settimana\";\nconst day = \"Giorno\";\nconst today = \"Oggi\";\nconst noEvent = \"Nessun evento\";\nconst allDay = \"Tutto il giorno\";\nconst deleteEvent = \"Cancella\";\nconst createEvent = \"Crea evento\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst it = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  it as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}