import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/he.es.js
var weekDays = [
  "שני",
  "שלישי",
  "רביעי",
  "חמישי",
  "שישי",
  "שבת",
  "ראשו<PERSON>"
];
var months = [
  "ינואר",
  "פברואר",
  "מרץ",
  "אפריל",
  "מאי",
  "יוני",
  "יולי",
  "אוגוסט",
  "ספטמבר",
  "אוקטובר",
  "נובמבר",
  "דצמבר"
];
var years = "שנים";
var year = "שנה";
var month = "חודש";
var week = "שבוע";
var day = "יום";
var today = "היום";
var noEvent = "אין אירועים";
var allDay = "כל היום";
var deleteEvent = "מחיקה";
var createEvent = "צור אירוע";
var dateFormat = "dddd D MMMM YYYY";
var he = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  he as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/he.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=he.es-VEN6SYR3.js.map
