import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/vi.es.js
var weekDays = [
  "Thứ hai",
  "<PERSON><PERSON><PERSON> ba",
  "<PERSON><PERSON><PERSON> tư",
  "<PERSON><PERSON><PERSON> năm",
  "<PERSON><PERSON><PERSON> sáu",
  "<PERSON><PERSON><PERSON> bảy",
  "<PERSON><PERSON> nhật"
];
var weekDaysShort = [
  "T2",
  "T3",
  "T4",
  "T5",
  "T6",
  "T7",
  "CN"
];
var months = [
  "Tháng 1",
  "Tháng 2",
  "Tháng 3",
  "Tháng 4",
  "Tháng 5",
  "Tháng 6",
  "Tháng 7",
  "Tháng 8",
  "Tháng 9",
  "Tháng 10",
  "Tháng 11",
  "Tháng 12"
];
var years = "Năm";
var year = "Năm nay";
var month = "Tháng";
var week = "Tuần";
var day = "Ngày";
var today = "Hôm nay";
var noEvent = "NKhông có Event";
var allDay = "Cả ngày";
var deleteEvent = "Xóa";
var createEvent = "Tạo event";
var dateFormat = "dddd MMMM D YYYY";
var vi = {
  weekDays,
  weekDaysShort,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  vi as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  weekDaysShort,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/vi.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=vi.es-ZV3XVPQ2.js.map
