import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/el.es.js
var weekDays = [
  "Δευτέρα",
  "Τρίτη",
  "Τετάρτη",
  "Πέμπτη",
  "Παρα<PERSON>κευ<PERSON>",
  "Σάββατο",
  "Κυριακ<PERSON>"
];
var months = [
  "Ιανουάριος",
  "Φεβρουάριος",
  "Μάρτι<PERSON>",
  "Απρίλιος",
  "Μάιος",
  "Ιούνιος",
  "Ιούλιος",
  "Αύγουστος",
  "Σεπτέμβριος",
  "Οκτώβριος",
  "Νοέμβριος",
  "Δεκέμβριος"
];
var monthsGenitive = [
  "Ιανουαρίου",
  "Φεβρουαρίου",
  "Μαρτίου",
  "Απριλίου",
  "Μαΐου",
  "Ιουνίου",
  "Ιουλίου",
  "Αυγούστου",
  "Σεπτεμβρίου",
  "Οκτωβρίου",
  "Νοεμβρίου",
  "Δεκεμβρίου"
];
var years = "Έτη";
var year = "Έτος";
var month = "Μήνα";
var week = "Εβδομάδα";
var day = "Ημέρα";
var today = "Σήμερα";
var noEvent = "Κανένα συμβάν";
var allDay = "Ημερήσιο συμβάν";
var deleteEvent = "Διαγραφή";
var createEvent = "Δημιουργία συμβάντος";
var dateFormat = "dddd D MMMMG YYYY";
var am = "π.μ.";
var pm = "μ.μ.";
var el = {
  weekDays,
  months,
  monthsGenitive,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat,
  am,
  pm
};
export {
  allDay,
  am,
  createEvent,
  dateFormat,
  day,
  el as default,
  deleteEvent,
  month,
  months,
  monthsGenitive,
  noEvent,
  pm,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/el.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=el.es-3IWAD5SM.js.map
