import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/da.es.js
var weekDays = [
  "Mandag",
  "Tirsdag",
  "Onsdag",
  "Torsdag",
  "Fredag",
  "Lørdag",
  "Søndag"
];
var months = [
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "April",
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "August",
  "September",
  "Oktober",
  "November",
  "December"
];
var years = "År (flertal)";
var year = "År";
var month = "Måned";
var week = "Uge";
var day = "Dag";
var today = "I dag";
var noEvent = "Ingen begivenhed";
var allDay = "Hele dagen";
var deleteEvent = "Slet";
var createEvent = "Opret et event";
var dateFormat = "dddd D MMMM YYYY";
var da = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  da as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/da.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=da.es-FAVMWYZQ.js.map
