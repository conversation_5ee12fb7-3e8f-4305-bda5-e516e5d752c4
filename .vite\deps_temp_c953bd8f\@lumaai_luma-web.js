import {
  AddEquation,
  BackSide,
  Box3,
  BufferAttribute,
  BufferGeometry,
  ClampToEdgeWrapping,
  CubeCamera,
  CubeTexture,
  CustomBlending,
  DataTexture,
  FrontSide,
  GLBufferAttribute,
  HalfFloatType,
  InstancedBufferAttribute,
  InstancedBufferGeometry,
  IntType,
  LinearMipmapLinearFilter,
  Matrix4,
  Mesh,
  MeshNormalMaterial,
  NearestFilter,
  NoBlending,
  NoColorSpace,
  OneFactor,
  OneMinusSrcAlphaFactor,
  PlaneGeometry,
  Quaternion,
  RGBAIntegerFormat,
  RGIntegerFormat,
  RawShaderMaterial,
  Scene,
  ShaderChunk,
  ShaderMaterial,
  Sphere,
  StaticDrawUsage,
  UVMapping,
  Uniform,
  UniformsLib,
  UnsignedByteType,
  UnsignedIntType,
  Vector2,
  Vector3,
  Vector4,
  WebGLCubeRenderTarget
} from "./chunk-ABWEC5N4.js";
import "./chunk-LK32TJAX.js";

// node_modules/@lumaai/luma-web/dist/library/luma-web.module.js
var F = { GIT_HASH: "25edf7d", DEBUG: false };
var YA = 'var iB=Object.create;var HA=Object.defineProperty;var oB=Object.getOwnPropertyDescriptor;var tB=Object.getOwnPropertyNames;var aB=Object.getPrototypeOf,DB=Object.prototype.hasOwnProperty;var cB=(o,t)=>()=>(o&&(t=o(o=0)),t);var OB=(o,t)=>()=>(t||o((t={exports:{}}).exports,t),t.exports);var sB=(o,t,I,T)=>{if(t&&typeof t=="object"||typeof t=="function")for(let c of tB(t))!DB.call(o,c)&&c!==I&&HA(o,c,{get:()=>t[c],enumerable:!(T=oB(t,c))||T.enumerable});return o};var NB=(o,t,I)=>(I=o!=null?iB(aB(o)):{},sB(t||!o||!o.__esModule?HA(I,"default",{value:o,enumerable:!0}):I,o));var J=cB(()=>{});var zA=OB((kA,rA)=>{J();var pA=(()=>{var o=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0;return function(t={}){var I=t,T,c;I.ready=new Promise((A,g)=>{T=A,c=g});var v=Object.assign({},I),m=[],_="./this.program",S=(A,g)=>{throw g},z=!0,L=!1,O="";function mA(A){return I.locateFile?I.locateFile(A,O):O+A}var SA,LA,x;(z||L)&&(L?O=self.location.href:typeof document<"u"&&document.currentScript&&(O=document.currentScript.src),o&&(O=o),O.indexOf("blob:")!==0?O=O.substr(0,O.replace(/[?#].*/,"").lastIndexOf("/")+1):O="",SA=A=>{var g=new XMLHttpRequest;return g.open("GET",A,!1),g.send(null),g.responseText},L&&(x=A=>{var g=new XMLHttpRequest;return g.open("GET",A,!1),g.responseType="arraybuffer",g.send(null),new Uint8Array(g.response)}),LA=(A,g,B)=>{var C=new XMLHttpRequest;C.open("GET",A,!0),C.responseType="arraybuffer",C.onload=()=>{if(C.status==200||C.status==0&&C.response){g(C.response);return}B()},C.onerror=B,C.send(null)});var dB=I.print||console.log.bind(console),$=I.printErr||console.error.bind(console);Object.assign(I,v),v=null,I.arguments&&(m=I.arguments),I.thisProgram&&(_=I.thisProgram),I.quit&&(S=I.quit);var W;I.wasmBinary&&(W=I.wasmBinary),typeof WebAssembly!="object"&&X("no native wasm support detected");function WA(A){for(var g=atob(A),B=new Uint8Array(g.length),C=0;C<g.length;++C)B[C]=g.charCodeAt(C);return B}function ZA(A){if(wA(A))return WA(A.slice(NA.length))}var oA,tA=!1,XA,AA,s,b,Z,R,d,aA,DA;function VA(){var A=oA.buffer;I.HEAP8=AA=new Int8Array(A),I.HEAP16=b=new Int16Array(A),I.HEAPU8=s=new Uint8Array(A),I.HEAPU16=Z=new Uint16Array(A),I.HEAP32=R=new Int32Array(A),I.HEAPU32=d=new Uint32Array(A),I.HEAPF32=aA=new Float32Array(A),I.HEAPF64=DA=new Float64Array(A)}var cA=[],OA=[],sA=[],PA=!1;function qA(){if(I.preRun)for(typeof I.preRun=="function"&&(I.preRun=[I.preRun]);I.preRun.length;)$A(I.preRun.shift());BA(cA)}function _A(){PA=!0,BA(OA)}function xA(){if(I.postRun)for(typeof I.postRun=="function"&&(I.postRun=[I.postRun]);I.postRun.length;)gg(I.postRun.shift());BA(sA)}function $A(A){cA.unshift(A)}function Ag(A){OA.unshift(A)}function gg(A){sA.unshift(A)}var y=0,gA=null,K=null;function Bg(A){y++,I.monitorRunDependencies&&I.monitorRunDependencies(y)}function Cg(A){if(y--,I.monitorRunDependencies&&I.monitorRunDependencies(y),y==0&&(gA!==null&&(clearInterval(gA),gA=null),K)){var g=K;K=null,g()}}function X(A){I.onAbort&&I.onAbort(A),A="Aborted("+A+")",$(A),tA=!0,XA=1,A+=". Build with -sASSERTIONS for more info.";var g=new WebAssembly.RuntimeError(A);throw c(g),g}var NA="data:application/octet-stream;base64,",wA=A=>A.startsWith(NA),U;U="data:application/octet-stream;base64,AGFzbQEAAAABZw5gBH9/f38AYAF/AX9gA39/fwBgBX9/f39/AGACf38AYAF/AGAAAGAGf39/f39/AGACf38Bf2ADf39/AX9gB39/f39/f38AYAl/fX19fX19f30AYAR/f35+AGAJf319fX19fX99AX8CYRABYQFhAAIBYQFiAAMBYQFjAAoBYQFkAAIBYQFlAAQBYQFmAAIBYQFnAAUBYQFoAAUBYQFpAAgBYQFqAAABYQFrAAQBYQFsAAoBYQFtAAYBYQFuAAEBYQFvAAIBYQFwAAQDHRwJAQEIBQEGBgwAAgUFAQ0IBgcHAwMAAAsJCQEEBAUBcAEVFQUGAQHgEuASBggBfwFBkOMECwcZBgFxAgABcgAWAXMBAAF0AB0BdQAVAXYAHAkaAQBBAQsUHyseJyAXKhQbGykUKCEjJhQiJCUKqVwcdAEBfyACRQRAIAAoAgQgASgCBEYPCyAAIAFGBEBBAQ8LIAEoAgQiAi0AACEBAkAgACgCBCIDLQAAIgBFDQAgACABRw0AA0AgAi0AASEBIAMtAAEiAEUNASACQQFqIQIgA0EBaiEDIAAgAUYNAAsLIAAgAUYLUgECf0Go3QAoAgAiASAAQQdqQXhxIgJqIQACQCACQQAgACABTRsNACAAPwBBEHRLBEAgABANRQ0BC0Go3QAgADYCACABDwtBkN8AQTA2AgBBfws2AQF/QQEgACAAQQFNGyEAAkADQCAAEBUiAQ0BQYTjACgCACIBBEAgAREGAAwBCwsQDAALIAEL2AIBAn8CQCABRQ0AIABBADoAACAAIAFqIgJBAWtBADoAACABQQNJDQAgAEEAOgACIABBADoAASACQQNrQQA6AAAgAkECa0EAOgAAIAFBB0kNACAAQQA6AAMgAkEEa0EAOgAAIAFBCUkNACAAQQAgAGtBA3EiA2oiAkEANgIAIAIgASADa0F8cSIDaiIBQQRrQQA2AgAgA0EJSQ0AIAJBADYCCCACQQA2AgQgAUEIa0EANgIAIAFBDGtBADYCACADQRlJDQAgAkEANgIYIAJBADYCFCACQQA2AhAgAkEANgIMIAFBEGtBADYCACABQRRrQQA2AgAgAUEYa0EANgIAIAFBHGtBADYCACADIAJBBHFBGHIiA2siAUEgSQ0AIAIgA2ohAgNAIAJCADcDGCACQgA3AxAgAkIANwMIIAJCADcDACACQSBqIQIgAUEgayIBQR9LDQALCyAACwYAIAAQHAvSKAEMfyMAQRBrIgokAAJAAkACQAJAAkACQAJAAkACQCAAQfQBTQRAQZTfACgCACIGQRAgAEELakF4cSAAQQtJGyIFQQN2IgB2IgFBA3EEQAJAIAFBf3NBAXEgAGoiAkEDdCIBQbzfAGoiACABQcTfAGooAgAiASgCCCIDRgRAQZTfACAGQX4gAndxNgIADAELIAMgADYCDCAAIAM2AggLIAFBCGohACABIAJBA3QiAkEDcjYCBCABIAJqIgEgASgCBEEBcjYCBAwKCyAFQZzfACgCACIHTQ0BIAEEQAJAQQIgAHQiAkEAIAJrciABIAB0cWgiAUEDdCIAQbzfAGoiAiAAQcTfAGooAgAiACgCCCIDRgRAQZTfACAGQX4gAXdxIgY2AgAMAQsgAyACNgIMIAIgAzYCCAsgACAFQQNyNgIEIAAgBWoiBCABQQN0IgEgBWsiA0EBcjYCBCAAIAFqIAM2AgAgBwRAIAdBeHFBvN8AaiEBQajfACgCACECAn8gBkEBIAdBA3Z0IgVxRQRAQZTfACAFIAZyNgIAIAEMAQsgASgCCAshBSABIAI2AgggBSACNgIMIAIgATYCDCACIAU2AggLIABBCGohAEGo3wAgBDYCAEGc3wAgAzYCAAwKC0GY3wAoAgAiC0UNASALaEECdEHE4QBqKAIAIgIoAgRBeHEgBWshBCACIQEDQAJAIAEoAhAiAEUEQCABKAIUIgBFDQELIAAoAgRBeHEgBWsiASAEIAEgBEkiARshBCAAIAIgARshAiAAIQEMAQsLIAIoAhghCSACIAIoAgwiA0cEQEGk3wAoAgAaIAIoAggiACADNgIMIAMgADYCCAwJCyACQRRqIgEoAgAiAEUEQCACKAIQIgBFDQMgAkEQaiEBCwNAIAEhCCAAIgNBFGoiASgCACIADQAgA0EQaiEBIAMoAhAiAA0ACyAIQQA2AgAMCAtBfyEFIABBv39LDQAgAEELaiIAQXhxIQVBmN8AKAIAIghFDQBBACAFayEEAkACQAJAAn9BACAFQYACSQ0AGkEfIAVB////B0sNABogBUEmIABBCHZnIgBrdkEBcSAAQQF0a0E+agsiB0ECdEHE4QBqKAIAIgFFBEBBACEADAELQQAhACAFQRkgB0EBdmtBACAHQR9HG3QhAgNAAkAgASgCBEF4cSAFayIGIARPDQAgASEDIAYiBA0AQQAhBCABIQAMAwsgACABKAIUIgYgBiABIAJBHXZBBHFqKAIQIgFGGyAAIAYbIQAgAkEBdCECIAENAAsLIAAgA3JFBEBBACEDQQIgB3QiAEEAIABrciAIcSIARQ0DIABoQQJ0QcThAGooAgAhAAsgAEUNAQsDQCAAKAIEQXhxIAVrIgIgBEkhASACIAQgARshBCAAIAMgARshAyAAKAIQIgEEfyABBSAAKAIUCyIADQALCyADRQ0AIARBnN8AKAIAIAVrTw0AIAMoAhghByADIAMoAgwiAkcEQEGk3wAoAgAaIAMoAggiACACNgIMIAIgADYCCAwHCyADQRRqIgEoAgAiAEUEQCADKAIQIgBFDQMgA0EQaiEBCwNAIAEhBiAAIgJBFGoiASgCACIADQAgAkEQaiEBIAIoAhAiAA0ACyAGQQA2AgAMBgsgBUGc3wAoAgAiA00EQEGo3wAoAgAhAAJAIAMgBWsiAUEQTwRAIAAgBWoiAiABQQFyNgIEIAAgA2ogATYCACAAIAVBA3I2AgQMAQsgACADQQNyNgIEIAAgA2oiASABKAIEQQFyNgIEQQAhAkEAIQELQZzfACABNgIAQajfACACNgIAIABBCGohAAwICyAFQaDfACgCACICSQRAQaDfACACIAVrIgE2AgBBrN8AQazfACgCACIAIAVqIgI2AgAgAiABQQFyNgIEIAAgBUEDcjYCBCAAQQhqIQAMCAtBACEAIAVBL2oiBAJ/QeziACgCAARAQfTiACgCAAwBC0H44gBCfzcCAEHw4gBCgKCAgICABDcCAEHs4gAgCkEMakFwcUHYqtWqBXM2AgBBgOMAQQA2AgBB0OIAQQA2AgBBgCALIgFqIgZBACABayIIcSIBIAVNDQdBzOIAKAIAIgMEQEHE4gAoAgAiByABaiIJIAdNDQggAyAJSQ0ICwJAQdDiAC0AAEEEcUUEQAJAAkACQAJAQazfACgCACIDBEBB1OIAIQADQCADIAAoAgAiB08EQCAHIAAoAgRqIANLDQMLIAAoAggiAA0ACwtBABARIgJBf0YNAyABIQZB8OIAKAIAIgBBAWsiAyACcQRAIAEgAmsgAiADakEAIABrcWohBgsgBSAGTw0DQcziACgCACIABEBBxOIAKAIAIgMgBmoiCCADTQ0EIAAgCEkNBAsgBhARIgAgAkcNAQwFCyAGIAJrIAhxIgYQESICIAAoAgAgACgCBGpGDQEgAiEACyAAQX9GDQEgBUEwaiAGTQRAIAAhAgwEC0H04gAoAgAiAiAEIAZrakEAIAJrcSICEBFBf0YNASACIAZqIQYgACECDAMLIAJBf0cNAgtB0OIAQdDiACgCAEEEcjYCAAsgARARIQJBABARIQAgAkF/Rg0FIABBf0YNBSAAIAJNDQUgACACayIGIAVBKGpNDQULQcTiAEHE4gAoAgAgBmoiADYCAEHI4gAoAgAgAEkEQEHI4gAgADYCAAsCQEGs3wAoAgAiBARAQdTiACEAA0AgAiAAKAIAIgEgACgCBCIDakYNAiAAKAIIIgANAAsMBAtBpN8AKAIAIgBBACAAIAJNG0UEQEGk3wAgAjYCAAtBACEAQdjiACAGNgIAQdTiACACNgIAQbTfAEF/NgIAQbjfAEHs4gAoAgA2AgBB4OIAQQA2AgADQCAAQQN0IgFBxN8AaiABQbzfAGoiAzYCACABQcjfAGogAzYCACAAQQFqIgBBIEcNAAtBoN8AIAZBKGsiAEF4IAJrQQdxIgFrIgM2AgBBrN8AIAEgAmoiATYCACABIANBAXI2AgQgACACakEoNgIEQbDfAEH84gAoAgA2AgAMBAsgAiAETQ0CIAEgBEsNAiAAKAIMQQhxDQIgACADIAZqNgIEQazfACAEQXggBGtBB3EiAGoiATYCAEGg3wBBoN8AKAIAIAZqIgIgAGsiADYCACABIABBAXI2AgQgAiAEakEoNgIEQbDfAEH84gAoAgA2AgAMAwtBACEDDAULQQAhAgwDC0Gk3wAoAgAgAksEQEGk3wAgAjYCAAsgAiAGaiEBQdTiACEAAkACQAJAA0AgASAAKAIARwRAIAAoAggiAA0BDAILCyAALQAMQQhxRQ0BC0HU4gAhAANAAkAgBCAAKAIAIgFPBEAgASAAKAIEaiIDIARLDQELIAAoAgghAAwBCwtBoN8AIAZBKGsiAEF4IAJrQQdxIgFrIgg2AgBBrN8AIAEgAmoiATYCACABIAhBAXI2AgQgACACakEoNgIEQbDfAEH84gAoAgA2AgAgBCADQScgA2tBB3FqQS9rIgAgACAEQRBqSRsiAUEbNgIEIAFB3OIAKQIANwIQIAFB1OIAKQIANwIIQdziACABQQhqNgIAQdjiACAGNgIAQdTiACACNgIAQeDiAEEANgIAIAFBGGohAANAIABBBzYCBCAAQQhqIQwgAEEEaiEAIAwgA0kNAAsgASAERg0CIAEgASgCBEF+cTYCBCAEIAEgBGsiAkEBcjYCBCABIAI2AgAgAkH/AU0EQCACQXhxQbzfAGohAAJ/QZTfACgCACIBQQEgAkEDdnQiAnFFBEBBlN8AIAEgAnI2AgAgAAwBCyAAKAIICyEBIAAgBDYCCCABIAQ2AgwgBCAANgIMIAQgATYCCAwDC0EfIQAgAkH///8HTQRAIAJBJiACQQh2ZyIAa3ZBAXEgAEEBdGtBPmohAAsgBCAANgIcIARCADcCECAAQQJ0QcThAGohAQJAQZjfACgCACIDQQEgAHQiBnFFBEBBmN8AIAMgBnI2AgAgASAENgIADAELIAJBGSAAQQF2a0EAIABBH0cbdCEAIAEoAgAhAwNAIAMiASgCBEF4cSACRg0DIABBHXYhAyAAQQF0IQAgASADQQRxaiIGKAIQIgMNAAsgBiAENgIQCyAEIAE2AhggBCAENgIMIAQgBDYCCAwCCyAAIAI2AgAgACAAKAIEIAZqNgIEIAJBeCACa0EHcWoiByAFQQNyNgIEIAFBeCABa0EHcWoiBCAFIAdqIgVrIQYCQEGs3wAoAgAgBEYEQEGs3wAgBTYCAEGg3wBBoN8AKAIAIAZqIgA2AgAgBSAAQQFyNgIEDAELQajfACgCACAERgRAQajfACAFNgIAQZzfAEGc3wAoAgAgBmoiADYCACAFIABBAXI2AgQgACAFaiAANgIADAELIAQoAgQiAkEDcUEBRgRAIAJBeHEhCQJAIAJB/wFNBEAgBCgCDCIAIAQoAggiAUYEQEGU3wBBlN8AKAIAQX4gAkEDdndxNgIADAILIAEgADYCDCAAIAE2AggMAQsgBCgCGCEIAkAgBCAEKAIMIgBHBEBBpN8AKAIAGiAEKAIIIgEgADYCDCAAIAE2AggMAQsCQCAEQRRqIgEoAgAiAkUEQCAEKAIQIgJFDQEgBEEQaiEBCwNAIAEhAyACIgBBFGoiASgCACICDQAgAEEQaiEBIAAoAhAiAg0ACyADQQA2AgAMAQtBACEACyAIRQ0AAkAgBCgCHCIBQQJ0QcThAGoiAigCACAERgRAIAIgADYCACAADQFBmN8AQZjfACgCAEF+IAF3cTYCAAwCCyAIQRBBFCAIKAIQIARGG2ogADYCACAARQ0BCyAAIAg2AhggBCgCECIBBEAgACABNgIQIAEgADYCGAsgBCgCFCIBRQ0AIAAgATYCFCABIAA2AhgLIAYgCWohBiAEIAlqIgQoAgQhAgsgBCACQX5xNgIEIAUgBkEBcjYCBCAFIAZqIAY2AgAgBkH/AU0EQCAGQXhxQbzfAGohAAJ/QZTfACgCACIBQQEgBkEDdnQiAnFFBEBBlN8AIAEgAnI2AgAgAAwBCyAAKAIICyEBIAAgBTYCCCABIAU2AgwgBSAANgIMIAUgATYCCAwBC0EfIQIgBkH///8HTQRAIAZBJiAGQQh2ZyIAa3ZBAXEgAEEBdGtBPmohAgsgBSACNgIcIAVCADcCECACQQJ0QcThAGohAQJAAkBBmN8AKAIAIgBBASACdCIDcUUEQEGY3wAgACADcjYCACABIAU2AgAMAQsgBkEZIAJBAXZrQQAgAkEfRxt0IQIgASgCACEAA0AgACIBKAIEQXhxIAZGDQIgAkEddiEAIAJBAXQhAiABIABBBHFqIgMoAhAiAA0ACyADIAU2AhALIAUgATYCGCAFIAU2AgwgBSAFNgIIDAELIAEoAggiACAFNgIMIAEgBTYCCCAFQQA2AhggBSABNgIMIAUgADYCCAsgB0EIaiEADAULIAEoAggiACAENgIMIAEgBDYCCCAEQQA2AhggBCABNgIMIAQgADYCCAtBoN8AKAIAIgAgBU0NAEGg3wAgACAFayIBNgIAQazfAEGs3wAoAgAiACAFaiICNgIAIAIgAUEBcjYCBCAAIAVBA3I2AgQgAEEIaiEADAMLQZDfAEEwNgIAQQAhAAwCCwJAIAdFDQACQCADKAIcIgBBAnRBxOEAaiIBKAIAIANGBEAgASACNgIAIAINAUGY3wAgCEF+IAB3cSIINgIADAILIAdBEEEUIAcoAhAgA0YbaiACNgIAIAJFDQELIAIgBzYCGCADKAIQIgAEQCACIAA2AhAgACACNgIYCyADKAIUIgBFDQAgAiAANgIUIAAgAjYCGAsCQCAEQQ9NBEAgAyAEIAVqIgBBA3I2AgQgACADaiIAIAAoAgRBAXI2AgQMAQsgAyAFQQNyNgIEIAMgBWoiAiAEQQFyNgIEIAIgBGogBDYCACAEQf8BTQRAIARBeHFBvN8AaiEAAn9BlN8AKAIAIgFBASAEQQN2dCIFcUUEQEGU3wAgASAFcjYCACAADAELIAAoAggLIQEgACACNgIIIAEgAjYCDCACIAA2AgwgAiABNgIIDAELQR8hACAEQf///wdNBEAgBEEmIARBCHZnIgBrdkEBcSAAQQF0a0E+aiEACyACIAA2AhwgAkIANwIQIABBAnRBxOEAaiEBAkACQCAIQQEgAHQiBXFFBEBBmN8AIAUgCHI2AgAgASACNgIADAELIARBGSAAQQF2a0EAIABBH0cbdCEAIAEoAgAhBQNAIAUiASgCBEF4cSAERg0CIABBHXYhBSAAQQF0IQAgASAFQQRxaiIGKAIQIgUNAAsgBiACNgIQCyACIAE2AhggAiACNgIMIAIgAjYCCAwBCyABKAIIIgAgAjYCDCABIAI2AgggAkEANgIYIAIgATYCDCACIAA2AggLIANBCGohAAwBCwJAIAlFDQACQCACKAIcIgBBAnRBxOEAaiIBKAIAIAJGBEAgASADNgIAIAMNAUGY3wAgC0F+IAB3cTYCAAwCCyAJQRBBFCAJKAIQIAJGG2ogAzYCACADRQ0BCyADIAk2AhggAigCECIABEAgAyAANgIQIAAgAzYCGAsgAigCFCIARQ0AIAMgADYCFCAAIAM2AhgLAkAgBEEPTQRAIAIgBCAFaiIAQQNyNgIEIAAgAmoiACAAKAIEQQFyNgIEDAELIAIgBUEDcjYCBCACIAVqIgMgBEEBcjYCBCADIARqIAQ2AgAgBwRAIAdBeHFBvN8AaiEAQajfACgCACEBAn9BASAHQQN2dCIFIAZxRQRAQZTfACAFIAZyNgIAIAAMAQsgACgCCAshBSAAIAE2AgggBSABNgIMIAEgADYCDCABIAU2AggLQajfACADNgIAQZzfACAENgIACyACQQhqIQALIApBEGokACAAC5oBAEHA3QBBBTYCAEHE3QBBADYCAEGhywBBAkHM0QBB8NEAQQFBAkEAEAJBgMsAQQlBgNIAQaTSAEEDQQRBABACQcTdAEHI3QAoAgA2AgBByN0AQcDdADYCAEHM3QBBBjYCAEHQ3QBBADYCABAXQdDdAEHI3QAoAgA2AgBByN0AQczdADYCAEHs3gBB9N0ANgIAQaTeAEEqNgIAC7EEAEHc2gBBpMwAEApB6NoAQcPLAEEBQQAQCUH02gBBvssAQQFBgH9B/wAQAUGM2wBBt8sAQQFBgH9B/wAQAUGA2wBBtcsAQQFBAEH/ARABQZjbAEGOywBBAkGAgH5B//8BEAFBpNsAQYXLAEECQQBB//8DEAFBsNsAQZ3LAEEEQYCAgIB4Qf////8HEAFBvNsAQZTLAEEEQQBBfxABQcjbAEHhywBBBEGAgICAeEH/////BxABQdTbAEHYywBBBEEAQX8QAUHg2wBBrcsAQoCAgICAgICAgH9C////////////ABAYQezbAEGsywBCAEJ/EBhB+NsAQabLAEEEEAVBhNwAQZ3MAEEIEAVBwNMAQfPLABAEQYjUAEGh0AAQBEHQ1ABBBEHmywAQA0Gc1QBBAkH/ywAQA0Ho1QBBBEGOzAAQA0Ho0QBByMsAEA9BkNYAQQBB3M8AEABBuNYAQQBBwtAAEABB4NYAQQFB+s8AEABBiNcAQQJBqcwAEABB0NIAQQNByMwAEABBsNcAQQRB8MwAEABB+NIAQQVBjc0AEABB2NcAQQRB59AAEABBgNgAQQVBhdEAEABBuNYAQQBB880AEABB4NYAQQFB0s0AEABBiNcAQQJBtc4AEABB0NIAQQNBk84AEABBsNcAQQRBu88AEABB+NIAQQVBmc8AEABBqNgAQQhB+M4AEABB0NgAQQlB1s4AEABB+NgAQQZBs80AEABBoNkAQQdBrNEAEAALHAAgACABQQggAqcgAkIgiKcgA6cgA0IgiKcQCwuaAQAgAEEBOgA1AkAgACgCBCACRw0AIABBAToANAJAIAAoAhAiAkUEQCAAQQE2AiQgACADNgIYIAAgATYCECADQQFHDQIgACgCMEEBRg0BDAILIAEgAkYEQCAAKAIYIgJBAkYEQCAAIAM2AhggAyECCyAAKAIwQQFHDQIgAkEBRg0BDAILIAAgACgCJEEBajYCJAsgAEEBOgA2CwtdAQF/IAAoAhAiA0UEQCAAQQE2AiQgACACNgIYIAAgATYCEA8LAkAgASADRgRAIAAoAhhBAkcNASAAIAI2AhgPCyAAQQE6ADYgAEECNgIYIAAgACgCJEEBajYCJAsLAgAL9QsBB38CQCAARQ0AIABBCGsiAiAAQQRrKAIAIgFBeHEiAGohBQJAIAFBAXENACABQQNxRQ0BIAIgAigCACIBayICQaTfACgCAEkNASAAIAFqIQACQAJAQajfACgCACACRwRAIAFB/wFNBEAgAUEDdiEEIAIoAgwiASACKAIIIgNGBEBBlN8AQZTfACgCAEF+IAR3cTYCAAwFCyADIAE2AgwgASADNgIIDAQLIAIoAhghBiACIAIoAgwiAUcEQCACKAIIIgMgATYCDCABIAM2AggMAwsgAkEUaiIEKAIAIgNFBEAgAigCECIDRQ0CIAJBEGohBAsDQCAEIQcgAyIBQRRqIgQoAgAiAw0AIAFBEGohBCABKAIQIgMNAAsgB0EANgIADAILIAUoAgQiAUEDcUEDRw0CQZzfACAANgIAIAUgAUF+cTYCBCACIABBAXI2AgQgBSAANgIADwtBACEBCyAGRQ0AAkAgAigCHCIDQQJ0QcThAGoiBCgCACACRgRAIAQgATYCACABDQFBmN8AQZjfACgCAEF+IAN3cTYCAAwCCyAGQRBBFCAGKAIQIAJGG2ogATYCACABRQ0BCyABIAY2AhggAigCECIDBEAgASADNgIQIAMgATYCGAsgAigCFCIDRQ0AIAEgAzYCFCADIAE2AhgLIAIgBU8NACAFKAIEIgFBAXFFDQACQAJAAkACQCABQQJxRQRAQazfACgCACAFRgRAQazfACACNgIAQaDfAEGg3wAoAgAgAGoiADYCACACIABBAXI2AgQgAkGo3wAoAgBHDQZBnN8AQQA2AgBBqN8AQQA2AgAPC0Go3wAoAgAgBUYEQEGo3wAgAjYCAEGc3wBBnN8AKAIAIABqIgA2AgAgAiAAQQFyNgIEIAAgAmogADYCAA8LIAFBeHEgAGohACABQf8BTQRAIAFBA3YhBCAFKAIMIgEgBSgCCCIDRgRAQZTfAEGU3wAoAgBBfiAEd3E2AgAMBQsgAyABNgIMIAEgAzYCCAwECyAFKAIYIQYgBSAFKAIMIgFHBEBBpN8AKAIAGiAFKAIIIgMgATYCDCABIAM2AggMAwsgBUEUaiIEKAIAIgNFBEAgBSgCECIDRQ0CIAVBEGohBAsDQCAEIQcgAyIBQRRqIgQoAgAiAw0AIAFBEGohBCABKAIQIgMNAAsgB0EANgIADAILIAUgAUF+cTYCBCACIABBAXI2AgQgACACaiAANgIADAMLQQAhAQsgBkUNAAJAIAUoAhwiA0ECdEHE4QBqIgQoAgAgBUYEQCAEIAE2AgAgAQ0BQZjfAEGY3wAoAgBBfiADd3E2AgAMAgsgBkEQQRQgBigCECAFRhtqIAE2AgAgAUUNAQsgASAGNgIYIAUoAhAiAwRAIAEgAzYCECADIAE2AhgLIAUoAhQiA0UNACABIAM2AhQgAyABNgIYCyACIABBAXI2AgQgACACaiAANgIAIAJBqN8AKAIARw0AQZzfACAANgIADwsgAEH/AU0EQCAAQXhxQbzfAGohAQJ/QZTfACgCACIDQQEgAEEDdnQiAHFFBEBBlN8AIAAgA3I2AgAgAQwBCyABKAIICyEAIAEgAjYCCCAAIAI2AgwgAiABNgIMIAIgADYCCA8LQR8hAyAAQf///wdNBEAgAEEmIABBCHZnIgFrdkEBcSABQQF0a0E+aiEDCyACIAM2AhwgAkIANwIQIANBAnRBxOEAaiEBAkACQAJAQZjfACgCACIEQQEgA3QiB3FFBEBBmN8AIAQgB3I2AgAgASACNgIAIAIgATYCGAwBCyAAQRkgA0EBdmtBACADQR9HG3QhAyABKAIAIQEDQCABIgQoAgRBeHEgAEYNAiADQR12IQEgA0EBdCEDIAQgAUEEcWoiB0EQaigCACIBDQALIAcgAjYCECACIAQ2AhgLIAIgAjYCDCACIAI2AggMAQsgBCgCCCIAIAI2AgwgBCACNgIIIAJBADYCGCACIAQ2AgwgAiAANgIIC0G03wBBtN8AKAIAQQFrIgBBfyAAGzYCAAsLkAUBBX8CfwJAAkAgACgCBCICIgFBA3FFDQBBACABLQAARQ0CGgNAIAFBAWoiAUEDcUUNASABLQAADQALDAELA0AgASIAQQRqIQEgACgCACIEQX9zIARBgYKECGtxQYCBgoR4cUUNAAsDQCAAIgFBAWohACABLQAADQALCyABIAJrC0EBaiIBEBUiAAR/An8gAUGABE8EQCAAIAIgARAOIAAMAQsgACABaiEDAkAgACACc0EDcUUEQAJAIABBA3FFBEAgACEBDAELIAFFBEAgACEBDAELIAAhAQNAIAEgAi0AADoAACACQQFqIQIgAUEBaiIBQQNxRQ0BIAEgA0kNAAsLAkAgA0F8cSIFQcAASQ0AIAEgBUFAaiIESw0AA0AgASACKAIANgIAIAEgAigCBDYCBCABIAIoAgg2AgggASACKAIMNgIMIAEgAigCEDYCECABIAIoAhQ2AhQgASACKAIYNgIYIAEgAigCHDYCHCABIAIoAiA2AiAgASACKAIkNgIkIAEgAigCKDYCKCABIAIoAiw2AiwgASACKAIwNgIwIAEgAigCNDYCNCABIAIoAjg2AjggASACKAI8NgI8IAJBQGshAiABQUBrIgEgBE0NAAsLIAEgBU8NAQNAIAEgAigCADYCACACQQRqIQIgAUEEaiIBIAVJDQALDAELIANBBEkEQCAAIQEMAQsgACADQQRrIgRLBEAgACEBDAELIAAhAQNAIAEgAi0AADoAACABIAItAAE6AAEgASACLQACOgACIAEgAi0AAzoAAyACQQRqIQIgAUEEaiIBIARNDQALCyABIANJBEADQCABIAItAAA6AAAgAkEBaiECIAFBAWoiASADRw0ACwsgAAsFQQALC0cBAX8jAEEQayIJJAAgCUEIaiABIAIgAyAEIAUgBiAHIAggABELACAJKAIMIgAQByAJKAIMIgcEQCAHEAYLIAlBEGokACAACzkBAX8jAEEQayICJAAgAkEIaiABIAARBAAgAigCDCIAEAcgAigCDCIBBEAgARAGCyACQRBqJAAgAAsuAEGhywBBAkHM0QBB8NEAQQFBAkEAEAJBgMsAQQlBgNIAQaTSAEEDQQRBABACCxoAIAAgASgCCCAFEBAEQCABIAIgAyAEEBkLCzcAIAAgASgCCCAFEBAEQCABIAIgAyAEEBkPCyAAKAIIIgAgASACIAMgBCAFIAAoAgAoAhQRBwALpwEAIAAgASgCCCAEEBAEQAJAIAEoAgQgAkcNACABKAIcQQFGDQAgASADNgIcCw8LAkAgACABKAIAIAQQEEUNAAJAIAIgASgCEEcEQCABKAIUIAJHDQELIANBAUcNASABQQE2AiAPCyABIAI2AhQgASADNgIgIAEgASgCKEEBajYCKAJAIAEoAiRBAUcNACABKAIYQQJHDQAgAUEBOgA2CyABQQQ2AiwLC4gCACAAIAEoAgggBBAQBEACQCABKAIEIAJHDQAgASgCHEEBRg0AIAEgAzYCHAsPCwJAIAAgASgCACAEEBAEQAJAIAIgASgCEEcEQCABKAIUIAJHDQELIANBAUcNAiABQQE2AiAPCyABIAM2AiACQCABKAIsQQRGDQAgAUEAOwE0IAAoAggiACABIAIgAkEBIAQgACgCACgCFBEHACABLQA1BEAgAUEDNgIsIAEtADRFDQEMAwsgAUEENgIsCyABIAI2AhQgASABKAIoQQFqNgIoIAEoAiRBAUcNASABKAIYQQJHDQEgAUEBOgA2DwsgACgCCCIAIAEgAiADIAQgACgCACgCGBEDAAsLMQAgACABKAIIQQAQEARAIAEgAiADEBoPCyAAKAIIIgAgASACIAMgACgCACgCHBEAAAsYACAAIAEoAghBABAQBEAgASACIAMQGgsL7QoCCn8CfSMAQZAQayIPJAAgD0GAEBATIQ8gB0EASgRAQwAAAAAgAyAGlCABIASUIAIgBZSSkowgCJMiEyATQwAAAABdGyETQwAAgD8gCCAIkpUhFEGw3QAoAgAhCwNAIAsgCUEGbGoiDC8BBCINQQp2Ig5BAnRBgMgAaigCACAOQQF0QYDKAGovAQAgDUH/B3FqQQJ0QYAIaigCAGq+IAOTIAaUIAwvAQAiDUEKdiIOQQJ0QYDIAGooAgAgDkEBdEGAygBqLwEAIA1B/wdxakECdEGACGooAgBqviABkyAElCAMLwECIgxBCnYiDUECdEGAyABqKAIAIA1BAXRBgMoAai8BACAMQf8HcWpBAnRBgAhqKAIAar4gApMgBZSSkiIIQwAAAABdRQRAQbjdACgCACARQQN0aiILIAk2AgAgCwJ/IBQgCCATk5RDAP9/R5QiCEMAAIBPXSAIQwAAAABgcQRAIAipDAELQQALQX9zIgtB//8DcTYCBCAPIAtB/wFxQQJ0aiIMIAwoAgBBAWo2AgAgDyALQQZ2QfwHcWpBgAhqIgsgCygCAEEBajYCACARQQFqIRFBsN0AKAIAIQsLIAlBAWoiCSAHRw0ACwtBACELA0AgDyALQQJ0IgdqIgkoAgAhDCAJIAo2AgAgDyAHQQRyaiIJKAIAIQ0gCSAKIAxqIgk2AgAgDyAHQQhyaiIKKAIAIQwgCiAJIA1qIgk2AgAgDyAHQQxyaiIHKAIAIQogByAJIAxqIgc2AgAgByAKaiEKIAtBBGoiC0GAAkcNAAtBACELIA9BgAhqIQdBACEJA0AgByAJQQJ0IgpqIgwoAgAhDSAMIAs2AgAgByAKQQRyaiIMKAIAIQ4gDCALIA1qIgs2AgAgByAKQQhyaiIMKAIAIQ0gDCALIA5qIgs2AgAgByAKQQxyaiIKKAIAIQwgCiALIA1qIgo2AgAgCiAMaiELIAlBBGoiCUGAAkcNAAsCQAJAAkAgEUEASgRAIBFBAXEhByARQQFrIgsNAUEAIQkMAgtBtN0AKAIAIQoMAgsgEUF+cSEMQQAhCUEAIQoDQCAJQQN0Ig1BuN0AKAIAaiIOKAIAIRAgDyAOKAIEIg5B/wFxQQJ0aiISIBIoAgAiEkEBajYCAEG83QAoAgAgEkEDdGoiEiAONgIEIBIgEDYCAEG43QAoAgAgDUEIcmoiDSgCACEOIA8gDSgCBCINQf8BcUECdGoiECAQKAIAIhBBAWo2AgBBvN0AKAIAIBBBA3RqIhAgDTYCBCAQIA42AgAgCUECaiEJIApBAmoiCiAMRw0ACwsgBwRAQbjdACgCACAJQQN0aiIHKAIAIQkgDyAHKAIEIgdB/wFxQQJ0aiIKIAooAgAiCkEBajYCAEG83QAoAgAgCkEDdGoiCiAHNgIEIAogCTYCAAtBACEJQbTdACgCACEKIBFBAEwNAEG83QAoAgAhByALBEAgEUF+cSENIA9BgAhqIQtBACEMA0AgByAJQQN0Ig5qIhAoAgAhEiALIBAoAgRBBnZB/P//H3FqIhAgECgCACIQQQFqNgIAIAogEEECdGogEjYCACAHIA5BCHJqIg4oAgAhECALIA4oAgRBBnZB/P//H3FqIg4gDigCACIOQQFqNgIAIAogDkECdGogEDYCACAJQQJqIQkgDEECaiIMIA1HDQALCyARQQFxRQ0AIAcgCUEDdGoiBygCACEJIA8gBygCBEEGdkH8//8fcWpBgAhqIgcgBygCACIHQQFqNgIAIAogB0ECdGogCTYCAAsgDyAKNgKMECAPIBE2AogQIABB+NIAIA9BiBBqEAg2AgQgAEGM3gA2AgAgD0GQEGokAAvNAwEFfyMAQUBqIgQkAAJ/QQEgACABQQAQEA0AGkEAIAFFDQAaIwBBQGoiAyQAIAEoAgAiBUEEaygCACEGIAVBCGsoAgAhBSADQgA3AiAgA0IANwIoIANCADcCMCADQgA3ADcgA0IANwIYIANBADYCFCADQczZADYCECADIAE2AgwgA0H82QA2AgggASAFaiEBQQAhBQJAIAZB/NkAQQAQEARAIANBATYCOCAGIANBCGogASABQQFBACAGKAIAKAIUEQcAIAFBACADKAIgQQFGGyEFDAELIAYgA0EIaiABQQFBACAGKAIAKAIYEQMAAkACQCADKAIsDgIAAQILIAMoAhxBACADKAIoQQFGG0EAIAMoAiRBAUYbQQAgAygCMEEBRhshBQwBCyADKAIgQQFHBEAgAygCMA0BIAMoAiRBAUcNASADKAIoQQFHDQELIAMoAhghBQsgA0FAayQAQQAgBSIBRQ0AGiAEQQxqQTQQExogBEEBNgI4IARBfzYCFCAEIAA2AhAgBCABNgIIIAEgBEEIaiACKAIAQQEgASgCACgCHBEAACAEKAIgIgBBAUYEQCACIAQoAhg2AgALIABBAUYLIQcgBEFAayQAIAcLCgAgACABQQAQEAsEACAAC7ABAQN/IwBBEGsiAiQAQazdACABNgIAQX8gAUEGbCIDIAFBAEgbEBIhBCABBEAgBCADEBMaC0Gw3QAgBDYCAEG03QBBfyABQQJ0IAFB/////wNLGxASNgIAQbjdAEF/IAFBA3QgAUH/////AUsbIgMQEjYCAEG83QAgAxASNgIAIAIgBDYCDCACIAFBA2w2AgggAEHQ0gAgAkEIahAINgIEIABBjN4ANgIAIAJBEGokAAsLrVUDAEGGCAvtSYAzAAAANAAAQDQAAIA0AACgNAAAwDQAAOA0AAAANQAAEDUAACA1AAAwNQAAQDUAAFA1AABgNQAAcDUAAIA1AACINQAAkDUAAJg1AACgNQAAqDUAALA1AAC4NQAAwDUAAMg1AADQNQAA2DUAAOA1AADoNQAA8DUAAPg1AAAANgAABDYAAAg2AAAMNgAAEDYAABQ2AAAYNgAAHDYAACA2AAAkNgAAKDYAACw2AAAwNgAANDYAADg2AAA8NgAAQDYAAEQ2AABINgAATDYAAFA2AABUNgAAWDYAAFw2AABgNgAAZDYAAGg2AABsNgAAcDYAAHQ2AAB4NgAAfDYAAIA2AACCNgAAhDYAAIY2AACINgAAijYAAIw2AACONgAAkDYAAJI2AACUNgAAljYAAJg2AACaNgAAnDYAAJ42AACgNgAAojYAAKQ2AACmNgAAqDYAAKo2AACsNgAArjYAALA2AACyNgAAtDYAALY2AAC4NgAAujYAALw2AAC+NgAAwDYAAMI2AADENgAAxjYAAMg2AADKNgAAzDYAAM42AADQNgAA0jYAANQ2AADWNgAA2DYAANo2AADcNgAA3jYAAOA2AADiNgAA5DYAAOY2AADoNgAA6jYAAOw2AADuNgAA8DYAAPI2AAD0NgAA9jYAAPg2AAD6NgAA/DYAAP42AAAANwAAATcAAAI3AAADNwAABDcAAAU3AAAGNwAABzcAAAg3AAAJNwAACjcAAAs3AAAMNwAADTcAAA43AAAPNwAAEDcAABE3AAASNwAAEzcAABQ3AAAVNwAAFjcAABc3AAAYNwAAGTcAABo3AAAbNwAAHDcAAB03AAAeNwAAHzcAACA3AAAhNwAAIjcAACM3AAAkNwAAJTcAACY3AAAnNwAAKDcAACk3AAAqNwAAKzcAACw3AAAtNwAALjcAAC83AAAwNwAAMTcAADI3AAAzNwAANDcAADU3AAA2NwAANzcAADg3AAA5NwAAOjcAADs3AAA8NwAAPTcAAD43AAA/NwAAQDcAAEE3AABCNwAAQzcAAEQ3AABFNwAARjcAAEc3AABINwAASTcAAEo3AABLNwAATDcAAE03AABONwAATzcAAFA3AABRNwAAUjcAAFM3AABUNwAAVTcAAFY3AABXNwAAWDcAAFk3AABaNwAAWzcAAFw3AABdNwAAXjcAAF83AABgNwAAYTcAAGI3AABjNwAAZDcAAGU3AABmNwAAZzcAAGg3AABpNwAAajcAAGs3AABsNwAAbTcAAG43AABvNwAAcDcAAHE3AAByNwAAczcAAHQ3AAB1NwAAdjcAAHc3AAB4NwAAeTcAAHo3AAB7NwAAfDcAAH03AAB+NwAAfzcAAIA3AICANwAAgTcAgIE3AACCNwCAgjcAAIM3AICDNwAAhDcAgIQ3AACFNwCAhTcAAIY3AICGNwAAhzcAgIc3AACINwCAiDcAAIk3AICJNwAAijcAgIo3AACLNwCAizcAAIw3AICMNwAAjTcAgI03AACONwCAjjcAAI83AICPNwAAkDcAgJA3AACRNwCAkTcAAJI3AICSNwAAkzcAgJM3AACUNwCAlDcAAJU3AICVNwAAljcAgJY3AACXNwCAlzcAAJg3AICYNwAAmTcAgJk3AACaNwCAmjcAAJs3AICbNwAAnDcAgJw3AACdNwCAnTcAAJ43AICeNwAAnzcAgJ83AACgNwCAoDcAAKE3AIChNwAAojcAgKI3AACjNwCAozcAAKQ3AICkNwAApTcAgKU3AACmNwCApjcAAKc3AICnNwAAqDcAgKg3AACpNwCAqTcAAKo3AICqNwAAqzcAgKs3AACsNwCArDcAAK03AICtNwAArjcAgK43AACvNwCArzcAALA3AICwNwAAsTcAgLE3AACyNwCAsjcAALM3AICzNwAAtDcAgLQ3AAC1NwCAtTcAALY3AIC2NwAAtzcAgLc3AAC4NwCAuDcAALk3AIC5NwAAujcAgLo3AAC7NwCAuzcAALw3AIC8NwAAvTcAgL03AAC+NwCAvjcAAL83AIC/NwAAwDcAgMA3AADBNwCAwTcAAMI3AIDCNwAAwzcAgMM3AADENwCAxDcAAMU3AIDFNwAAxjcAgMY3AADHNwCAxzcAAMg3AIDINwAAyTcAgMk3AADKNwCAyjcAAMs3AIDLNwAAzDcAgMw3AADNNwCAzTcAAM43AIDONwAAzzcAgM83AADQNwCA0DcAANE3AIDRNwAA0jcAgNI3AADTNwCA0zcAANQ3AIDUNwAA1TcAgNU3AADWNwCA1jcAANc3AIDXNwAA2DcAgNg3AADZNwCA2TcAANo3AIDaNwAA2zcAgNs3AADcNwCA3DcAAN03AIDdNwAA3jcAgN43AADfNwCA3zcAAOA3AIDgNwAA4TcAgOE3AADiNwCA4jcAAOM3AIDjNwAA5DcAgOQ3AADlNwCA5TcAAOY3AIDmNwAA5zcAgOc3AADoNwCA6DcAAOk3AIDpNwAA6jcAgOo3AADrNwCA6zcAAOw3AIDsNwAA7TcAgO03AADuNwCA7jcAAO83AIDvNwAA8DcAgPA3AADxNwCA8TcAAPI3AIDyNwAA8zcAgPM3AAD0NwCA9DcAAPU3AID1NwAA9jcAgPY3AAD3NwCA9zcAAPg3AID4NwAA+TcAgPk3AAD6NwCA+jcAAPs3AID7NwAA/DcAgPw3AAD9NwCA/TcAAP43AID+NwAA/zcAgP83AAAAOABAADgAgAA4AMAAOAAAATgAQAE4AIABOADAATgAAAI4AEACOACAAjgAwAI4AAADOABAAzgAgAM4AMADOAAABDgAQAQ4AIAEOADABDgAAAU4AEAFOACABTgAwAU4AAAGOABABjgAgAY4AMAGOAAABzgAQAc4AIAHOADABzgAAAg4AEAIOACACDgAwAg4AAAJOABACTgAgAk4AMAJOAAACjgAQAo4AIAKOADACjgAAAs4AEALOACACzgAwAs4AAAMOABADDgAgAw4AMAMOAAADTgAQA04AIANOADADTgAAA44AEAOOACADjgAwA44AAAPOABADzgAgA84AMAPOAAAEDgAQBA4AIAQOADAEDgAABE4AEAROACAETgAwBE4AAASOABAEjgAgBI4AMASOAAAEzgAQBM4AIATOADAEzgAABQ4AEAUOACAFDgAwBQ4AAAVOABAFTgAgBU4AMAVOAAAFjgAQBY4AIAWOADAFjgAABc4AEAXOACAFzgAwBc4AAAYOABAGDgAgBg4AMAYOAAAGTgAQBk4AIAZOADAGTgAABo4AEAaOACAGjgAwBo4AAAbOABAGzgAgBs4AMAbOAAAHDgAQBw4AIAcOADAHDgAAB04AEAdOACAHTgAwB04AAAeOABAHjgAgB44AMAeOAAAHzgAQB84AIAfOADAHzgAACA4AEAgOACAIDgAwCA4AAAhOABAITgAgCE4AMAhOAAAIjgAQCI4AIAiOADAIjgAACM4AEAjOACAIzgAwCM4AAAkOABAJDgAgCQ4AMAkOAAAJTgAQCU4AIAlOADAJTgAACY4AEAmOACAJjgAwCY4AAAnOABAJzgAgCc4AMAnOAAAKDgAQCg4AIAoOADAKDgAACk4AEApOACAKTgAwCk4AAAqOABAKjgAgCo4AMAqOAAAKzgAQCs4AIArOADAKzgAACw4AEAsOACALDgAwCw4AAAtOABALTgAgC04AMAtOAAALjgAQC44AIAuOADALjgAAC84AEAvOACALzgAwC84AAAwOABAMDgAgDA4AMAwOAAAMTgAQDE4AIAxOADAMTgAADI4AEAyOACAMjgAwDI4AAAzOABAMzgAgDM4AMAzOAAANDgAQDQ4AIA0OADANDgAADU4AEA1OACANTgAwDU4AAA2OABANjgAgDY4AMA2OAAANzgAQDc4AIA3OADANzgAADg4AEA4OACAODgAwDg4AAA5OABAOTgAgDk4AMA5OAAAOjgAQDo4AIA6OADAOjgAADs4AEA7OACAOzgAwDs4AAA8OABAPDgAgDw4AMA8OAAAPTgAQD04AIA9OADAPTgAAD44AEA+OACAPjgAwD44AAA/OABAPzgAgD84AMA/OAAAQDgAQEA4AIBAOADAQDgAAEE4AEBBOACAQTgAwEE4AABCOABAQjgAgEI4AMBCOAAAQzgAQEM4AIBDOADAQzgAAEQ4AEBEOACARDgAwEQ4AABFOABARTgAgEU4AMBFOAAARjgAQEY4AIBGOADARjgAAEc4AEBHOACARzgAwEc4AABIOABASDgAgEg4AMBIOAAASTgAQEk4AIBJOADASTgAAEo4AEBKOACASjgAwEo4AABLOABASzgAgEs4AMBLOAAATDgAQEw4AIBMOADATDgAAE04AEBNOACATTgAwE04AABOOABATjgAgE44AMBOOAAATzgAQE84AIBPOADATzgAAFA4AEBQOACAUDgAwFA4AABROABAUTgAgFE4AMBROAAAUjgAQFI4AIBSOADAUjgAAFM4AEBTOACAUzgAwFM4AABUOABAVDgAgFQ4AMBUOAAAVTgAQFU4AIBVOADAVTgAAFY4AEBWOACAVjgAwFY4AABXOABAVzgAgFc4AMBXOAAAWDgAQFg4AIBYOADAWDgAAFk4AEBZOACAWTgAwFk4AABaOABAWjgAgFo4AMBaOAAAWzgAQFs4AIBbOADAWzgAAFw4AEBcOACAXDgAwFw4AABdOABAXTgAgF04AMBdOAAAXjgAQF44AIBeOADAXjgAAF84AEBfOACAXzgAwF84AABgOABAYDgAgGA4AMBgOAAAYTgAQGE4AIBhOADAYTgAAGI4AEBiOACAYjgAwGI4AABjOABAYzgAgGM4AMBjOAAAZDgAQGQ4AIBkOADAZDgAAGU4AEBlOACAZTgAwGU4AABmOABAZjgAgGY4AMBmOAAAZzgAQGc4AIBnOADAZzgAAGg4AEBoOACAaDgAwGg4AABpOABAaTgAgGk4AMBpOAAAajgAQGo4AIBqOADAajgAAGs4AEBrOACAazgAwGs4AABsOABAbDgAgGw4AMBsOAAAbTgAQG04AIBtOADAbTgAAG44AEBuOACAbjgAwG44AABvOABAbzgAgG84AMBvOAAAcDgAQHA4AIBwOADAcDgAAHE4AEBxOACAcTgAwHE4AAByOABAcjgAgHI4AMByOAAAczgAQHM4AIBzOADAczgAAHQ4AEB0OACAdDgAwHQ4AAB1OABAdTgAgHU4AMB1OAAAdjgAQHY4AIB2OADAdjgAAHc4AEB3OACAdzgAwHc4AAB4OABAeDgAgHg4AMB4OAAAeTgAQHk4AIB5OADAeTgAAHo4AEB6OACAejgAwHo4AAB7OABAezgAgHs4AMB7OAAAfDgAQHw4AIB8OADAfDgAAH04AEB9OACAfTgAwH04AAB+OABAfjgAgH44AMB+OAAAfzgAQH84AIB/OADAfzgAAAA4ACAAOABAADgAYAA4AIAAOACgADgAwAA4AOAAOAAAATgAIAE4AEABOABgATgAgAE4AKABOADAATgA4AE4AAACOAAgAjgAQAI4AGACOACAAjgAoAI4AMACOADgAjgAAAM4ACADOABAAzgAYAM4AIADOACgAzgAwAM4AOADOAAABDgAIAQ4AEAEOABgBDgAgAQ4AKAEOADABDgA4AQ4AAAFOAAgBTgAQAU4AGAFOACABTgAoAU4AMAFOADgBTgAAAY4ACAGOABABjgAYAY4AIAGOACgBjgAwAY4AOAGOAAABzgAIAc4AEAHOABgBzgAgAc4AKAHOADABzgA4Ac4AAAIOAAgCDgAQAg4AGAIOACACDgAoAg4AMAIOADgCDgAAAk4ACAJOABACTgAYAk4AIAJOACgCTgAwAk4AOAJOAAACjgAIAo4AEAKOABgCjgAgAo4AKAKOADACjgA4Ao4AAALOAAgCzgAQAs4AGALOACACzgAoAs4AMALOADgCzgAAAw4ACAMOABADDgAYAw4AIAMOACgDDgAwAw4AOAMOAAADTgAIA04AEANOABgDTgAgA04AKANOADADTgA4A04AAAOOAAgDjgAQA44AGAOOACADjgAoA44AMAOOADgDjgAAA84ACAPOABADzgAYA84AIAPOACgDzgAwA84AOAPOAAAEDgAIBA4AEAQOABgEDgAgBA4AKAQOADAEDgA4BA4AAAROAAgETgAQBE4AGAROACAETgAoBE4AMAROADgETgAABI4ACASOABAEjgAYBI4AIASOACgEjgAwBI4AOASOAAAEzgAIBM4AEATOABgEzgAgBM4AKATOADAEzgA4BM4AAAUOAAgFDgAQBQ4AGAUOACAFDgAoBQ4AMAUOADgFDgAABU4ACAVOABAFTgAYBU4AIAVOACgFTgAwBU4AOAVOAAAFjgAIBY4AEAWOABgFjgAgBY4AKAWOADAFjgA4BY4AAAXOAAgFzgAQBc4AGAXOACAFzgAoBc4AMAXOADgFzgAABg4ACAYOABAGDgAYBg4AIAYOACgGDgAwBg4AOAYOAAAGTgAIBk4AEAZOABgGTgAgBk4AKAZOADAGTgA4Bk4AAAaOAAgGjgAQBo4AGAaOACAGjgAoBo4AMAaOADgGjgAABs4ACAbOABAGzgAYBs4AIAbOACgGzgAwBs4AOAbOAAAHDgAIBw4AEAcOABgHDgAgBw4AKAcOADAHDgA4Bw4AAAdOAAgHTgAQB04AGAdOACAHTgAoB04AMAdOADgHTgAAB44ACAeOABAHjgAYB44AIAeOACgHjgAwB44AOAeOAAAHzgAIB84AEAfOABgHzgAgB84AKAfOADAHzgA4B84AAAgOAAgIDgAQCA4AGAgOACAIDgAoCA4AMAgOADgIDgAACE4ACAhOABAITgAYCE4AIAhOACgITgAwCE4AOAhOAAAIjgAICI4AEAiOABgIjgAgCI4AKAiOADAIjgA4CI4AAAjOAAgIzgAQCM4AGAjOACAIzgAoCM4AMAjOADgIzgAACQ4ACAkOABAJDgAYCQ4AIAkOACgJDgAwCQ4AOAkOAAAJTgAICU4AEAlOABgJTgAgCU4AKAlOADAJTgA4CU4AAAmOAAgJjgAQCY4AGAmOACAJjgAoCY4AMAmOADgJjgAACc4ACAnOABAJzgAYCc4AIAnOACgJzgAwCc4AOAnOAAAKDgAICg4AEAoOABgKDgAgCg4AKAoOADAKDgA4Cg4AAApOAAgKTgAQCk4AGApOACAKTgAoCk4AMApOADgKTgAACo4ACAqOABAKjgAYCo4AIAqOACgKjgAwCo4AOAqOAAAKzgAICs4AEArOABgKzgAgCs4AKArOADAKzgA4Cs4AAAsOAAgLDgAQCw4AGAsOACALDgAoCw4AMAsOADgLDgAAC04ACAtOABALTgAYC04AIAtOACgLTgAwC04AOAtOAAALjgAIC44AEAuOABgLjgAgC44AKAuOADALjgA4C44AAAvOAAgLzgAQC84AGAvOACALzgAoC84AMAvOADgLzgAADA4ACAwOABAMDgAYDA4AIAwOACgMDgAwDA4AOAwOAAAMTgAIDE4AEAxOABgMTgAgDE4AKAxOADAMTgA4DE4AAAyOAAgMjgAQDI4AGAyOACAMjgAoDI4AMAyOADgMjgAADM4ACAzOABAMzgAYDM4AIAzOACgMzgAwDM4AOAzOAAANDgAIDQ4AEA0OABgNDgAgDQ4AKA0OADANDgA4DQ4AAA1OAAgNTgAQDU4AGA1OACANTgAoDU4AMA1OADgNTgAADY4ACA2OABANjgAYDY4AIA2OACgNjgAwDY4AOA2OAAANzgAIDc4AEA3OABgNzgAgDc4AKA3OADANzgA4Dc4AAA4OAAgODgAQDg4AGA4OACAODgAoDg4AMA4OADgODgAADk4ACA5OABAOTgAYDk4AIA5OACgOTgAwDk4AOA5OAAAOjgAIDo4AEA6OABgOjgAgDo4AKA6OADAOjgA4Do4AAA7OAAgOzgAQDs4AGA7OACAOzgAoDs4AMA7OADgOzgAADw4ACA8OABAPDgAYDw4AIA8OACgPDgAwDw4AOA8OAAAPTgAID04AEA9OABgPTgAgD04AKA9OADAPTgA4D04AAA+OAAgPjgAQD44AGA+OACAPjgAoD44AMA+OADgPjgAAD84ACA/OABAPzgAYD84AIA/OACgPzgAwD84AOA/OAAAQDgAIEA4AEBAOABgQDgAgEA4AKBAOADAQDgA4EA4AABBOAAgQTgAQEE4AGBBOACAQTgAoEE4AMBBOADgQTgAAEI4ACBCOABAQjgAYEI4AIBCOACgQjgAwEI4AOBCOAAAQzgAIEM4AEBDOABgQzgAgEM4AKBDOADAQzgA4EM4AABEOAAgRDgAQEQ4AGBEOACARDgAoEQ4AMBEOADgRDgAAEU4ACBFOABARTgAYEU4AIBFOACgRTgAwEU4AOBFOAAARjgAIEY4AEBGOABgRjgAgEY4AKBGOADARjgA4EY4AABHOAAgRzgAQEc4AGBHOACARzgAoEc4AMBHOADgRzgAAEg4ACBIOABASDgAYEg4AIBIOACgSDgAwEg4AOBIOAAASTgAIEk4AEBJOABgSTgAgEk4AKBJOADASTgA4Ek4AABKOAAgSjgAQEo4AGBKOACASjgAoEo4AMBKOADgSjgAAEs4ACBLOABASzgAYEs4AIBLOACgSzgAwEs4AOBLOAAATDgAIEw4AEBMOABgTDgAgEw4AKBMOADATDgA4Ew4AABNOAAgTTgAQE04AGBNOACATTgAoE04AMBNOADgTTgAAE44ACBOOABATjgAYE44AIBOOACgTjgAwE44AOBOOAAATzgAIE84AEBPOABgTzgAgE84AKBPOADATzgA4E84AABQOAAgUDgAQFA4AGBQOACAUDgAoFA4AMBQOADgUDgAAFE4ACBROABAUTgAYFE4AIBROACgUTgAwFE4AOBROAAAUjgAIFI4AEBSOABgUjgAgFI4AKBSOADAUjgA4FI4AABTOAAgUzgAQFM4AGBTOACAUzgAoFM4AMBTOADgUzgAAFQ4ACBUOABAVDgAYFQ4AIBUOACgVDgAwFQ4AOBUOAAAVTgAIFU4AEBVOABgVTgAgFU4AKBVOADAVTgA4FU4AABWOAAgVjgAQFY4AGBWOACAVjgAoFY4AMBWOADgVjgAAFc4ACBXOABAVzgAYFc4AIBXOACgVzgAwFc4AOBXOAAAWDgAIFg4AEBYOABgWDgAgFg4AKBYOADAWDgA4Fg4AABZOAAgWTgAQFk4AGBZOACAWTgAoFk4AMBZOADgWTgAAFo4ACBaOABAWjgAYFo4AIBaOACgWjgAwFo4AOBaOAAAWzgAIFs4AEBbOABgWzgAgFs4AKBbOADAWzgA4Fs4AABcOAAgXDgAQFw4AGBcOACAXDgAoFw4AMBcOADgXDgAAF04ACBdOABAXTgAYF04AIBdOACgXTgAwF04AOBdOAAAXjgAIF44AEBeOABgXjgAgF44AKBeOADAXjgA4F44AABfOAAgXzgAQF84AGBfOACAXzgAoF84AMBfOADgXzgAAGA4ACBgOABAYDgAYGA4AIBgOACgYDgAwGA4AOBgOAAAYTgAIGE4AEBhOABgYTgAgGE4AKBhOADAYTgA4GE4AABiOAAgYjgAQGI4AGBiOACAYjgAoGI4AMBiOADgYjgAAGM4ACBjOABAYzgAYGM4AIBjOACgYzgAwGM4AOBjOAAAZDgAIGQ4AEBkOABgZDgAgGQ4AKBkOADAZDgA4GQ4AABlOAAgZTgAQGU4AGBlOACAZTgAoGU4AMBlOADgZTgAAGY4ACBmOABAZjgAYGY4AIBmOACgZjgAwGY4AOBmOAAAZzgAIGc4AEBnOABgZzgAgGc4AKBnOADAZzgA4Gc4AABoOAAgaDgAQGg4AGBoOACAaDgAoGg4AMBoOADgaDgAAGk4ACBpOABAaTgAYGk4AIBpOACgaTgAwGk4AOBpOAAAajgAIGo4AEBqOABgajgAgGo4AKBqOADAajgA4Go4AABrOAAgazgAQGs4AGBrOACAazgAoGs4AMBrOADgazgAAGw4ACBsOABAbDgAYGw4AIBsOACgbDgAwGw4AOBsOAAAbTgAIG04AEBtOABgbTgAgG04AKBtOADAbTgA4G04AABuOAAgbjgAQG44AGBuOACAbjgAoG44AMBuOADgbjgAAG84ACBvOABAbzgAYG84AIBvOACgbzgAwG84AOBvOAAAcDgAIHA4AEBwOABgcDgAgHA4AKBwOADAcDgA4HA4AABxOAAgcTgAQHE4AGBxOACAcTgAoHE4AMBxOADgcTgAAHI4ACByOABAcjgAYHI4AIByOACgcjgAwHI4AOByOAAAczgAIHM4AEBzOABgczgAgHM4AKBzOADAczgA4HM4AAB0OAAgdDgAQHQ4AGB0OACAdDgAoHQ4AMB0OADgdDgAAHU4ACB1OABAdTgAYHU4AIB1OACgdTgAwHU4AOB1OAAAdjgAIHY4AEB2OABgdjgAgHY4AKB2OADAdjgA4HY4AAB3OAAgdzgAQHc4AGB3OACAdzgAoHc4AMB3OADgdzgAAHg4ACB4OABAeDgAYHg4AIB4OACgeDgAwHg4AOB4OAAAeTgAIHk4AEB5OABgeTgAgHk4AKB5OADAeTgA4Hk4AAB6OAAgejgAQHo4AGB6OACAejgAoHo4AMB6OADgejgAAHs4ACB7OABAezgAYHs4AIB7OACgezgAwHs4AOB7OAAAfDgAIHw4AEB8OABgfDgAgHw4AKB8OADAfDgA4Hw4AAB9OAAgfTgAQH04AGB9OACAfTgAoH04AMB9OADgfTgAAH44ACB+OABAfjgAYH44AIB+OACgfjgAwH44AOB+OAAAfzgAIH84AEB/OABgfzgAgH84AKB/OADAfzgA4H84AAAAAAAAgAAAAAABAACAAQAAAAIAAIACAAAAAwAAgAMAAAAEAACABAAAAAUAAIAFAAAABgAAgAYAAAAHAACABwAAAAgAAIAIAAAACQAAgAkAAAAKAACACgAAAAsAAIALAAAADAAAgAwAAAANAACADQAAAA4AAIAOAAAADwAAgEcAAACAAACAgAAAAIEAAICBAAAAggAAgIIAAACDAACAgwAAAIQAAICEAAAAhQAAgIUAAACGAACAhgAAAIcAAICHAAAAiAAAgIgAAACJAACAiQAAAIoAAICKAAAAiwAAgIsAAACMAACAjAAAAI0AAICNAAAAjgAAgI4AAACPAACAxwAAAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQAAAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEc29ydAB1bnNpZ25lZCBzaG9ydAB1bnNpZ25lZCBpbnQAaW5pdABmbG9hdAB1aW50NjRfdAB1bnNpZ25lZCBjaGFyAGJvb2wAZW1zY3JpcHRlbjo6dmFsAHVuc2lnbmVkIGxvbmcAc3RkOjp3c3RyaW5nAHN0ZDo6c3RyaW5nAHN0ZDo6dTE2c3RyaW5nAHN0ZDo6dTMyc3RyaW5nAGRvdWJsZQB2b2lkAGVtc2NyaXB0ZW46Om1lbW9yeV92aWV3PHNob3J0PgBlbXNjcmlwdGVuOjptZW1vcnlfdmlldzx1bnNpZ25lZCBzaG9ydD4AZW1zY3JpcHRlbjo6bWVtb3J5X3ZpZXc8aW50PgBlbXNjcmlwdGVuOjptZW1vcnlfdmlldzx1bnNpZ25lZCBpbnQ+AGVtc2NyaXB0ZW46Om1lbW9yeV92aWV3PGZsb2F0PgBlbXNjcmlwdGVuOjptZW1vcnlfdmlldzx1aW50OF90PgBlbXNjcmlwdGVuOjptZW1vcnlfdmlldzxpbnQ4X3Q+AGVtc2NyaXB0ZW46Om1lbW9yeV92aWV3PHVpbnQxNl90PgBlbXNjcmlwdGVuOjptZW1vcnlfdmlldzxpbnQxNl90PgBlbXNjcmlwdGVuOjptZW1vcnlfdmlldzx1aW50NjRfdD4AZW1zY3JpcHRlbjo6bWVtb3J5X3ZpZXc8aW50NjRfdD4AZW1zY3JpcHRlbjo6bWVtb3J5X3ZpZXc8dWludDMyX3Q+AGVtc2NyaXB0ZW46Om1lbW9yeV92aWV3PGludDMyX3Q+AGVtc2NyaXB0ZW46Om1lbW9yeV92aWV3PGNoYXI+AGVtc2NyaXB0ZW46Om1lbW9yeV92aWV3PHVuc2lnbmVkIGNoYXI+AHN0ZDo6YmFzaWNfc3RyaW5nPHVuc2lnbmVkIGNoYXI+AGVtc2NyaXB0ZW46Om1lbW9yeV92aWV3PHNpZ25lZCBjaGFyPgBlbXNjcmlwdGVuOjptZW1vcnlfdmlldzxsb25nPgBlbXNjcmlwdGVuOjptZW1vcnlfdmlldzx1bnNpZ25lZCBsb25nPgBlbXNjcmlwdGVuOjptZW1vcnlfdmlldzxkb3VibGU+AOgoAACwLQAATjEwZW1zY3JpcHRlbjN2YWxFAAAULgAA1CgAAGlpaQBBgNIAC6YL6CgAAPgtAAD4LQAA+C0AAPgtAAD4LQAA+C0AALAtAAD4LQAAaWlmZmZmZmZpZgBOMTBlbXNjcmlwdGVuMTFtZW1vcnlfdmlld0l0RUUAAAAULgAALykAAE4xMGVtc2NyaXB0ZW4xMW1lbW9yeV92aWV3SWpFRQAAFC4AAFgpAABOU3QzX18yMTJiYXNpY19zdHJpbmdJY05TXzExY2hhcl90cmFpdHNJY0VFTlNfOWFsbG9jYXRvckljRUVFRQAAFC4AAIApAABOU3QzX18yMTJiYXNpY19zdHJpbmdJaE5TXzExY2hhcl90cmFpdHNJaEVFTlNfOWFsbG9jYXRvckloRUVFRQAAFC4AAMgpAABOU3QzX18yMTJiYXNpY19zdHJpbmdJd05TXzExY2hhcl90cmFpdHNJd0VFTlNfOWFsbG9jYXRvckl3RUVFRQAAFC4AABAqAABOU3QzX18yMTJiYXNpY19zdHJpbmdJRHNOU18xMWNoYXJfdHJhaXRzSURzRUVOU185YWxsb2NhdG9ySURzRUVFRQAAABQuAABYKgAATlN0M19fMjEyYmFzaWNfc3RyaW5nSURpTlNfMTFjaGFyX3RyYWl0c0lEaUVFTlNfOWFsbG9jYXRvcklEaUVFRUUAAAAULgAApCoAAE4xMGVtc2NyaXB0ZW4xMW1lbW9yeV92aWV3SWNFRQAAFC4AAPAqAABOMTBlbXNjcmlwdGVuMTFtZW1vcnlfdmlld0lhRUUAABQuAAAYKwAATjEwZW1zY3JpcHRlbjExbWVtb3J5X3ZpZXdJaEVFAAAULgAAQCsAAE4xMGVtc2NyaXB0ZW4xMW1lbW9yeV92aWV3SXNFRQAAFC4AAGgrAABOMTBlbXNjcmlwdGVuMTFtZW1vcnlfdmlld0lpRUUAABQuAACQKwAATjEwZW1zY3JpcHRlbjExbWVtb3J5X3ZpZXdJbEVFAAAULgAAuCsAAE4xMGVtc2NyaXB0ZW4xMW1lbW9yeV92aWV3SW1FRQAAFC4AAOArAABOMTBlbXNjcmlwdGVuMTFtZW1vcnlfdmlld0l4RUUAABQuAAAILAAATjEwZW1zY3JpcHRlbjExbWVtb3J5X3ZpZXdJeUVFAAAULgAAMCwAAE4xMGVtc2NyaXB0ZW4xMW1lbW9yeV92aWV3SWZFRQAAFC4AAFgsAABOMTBlbXNjcmlwdGVuMTFtZW1vcnlfdmlld0lkRUUAABQuAACALAAATjEwX19jeHhhYml2MTE2X19zaGltX3R5cGVfaW5mb0UAAAAAPC4AAKgsAACgLgAATjEwX19jeHhhYml2MTE3X19jbGFzc190eXBlX2luZm9FAAAAPC4AANgsAADMLAAAAAAAAEwtAAAHAAAACAAAAAkAAAAKAAAACwAAAE4xMF9fY3h4YWJpdjEyM19fZnVuZGFtZW50YWxfdHlwZV9pbmZvRQA8LgAAJC0AAMwsAAB2AAAAEC0AAFgtAABiAAAAEC0AAGQtAABjAAAAEC0AAHAtAABoAAAAEC0AAHwtAABhAAAAEC0AAIgtAABzAAAAEC0AAJQtAAB0AAAAEC0AAKAtAABpAAAAEC0AAKwtAABqAAAAEC0AALgtAABsAAAAEC0AAMQtAABtAAAAEC0AANAtAAB4AAAAEC0AANwtAAB5AAAAEC0AAOgtAABmAAAAEC0AAPQtAABkAAAAEC0AAAAuAAAAAAAA/CwAAAcAAAAMAAAACQAAAAoAAAANAAAADgAAAA8AAAAQAAAAAAAAAIQuAAAHAAAAEQAAAAkAAAAKAAAADQAAABIAAAATAAAAFAAAAE4xMF9fY3h4YWJpdjEyMF9fc2lfY2xhc3NfdHlwZV9pbmZvRQAAAAA8LgAAXC4AAPwsAABTdDl0eXBlX2luZm8AAAAAFC4AAJAuAEGo3QALA5AxAQ==",wA(U)||(U=mA(U));function Ig(A){if(A==U&&W)return new Uint8Array(W);var g=ZA(A);if(g)return g;if(x)return x(A);throw"both async and sync fetching of the wasm failed"}function Qg(A){return Promise.resolve().then(()=>Ig(A))}function Eg(A,g,B){return Qg(A).then(C=>WebAssembly.instantiate(C,g)).then(C=>C).then(B,C=>{$(`failed to asynchronously prepare wasm: ${C}`),X(C)})}function rg(A,g,B,C){return Eg(g,B,C)}function eg(){var A={a:EB};function g(C,Q){return G=C.exports,oA=G.q,VA(),TA=G.s,Ag(G.r),Cg("wasm-instantiate"),G}Bg("wasm-instantiate");function B(C){g(C.instance)}if(I.instantiateWasm)try{return I.instantiateWasm(A,g)}catch(C){$(`Module.instantiateWasm callback failed with error: ${C}`),c(C)}return rg(W,U,A,B).catch(c),{}}var BA=A=>{for(;A.length>0;)A.shift()(I)},FB=I.noExitRuntime||!0,ng=(A,g,B,C,Q)=>{},ig=()=>{for(var A=new Array(256),g=0;g<256;++g)A[g]=String.fromCharCode(g);lA=A},lA,F=A=>{for(var g="",B=A;s[B];)g+=lA[s[B++]];return g},j={},M={},V={},fA,w=A=>{throw new fA(A)},dA,FA=A=>{throw new dA(A)},og=(A,g,B)=>{A.forEach(function(r){V[r]=g});function C(r){var i=B(r);i.length!==A.length&&FA("Mismatched type converter count");for(var e=0;e<A.length;++e)u(A[e],i[e])}var Q=new Array(g.length),E=[],n=0;g.forEach((r,i)=>{M.hasOwnProperty(r)?Q[i]=M[r]:(E.push(r),j.hasOwnProperty(r)||(j[r]=[]),j[r].push(()=>{Q[i]=M[r],++n,n===E.length&&C(Q)}))}),E.length===0&&C(Q)};function tg(A,g,B={}){var C=g.name;if(A||w(`type "${C}" must have a positive integer typeid pointer`),M.hasOwnProperty(A)){if(B.ignoreDuplicateRegistrations)return;w(`Cannot register type \'${C}\' twice`)}if(M[A]=g,delete V[A],j.hasOwnProperty(A)){var Q=j[A];delete j[A],Q.forEach(E=>E())}}function u(A,g,B={}){if(!("argPackAdvance"in g))throw new TypeError("registerType registeredInstance requires argPackAdvance");return tg(A,g,B)}var h=8,ag=(A,g,B,C)=>{g=F(g),u(A,{name:g,fromWireType:function(Q){return!!Q},toWireType:function(Q,E){return E?B:C},argPackAdvance:h,readValueFromPointer:function(Q){return this.fromWireType(s[Q])},destructorFunction:null})};function Dg(){Object.assign(GA.prototype,{get(A){return this.allocated[A]},has(A){return this.allocated[A]!==void 0},allocate(A){var g=this.freelist.pop()||this.allocated.length;return this.allocated[g]=A,g},free(A){this.allocated[A]=void 0,this.freelist.push(A)}})}function GA(){this.allocated=[void 0],this.freelist=[]}var f=new GA,uA=A=>{A>=f.reserved&&--f.get(A).refcount===0&&f.free(A)},cg=()=>{for(var A=0,g=f.reserved;g<f.allocated.length;++g)f.allocated[g]!==void 0&&++A;return A},Og=()=>{f.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),f.reserved=f.allocated.length,I.count_emval_handles=cg},CA={toValue:A=>(A||w("Cannot use deleted val. handle = "+A),f.get(A).value),toHandle:A=>{switch(A){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return f.allocate({refcount:1,value:A})}}};function YA(A){return this.fromWireType(R[A>>2])}var sg=(A,g)=>{g=F(g),u(A,{name:g,fromWireType:B=>{var C=CA.toValue(B);return uA(B),C},toWireType:(B,C)=>CA.toHandle(C),argPackAdvance:h,readValueFromPointer:YA,destructorFunction:null})},Ng=(A,g)=>{switch(g){case 4:return function(B){return this.fromWireType(aA[B>>2])};case 8:return function(B){return this.fromWireType(DA[B>>3])};default:throw new TypeError(`invalid float width (${g}): ${A}`)}},wg=(A,g,B)=>{g=F(g),u(A,{name:g,fromWireType:C=>C,toWireType:(C,Q)=>Q,argPackAdvance:h,readValueFromPointer:Ng(g,B),destructorFunction:null})},IA=(A,g)=>Object.defineProperty(g,"name",{value:A}),lg=A=>{for(;A.length;){var g=A.pop(),B=A.pop();B(g)}};function fg(A,g){if(!(A instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof A} which is not a function`);var B=IA(A.name||"unknownFunctionName",function(){});B.prototype=A.prototype;var C=new B,Q=A.apply(C,g);return Q instanceof Object?Q:C}function dg(A,g,B,C,Q,E){var n=g.length;n<2&&w("argTypes array size mismatch! Must at least get return value and \'this\' types!");for(var r=g[1]!==null&&B!==null,i=!1,e=1;e<g.length;++e)if(g[e]!==null&&g[e].destructorFunction===void 0){i=!0;break}for(var D=g[0].name!=="void",a="",l="",e=0;e<n-2;++e)a+=(e!==0?", ":"")+"arg"+e,l+=(e!==0?", ":"")+"arg"+e+"Wired";var N=`\n        return function (${a}) {\n        if (arguments.length !== ${n-2}) {\n          throwBindingError(\'function ${A} called with \' + arguments.length + \' arguments, expected ${n-2}\');\n        }`;i&&(N+=`var destructors = [];\n`);var H=i?"destructors":"null",p=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],k=[w,C,Q,lg,g[0],g[1]];r&&(N+="var thisWired = classParam.toWireType("+H+`, this);\n`);for(var e=0;e<n-2;++e)N+="var arg"+e+"Wired = argType"+e+".toWireType("+H+", arg"+e+"); // "+g[e+2].name+`\n`,p.push("argType"+e),k.push(g[e+2]);if(r&&(l="thisWired"+(l.length>0?", ":"")+l),N+=(D||E?"var rv = ":"")+"invoker(fn"+(l.length>0?", ":"")+l+`);\n`,i)N+=`runDestructors(destructors);\n`;else for(var e=r?1:2;e<g.length;++e){var EA=e===1?"thisWired":"arg"+(e-2)+"Wired";g[e].destructorFunction!==null&&(N+=EA+"_dtor("+EA+"); // "+g[e].name+`\n`,p.push(EA+"_dtor"),k.push(g[e].destructorFunction))}D&&(N+=`var ret = retType.fromWireType(rv);\nreturn ret;\n`),N+=`}\n`,p.push(N);var nB=fg(Function,p).apply(null,k);return IA(A,nB)}var Fg=(A,g,B)=>{if(A[g].overloadTable===void 0){var C=A[g];A[g]=function(){return A[g].overloadTable.hasOwnProperty(arguments.length)||w(`Function \'${B}\' called with an invalid number of arguments (${arguments.length}) - expects one of (${A[g].overloadTable})!`),A[g].overloadTable[arguments.length].apply(this,arguments)},A[g].overloadTable=[],A[g].overloadTable[C.argCount]=C}},Gg=(A,g,B)=>{I.hasOwnProperty(A)?((B===void 0||I[A].overloadTable!==void 0&&I[A].overloadTable[B]!==void 0)&&w(`Cannot register public name \'${A}\' twice`),Fg(I,A,A),I.hasOwnProperty(B)&&w(`Cannot register multiple overloads of a function with the same number of arguments (${B})!`),I[A].overloadTable[B]=g):(I[A]=g,B!==void 0&&(I[A].numArguments=B))},ug=(A,g)=>{for(var B=[],C=0;C<A;C++)B.push(d[g+C*4>>2]);return B},Yg=(A,g,B)=>{I.hasOwnProperty(A)||FA("Replacing nonexistant public symbol"),I[A].overloadTable!==void 0&&B!==void 0?I[A].overloadTable[B]=g:(I[A]=g,I[A].argCount=B)},Tg=(A,g,B)=>{var C=I["dynCall_"+A];return B&&B.length?C.apply(null,[g].concat(B)):C.call(null,g)},P=[],TA,yA=A=>{var g=P[A];return g||(A>=P.length&&(P.length=A+1),P[A]=g=TA.get(A)),g},yg=(A,g,B)=>{if(A.includes("j"))return Tg(A,g,B);var C=yA(g).apply(null,B);return C},Mg=(A,g)=>{var B=[];return function(){return B.length=0,Object.assign(B,arguments),yg(A,g,B)}},hg=(A,g)=>{A=F(A);function B(){return A.includes("j")?Mg(A,g):yA(g)}var C=B();return typeof C!="function"&&w(`unknown function pointer with signature ${A}: ${g}`),C},vg=(A,g)=>{var B=IA(g,function(C){this.name=g,this.message=C;var Q=new Error(C).stack;Q!==void 0&&(this.stack=this.toString()+`\n`+Q.replace(/^Error(:[^\\n]*)?\\n/,""))});return B.prototype=Object.create(A.prototype),B.prototype.constructor=B,B.prototype.toString=function(){return this.message===void 0?this.name:`${this.name}: ${this.message}`},B},MA,hA=A=>{var g=UA(A),B=F(g);return Y(g),B},Rg=(A,g)=>{var B=[],C={};function Q(E){if(!C[E]&&!M[E]){if(V[E]){V[E].forEach(Q);return}B.push(E),C[E]=!0}}throw g.forEach(Q),new MA(`${A}: `+B.map(hA).join([", "]))},Ug=A=>{A=A.trim();let g=A.indexOf("(");return g!==-1?A.substr(0,g):A},jg=(A,g,B,C,Q,E,n)=>{var r=ug(g,B);A=F(A),A=Ug(A),Q=hg(C,Q),Gg(A,function(){Rg(`Cannot call ${A} due to unbound types`,r)},g-1),og([],r,function(i){var e=[i[0],null].concat(i.slice(1));return Yg(A,dg(A,e,null,Q,E,n),g-1),[]})},Hg=(A,g,B)=>{switch(g){case 1:return B?C=>AA[C>>0]:C=>s[C>>0];case 2:return B?C=>b[C>>1]:C=>Z[C>>1];case 4:return B?C=>R[C>>2]:C=>d[C>>2];default:throw new TypeError(`invalid integer width (${g}): ${A}`)}},pg=(A,g,B,C,Q)=>{g=F(g),Q===-1&&(Q=4294967295);var E=D=>D;if(C===0){var n=32-8*B;E=D=>D<<n>>>n}var r=g.includes("unsigned"),i=(D,a)=>{},e;r?e=function(D,a){return i(a,this.name),a>>>0}:e=function(D,a){return i(a,this.name),a},u(A,{name:g,fromWireType:E,toWireType:e,argPackAdvance:h,readValueFromPointer:Hg(g,B,C!==0),destructorFunction:null})},kg=(A,g,B)=>{var C=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],Q=C[g];function E(n){var r=d[n>>2],i=d[n+4>>2];return new Q(AA.buffer,i,r)}B=F(B),u(A,{name:B,fromWireType:E,argPackAdvance:h,readValueFromPointer:E},{ignoreDuplicateRegistrations:!0})};function zg(A){return this.fromWireType(d[A>>2])}var bg=(A,g,B,C)=>{if(!(C>0))return 0;for(var Q=B,E=B+C-1,n=0;n<A.length;++n){var r=A.charCodeAt(n);if(r>=55296&&r<=57343){var i=A.charCodeAt(++n);r=65536+((r&1023)<<10)|i&1023}if(r<=127){if(B>=E)break;g[B++]=r}else if(r<=2047){if(B+1>=E)break;g[B++]=192|r>>6,g[B++]=128|r&63}else if(r<=65535){if(B+2>=E)break;g[B++]=224|r>>12,g[B++]=128|r>>6&63,g[B++]=128|r&63}else{if(B+3>=E)break;g[B++]=240|r>>18,g[B++]=128|r>>12&63,g[B++]=128|r>>6&63,g[B++]=128|r&63}}return g[B]=0,B-Q},Kg=(A,g,B)=>bg(A,s,g,B),Jg=A=>{for(var g=0,B=0;B<A.length;++B){var C=A.charCodeAt(B);C<=127?g++:C<=2047?g+=2:C>=55296&&C<=57343?(g+=4,++B):g+=3}return g},vA=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0,mg=(A,g,B)=>{for(var C=g+B,Q=g;A[Q]&&!(Q>=C);)++Q;if(Q-g>16&&A.buffer&&vA)return vA.decode(A.subarray(g,Q));for(var E="";g<Q;){var n=A[g++];if(!(n&128)){E+=String.fromCharCode(n);continue}var r=A[g++]&63;if((n&224)==192){E+=String.fromCharCode((n&31)<<6|r);continue}var i=A[g++]&63;if((n&240)==224?n=(n&15)<<12|r<<6|i:n=(n&7)<<18|r<<12|i<<6|A[g++]&63,n<65536)E+=String.fromCharCode(n);else{var e=n-65536;E+=String.fromCharCode(55296|e>>10,56320|e&1023)}}return E},Sg=(A,g)=>A?mg(s,A,g):"",Lg=(A,g)=>{g=F(g);var B=g==="std::string";u(A,{name:g,fromWireType(C){var Q=d[C>>2],E=C+4,n;if(B)for(var r=E,i=0;i<=Q;++i){var e=E+i;if(i==Q||s[e]==0){var D=e-r,a=Sg(r,D);n===void 0?n=a:(n+=String.fromCharCode(0),n+=a),r=e+1}}else{for(var l=new Array(Q),i=0;i<Q;++i)l[i]=String.fromCharCode(s[E+i]);n=l.join("")}return Y(C),n},toWireType(C,Q){Q instanceof ArrayBuffer&&(Q=new Uint8Array(Q));var E,n=typeof Q=="string";n||Q instanceof Uint8Array||Q instanceof Uint8ClampedArray||Q instanceof Int8Array||w("Cannot pass non-string to std::string"),B&&n?E=Jg(Q):E=Q.length;var r=QA(4+E+1),i=r+4;if(d[r>>2]=E,B&&n)Kg(Q,i,E+1);else if(n)for(var e=0;e<E;++e){var D=Q.charCodeAt(e);D>255&&(Y(i),w("String has UTF-16 code units that do not fit in 8 bits")),s[i+e]=D}else for(var e=0;e<E;++e)s[i+e]=Q[e];return C!==null&&C.push(Y,r),r},argPackAdvance:h,readValueFromPointer:zg,destructorFunction(C){Y(C)}})},RA=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0,Wg=(A,g)=>{for(var B=A,C=B>>1,Q=C+g/2;!(C>=Q)&&Z[C];)++C;if(B=C<<1,B-A>32&&RA)return RA.decode(s.subarray(A,B));for(var E="",n=0;!(n>=g/2);++n){var r=b[A+n*2>>1];if(r==0)break;E+=String.fromCharCode(r)}return E},Zg=(A,g,B)=>{if(B===void 0&&(B=2147483647),B<2)return 0;B-=2;for(var C=g,Q=B<A.length*2?B/2:A.length,E=0;E<Q;++E){var n=A.charCodeAt(E);b[g>>1]=n,g+=2}return b[g>>1]=0,g-C},Xg=A=>A.length*2,Vg=(A,g)=>{for(var B=0,C="";!(B>=g/4);){var Q=R[A+B*4>>2];if(Q==0)break;if(++B,Q>=65536){var E=Q-65536;C+=String.fromCharCode(55296|E>>10,56320|E&1023)}else C+=String.fromCharCode(Q)}return C},Pg=(A,g,B)=>{if(B===void 0&&(B=2147483647),B<4)return 0;for(var C=g,Q=C+B-4,E=0;E<A.length;++E){var n=A.charCodeAt(E);if(n>=55296&&n<=57343){var r=A.charCodeAt(++E);n=65536+((n&1023)<<10)|r&1023}if(R[g>>2]=n,g+=4,g+4>Q)break}return R[g>>2]=0,g-C},qg=A=>{for(var g=0,B=0;B<A.length;++B){var C=A.charCodeAt(B);C>=55296&&C<=57343&&++B,g+=4}return g},_g=(A,g,B)=>{B=F(B);var C,Q,E,n,r;g===2?(C=Wg,Q=Zg,n=Xg,E=()=>Z,r=1):g===4&&(C=Vg,Q=Pg,n=qg,E=()=>d,r=2),u(A,{name:B,fromWireType:i=>{for(var e=d[i>>2],D=E(),a,l=i+4,N=0;N<=e;++N){var H=i+4+N*g;if(N==e||D[H>>r]==0){var p=H-l,k=C(l,p);a===void 0?a=k:(a+=String.fromCharCode(0),a+=k),l=H+g}}return Y(i),a},toWireType:(i,e)=>{typeof e!="string"&&w(`Cannot pass non-string to C++ string type ${B}`);var D=n(e),a=QA(4+D+g);return d[a>>2]=D>>r,Q(e,a+4,D+g),i!==null&&i.push(Y,a),a},argPackAdvance:h,readValueFromPointer:YA,destructorFunction(i){Y(i)}})},xg=(A,g)=>{g=F(g),u(A,{isVoid:!0,name:g,argPackAdvance:0,fromWireType:()=>{},toWireType:(B,C)=>{}})},$g=A=>{A>4&&(f.get(A).refcount+=1)},AB=(A,g)=>{var B=M[A];return B===void 0&&w(g+" has unknown type "+hA(A)),B},gB=(A,g)=>{A=AB(A,"_emval_take_value");var B=A.readValueFromPointer(g);return CA.toHandle(B)},BB=()=>{X("")},CB=(A,g,B)=>s.copyWithin(A,g,g+B),IB=A=>{X("OOM")},QB=A=>{var g=s.length;A>>>=0,IB(A)};ig(),fA=I.BindingError=class extends Error{constructor(g){super(g),this.name="BindingError"}},dA=I.InternalError=class extends Error{constructor(g){super(g),this.name="InternalError"}},Dg(),Og(),MA=I.UnboundTypeError=vg(Error,"UnboundTypeError");var EB={l:ng,j:ag,p:sg,f:wg,c:jg,b:pg,a:kg,e:Lg,d:_g,k:xg,g:uA,h:$g,i:gB,m:BB,o:CB,n:QB},G=eg(),rB=()=>(rB=G.r)(),UA=A=>(UA=G.t)(A),eB=()=>(eB=G.__errno_location)(),QA=A=>(QA=G.u)(A),Y=A=>(Y=G.v)(A),q;K=function A(){q||jA(),q||(K=A)};function jA(){if(y>0||(qA(),y>0))return;function A(){q||(q=!0,I.calledRun=!0,!tA&&(_A(),T(I),I.onRuntimeInitialized&&I.onRuntimeInitialized(),xA()))}I.setStatus?(I.setStatus("Running..."),setTimeout(function(){setTimeout(function(){I.setStatus("")},1),A()},1)):A()}if(I.preInit)for(typeof I.preInit=="function"&&(I.preInit=[I.preInit]);I.preInit.length>0;)I.preInit.pop()();return jA(),t.ready}})();typeof kA=="object"&&typeof rA=="object"?rA.exports=pA:typeof define=="function"&&define.amd&&define([],()=>pA)});J();var JA=NB(zA()),eA=null,nA=null,bA=0,iA=[];function KA(){if(nA)for(;iA.length;){let o=iA.shift();nA.set(o,bA),bA+=o.length}}self.onmessage=function(o){let t=o.data;switch(t.type){case"init":lB("Worker starting.");let I=t.maxpts;(0,JA.default)().then(O=>{eA=O,nA=eA.init(I),KA(),postMessage({type:"ready"})}).catch(O=>fB(O));break;case"points":let T=t.data;iA.push(T),KA();break;case"sort":let c=t.eye,v=t.dir,m=t.end,_=t.bound,S=eA.sort(c[0],c[1],c[2],v[0],v[1],v[2],m,_),z=new Uint32Array(S.length);z.set(S),postMessage({type:"indices",indices:z,offset:m},[z.buffer]);break}};function lB(o){postMessage({type:"log",str:o})}function fB(o){postMessage({type:"error",str:o})}\n';
var dA = new Blob([YA], { type: "text/javascript" });
var KA = URL.createObjectURL(dA);
var K = class extends (typeof Worker == "function" ? Worker : null) {
  constructor() {
    super(KA);
  }
};
var EA = `#version 300 es
precision highp float;precision highp int;const float A=0.1,B=0.2;uniform mat4 view,proj;uniform vec2 res2,ires2;uniform vec3 cpos;uniform float inv_sqrt_scale_to_world;uniform int offset,size,debug;uniform vec3 zs_aa_ts,scene_center;uniform vec2 load_r,reveal_r,solid_r;uniform highp usampler2D s0,s1;
#ifdef HAVE_SEMANTICS
uniform uint semanticsMask;uniform lowp usampler2D s3;
#endif
in vec2 a0;in int a1;out vec4 v_color;out vec2 v_uv;ivec2 C(int D){return ivec2(4*((D>>4)&0x1ff)+(D&3),4*(D>>13)+((D>>2)&3));}
#ifdef HAVE_SH
uniform mediump sampler2D s2;mediump vec3 E(ivec2 F,int G,int H){return texelFetch(s2,F+ivec2(G,H),0).rgb;}mediump vec3 I(uint J,vec3 K){ivec2 F=ivec2(int(J&0xffu),int(J>>8u))*4;float L=K.x,M=K.y,N=K.z,O=L*L,P=M*M,Q=N*N,R=L*M,S=M*N,T=L*N;return-0.4886025*E(F,0,0)*M+0.4886025*E(F,1,0)*N-0.4886025*E(F,2,0)*L+1.0925484*E(F,3,0)*R-1.0925484*E(F,0,1)*S+0.3153916*E(F,1,1)*(3.*Q-1.)-1.0925484*E(F,2,1)*T+0.5462742*E(F,3,1)*(O-P)-0.5900436*E(F,0,2)*M*(3.*O-P)+2.8906114*E(F,1,2)*R*N-0.4570458*E(F,2,2)*M*(5.*Q-1.)+0.3731763*E(F,3,2)*N*(5.*Q-3.)-0.4570458*E(F,0,3)*L*(5.*Q-1.)+1.4453057*E(F,1,3)*N*(O-P)-0.5900436*E(F,2,3)*L*(O-3.*P);}
#endif
vec3 U(float V){vec3 W=fract(vec3(1.,255.,65025.)*V);return W-W.yzz*vec3(1./255.,1./255.,0.);}
#pragma shader_hook_injection
void main(){int D=a1;if(gl_InstanceID<size-offset){D=size-1-gl_InstanceID;}ivec2 X=C(D);uint Y=255u;
#ifdef HAVE_SEMANTICS
Y=texelFetch(s3,X,0).r;if((Y&semanticsMask)==0u){gl_Position=vec4(0.,0.,2.,1.);return;}
#endif
uvec2 Z=texelFetch(s0,X,0).xy;vec2 a=unpackHalf2x16(Z.x),b=unpackHalf2x16(Z.y);vec3 c=vec3(a,b.x);
#ifdef SHADER_HOOK_getSplatTransform
mat4 d=_shaderHook_getSplatTransform(c,Y)*view;
#else
mat4 d=view;
#endif
vec4 e=d*vec4(c,1.);vec4 f=proj*e;float g=1.2*f.w;if(f.z<-f.w||f.x<-g||f.x>g||f.y<-g||f.y>g){gl_Position=vec4(0.,0.,2.,1.);return;}float h=1./f.w;vec2 i=f.xy*h;uvec4 j=texelFetch(s1,X,0);vec2 k=unpackHalf2x16(j.x),l=unpackHalf2x16(j.y),m=unpackHalf2x16(j.z);vec3 n=vec3(b.y,k),o=vec3(l,m.x);n*=zs_aa_ts.z;o*=zs_aa_ts.z;uint p=j.z>>16u;mediump float q=float(j.a&0xffu),r=float((j.a>>8)&0xffu),s=float((j.a>>16)&0xffu),t=float((j.a>>24)&0xffu);mediump vec4 u=vec4(q,r,s,t)*(1./255.);vec2 v=vec2(proj[0][0]*res2.x,proj[1][1]*res2.y);float w=-h,x=w*w;mat3 y=mat3(-v.x*w,0,0,0,-v.y*w,0,v.x*e.x*x,v.y*e.y*x,0),z=mat3(d[0].xyz,d[1].xyz,d[2].xyz),AA=y*z,AB=mat3(n.x,o.x,o.y,o.x,n.y,o.z,o.y,o.z,n.z),AC=AA*AB*transpose(AA);float AD=AC[0][0]+zs_aa_ts.y,AE=AC[0][1],AF=AC[1][1]+zs_aa_ts.y;vec3 AG=(c-cpos);float AH=dot(AG,AG)*inv_sqrt_scale_to_world;float AI=(AH-A)/(B-A);AI=clamp(AI,0.,1.);float AJ=length(c-scene_center);float AK=clamp((load_r.x-AJ)*load_r.y,0.,1.);float AL=clamp((reveal_r.x-AJ)*reveal_r.y,0.,1.);float AM=clamp((solid_r.x-AJ)*solid_r.y,0.,1.);float AN=1.-AM;float AO=(AD+AF)/2.,AP=length(vec2((AD-AF)/2.,AE)),AQ=AO+AP,AR=max(AO-AP,0.1);vec2 AS=normalize(vec2(AE,AQ-AD)),AT=vec2(AS.y,-AS.x),AU=AI*mix(sqrt(2.*vec2(AQ,AR)),vec2(1.),AN),AV=min(AU.x,1024.)*AS,AW=min(AU.y,1024.)*AT;gl_Position=vec4(i+a0.x*AV*ires2+a0.y*AW*ires2,f.z*h,1.);v_uv=a0;u.a*=AI*AK*AL;
#ifndef RD 
#ifdef HAVE_SH
u.rgb+=I(p,normalize(c-cpos));
#endif
if(debug==2){v_color=vec4(vec3(u.a),1.);}else{
#ifdef SHADER_HOOK_getSplatColor
u=_shaderHook_getSplatColor(u,c,Y);
#endif
v_color=u;}
#else
v_color=vec4(U(f.w*zs_aa_ts.x),u.a);
#endif
#ifdef SHADER_HOOK_onMainEnd
_shaderHook_onMainEnd();
#endif
}`;
var iA = `#version 300 es
precision highp float;uniform float ellip;in vec4 v_color;in vec2 v_uv;out vec4 fc;
#pragma shader_hook_injection
void main(){float A=-dot(v_uv,v_uv);if(A<-4.){discard;}float B=max(exp(A),ellip)*v_color.a;
#ifndef RD 
fc=vec4(v_color.rgb,B);
#ifdef SHADER_HOOK_getFragmentColor
fc=_shaderHook_getFragmentColor(fc);
#endif
fc.rgb*=fc.a;
#else 
if(B<0.25){discard;}fc=vec4(v_color.rgb,1.);
#endif
}`;
var b = `#version 300 es
precision highp float;uniform mat4 view,proj;uniform float radius;uniform vec3 origin;in vec3 a_pos;out vec3 v_pos;
#pragma shader_hook_injection
void main(){gl_Position=proj*(view*vec4(radius*a_pos+origin,1.));gl_Position.z=0.;v_pos=a_pos;
#ifdef SHADER_HOOK_onMainEnd
_shaderHook_onMainEnd();
#endif
}`;
var X = `#version 300 es
precision mediump float;uniform lowp samplerCube s0;uniform float fade;in vec3 v_pos;out vec4 fc;
#pragma shader_hook_injection
void main(){vec3 A=v_pos;fc=fade*texture(s0,vec3(A.x,-A.y,-A.z));
#ifdef SHADER_HOOK_getFragmentColor
fc=_shaderHook_getFragmentColor(fc);
#endif
}`;
var u = class {
  constructor(A, g, I, B = void 0) {
    if (B) {
      let C = /(#version\s+[^\n]+)/;
      C.test(g) ? g = g.replace(C, `$1
${B}`) : g = `${B}
${g}`, C.test(I) ? I = I.replace(C, `$1
${B}`) : I = `${B}
${I}`;
    }
    this.gl = A, this.vs = this._compile(A.VERTEX_SHADER, g), this.fs = this._compile(A.FRAGMENT_SHADER, I), this.program = this._link(this.vs, this.fs), this.aLoc = /* @__PURE__ */ new Map(), this.uLoc = /* @__PURE__ */ new Map();
  }
  use() {
    this.gl.useProgram(this.program);
  }
  uniform(A) {
    let g = this.uLoc.get(A);
    return g !== void 0 || (g = this.gl.getUniformLocation(this.program, A), g === null && console.warn(`Uniform ${A} not found.`), this.uLoc.set(A, g)), g;
  }
  attrib(A) {
    let g = this.aLoc.get(A);
    return g !== void 0 || (g = this.gl.getAttribLocation(this.program, A), g === null && console.warn(`Attribute ${A} not found.`), this.aLoc.set(A, g)), g;
  }
  relink() {
    let A = this.gl;
    A.linkProgram(this.program);
    var g = A.getProgramParameter(this.program, A.LINK_STATUS);
    if (!g) throw new Error("Error linking program:" + A.getProgramInfoLog(this.program));
  }
  _compile(A, g) {
    let I = this.gl, B = I.createShader(A);
    I.shaderSource(B, g), I.compileShader(B);
    var C = I.getShaderParameter(B, I.COMPILE_STATUS);
    if (!C) throw console.error(I.getShaderInfoLog(B)), console.log(g), new Error(`Error compiling ${A == I.VERTEX_SHADER ? "vertex" : "fragment"} shader: ${I.getShaderInfoLog(B)}`);
    return B;
  }
  _link(A, g) {
    let I = this.gl, B = I.createProgram();
    I.attachShader(B, A), I.attachShader(B, g), I.linkProgram(B);
    var C = I.getProgramParameter(B, I.LINK_STATUS);
    if (!C) throw new Error("Error linking program:" + I.getProgramInfoLog(B));
    return B;
  }
  delete() {
    this.gl.deleteProgram(this.program), this.gl.deleteShader(this.vs), this.gl.deleteShader(this.fs), this.program = null, this.vs = null, this.fs = null;
  }
};
var oA = Float32Array;
function eA(a2, A, g) {
  return g = g || new oA(3), g[0] = a2[0] - A[0], g[1] = a2[1] - A[1], g[2] = a2[2] - A[2], g;
}
function tA(a2) {
  return Math.sqrt(a2[0] * a2[0] + a2[1] * a2[1] + a2[2] * a2[2]);
}
function v(a2, A) {
  let g = a2[0] - A[0], I = a2[1] - A[1], B = a2[2] - A[2];
  return g * g + I * I + B * B;
}
function P(a2, A) {
  return A = A || new oA(3), A[0] = a2[0], A[1] = a2[1], A[2] = a2[2], A;
}
var G = class {
  constructor() {
    this.listeners = new Array();
  }
  addListener(A, g = 0) {
    let I = { priority: g, listener: A, remove: () => this.removeListener(A) };
    return A !== null && this.listeners.push(I), I;
  }
  removeListener(A) {
    let g = 0;
    for (; g < this.listeners.length && this.listeners[g].listener !== A; g++) ;
    this.listeners.splice(g, 1);
  }
  once(A, g = 0) {
    let I = (B) => {
      A(B), this.removeListener(I);
    };
    return this.addListener(I, g);
  }
  dispatch(A, g) {
    if (this.listeners.length !== 0) return this.dispatchWithExistingEvent(A, g);
  }
  dispatchWithExistingEvent(A, g = 1 / 0) {
    if (this.listeners.length === 0) return;
    let I = this.patchPayload(A);
    this.sortPriorityDescending();
    for (let B = 0; B < this.listeners.length; B++) if (!(this.listeners[B].priority > g) && (this.listeners[B].listener(I), typeof I == "object" && I.propagationStopped)) return;
  }
  hasListeners() {
    return this.listeners.length > 0;
  }
  sortPriorityDescending() {
    this.listeners.sort((A, g) => g.priority - A.priority);
  }
  patchPayload(A) {
    return A instanceof Event && A.propagationStopped === void 0 && (A.propagationStopped = false, A._stopPropagation = A.stopPropagation, A.stopPropagation = HA), A;
  }
};
function HA() {
  this.propagationStopped = true, this._stopPropagation();
}
var sA = ((I) => (I[I.BACKGROUND = 1] = "BACKGROUND", I[I.FOREGROUND = 2] = "FOREGROUND", I[I.ALL = 255] = "ALL", I))(sA || {});
var O = class {
  constructor(A, g) {
    this.loader = A;
    this.semanticsMask = 255;
    this.hasSphericalHarmonicsTexture = false;
    this.hasSemanticsTexture = false;
    this.hasSkyboxTexture = false;
    this.maxSortAge = 1 / 0;
    this.sortAge = 0;
    this.indicesNeedUpload = false;
    this.gaussTextureUpdatePayloads = [null, null];
    this.gaussTextureUploadedHeight = [0, 0];
    this.shTextureUpdatePayload = null;
    this.semanticsTextureUpdatePayload = null;
    this.skyboxTextureUpdatePayload = [];
    this.partialIndices = new Uint32Array(0);
    this.numVisible = 0;
    this.sortEnd = 0;
    this.needsSort = false;
    this.enableEnd = true;
    this.workerBusy = false;
    this.loadingAnimation = { enabled: true, particleRevealEnabled: false, particleSolidDelay_ms: 3500, particleRevealSpeed: 1, particleRevealOffset_ms: 0, startTime_ms: -1 };
    this.shaderParams = { loadR1: 0, loadR2: 0, revealR1: 0, revealR2: 0, solidR1: 0, solidR2: 0, debugView: 0, tweakScale: 1 };
    this.events = { onLoad: new G(), onWorkerError: new G(), onRequestRender: new G() };
    this.glObjects = null;
    this.compiledShaders = /* @__PURE__ */ new Map();
    this.lastEye = new Float32Array(3);
    this.lastDir = new Float32Array(3);
    this.numWorkerPoints = 0;
    this.lastWorkerPoints = 0;
    this.loaderMetaReady = false;
    this.shaderHooks = null;
    this.resolveWorkerReady = () => {
    };
    g.loadingAnimationEnabled != null && (this.loadingAnimation.enabled = g.loadingAnimationEnabled), g.particleRevealEnabled != null && (this.loadingAnimation.particleRevealEnabled = g.particleRevealEnabled), this.worker = new K(), this.worker.onmessage = this.onWorkerMessage.bind(this), this.workerBusy = true, this.workerReady = new Promise((I) => {
      this.resolveWorkerReady = I;
    }), A.events.partialUpdate.addListener(() => {
      this.partialUpdate();
    }), A.events.updateGauss1Texture.addListener((I) => {
      this.queueUpdateGaussTexture(0, I);
    }), A.events.updateGauss2Texture.addListener((I) => {
      this.queueUpdateGaussTexture(1, I);
    }), A.gauss1Ready.then((I) => {
      this.queueUpdateGaussTexture(0, I);
    }), A.gauss2Ready.then((I) => {
      this.queueUpdateGaussTexture(1, I);
    }), A.shReady.then((I) => {
      I != null && this.queueUpdateSHTexture(I);
    }), A.metaReady.then(() => {
      this.loaderMetaReady = true, this.initWorker();
    }), A.semanticsReady.then((I) => {
      if (I) {
        let B = this.convertSemanticsToTiledTexture(I);
        this.queueUpdateSemanticsTexture(B);
      }
    }), A.skyboxReady.then((I) => {
      if (I) for (let B of I) this.queueUpdateSkyboxTexture(B);
    }), A.allReady.then(() => {
      this.partialUpdate();
    }), Promise.all([A.allReady, this.workerReady]).then(() => {
      this.events.onLoad.dispatch(this);
    }), this.handleQuirks();
  }
  get numSplats() {
    return this.loader.numSplats;
  }
  syncGpuResources(A) {
    let g = false;
    return this.glObjects == null && this.loaderMetaReady && (this.initGLObjects(A), g = true), this.syncQueuedGpuData(A) || g;
  }
  drawSkybox(A, g) {
    let I = this.glObjects;
    if (!I) return;
    A.disable(A.DEPTH_TEST);
    let B = this.getShader(A, "skybox", b, X, 0);
    B.use();
    let C = new Float32Array(16);
    C.set(g.viewMatrix), C.subarray(12, 15).set([0, 0, 0]), A.uniformMatrix4fv(B.uniform("view"), false, C), A.uniformMatrix4fv(B.uniform("proj"), false, g.projectionMatrix);
    let Q = (this.shaderParams.solidR1 + this.shaderParams.solidR2) / 2, E = this.loader.sceneRadius(), t = E * 0.9, o = Math.min(Math.max((Q - t) / (E - t), 0), 1);
    A.uniform1f(B.uniform("radius"), this.loader.skybox.distance), A.uniform3fv(B.uniform("origin"), this.loader.skybox.origin), A.uniform1f(B.uniform("fade"), o), A.activeTexture(A.TEXTURE0), A.bindTexture(A.TEXTURE_CUBE_MAP, I.skyboxTexture), A.uniform1i(B.uniform("s0"), 0);
    let e = B.attrib("a_pos");
    A.bindBuffer(A.ARRAY_BUFFER, I.skyboxVertices), A.vertexAttribPointer(e, 3, A.FLOAT, false, 0, 0), A.vertexAttribDivisor(e, 0), A.enableVertexAttribArray(e), A.drawArrays(A.TRIANGLES, 0, 6 * 2 * 3), A.disableVertexAttribArray(e);
  }
  draw(A, g, I, B, C) {
    let Q = this.glObjects, E = this.shaderParams;
    if (!Q) return;
    this.hasSkyboxTexture && !g && this.drawSkybox(A, C), g || this.requestSort(C.position, C.direction), this.tickAnimation();
    let t = this.shaderHooks != null, o = (this.semanticsMask != 255 || t) && this.hasSemanticsTexture, e = (this.hasSphericalHarmonicsTexture ? 2 : 0) | (o ? 4 : 0), i;
    g ? (i = this.getShader(A, "splat", k, H, e | 1), A.clearColor(1, 1, 1, 1), A.disable(A.BLEND)) : (i = this.getShader(A, "splat", k, H, e), A.enable(A.BLEND), A.blendFunc(A.ONE, A.ONE_MINUS_SRC_ALPHA)), A.disable(A.DEPTH_TEST), i.use(), A.uniformMatrix4fv(i.uniform("view"), false, C.viewMatrix), A.uniformMatrix4fv(i.uniform("proj"), false, C.projectionMatrix), A.uniform2f(i.uniform("res2"), I / 2, B / 2), A.uniform2f(i.uniform("ires2"), 2 / I, 2 / B), A.uniform1f(i.uniform("inv_sqrt_scale_to_world"), 1 / Math.sqrt(this.loader.scaleToWorld)), A.uniform1i(i.uniform("offset"), this.sortEnd), A.uniform1i(i.uniform("size"), this.numSplats), A.uniform3f(i.uniform("zs_aa_ts"), 1 / C.far, this.loader.antialias, Math.pow(E.tweakScale, 2)), A.uniform3fv(i.uniform("cpos"), C.position), A.uniform3fv(i.uniform("scene_center"), this.loader.sceneCenter), A.uniform2f(i.uniform("load_r"), E.loadR2, 1 / (E.loadR2 - E.loadR1)), A.uniform2f(i.uniform("reveal_r"), E.revealR2, 1 / (E.revealR2 - E.revealR1)), A.uniform2f(i.uniform("solid_r"), E.solidR2, 1 / (E.solidR2 - E.solidR1)), A.uniform1i(i.uniform("debug"), E.debugView), A.uniform1f(i.uniform("ellip"), E.debugView > 0 ? 1 : 0), e & 4 && (A.uniform1ui(i.uniform("semanticsMask"), this.semanticsMask), A.activeTexture(A.TEXTURE3), A.bindTexture(A.TEXTURE_2D, Q.semanticsTexture), A.uniform1i(i.uniform("s3"), 3)), A.activeTexture(A.TEXTURE0), A.bindTexture(A.TEXTURE_2D, Q.gaussTextures[0]), A.uniform1i(i.uniform("s0"), 0), A.activeTexture(A.TEXTURE1), A.bindTexture(A.TEXTURE_2D, Q.gaussTextures[1]), A.uniform1i(i.uniform("s1"), 1), !g && this.loader.haveSH && (A.activeTexture(A.TEXTURE2), A.bindTexture(A.TEXTURE_2D, Q.shTexture), A.uniform1i(i.uniform("s2"), 2)), A.bindBuffer(A.ARRAY_BUFFER, Q.quadVertices);
    let D = i.attrib("a0");
    A.vertexAttribPointer(D, 2, A.FLOAT, false, 0, 0), A.vertexAttribDivisor(D, 0), A.enableVertexAttribArray(D), A.bindBuffer(A.ARRAY_BUFFER, Q.indexBuffer1);
    let s = i.attrib("a1");
    A.vertexAttribIPointer(s, 1, A.INT, 0, 0), A.vertexAttribDivisor(s, 1), A.enableVertexAttribArray(s), this.numSplats < this.sortEnd && console.log("!"), A.drawArraysInstanced(A.TRIANGLE_FAN, 0, 4, this.numSplats - this.sortEnd + this.numVisible), A.disableVertexAttribArray(D), A.disableVertexAttribArray(s), this.sortAge++;
  }
  requestSort(A, g) {
    return this.worker && this.loader.radiusList && !this.workerBusy && (this.cameraChanged(A, g) || this.numWorkerPoints > this.lastWorkerPoints || this.sortAge >= this.maxSortAge || this.needsSort) ? (this.queueSort(A, g), this.needsSort = false, true) : false;
  }
  queueSort(A, g, I) {
    let B = tA(eA(A, this.loader.sceneCenter)), C = 1.5 * Math.max(B, this.loader.minRadius), Q = this.loader.radiusList, E = Q.findIndex((o) => o > C), t;
    if (E >= 0 && this.enableEnd ? (t = E * this.loader.radiusStep, t = Math.min(t, this.loader.numSplats)) : (E = Q.length - 1, t = this.loader.numSplats), t = Math.min(t, this.numWorkerPoints), this.worker.postMessage({ type: "sort", eye: A, dir: g, end: t, bound: Q[E] }), this.workerBusy = true, P(A, this.lastEye), P(g, this.lastDir), this.lastWorkerPoints = this.numWorkerPoints, I) {
      let o = (e) => {
        var _a;
        e.data.type === "indices" && ((_a = this.worker) == null ? void 0 : _a.removeEventListener("message", o), I());
      };
      this.worker.addEventListener("message", o, { once: true });
    }
  }
  tickAnimation() {
    if (this.loadingAnimation.enabled) {
      let A = false;
      A = this.tickSoftLoadingBoundary() || A, A = this.tickLoadingAnimation() || A, A ? this.events.onRequestRender.dispatch() : this.loadingAnimation.enabled = !this.loader.isComplete;
    } else {
      let A = this.loader.sceneRadius(), g = 1.1;
      this.shaderParams.loadR1 = A, this.shaderParams.loadR2 = A * g, this.shaderParams.revealR1 = A, this.shaderParams.revealR2 = A * g, this.shaderParams.solidR1 = A, this.shaderParams.solidR2 = A * g;
    }
  }
  setShaderHooks(A) {
    for (let g of this.compiledShaders.values()) for (let I of g.values()) I.delete();
    this.compiledShaders.clear(), this.shaderHooks = A;
  }
  dispose() {
    this.worker && this.worker.terminate(), this.glObjects && (this.glObjects.dispose(), this.glObjects = null);
    for (let A of this.compiledShaders.values()) for (let g of A.values()) g.delete();
    this.compiledShaders.clear();
  }
  initWorker() {
    this.worker.postMessage({ type: "init", maxpts: this.loader.totalSplats });
  }
  updateWorkerPoints() {
    let A = this.loader.cpuPtsCount;
    if (this.worker && this.numWorkerPoints < A) {
      let g = A - this.numWorkerPoints, I = new Uint16Array(3 * g);
      I.set(this.loader.cpuPoints.subarray(3 * this.numWorkerPoints, 3 * A)), this.worker.postMessage({ type: "points", data: I, offset: this.numWorkerPoints }), this.numWorkerPoints = A;
    }
  }
  cameraChanged(A, g) {
    let I = v(A, this.lastEye), B = v(g, this.lastDir);
    return I > 1e-3 || B > 1e-3;
  }
  onWorkerMessage(A) {
    let g = "color: #ff00ff", I = A.data;
    switch (I.type) {
      case "indices":
        this.partialIndices = I.indices, this.sortEnd = I.offset, this.queueUpdateIndices(), this.workerBusy = false, this.sortAge = 0, this.events.onRequestRender.dispatch();
        break;
      case "ready":
        this.workerBusy = false, console.log("%cSorter ready.", g), this.events.onRequestRender.dispatch(), this.resolveWorkerReady(this.worker);
        break;
      case "log":
        console.log(`%cSorter: ${I.str}`, g);
        break;
      case "error":
        console.error(`%cSorter error: ${I.str}`, g), this.events.onWorkerError.dispatch(I.str);
        break;
    }
  }
  partialUpdate() {
    this.updateWorkerPoints(), this.queueUpdateIndices(), this.needsSort = true, this.events.onRequestRender.dispatch(), this.loadingAnimation.startTime_ms < 0 && (this.loadingAnimation.startTime_ms = performance.now());
  }
  syncQueuedGpuData(A) {
    if (!this.glObjects) return false;
    let g = this.glObjects, I = false;
    if (this.indicesNeedUpload) {
      A.bindBuffer(A.ARRAY_BUFFER, g.indexBuffer1);
      let C = (this.loader.numSplats - this.sortEnd) * this.partialIndices.BYTES_PER_ELEMENT;
      A.bufferSubData(A.ARRAY_BUFFER, C, this.partialIndices), A.bindBuffer(A.ARRAY_BUFFER, null), this.numVisible = this.partialIndices.length, this.indicesNeedUpload = false, I = true;
    }
    for (let B = 0; B < 2; B++) {
      let C = this.gaussTextureUpdatePayloads[B];
      if (C == null) continue;
      let Q = this.gaussTextureUploadedHeight[B], E = C.currentHeight - Q, t = g.gaussTextures[B];
      if (E > 0) {
        A.bindTexture(A.TEXTURE_2D, t), this.gaussTextureUploadedHeight[B] === 0 && (A.texImage2D(A.TEXTURE_2D, 0, C.internalFormat, C.width, C.height, 0, C.format, C.type, null), this.setGLTexParameters(A, A.TEXTURE_2D, C.minMagFilter));
        let o = Q * C.width * C.channels, e = this.replaceGLPixelStoreParametersForPayload(A, C);
        A.texSubImage2D(A.TEXTURE_2D, 0, 0, Q, C.width, E, C.format, C.type, C.data, o), this.setGLPixelStoreParameters(A, e);
      }
      this.gaussTextureUploadedHeight[B] = C.currentHeight, this.gaussTextureUpdatePayloads[B] = null, I = true;
    }
    for (this.shTextureUpdatePayload && (this.uploadTexture(A, g.shTexture, this.shTextureUpdatePayload), this.hasSphericalHarmonicsTexture = true, this.shTextureUpdatePayload = null, I = true), this.semanticsTextureUpdatePayload && (this.uploadTexture(A, g.semanticsTexture, this.semanticsTextureUpdatePayload), this.hasSemanticsTexture = true, this.semanticsTextureUpdatePayload = null, I = true); this.skyboxTextureUpdatePayload.length; ) {
      let B = this.skyboxTextureUpdatePayload.shift();
      this.uploadTexture(A, g.skyboxTexture, B), this.hasSkyboxTexture = true, I = true;
    }
    return I;
  }
  uploadTexture(A, g, I) {
    let B = I.target;
    B >= A.TEXTURE_CUBE_MAP_POSITIVE_X && B <= A.TEXTURE_CUBE_MAP_NEGATIVE_Z && (B = A.TEXTURE_CUBE_MAP), A.bindTexture(B, g);
    let C = this.replaceGLPixelStoreParametersForPayload(A, I);
    A.texImage2D(I.target, 0, I.internalFormat, I.width, I.height, 0, I.format, I.type, I.data), this.setGLTexParameters(A, B, I.minMagFilter), this.setGLPixelStoreParameters(A, C);
  }
  replaceGLPixelStoreParametersForPayload(A, g) {
    let I = this.getGLPixelStoreParameters(A), B = { UNPACK_ALIGNMENT: 1, UNPACK_FLIP_Y_WEBGL: false, UNPACK_PREMULTIPLY_ALPHA_WEBGL: false, UNPACK_COLORSPACE_CONVERSION_WEBGL: A.NONE, UNPACK_ROW_LENGTH: g.width, UNPACK_IMAGE_HEIGHT: g.height, UNPACK_SKIP_PIXELS: 0, UNPACK_SKIP_ROWS: 0, UNPACK_SKIP_IMAGES: 0 };
    return this.setGLPixelStoreParameters(A, B), I;
  }
  getGLPixelStoreParameters(A) {
    return { UNPACK_ALIGNMENT: A.getParameter(A.UNPACK_ALIGNMENT), UNPACK_FLIP_Y_WEBGL: A.getParameter(A.UNPACK_FLIP_Y_WEBGL), UNPACK_PREMULTIPLY_ALPHA_WEBGL: A.getParameter(A.UNPACK_PREMULTIPLY_ALPHA_WEBGL), UNPACK_COLORSPACE_CONVERSION_WEBGL: A.getParameter(A.UNPACK_COLORSPACE_CONVERSION_WEBGL), UNPACK_ROW_LENGTH: A.getParameter(A.UNPACK_ROW_LENGTH), UNPACK_IMAGE_HEIGHT: A.getParameter(A.UNPACK_IMAGE_HEIGHT), UNPACK_SKIP_PIXELS: A.getParameter(A.UNPACK_SKIP_PIXELS), UNPACK_SKIP_ROWS: A.getParameter(A.UNPACK_SKIP_ROWS), UNPACK_SKIP_IMAGES: A.getParameter(A.UNPACK_SKIP_IMAGES) };
  }
  setGLPixelStoreParameters(A, g) {
    A.pixelStorei(A.UNPACK_ALIGNMENT, g.UNPACK_ALIGNMENT), A.pixelStorei(A.UNPACK_FLIP_Y_WEBGL, g.UNPACK_FLIP_Y_WEBGL), A.pixelStorei(A.UNPACK_PREMULTIPLY_ALPHA_WEBGL, g.UNPACK_PREMULTIPLY_ALPHA_WEBGL), A.pixelStorei(A.UNPACK_COLORSPACE_CONVERSION_WEBGL, g.UNPACK_COLORSPACE_CONVERSION_WEBGL), A.pixelStorei(A.UNPACK_ROW_LENGTH, g.UNPACK_ROW_LENGTH), A.pixelStorei(A.UNPACK_IMAGE_HEIGHT, g.UNPACK_IMAGE_HEIGHT), A.pixelStorei(A.UNPACK_SKIP_PIXELS, g.UNPACK_SKIP_PIXELS), A.pixelStorei(A.UNPACK_SKIP_ROWS, g.UNPACK_SKIP_ROWS), A.pixelStorei(A.UNPACK_SKIP_IMAGES, g.UNPACK_SKIP_IMAGES);
  }
  setGLTexParameters(A, g, I) {
    A.texParameteri(g, A.TEXTURE_MIN_FILTER, I), A.texParameteri(g, A.TEXTURE_MAG_FILTER, I), A.texParameteri(g, A.TEXTURE_WRAP_S, A.CLAMP_TO_EDGE), A.texParameteri(g, A.TEXTURE_WRAP_T, A.CLAMP_TO_EDGE), g == WebGL2RenderingContext.TEXTURE_CUBE_MAP && A.texParameteri(g, A.TEXTURE_WRAP_R, A.CLAMP_TO_EDGE);
  }
  initGLObjects(A) {
    let g = this.loader.totalSplats, I = A.createBuffer(), B = A.createBuffer();
    if (!I || !B) throw new Error("Failed to create buffer");
    A.bindBuffer(A.ARRAY_BUFFER, I), A.bufferData(A.ARRAY_BUFFER, 4 * g, A.DYNAMIC_DRAW), A.bindBuffer(A.ARRAY_BUFFER, B), A.bufferData(A.ARRAY_BUFFER, 4 * g, A.DYNAMIC_DRAW), this.sortEnd === 0 && (this.sortEnd = this.loader.numSplats);
    let C = A.createTexture(), Q = A.createTexture(), E = A.createTexture(), t = A.createTexture(), o = A.createTexture();
    if (!C || !Q || !E || !t || !o) throw new Error("Failed to create texture");
    let e = A.createBuffer();
    if (!e) throw new Error("Failed to create buffer");
    A.bindBuffer(A.ARRAY_BUFFER, e), A.bufferData(A.ARRAY_BUFFER, new Float32Array([-2, -2, 2, -2, 2, 2, -2, 2]), A.STATIC_DRAW);
    let i = this.createSkyboxCube(A);
    this.glObjects = { gaussTextures: [C, Q], shTexture: E, semanticsTexture: t, indexBuffer1: I, indexBuffer2: B, quadVertices: e, skyboxTexture: o, skyboxVertices: i, dispose: () => {
      A.deleteTexture(C), A.deleteTexture(Q), A.deleteTexture(E), A.deleteBuffer(I), A.deleteBuffer(B), A.deleteTexture(o), A.deleteBuffer(i);
    } };
  }
  createSkyboxCube(A) {
    let g = A.createBuffer();
    if (!g) throw new Error("Failed to create buffer");
    return A.bindBuffer(A.ARRAY_BUFFER, g), A.bufferData(A.ARRAY_BUFFER, Z, A.STATIC_DRAW), g;
  }
  getShader(A, g, I, B, C) {
    let Q = this.compiledShaders.get(g);
    Q || (Q = /* @__PURE__ */ new Map(), this.compiledShaders.set(g, Q));
    let E = Q.get(C);
    return E || (E = this.compileShader(A, I, B, C, this.shaderHooks), Q.set(C, E)), E;
  }
  compileShader(A, g, I, B, C) {
    let Q = this.getShaderDefines(B), E = _(g, I, C);
    return new u(A, E.vertexShader, E.fragmentShader, Q);
  }
  getShaderDefines(A) {
    let g = "";
    for (let I in aA) {
      let B = parseInt(I);
      A & B && (g += `#define ${aA[B]}
`);
    }
    return g;
  }
  queueUpdateIndices() {
    this.indicesNeedUpload = this.partialIndices.length >= 0 && this.loaderMetaReady;
  }
  queueUpdateGaussTexture(A, g) {
    this.gaussTextureUpdatePayloads[A] = g;
  }
  queueUpdateSHTexture(A) {
    this.shTextureUpdatePayload = A;
  }
  queueUpdateSemanticsTexture(A) {
    this.semanticsTextureUpdatePayload = A;
  }
  queueUpdateSkyboxTexture(A) {
    this.skyboxTextureUpdatePayload.push(A);
  }
  convertSemanticsToTiledTexture(A) {
    let g = 2048, I = A.byteLength * 8 - 1, B = this.tiledCoords(I | 0).y, C = Math.ceil(B / 4) * 4, Q = 1, E = WebGL2RenderingContext.R8UI, t = WebGL2RenderingContext.RED_INTEGER, o = WebGL2RenderingContext.UNSIGNED_BYTE, e = new Uint8Array(g * C * Q), i = new Uint8Array(A);
    for (let s = 0; s < i.length; s++) {
      let r = i[s];
      for (let y = 0; y < 8; y++) {
        let h = s * 8 + y, R = r >> y & 1, { x: M, y: d } = this.tiledCoords(h | 0), Y = d * g + M, f = R + 1;
        e[Y * Q] = f;
      }
    }
    return { target: WebGL2RenderingContext.TEXTURE_2D, internalFormat: E, format: t, type: o, width: g, height: C, data: e, currentHeight: C, channels: Q, minMagFilter: WebGL2RenderingContext.NEAREST, unpackAlignment: 1, complete: true };
  }
  tiledCoords(A) {
    return { x: 4 * (A >> 4 & 511) + (A & 3), y: 4 * (A >> 13) + (A >> 2 & 3) };
  }
  tickLoadingAnimation() {
    if (this.loadingAnimation.startTime_ms < 0) return false;
    let A = this.shaderParams, g = this.loadingAnimation, I = this.loader.sceneRadius();
    g.particleRevealEnabled || (this.shaderParams.revealR1 = I, this.shaderParams.revealR2 = I, this.shaderParams.solidR1 = I, this.shaderParams.solidR2 = I);
    let B = (performance.now() - g.startTime_ms + g.particleRevealOffset_ms) * g.particleRevealSpeed, C = () => A.revealR1 >= I && A.solidR1 >= I;
    if (C()) return false;
    let Q = function(o) {
      return Math.pow(Math.max((o + 1e3) / 5e3, 0), 2.5);
    }, E = Q(B) * I;
    A.revealR1 = E * 0.9, A.revealR2 = E * 1.1;
    let t = Q(B - g.particleSolidDelay_ms) * I;
    return A.solidR1 = t * 0.9, A.solidR2 = t * 1.1, !C();
  }
  tickSoftLoadingBoundary() {
    let A = this.shaderParams, g = this.loader.loadedRadius, I = 0.99;
    if (A.loadR1 / g > I || A.loadR2 / g > I) return false;
    let B = 0.1, C = 0.05;
    return A.loadR1 = C * g + (1 - C) * A.loadR1, A.loadR2 = B * g + (1 - B) * A.loadR2, true;
  }
  handleQuirks() {
    this.loader.metaReady.then((A) => {
      let g = A.radiusList, I = true;
      for (let B = 1; B < g.length; B++) if (g[B] < g[B - 1]) {
        I = false;
        break;
      }
      I || (this.enableEnd = false, console.warn("Disabling partial sort due to non-monotonic radius list", this.loader.dataset));
    });
  }
};
var k = EA;
var H = iA;
var V = b;
var z = X;
var Z = new Float32Array([1, -1, -1, 1, -1, 1, 1, 1, 1, 1, 1, 1, 1, 1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 1, 1, -1, -1, 1, -1, 1, -1, 1, 1, -1, 1, 1, 1, 1, 1, 1, -1, 1, 1, -1, 1, -1, -1, -1, -1, -1, -1, 1, 1, -1, -1, 1, -1, -1, -1, -1, 1, 1, -1, 1, -1, -1, 1, -1, 1, 1, 1, 1, 1, 1, 1, 1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 1, 1, -1, -1, 1, -1]);
function _(a2, A, g) {
  let I = [], B = [];
  if (g == null ? void 0 : g.vertexShaderHooks) {
    let C = g.vertexShaderHooks;
    if (C == null ? void 0 : C.additionalUniforms) for (let Q in C.additionalUniforms) {
      let E = C.additionalUniforms[Q];
      I.push(`uniform ${E[0]} ${Q};`);
    }
    C.additionalGlobals && I.push(C.additionalGlobals), C.getSplatTransform && (I.push("#define SHADER_HOOK_getSplatTransform"), I.push(`mat4 _shaderHook_getSplatTransform${C.getSplatTransform}`)), C.onMainEnd && (I.push("#define SHADER_HOOK_onMainEnd"), I.push(`void _shaderHook_onMainEnd${C.onMainEnd}`)), C.getSplatColor && (I.push("#define SHADER_HOOK_getSplatColor"), I.push(`vec4 _shaderHook_getSplatColor${C.getSplatColor}`));
  }
  if (g == null ? void 0 : g.fragmentShaderHooks) {
    let C = g.fragmentShaderHooks;
    if (C == null ? void 0 : C.additionalUniforms) for (let Q in C.additionalUniforms) {
      let E = C.additionalUniforms[Q];
      B.push(`uniform ${E[0]} ${Q};`);
    }
    C.additionalGlobals && B.push(C.additionalGlobals), C.getFragmentColor && (B.push("#define SHADER_HOOK_getFragmentColor"), B.push(`vec4 _shaderHook_getFragmentColor${C.getFragmentColor}`));
  }
  return { vertexShader: a2.replace("#pragma shader_hook_injection", I.join(`
`)), fragmentShader: A.replace("#pragma shader_hook_injection", B.join(`
`)) };
}
var aA = { 1: "RD", 2: "HAVE_SH", 4: "HAVE_SEMANTICS" };
var lA = 'var ZI=Object.create;var $A=Object.defineProperty;var pI=Object.getOwnPropertyDescriptor;var zI=Object.getOwnPropertyNames;var uI=Object.getPrototypeOf,bI=Object.prototype.hasOwnProperty;var PI=(y,R)=>()=>(y&&(R=y(y=0)),R);var mI=(y,R)=>()=>(R||y((R={exports:{}}).exports,R),R.exports);var vI=(y,R,Q,d)=>{if(R&&typeof R=="object"||typeof R=="function")for(let K of zI(R))!bI.call(y,K)&&K!==Q&&$A(y,K,{get:()=>R[K],enumerable:!(d=pI(R,K))||d.enumerable});return y};var _I=(y,R,Q)=>(Q=y!=null?ZI(uI(y)):{},vI(R||!y||!y.__esModule?$A(Q,"default",{value:y,enumerable:!0}):Q,y));var m=PI(()=>{});var Ig=mI((gg,SA)=>{m();var Ag=(()=>{var y=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0;return function(R={}){var Q=R,d,K;Q.ready=new Promise((A,g)=>{d=A,K=g});var v=Object.assign({},Q),_=[],p="./this.program",$=(A,g)=>{throw g},KA=!0,X=!1,U="";function z(A){return Q.locateFile?Q.locateFile(A,U):U+A}var AA,gA,GA;(KA||X)&&(X?U=self.location.href:typeof document<"u"&&document.currentScript&&(U=document.currentScript.src),y&&(U=y),U.indexOf("blob:")!==0?U=U.substr(0,U.replace(/[?#].*/,"").lastIndexOf("/")+1):U="",AA=A=>{var g=new XMLHttpRequest;return g.open("GET",A,!1),g.send(null),g.responseText},X&&(GA=A=>{var g=new XMLHttpRequest;return g.open("GET",A,!1),g.responseType="arraybuffer",g.send(null),new Uint8Array(g.response)}),gA=(A,g,I)=>{var B=new XMLHttpRequest;B.open("GET",A,!0),B.responseType="arraybuffer",B.onload=()=>{if(B.status==200||B.status==0&&B.response){g(B.response);return}I()},B.onerror=I,B.send(null)});var BB=Q.print||console.log.bind(console),yA=Q.printErr||console.error.bind(console);Object.assign(Q,v),v=null,Q.arguments&&(_=Q.arguments),Q.thisProgram&&(p=Q.thisProgram),Q.quit&&($=Q.quit);var IA;Q.wasmBinary&&(IA=Q.wasmBinary),typeof WebAssembly!="object"&&CA("no native wasm support detected");function Qg(A){for(var g=atob(A),I=new Uint8Array(g.length),B=0;B<g.length;++B)I[B]=g.charCodeAt(B);return I}function Eg(A){if(dA(A))return Qg(A.slice(rA.length))}var UA,JA=!1,Dg,r,J,u,BA,M,L,hA,HA;function ig(){var A=UA.buffer;Q.HEAP8=r=new Int8Array(A),Q.HEAP16=u=new Int16Array(A),Q.HEAPU8=J=new Uint8Array(A),Q.HEAPU16=BA=new Uint16Array(A),Q.HEAP32=M=new Int32Array(A),Q.HEAPU32=L=new Uint32Array(A),Q.HEAPF32=hA=new Float32Array(A),Q.HEAPF64=HA=new Float64Array(A)}var kA=[],cA=[],tA=[],og=!1;function wg(){if(Q.preRun)for(typeof Q.preRun=="function"&&(Q.preRun=[Q.preRun]);Q.preRun.length;)yg(Q.preRun.shift());NA(kA)}function Fg(){og=!0,NA(cA)}function Gg(){if(Q.postRun)for(typeof Q.postRun=="function"&&(Q.postRun=[Q.postRun]);Q.postRun.length;)Ng(Q.postRun.shift());NA(tA)}function yg(A){kA.unshift(A)}function Rg(A){cA.unshift(A)}function Ng(A){tA.unshift(A)}var T=0,RA=null,b=null;function sg(A){T++,Q.monitorRunDependencies&&Q.monitorRunDependencies(T)}function ag(A){if(T--,Q.monitorRunDependencies&&Q.monitorRunDependencies(T),T==0&&(RA!==null&&(clearInterval(RA),RA=null),b)){var g=b;b=null,g()}}function CA(A){Q.onAbort&&Q.onAbort(A),A="Aborted("+A+")",yA(A),JA=!0,Dg=1,A+=". Build with -sASSERTIONS for more info.";var g=new WebAssembly.RuntimeError(A);throw K(g),g}var rA="data:application/octet-stream;base64,",dA=A=>A.startsWith(rA),V;V="data:application/octet-stream;base64,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",dA(V)||(V=z(V));function Lg(A){if(A==V&&IA)return new Uint8Array(IA);var g=Eg(A);if(g)return g;if(GA)return GA(A);throw"both async and sync fetching of the wasm failed"}function Mg(A){return Promise.resolve().then(()=>Lg(A))}function Sg(A,g,I){return Mg(A).then(B=>WebAssembly.instantiate(B,g)).then(B=>B).then(I,B=>{yA(`failed to asynchronously prepare wasm: ${B}`),CA(B)})}function Yg(A,g,I,B){return Sg(g,I,B)}function Kg(){var A={a:eI};function g(B,C){return Y=B.exports,UA=Y.x,ig(),lA=Y.z,Rg(Y.y),ag("wasm-instantiate"),Y}sg("wasm-instantiate");function I(B){g(B.instance)}if(Q.instantiateWasm)try{return Q.instantiateWasm(A,g)}catch(B){yA(`Module.instantiateWasm callback failed with error: ${B}`),K(B)}return Yg(IA,V,A,I).catch(K),{}}var NA=A=>{for(;A.length>0;)A.shift()(Q)},CB=Q.noExitRuntime||!0;function Ug(A){this.excPtr=A,this.ptr=A-24,this.set_type=function(g){L[this.ptr+4>>2]=g},this.get_type=function(){return L[this.ptr+4>>2]},this.set_destructor=function(g){L[this.ptr+8>>2]=g},this.get_destructor=function(){return L[this.ptr+8>>2]},this.set_caught=function(g){g=g?1:0,r[this.ptr+12>>0]=g},this.get_caught=function(){return r[this.ptr+12>>0]!=0},this.set_rethrown=function(g){g=g?1:0,r[this.ptr+13>>0]=g},this.get_rethrown=function(){return r[this.ptr+13>>0]!=0},this.init=function(g,I){this.set_adjusted_ptr(0),this.set_type(g),this.set_destructor(I)},this.set_adjusted_ptr=function(g){L[this.ptr+16>>2]=g},this.get_adjusted_ptr=function(){return L[this.ptr+16>>2]},this.get_exception_ptr=function(){var g=vA(this.get_type());if(g)return L[this.excPtr>>2];var I=this.get_adjusted_ptr();return I!==0?I:this.excPtr}}var qA=0,Jg=0,hg=(A,g,I)=>{var B=new Ug(A);throw B.init(g,I),qA=A,Jg++,qA},Hg=(A,g,I,B,C)=>{},kg=()=>{for(var A=new Array(256),g=0;g<256;++g)A[g]=String.fromCharCode(g);fA=A},fA,k=A=>{for(var g="",I=A;J[I];)g+=fA[J[I++]];return g},Z={},j={},QA={},nA,H=A=>{throw new nA(A)},eA,OA=A=>{throw new eA(A)},cg=(A,g,I)=>{A.forEach(function(o){QA[o]=g});function B(o){var F=I(o);F.length!==A.length&&OA("Mismatched type converter count");for(var w=0;w<A.length;++w)q(A[w],F[w])}var C=new Array(g.length),D=[],i=0;g.forEach((o,F)=>{j.hasOwnProperty(o)?C[F]=j[o]:(D.push(o),Z.hasOwnProperty(o)||(Z[o]=[]),Z[o].push(()=>{C[F]=j[o],++i,i===D.length&&B(C)}))}),D.length===0&&B(C)};function tg(A,g,I={}){var B=g.name;if(A||H(`type "${B}" must have a positive integer typeid pointer`),j.hasOwnProperty(A)){if(I.ignoreDuplicateRegistrations)return;H(`Cannot register type \'${B}\' twice`)}if(j[A]=g,delete QA[A],Z.hasOwnProperty(A)){var C=Z[A];delete Z[A],C.forEach(D=>D())}}function q(A,g,I={}){if(!("argPackAdvance"in g))throw new TypeError("registerType registeredInstance requires argPackAdvance");return tg(A,g,I)}var l=8,rg=(A,g,I,B)=>{g=k(g),q(A,{name:g,fromWireType:function(C){return!!C},toWireType:function(C,D){return D?I:B},argPackAdvance:l,readValueFromPointer:function(C){return this.fromWireType(J[C])},destructorFunction:null})};function dg(){Object.assign(WA.prototype,{get(A){return this.allocated[A]},has(A){return this.allocated[A]!==void 0},allocate(A){var g=this.freelist.pop()||this.allocated.length;return this.allocated[g]=A,g},free(A){this.allocated[A]=void 0,this.freelist.push(A)}})}function WA(){this.allocated=[void 0],this.freelist=[]}var c=new WA,TA=A=>{A>=c.reserved&&--c.get(A).refcount===0&&c.free(A)},qg=()=>{for(var A=0,g=c.reserved;g<c.allocated.length;++g)c.allocated[g]!==void 0&&++A;return A},fg=()=>{c.allocated.push({value:void 0},{value:null},{value:!0},{value:!1}),c.reserved=c.allocated.length,Q.count_emval_handles=qg},W={toValue:A=>(A||H("Cannot use deleted val. handle = "+A),c.get(A).value),toHandle:A=>{switch(A){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return c.allocate({refcount:1,value:A})}}};function jA(A){return this.fromWireType(M[A>>2])}var ng=(A,g)=>{g=k(g),q(A,{name:g,fromWireType:I=>{var B=W.toValue(I);return TA(I),B},toWireType:(I,B)=>W.toHandle(B),argPackAdvance:l,readValueFromPointer:jA,destructorFunction:null})},eg=(A,g)=>{switch(g){case 4:return function(I){return this.fromWireType(hA[I>>2])};case 8:return function(I){return this.fromWireType(HA[I>>3])};default:throw new TypeError(`invalid float width (${g}): ${A}`)}},Og=(A,g,I)=>{g=k(g),q(A,{name:g,fromWireType:B=>B,toWireType:(B,C)=>C,argPackAdvance:l,readValueFromPointer:eg(g,I),destructorFunction:null})},sA=(A,g)=>Object.defineProperty(g,"name",{value:A}),Wg=A=>{for(;A.length;){var g=A.pop(),I=A.pop();I(g)}};function Tg(A,g){if(!(A instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof A} which is not a function`);var I=sA(A.name||"unknownFunctionName",function(){});I.prototype=A.prototype;var B=new I,C=A.apply(B,g);return C instanceof Object?C:B}function jg(A,g,I,B,C,D){var i=g.length;i<2&&H("argTypes array size mismatch! Must at least get return value and \'this\' types!");for(var o=g[1]!==null&&I!==null,F=!1,w=1;w<g.length;++w)if(g[w]!==null&&g[w].destructorFunction===void 0){F=!0;break}for(var a=g[0].name!=="void",N="",s="",w=0;w<i-2;++w)N+=(w!==0?", ":"")+"arg"+w,s+=(w!==0?", ":"")+"arg"+w+"Wired";var S=`\n        return function (${N}) {\n        if (arguments.length !== ${i-2}) {\n          throwBindingError(\'function ${A} called with \' + arguments.length + \' arguments, expected ${i-2}\');\n        }`;F&&(S+=`var destructors = [];\n`);var n=F?"destructors":"null",e=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],O=[H,B,C,Wg,g[0],g[1]];o&&(S+="var thisWired = classParam.toWireType("+n+`, this);\n`);for(var w=0;w<i-2;++w)S+="var arg"+w+"Wired = argType"+w+".toWireType("+n+", arg"+w+"); // "+g[w+2].name+`\n`,e.push("argType"+w),O.push(g[w+2]);if(o&&(s="thisWired"+(s.length>0?", ":"")+s),S+=(a||D?"var rv = ":"")+"invoker(fn"+(s.length>0?", ":"")+s+`);\n`,F)S+=`runDestructors(destructors);\n`;else for(var w=o?1:2;w<g.length;++w){var x=w===1?"thisWired":"arg"+(w-2)+"Wired";g[w].destructorFunction!==null&&(S+=x+"_dtor("+x+"); // "+g[w].name+`\n`,e.push(x+"_dtor"),O.push(g[w].destructorFunction))}a&&(S+=`var ret = retType.fromWireType(rv);\nreturn ret;\n`),S+=`}\n`,e.push(S);var E=Tg(Function,e).apply(null,O);return sA(A,E)}var lg=(A,g,I)=>{if(A[g].overloadTable===void 0){var B=A[g];A[g]=function(){return A[g].overloadTable.hasOwnProperty(arguments.length)||H(`Function \'${I}\' called with an invalid number of arguments (${arguments.length}) - expects one of (${A[g].overloadTable})!`),A[g].overloadTable[arguments.length].apply(this,arguments)},A[g].overloadTable=[],A[g].overloadTable[B.argCount]=B}},xg=(A,g,I)=>{Q.hasOwnProperty(A)?((I===void 0||Q[A].overloadTable!==void 0&&Q[A].overloadTable[I]!==void 0)&&H(`Cannot register public name \'${A}\' twice`),lg(Q,A,A),Q.hasOwnProperty(I)&&H(`Cannot register multiple overloads of a function with the same number of arguments (${I})!`),Q[A].overloadTable[I]=g):(Q[A]=g,I!==void 0&&(Q[A].numArguments=I))},Xg=(A,g)=>{for(var I=[],B=0;B<A;B++)I.push(L[g+B*4>>2]);return I},Vg=(A,g,I)=>{Q.hasOwnProperty(A)||OA("Replacing nonexistant public symbol"),Q[A].overloadTable!==void 0&&I!==void 0?Q[A].overloadTable[I]=g:(Q[A]=g,Q[A].argCount=I)},Zg=(A,g,I)=>{var B=Q["dynCall_"+A];return I&&I.length?B.apply(null,[g].concat(I)):B.call(null,g)},EA=[],lA,xA=A=>{var g=EA[A];return g||(A>=EA.length&&(EA.length=A+1),EA[A]=g=lA.get(A)),g},pg=(A,g,I)=>{if(A.includes("j"))return Zg(A,g,I);var B=xA(g).apply(null,I);return B},zg=(A,g)=>{var I=[];return function(){return I.length=0,Object.assign(I,arguments),pg(A,g,I)}},ug=(A,g)=>{A=k(A);function I(){return A.includes("j")?zg(A,g):xA(g)}var B=I();return typeof B!="function"&&H(`unknown function pointer with signature ${A}: ${g}`),B},bg=(A,g)=>{var I=sA(g,function(B){this.name=g,this.message=B;var C=new Error(B).stack;C!==void 0&&(this.stack=this.toString()+`\n`+C.replace(/^Error(:[^\\n]*)?\\n/,""))});return I.prototype=Object.create(A.prototype),I.prototype.constructor=I,I.prototype.toString=function(){return this.message===void 0?this.name:`${this.name}: ${this.message}`},I},XA,VA=A=>{var g=mA(A),I=k(g);return f(g),I},Pg=(A,g)=>{var I=[],B={};function C(D){if(!B[D]&&!j[D]){if(QA[D]){QA[D].forEach(C);return}I.push(D),B[D]=!0}}throw g.forEach(C),new XA(`${A}: `+I.map(VA).join([", "]))},mg=A=>{A=A.trim();let g=A.indexOf("(");return g!==-1?A.substr(0,g):A},vg=(A,g,I,B,C,D,i)=>{var o=Xg(g,I);A=k(A),A=mg(A),C=ug(B,C),xg(A,function(){Pg(`Cannot call ${A} due to unbound types`,o)},g-1),cg([],o,function(F){var w=[F[0],null].concat(F.slice(1));return Vg(A,jg(A,w,null,C,D,i),g-1),[]})},_g=(A,g,I)=>{switch(g){case 1:return I?B=>r[B>>0]:B=>J[B>>0];case 2:return I?B=>u[B>>1]:B=>BA[B>>1];case 4:return I?B=>M[B>>2]:B=>L[B>>2];default:throw new TypeError(`invalid integer width (${g}): ${A}`)}},$g=(A,g,I,B,C)=>{g=k(g),C===-1&&(C=4294967295);var D=a=>a;if(B===0){var i=32-8*I;D=a=>a<<i>>>i}var o=g.includes("unsigned"),F=(a,N)=>{},w;o?w=function(a,N){return F(N,this.name),N>>>0}:w=function(a,N){return F(N,this.name),N},q(A,{name:g,fromWireType:D,toWireType:w,argPackAdvance:l,readValueFromPointer:_g(g,I,B!==0),destructorFunction:null})},AI=(A,g,I)=>{var B=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],C=B[g];function D(i){var o=L[i>>2],F=L[i+4>>2];return new C(r.buffer,F,o)}I=k(I),q(A,{name:I,fromWireType:D,argPackAdvance:l,readValueFromPointer:D},{ignoreDuplicateRegistrations:!0})};function gI(A){return this.fromWireType(L[A>>2])}var ZA=(A,g,I,B)=>{if(!(B>0))return 0;for(var C=I,D=I+B-1,i=0;i<A.length;++i){var o=A.charCodeAt(i);if(o>=55296&&o<=57343){var F=A.charCodeAt(++i);o=65536+((o&1023)<<10)|F&1023}if(o<=127){if(I>=D)break;g[I++]=o}else if(o<=2047){if(I+1>=D)break;g[I++]=192|o>>6,g[I++]=128|o&63}else if(o<=65535){if(I+2>=D)break;g[I++]=224|o>>12,g[I++]=128|o>>6&63,g[I++]=128|o&63}else{if(I+3>=D)break;g[I++]=240|o>>18,g[I++]=128|o>>12&63,g[I++]=128|o>>6&63,g[I++]=128|o&63}}return g[I]=0,I-C},II=(A,g,I)=>ZA(A,J,g,I),pA=A=>{for(var g=0,I=0;I<A.length;++I){var B=A.charCodeAt(I);B<=127?g++:B<=2047?g+=2:B>=55296&&B<=57343?(g+=4,++I):g+=3}return g},zA=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0,BI=(A,g,I)=>{for(var B=g+I,C=g;A[C]&&!(C>=B);)++C;if(C-g>16&&A.buffer&&zA)return zA.decode(A.subarray(g,C));for(var D="";g<C;){var i=A[g++];if(!(i&128)){D+=String.fromCharCode(i);continue}var o=A[g++]&63;if((i&224)==192){D+=String.fromCharCode((i&31)<<6|o);continue}var F=A[g++]&63;if((i&240)==224?i=(i&15)<<12|o<<6|F:i=(i&7)<<18|o<<12|F<<6|A[g++]&63,i<65536)D+=String.fromCharCode(i);else{var w=i-65536;D+=String.fromCharCode(55296|w>>10,56320|w&1023)}}return D},DA=(A,g)=>A?BI(J,A,g):"",CI=(A,g)=>{g=k(g);var I=g==="std::string";q(A,{name:g,fromWireType(B){var C=L[B>>2],D=B+4,i;if(I)for(var o=D,F=0;F<=C;++F){var w=D+F;if(F==C||J[w]==0){var a=w-o,N=DA(o,a);i===void 0?i=N:(i+=String.fromCharCode(0),i+=N),o=w+1}}else{for(var s=new Array(C),F=0;F<C;++F)s[F]=String.fromCharCode(J[D+F]);i=s.join("")}return f(B),i},toWireType(B,C){C instanceof ArrayBuffer&&(C=new Uint8Array(C));var D,i=typeof C=="string";i||C instanceof Uint8Array||C instanceof Uint8ClampedArray||C instanceof Int8Array||H("Cannot pass non-string to std::string"),I&&i?D=pA(C):D=C.length;var o=MA(4+D+1),F=o+4;if(L[o>>2]=D,I&&i)II(C,F,D+1);else if(i)for(var w=0;w<D;++w){var a=C.charCodeAt(w);a>255&&(f(F),H("String has UTF-16 code units that do not fit in 8 bits")),J[F+w]=a}else for(var w=0;w<D;++w)J[F+w]=C[w];return B!==null&&B.push(f,o),o},argPackAdvance:l,readValueFromPointer:gI,destructorFunction(B){f(B)}})},uA=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0,QI=(A,g)=>{for(var I=A,B=I>>1,C=B+g/2;!(B>=C)&&BA[B];)++B;if(I=B<<1,I-A>32&&uA)return uA.decode(J.subarray(A,I));for(var D="",i=0;!(i>=g/2);++i){var o=u[A+i*2>>1];if(o==0)break;D+=String.fromCharCode(o)}return D},EI=(A,g,I)=>{if(I===void 0&&(I=2147483647),I<2)return 0;I-=2;for(var B=g,C=I<A.length*2?I/2:A.length,D=0;D<C;++D){var i=A.charCodeAt(D);u[g>>1]=i,g+=2}return u[g>>1]=0,g-B},DI=A=>A.length*2,iI=(A,g)=>{for(var I=0,B="";!(I>=g/4);){var C=M[A+I*4>>2];if(C==0)break;if(++I,C>=65536){var D=C-65536;B+=String.fromCharCode(55296|D>>10,56320|D&1023)}else B+=String.fromCharCode(C)}return B},oI=(A,g,I)=>{if(I===void 0&&(I=2147483647),I<4)return 0;for(var B=g,C=B+I-4,D=0;D<A.length;++D){var i=A.charCodeAt(D);if(i>=55296&&i<=57343){var o=A.charCodeAt(++D);i=65536+((i&1023)<<10)|o&1023}if(M[g>>2]=i,g+=4,g+4>C)break}return M[g>>2]=0,g-B},wI=A=>{for(var g=0,I=0;I<A.length;++I){var B=A.charCodeAt(I);B>=55296&&B<=57343&&++I,g+=4}return g},FI=(A,g,I)=>{I=k(I);var B,C,D,i,o;g===2?(B=QI,C=EI,i=DI,D=()=>BA,o=1):g===4&&(B=iI,C=oI,i=wI,D=()=>L,o=2),q(A,{name:I,fromWireType:F=>{for(var w=L[F>>2],a=D(),N,s=F+4,S=0;S<=w;++S){var n=F+4+S*g;if(S==w||a[n>>o]==0){var e=n-s,O=B(s,e);N===void 0?N=O:(N+=String.fromCharCode(0),N+=O),s=n+g}}return f(F),N},toWireType:(F,w)=>{typeof w!="string"&&H(`Cannot pass non-string to C++ string type ${I}`);var a=i(w),N=MA(4+a+g);return L[N>>2]=a>>o,C(w,N+4,a+g),F!==null&&F.push(f,N),N},argPackAdvance:l,readValueFromPointer:jA,destructorFunction(F){f(F)}})},GI=(A,g)=>{g=k(g),q(A,{isVoid:!0,name:g,argPackAdvance:0,fromWireType:()=>{},toWireType:(I,B)=>{}})},yI=A=>{A>4&&(c.get(A).refcount+=1)},RI={},NI=A=>{var g=RI[A];return g===void 0?k(A):g},sI=A=>W.toHandle(NI(A)),aI=()=>W.toHandle({}),LI=(A,g,I)=>{A=W.toValue(A),g=W.toValue(g),I=W.toValue(I),A[g]=I},MI=(A,g)=>{var I=j[A];return I===void 0&&H(g+" has unknown type "+VA(A)),I},SI=(A,g)=>{A=MI(A,"_emval_take_value");var I=A.readValueFromPointer(g);return W.toHandle(I)},YI=()=>{CA("")},KI=(A,g,I)=>J.copyWithin(A,g,g+I),UI=A=>{CA("OOM")},JI=A=>{var g=J.length;A>>>=0,UI(A)},aA={},hI=()=>p||"./this.program",P=()=>{if(!P.strings){var A=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",g={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:A,_:hI()};for(var I in aA)aA[I]===void 0?delete g[I]:g[I]=aA[I];var B=[];for(var I in g)B.push(`${I}=${g[I]}`);P.strings=B}return P.strings},HI=(A,g)=>{for(var I=0;I<A.length;++I)r[g++>>0]=A.charCodeAt(I);r[g>>0]=0},LA={varargs:void 0,get(){var A=M[+LA.varargs>>2];return LA.varargs+=4,A},getp(){return LA.get()},getStr(A){var g=DA(A);return g}},kI=(A,g)=>{var I=0;return P().forEach((B,C)=>{var D=g+I;L[A+C*4>>2]=D,HI(B,D),I+=B.length+1}),0},cI=(A,g)=>{var I=P();L[A>>2]=I.length;var B=0;return I.forEach(C=>B+=C.length+1),L[g>>2]=B,0},iA=A=>A%4===0&&(A%100!==0||A%400===0),tI=(A,g)=>{for(var I=0,B=0;B<=g;I+=A[B++]);return I},bA=[31,29,31,30,31,30,31,31,30,31,30,31],PA=[31,28,31,30,31,30,31,31,30,31,30,31],rI=(A,g)=>{for(var I=new Date(A.getTime());g>0;){var B=iA(I.getFullYear()),C=I.getMonth(),D=(B?bA:PA)[C];if(g>D-I.getDate())g-=D-I.getDate()+1,I.setDate(1),C<11?I.setMonth(C+1):(I.setMonth(0),I.setFullYear(I.getFullYear()+1));else return I.setDate(I.getDate()+g),I}return I};function dI(A,g,I){var B=I>0?I:pA(A)+1,C=new Array(B),D=ZA(A,C,0,C.length);return g&&(C.length=D),C}var qI=(A,g)=>{r.set(A,g)},fI=(A,g,I,B)=>{var C=L[B+40>>2],D={tm_sec:M[B>>2],tm_min:M[B+4>>2],tm_hour:M[B+8>>2],tm_mday:M[B+12>>2],tm_mon:M[B+16>>2],tm_year:M[B+20>>2],tm_wday:M[B+24>>2],tm_yday:M[B+28>>2],tm_isdst:M[B+32>>2],tm_gmtoff:M[B+36>>2],tm_zone:C?DA(C):""},i=DA(I),o={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var F in o)i=i.replace(new RegExp(F,"g"),o[F]);var w=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],a=["January","February","March","April","May","June","July","August","September","October","November","December"];function N(E,G,t){for(var h=typeof E=="number"?E.toString():E||"";h.length<G;)h=t[0]+h;return h}function s(E,G){return N(E,G,"0")}function S(E,G){function t(wA){return wA<0?-1:wA>0?1:0}var h;return(h=t(E.getFullYear()-G.getFullYear()))===0&&(h=t(E.getMonth()-G.getMonth()))===0&&(h=t(E.getDate()-G.getDate())),h}function n(E){switch(E.getDay()){case 0:return new Date(E.getFullYear()-1,11,29);case 1:return E;case 2:return new Date(E.getFullYear(),0,3);case 3:return new Date(E.getFullYear(),0,2);case 4:return new Date(E.getFullYear(),0,1);case 5:return new Date(E.getFullYear()-1,11,31);case 6:return new Date(E.getFullYear()-1,11,30)}}function e(E){var G=rI(new Date(E.tm_year+1900,0,1),E.tm_yday),t=new Date(G.getFullYear(),0,4),h=new Date(G.getFullYear()+1,0,4),wA=n(t),VI=n(h);return S(wA,G)<=0?S(VI,G)<=0?G.getFullYear()+1:G.getFullYear():G.getFullYear()-1}var O={"%a":E=>w[E.tm_wday].substring(0,3),"%A":E=>w[E.tm_wday],"%b":E=>a[E.tm_mon].substring(0,3),"%B":E=>a[E.tm_mon],"%C":E=>{var G=E.tm_year+1900;return s(G/100|0,2)},"%d":E=>s(E.tm_mday,2),"%e":E=>N(E.tm_mday,2," "),"%g":E=>e(E).toString().substring(2),"%G":E=>e(E),"%H":E=>s(E.tm_hour,2),"%I":E=>{var G=E.tm_hour;return G==0?G=12:G>12&&(G-=12),s(G,2)},"%j":E=>s(E.tm_mday+tI(iA(E.tm_year+1900)?bA:PA,E.tm_mon-1),3),"%m":E=>s(E.tm_mon+1,2),"%M":E=>s(E.tm_min,2),"%n":()=>`\n`,"%p":E=>E.tm_hour>=0&&E.tm_hour<12?"AM":"PM","%S":E=>s(E.tm_sec,2),"%t":()=>"	","%u":E=>E.tm_wday||7,"%U":E=>{var G=E.tm_yday+7-E.tm_wday;return s(Math.floor(G/7),2)},"%V":E=>{var G=Math.floor((E.tm_yday+7-(E.tm_wday+6)%7)/7);if((E.tm_wday+371-E.tm_yday-2)%7<=2&&G++,G){if(G==53){var h=(E.tm_wday+371-E.tm_yday)%7;h!=4&&(h!=3||!iA(E.tm_year))&&(G=1)}}else{G=52;var t=(E.tm_wday+7-E.tm_yday-1)%7;(t==4||t==5&&iA(E.tm_year%400-1))&&G++}return s(G,2)},"%w":E=>E.tm_wday,"%W":E=>{var G=E.tm_yday+7-(E.tm_wday+6)%7;return s(Math.floor(G/7),2)},"%y":E=>(E.tm_year+1900).toString().substring(2),"%Y":E=>E.tm_year+1900,"%z":E=>{var G=E.tm_gmtoff,t=G>=0;return G=Math.abs(G)/60,G=G/60*100+G%60,(t?"+":"-")+("0000"+G).slice(-4)},"%Z":E=>E.tm_zone,"%%":()=>"%"};i=i.replace(/%%/g,"\\0\\0");for(var F in O)i.includes(F)&&(i=i.replace(new RegExp(F,"g"),O[F](D)));i=i.replace(/\\0\\0/g,"%");var x=dI(i,!1);return x.length>g?0:(qI(x,A),x.length-1)},nI=(A,g,I,B,C)=>fI(A,g,I,B);kg(),nA=Q.BindingError=class extends Error{constructor(g){super(g),this.name="BindingError"}},eA=Q.InternalError=class extends Error{constructor(g){super(g),this.name="InternalError"}},dg(),fg(),XA=Q.UnboundTypeError=bg(Error,"UnboundTypeError");var eI={a:hg,p:Hg,m:rg,v:ng,l:Og,e:vg,d:$g,b:AI,k:CI,i:FI,n:GI,c:TA,o:yI,h:sI,w:aI,f:LI,g:SI,j:YI,u:KI,t:JI,r:kI,s:cI,q:nI},Y=Kg(),OI=()=>(OI=Y.y)(),f=A=>(f=Y.A)(A),MA=A=>(MA=Y.B)(A),mA=A=>(mA=Y.C)(A),WI=()=>(WI=Y.__errno_location)(),vA=A=>(vA=Y.D)(A),TI=Q.dynCall_ji=(A,g)=>(TI=Q.dynCall_ji=Y.E)(A,g),jI=Q.dynCall_viijii=(A,g,I,B,C,D,i)=>(jI=Q.dynCall_viijii=Y.F)(A,g,I,B,C,D,i),lI=Q.dynCall_iiiiij=(A,g,I,B,C,D,i)=>(lI=Q.dynCall_iiiiij=Y.G)(A,g,I,B,C,D,i),xI=Q.dynCall_iiiiijj=(A,g,I,B,C,D,i,o,F)=>(xI=Q.dynCall_iiiiijj=Y.H)(A,g,I,B,C,D,i,o,F),XI=Q.dynCall_iiiiiijj=(A,g,I,B,C,D,i,o,F,w)=>(XI=Q.dynCall_iiiiiijj=Y.I)(A,g,I,B,C,D,i,o,F,w),oA;b=function A(){oA||_A(),oA||(b=A)};function _A(){if(T>0||(wg(),T>0))return;function A(){oA||(oA=!0,Q.calledRun=!0,!JA&&(Fg(),d(Q),Q.onRuntimeInitialized&&Q.onRuntimeInitialized(),Gg()))}Q.setStatus?(Q.setStatus("Running..."),setTimeout(function(){setTimeout(function(){Q.setStatus("")},1),A()},1)):A()}if(Q.preInit)for(typeof Q.preInit=="function"&&(Q.preInit=[Q.preInit]);Q.preInit.length>0;)Q.preInit.pop()();return _A(),R.ready}})();typeof gg=="object"&&typeof SA=="object"?SA.exports=Ag:typeof define=="function"&&define.amd&&define([],()=>Ag)});m();var Bg=_I(Ig()),FA=null,YA=[];function AB(y,R,Q,d,K){YA.push({data:y,maxSize:R,sd:Q,width:d,tile:K})}function Cg(){if(FA)for(;YA.length;){let{data:y,maxSize:R,sd:Q,width:d,tile:K}=YA.shift(),{input:v,tex1:_,tex2:p,xyz:$}=FA.alloc(y.length,R);v.set(y);let KA=performance.now(),X=FA.decode(Q,d,K),U=new Uint32Array(_.length),z=new Uint32Array(p.length),AA=new Uint16Array($.length);U.set(_),z.set(p),AA.set($);let gA=AA.subarray(0,3*X);postMessage({type:"decoded",size:X,tex1:U,tex2:z,xyz:gA},[U.buffer,z.buffer,gA.buffer])}}self.onmessage=function(y){let R=y.data;switch(R.type){case"decode":AB(R.data,R.maxSize,R.sd,R.width,R.tile),Cg();break}};gB("worker starting.");(0,Bg.default)().then(y=>{FA=y,postMessage({type:"ready"}),Cg()}).catch(y=>IB(y));function gB(y){postMessage({type:"log",str:y})}function IB(y){postMessage({type:"error",str:y})}\n';
var JA = new Blob([lA], { type: "text/javascript" });
var fA = URL.createObjectURL(JA);
var l = class extends (typeof Worker == "function" ? Worker : null) {
  constructor() {
    super(fA);
  }
};
var uA = "https://webapp.engineeringlumalabs.com/api/v3/captures";
var L = 2048;
var p = 4;
var T = class {
  constructor(A, g = true) {
    typeof A == "string" ? A.toLowerCase().startsWith("https://lumalabs.ai") ? this.dataset = { captureUrl: A } : this.dataset = { src: A } : this.dataset = A, this.streaming = g, this.fetchOptions = this.dataset.fetchOptions ?? void 0, this.metaVersion = 0, this.numSplats = 0, this.totalSplats = 0, this.totalBytes = 0, this.currentBytes = 0, this.initialPose = new Float32Array([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 1]), this.scaleToWorld = 1, this.animFrames = [], this.animFps = 0, this.cpuPoints = new Uint16Array(0), this.sceneCenter = [0, 0, 0], this.radiusList = null, this.radiusStep = 1024, this.minRadius = 2, this.loadedRadius = 0, this.isComplete = false, this.antialias = 0.3, this.haveSH = false, this.haveSemantics = false, this.haveSkybox = false, this.skybox = { type: "sphere", distance: 1e3, origin: [0, 0, 0] }, this.events = { partialUpdate: new G(), progress: new G(), showProgress: new G(), hideProgress: new G(), updateGauss1Texture: new G(), updateGauss2Texture: new G(), updateSHTexture: new G(), updateSkyboxTexture: new G(), updateCpuPoints: new G(), complete: new G() }, this.blocksDowloaded = 0, this.gauss1Data = null, this.gauss2Data = null, this.gauss1Count = 0, this.gauss2Count = 0, this.gauss1Height = 0, this.gauss2Height = 0, this.cpuPtsCount = 0, this.initWorker(), this.bytesReady = 0, this.totalBytes = 1e10, this.streaming ? setTimeout(() => {
      (!this.totalBytes || this.currentBytes / this.totalBytes < 0.1) && this.showProgress();
    }, 3e3) : this.showProgress(), this.metaReady, this.allReady, this.coreBinReady, this.gauss1Ready, this.gauss2Ready, this.shReady, this.semanticsReady, this.skyboxReady, this.downloadAllFiles(), this.allReady.then(() => {
      var _a;
      this.hideProgress(), this.loadedRadius = this.sceneRadius(), this.events.complete.dispatch(this), this.isComplete = true, (_a = this.worker) == null ? void 0 : _a.terminate();
    }).catch((I) => console.error(I));
  }
  sceneRadius() {
    return this.radiusList ? 1.05 * this.radiusList[this.radiusList.length - 1] : 0;
  }
  addProgress(A) {
    this.bytesReady += A;
    let g = Math.min(this.bytesReady * 100 / this.totalBytes, 100) / 100;
    this.events.progress.dispatch({ bytesReady: this.bytesReady, totalBytes: this.totalBytes, progress: g });
  }
  showProgress() {
    this.events.showProgress.dispatch();
  }
  hideProgress() {
    this.events.hideProgress.dispatch();
  }
  convertArtifactArray(A) {
    let g = {};
    for (let I of A) g[I.type] = I.url;
    return g;
  }
  async getArtifacts() {
    let A = this.dataset.uuid;
    if (A == null && this.dataset.captureUrl != null) {
      let g = this.dataset.captureUrl.split("/").pop() ?? "", I = /^([\w-]+)/.exec(g);
      A = I ? I[1] : null;
    }
    if (this.dataset.artifacts != null) return this.dataset.artifacts;
    if (A != null) return (await fetch(`${uA}/${A}/public`, this.fetchOptions)).json().then((g) => {
      let I = (g.response ?? g.latestRun).artifacts;
      if (I == null) throw new Error(`No artifacts run for capture ${A}`);
      return this.convertArtifactArray(I);
    });
    if (this.dataset.src != null) return { gs_web_meta: `${this.dataset.src}/gs_web_meta.json`, gs_web_gauss1: `${this.dataset.src}/gs_web_gauss1.bin`, gs_web_gauss2: `${this.dataset.src}/gs_web_gauss2.bin`, gs_web_sh: `${this.dataset.src}/gs_web_sh.bin`, gs_web_webmeta: `${this.dataset.src}/gs_web_webmeta.json`, gs_compressed: `${this.dataset.src}/gs_compressed.bin`, gs_compressed_meta: `${this.dataset.src}/gs_compressed_meta.json`, with_background_gs_camera_params: `${this.dataset.src}/with_background_gs_camera_params.json`, semantics: `${this.dataset.src}/semantics.bin`, skybox: `${this.dataset.src}/skybox.jpg`, skybox_meta: `${this.dataset.src}/skybox_meta.json` };
    throw new Error(`Cannot get artifacts from ${JSON.stringify(this.dataset)}`);
  }
  downloadAllFiles() {
    let A = this.getArtifacts();
    this.metaReady = A.then((g) => {
      let I = g.gs_web_webmeta && g.gs_web_gauss1 && g.gs_web_gauss2, B = g.gs_compressed && g.gs_compressed_meta;
      if (!I && !B) throw new Error(`Missing required artifacts in ${JSON.stringify(g)}`);
      return this.haveSemantics = !!g.semantics, Promise.all([this.downloadMeta(g.gs_compressed_meta ?? g.gs_web_webmeta).catch((C) => {
        if (g.gs_web_webmeta != null) return this.downloadMeta(g.gs_web_webmeta);
        throw `Unable to download metadata: ${C}`;
      }), this.downloadMeta2(g.gs_web_meta), this.downloadMeta3(g.with_background_gs_camera_params)]).then((C) => {
        let Q = { artifacts: g };
        for (let t of C) Object.assign(Q, t);
        return Q;
      });
    }), this.coreReady = A.then((g) => g.gs_compressed ? this.downloadCoreBin(g.gs_compressed) : null), this.gauss1Ready = this.metaReady.then((g) => g.metaVersion === 1 ? this.downloadGauss1(g.artifacts.gs_web_gauss1) : new Promise((I, B) => {
      let C = this.events.updateGauss1Texture.addListener((Q) => {
        Q.complete && (I(Q), C.remove());
      });
    })), this.gauss2Ready = this.metaReady.then((g) => g.metaVersion === 1 ? this.downloadGauss2(g.artifacts.gs_web_gauss2) : new Promise((I, B) => {
      let C = this.events.updateGauss2Texture.addListener((Q) => {
        Q.complete && (I(Q), C.remove());
      });
    })), this.shReady = this.metaReady.then((g) => g.haveSH ? this.downloadSHTexture(g.artifacts.gs_web_sh) : null), this.semanticsReady = A.then((g) => g.semantics ? this.fetchFile(g.semantics).catch((I) => null) : null), this.skyboxReady = A.then((g) => g.skybox && g.skybox_meta ? this.downloadSkybox(g) : null), this.allReady = this.metaReady.then((g) => {
      let I = [this.coreReady, this.gauss1Ready, this.gauss2Ready, this.semanticsReady, this.skyboxReady];
      return this.haveSH && I.push(this.shReady), Promise.all(I);
    }).then(() => {
    });
  }
  async fetchFile(A, g = void 0, I = 256 * 1024) {
    let B = await fetch(A, this.fetchOptions);
    if (!B.ok) throw new Error(`${A} ${B.statusText}`);
    let C = new Uint8Array(1 << 20), Q = function(e, i) {
      if (i <= e.length) return e;
      let D = e.length;
      for (; D <= i; ) D *= 2;
      let s = new Uint8Array(D);
      return s.set(e), s;
    }, E = 0, t = 0;
    if (B.body == null) throw new Error(`Response body is null for ${A}`);
    let o = B.body.getReader();
    for (; ; ) {
      let { done: e, value: i } = await o.read();
      if (e) break;
      if (i == null) throw new Error(`Read value is null for ${A}`);
      C = Q(C, E + i.length), C.set(i, E), E += i.length, this.addProgress(i.length), this.currentBytes = E, this.streaming && g && E - t >= I && (g(C.subarray(0, E), false), t = E);
    }
    return C.subarray(0, E);
  }
  parseJSON(A) {
    let I = new TextDecoder().decode(A);
    return JSON.parse(I);
  }
  downloadMeta(A) {
    return this.fetchFile(A).then((g) => {
      let I = this.parseJSON(g);
      if (this.metaVersion = I.version, this.metaVersion == 1) this.totalSplats = I.num_splats, this.totalBytes = I.total_bytes, this.sceneCenter = I.scene_center ?? this.sceneCenter, this.radiusList = new Float32Array(I.rlist), this.radiusStep = I.rstep ?? this.radiusStep, this.minRadius = I.minr ?? this.minRadius, this.antialias = I.antialias ?? this.antialias, this.haveSH = I.have_sh ?? this.haveSH;
      else {
        let B = I.gaussians, C = B.core, Q = B.options ?? {}, E = B.radius ?? {}, t = I.scene ?? {}, o = I.camera ?? {};
        this.totalSplats = B.size, this.totalBytes = C.file_size, this.sceneCenter = t.center ?? this.sceneCenter, this.radiusList = new Float32Array(E.list), this.radiusStep = E.step ?? this.radiusStep, this.minRadius = E.min ?? this.minRadius, this.antialias = Q.cov2_antialias ?? this.antialias, this.haveSH = false, this.scaleToWorld = t.scale_to_world ?? this.scaleToWorld, o.initial_pose && (this.initialPose = DA(o.initial_pose[0]));
      }
      return { metaVersion: this.metaVersion, totalSplats: this.totalSplats, totalBytes: this.totalBytes, sceneCenter: this.sceneCenter, radiusList: this.radiusList, radiusStep: this.radiusStep, minRadius: this.minRadius, antialias: this.antialias, haveSH: this.haveSH, scaleToWorld: this.scaleToWorld, initialPose: this.initialPose };
    });
  }
  downloadMeta2(A) {
    return A ? this.fetchFile(A).then((g) => {
      let I = this.parseJSON(g);
      return this.scaleToWorld = I.scale_to_world ?? this.scaleToWorld, this.initialPose = DA(I.camera.initial_pose[0]), { scaleToWorld: this.scaleToWorld, initialPose: this.initialPose };
    }).catch((g) => (console.warn(`downloadMeta2 error: ${g}, for URL: ${A}`), null)) : {};
  }
  downloadMeta3(A) {
    return this.fetchFile(A).then((g) => {
      let I = this.parseJSON(g), B = I.c2w;
      this.animFrames = new Array(B.length);
      for (let C = 0; C < B.length; C++) {
        this.animFrames[C] = new Float32Array(16);
        for (let Q = 0; Q < 4; Q++) for (let E = 0; E < 4; E++) this.animFrames[C][E * 4 + Q] = B[C][Q][E];
      }
      return this.animFps = I.fps, { animFrames: this.animFrames, animFps: this.animFps };
    }).catch((g) => console.log(g, A));
  }
  partialSizes(A, g) {
    let I = A.length / g, B = p * L;
    return I = Math.floor(I / B) * B, [I / L, Math.min(I, this.totalSplats)];
  }
  downloadCoreBin(A) {
    let g = (I, B) => {
      let C = new Uint32Array(I.buffer, 0, Math.floor(I.length / 4)), [Q, E, t, o, e, i] = C.subarray(0, 6);
      if (Q != 1397183820) throw Error(`Unsupported core.bin format: ${Q}`);
      if (E != 1) throw Error(`Unsupported core.bin version: ${E}`);
      if (!this.totalSplats) this.totalSplats = t;
      else if (this.totalSplats != t) throw Error(`Mismatched splat count: ${t}`);
      for (let D = 6, s = 0; D < C.length; s++) {
        let r = C[D++];
        if (s >= this.blocksDowloaded && D + r <= C.length) {
          let y = I.slice(4 * D, 4 * (D + r));
          this.decodeBlock(y, o, i), this.blocksDowloaded++;
        }
        D += r, i && ++i;
      }
    };
    return this.fetchFile(A, this.streaming ? g : void 0).then((I) => g(I, true));
  }
  updateCoreBlock(A, g, I, B) {
    let C = (r) => Math.ceil(r / (L * p)) * p, Q = L, E = C(this.totalSplats), t = C(this.gauss1Count), o = C(this.gauss1Count + A), e = o >= E;
    this.gauss1Data || (this.gauss1Data = new Uint32Array(2 * Q * E)), this.gauss2Data || (this.gauss2Data = new Uint32Array(4 * Q * E));
    let i = o - t;
    this.gauss1Data.set(g.subarray(0, 2 * i * Q), 2 * t * Q), this.gauss2Data.set(I.subarray(0, 4 * i * Q), 4 * t * Q);
    let D = (r, y, h, R) => ({ width: Q, height: E, channels: y, currentHeight: o, data: r, format: h, internalFormat: R, target: WebGL2RenderingContext.TEXTURE_2D, type: WebGL2RenderingContext.UNSIGNED_INT, unpackAlignment: 1, minMagFilter: WebGL2RenderingContext.NEAREST, complete: e });
    this.events.updateGauss1Texture.dispatch(D(this.gauss1Data, 2, WebGL2RenderingContext.RG_INTEGER, WebGL2RenderingContext.RG32UI)), this.gauss1Count += A, this.gauss1Height = o, this.events.updateGauss2Texture.dispatch(D(this.gauss2Data, 4, WebGL2RenderingContext.RGBA_INTEGER, WebGL2RenderingContext.RGBA32UI)), this.gauss2Count += A, this.gauss2Height = o, this.cpuPoints.length || (this.cpuPoints = new Uint16Array(3 * this.totalSplats));
    let s = this.cpuPtsCount;
    this.cpuPoints.set(B, 3 * s), this.cpuPtsCount += A, this.events.updateCpuPoints.dispatch({ start: s, end: this.cpuPtsCount, cpuPoints: this.cpuPoints }), this.partialUpdate();
  }
  downloadGauss1(A) {
    let g = (I, B) => {
      let C = new Uint32Array(I.buffer, 0, 4), [Q, E, t, o] = C;
      if (Q != 1 || E != L || o != 2) throw new Error(`${A}: invalid data`);
      let e = C.byteLength, i = new Uint32Array(I.buffer, e, Math.floor((I.length - e) / 4)), D = this.totalSplats, s = 0;
      B ? s = t : [s, D] = this.partialSizes(i, 2), B = B || s == t;
      let y = this.gauss1Height * E * 2, h = { width: E, height: t, channels: 2, currentHeight: s, data: i, target: WebGL2RenderingContext.TEXTURE_2D, internalFormat: WebGL2RenderingContext.RG32UI, format: WebGL2RenderingContext.RG_INTEGER, type: WebGL2RenderingContext.UNSIGNED_INT, unpackAlignment: 1, minMagFilter: WebGL2RenderingContext.NEAREST, complete: B };
      return this.events.updateGauss1Texture.dispatch(h), this.gauss1Count = D, this.gauss1Height = s, this.extractCpuPoints(I, E, D), this.partialUpdate(), h;
    };
    return this.fetchFile(A, this.streaming ? g : void 0).then((I) => g(I, true));
  }
  downloadGauss2(A) {
    let g = (I, B) => {
      let C = new Uint32Array(I.buffer, 0, 4), [Q, E, t, o] = C;
      if (Q != 1 || E != L || o != 4) throw new Error(`${A}: invalid data`);
      let e = C.byteLength, i = new Uint32Array(I.buffer, e, Math.floor((I.length - e) / 4)), D = this.totalSplats, s = 0;
      B ? s = t : [s, D] = this.partialSizes(i, 4), B = B || s == t;
      let y = this.gauss2Height * E * 4, h = { width: E, height: t, channels: 4, currentHeight: s, data: i, target: WebGL2RenderingContext.TEXTURE_2D, internalFormat: WebGL2RenderingContext.RGBA32UI, format: WebGL2RenderingContext.RGBA_INTEGER, type: WebGL2RenderingContext.UNSIGNED_INT, unpackAlignment: 1, minMagFilter: WebGL2RenderingContext.NEAREST, complete: B };
      return this.events.updateGauss2Texture.dispatch(h), this.gauss2Count = D, this.gauss2Height = s, this.partialUpdate(), h;
    };
    return this.fetchFile(A, this.streaming ? g : void 0).then((I) => g(I, true));
  }
  partialUpdate() {
    if (this.numSplats = Math.min(this.gauss1Count, this.gauss2Count), this.radiusList) {
      let A = Math.floor(this.numSplats / this.radiusStep);
      this.loadedRadius = this.radiusList[Math.min(A, this.radiusList.length - 1)];
    }
    this.streaming && this.events.partialUpdate.dispatch();
  }
  downloadSHTexture(A) {
    return this.fetchFile(A).then((g) => {
      let I = new Uint32Array(g.buffer, 0, 2), B = I[0], C = I[1], Q = 256, E = Math.ceil(C / Q), t = new Uint16Array(g.buffer, I.byteLength), o = { width: 4 * Q, height: 4 * E, channels: 3, currentHeight: 4 * E, target: WebGL2RenderingContext.TEXTURE_2D, format: WebGL2RenderingContext.RGB, internalFormat: WebGL2RenderingContext.RGB16F, type: WebGL2RenderingContext.HALF_FLOAT, unpackAlignment: 1, minMagFilter: WebGL2RenderingContext.NEAREST, data: t, complete: true };
      return this.events.updateSHTexture.dispatch(o), o;
    });
  }
  downloadSkybox(A) {
    return this.fetchFile(A.skybox_meta).then((g) => {
      let I = this.parseJSON(g), C = (I.cubemap ?? {}).order ?? ["py", "pz", "ny", "nx", "px", "nz"];
      return this.skybox.distance = I.distance ?? this.skybox.distance, this.skybox.origin = I.origin ?? this.skybox.origin, this.fetchFile(A.skybox).then((Q) => {
        let E = new Blob([Q], { type: "image/jpeg" });
        return new Promise((o) => {
          let e = new Image();
          e.onload = () => o(e), e.src = URL.createObjectURL(E);
        }).then((o) => {
          var _a;
          this.haveSkybox = true;
          let e = o.width;
          if (o.height != 6 * e) throw new Error("Invalid skybox image");
          let i = [];
          for (let D = 0; D < 6; D++) {
            let s = document.createElement("canvas");
            s.width = s.height = e, (_a = s.getContext("2d")) == null ? void 0 : _a.drawImage(o, 0, -D * e);
            let r = { px: WebGL2RenderingContext.TEXTURE_CUBE_MAP_POSITIVE_X, nx: WebGL2RenderingContext.TEXTURE_CUBE_MAP_NEGATIVE_X, py: WebGL2RenderingContext.TEXTURE_CUBE_MAP_POSITIVE_Y, ny: WebGL2RenderingContext.TEXTURE_CUBE_MAP_NEGATIVE_Y, pz: WebGL2RenderingContext.TEXTURE_CUBE_MAP_POSITIVE_Z, nz: WebGL2RenderingContext.TEXTURE_CUBE_MAP_NEGATIVE_Z }, y = { width: e, height: e, channels: 3, currentHeight: e, target: r[C[D]], format: WebGL2RenderingContext.RGB, internalFormat: WebGL2RenderingContext.RGB, type: WebGL2RenderingContext.UNSIGNED_BYTE, unpackAlignment: 1, minMagFilter: WebGL2RenderingContext.LINEAR, data: s, complete: true };
            this.events.updateSkyboxTexture.dispatch(y), i.push(y);
          }
          return i;
        });
      });
    });
  }
  extractCpuPoints(A, g, I) {
    let B = new Uint16Array(A.buffer, 16, Math.floor((A.length - 16) / 2));
    this.cpuPoints.length || (this.cpuPoints = new Uint16Array(3 * this.totalSplats));
    let C = this.cpuPtsCount;
    for (let Q = C; Q < I; Q++) {
      let E = 4 * (Q >> 4 & 511) + (Q & 3), t = 4 * (Q >> 13) + (Q >> 2 & 3), o = 4 * (g * t + E);
      this.cpuPoints[3 * Q + 0] = B[o + 0], this.cpuPoints[3 * Q + 1] = B[o + 1], this.cpuPoints[3 * Q + 2] = B[o + 2];
    }
    this.cpuPtsCount = I, this.events.updateCpuPoints.dispatch({ start: C, end: I, cpuPoints: this.cpuPoints });
  }
  initWorker() {
    this.worker = new l(), this.worker.onmessage = this.onWorkerMessage.bind(this);
  }
  decodeBlock(A, g, I) {
    var _a;
    (_a = this.worker) == null ? void 0 : _a.postMessage({ type: "decode", data: A, maxSize: g, sd: I, width: L, tile: p }, [A.buffer]);
  }
  onWorkerMessage(A) {
    let g = "color: #00ff00", I = A.data;
    switch (I.type) {
      case "decoded":
        this.updateCoreBlock(I.size, I.tex1, I.tex2, I.xyz);
        break;
      case "ready":
        console.log("%cDecoder ready.", g);
        break;
      case "log":
        console.log(`%cDecoder: ${I.str}`, g);
        break;
      case "error":
        console.error(`%cDecoder error: ${I.str}`, g);
        break;
    }
  }
};
function DA(a2) {
  let A = new Float32Array(16);
  for (let g = 0; g < 4; g++) for (let I = 0; I < 4; I++) A[I * 4 + g] = a2[g][I];
  return A;
}
var yA = new Float32Array([-2, -2, 2, -2, 2, 2, 2, 2, -2, 2, -2, -2]);
var U = new Vector3(-1, -1, 1);
var hA = false;
var GA = class a extends Mesh {
  constructor(g = {}) {
    hA || (console.log(`%cLumaSplatsThree git version #${F.GIT_HASH}`, "color: magenta"), hA = true);
    let I;
    typeof g == "string" ? I = { source: g } : I = g;
    let B = I.enableThreeShaderIntegration ?? true, C = new InstancedBufferGeometry();
    C.setAttribute("a0", new BufferAttribute(yA, 2)), C.setDrawRange(0, 0), C.boundingSphere = new Sphere(new Vector3(0, 0, 0), 1);
    let Q = new InstancedBufferAttribute(new Int32Array(0), 1, false, 1);
    Q.setUsage(StaticDrawUsage), Q.gpuType = IntType, Q.needsUpdate = false, C.setAttribute("a1", Q);
    let E = new Qg(), t = new Eg();
    super(C, B ? E : t);
    this.boundingBox = new Box3();
    this.boundingSphere = new Sphere();
    this.semanticsMask = 255;
    this.preventDraw = false;
    this.preventSort = false;
    this._source = null;
    this.onInitialCameraTransform = null;
    this.onLoad = null;
    this.onProgress = null;
    this.splatIndexAttribute = null;
    this.lumaSplatsWebGL = null;
    this.addedSkybox = false;
    this.instancedQuadGeometry = null;
    this.dispose = () => {
      var _a;
      (_a = this.lumaSplatsWebGL) == null ? void 0 : _a.dispose(), this.geometry.dispose(), this.material.dispose(), this.skybox.material.dispose(), this.skybox.geometry.dispose();
    };
    this._loaderEventListeners = new Array();
    this.onPointsUpdate = (g2) => {
      let I2 = 1 / 0, B2 = 1 / 0, C2 = 1 / 0, Q2 = -1 / 0, E2 = -1 / 0, t2 = -1 / 0, o2 = g2.end - g2.start;
      for (let e2 = g2.start; e2 < g2.end; e2++) {
        let i2 = g2.cpuPoints[e2 * 3 + 0], D2 = g2.cpuPoints[e2 * 3 + 1], s2 = g2.cpuPoints[e2 * 3 + 2], r2 = CA(i2) * U.x, y2 = CA(D2) * U.y, h2 = CA(s2) * U.z;
        I2 = Math.min(I2, r2), B2 = Math.min(B2, y2), C2 = Math.min(C2, h2), Q2 = Math.max(Q2, r2), E2 = Math.max(E2, y2), t2 = Math.max(t2, h2);
      }
      o2 > 0 && (this.boundingBox.expandByPoint(new Vector3(I2, B2, C2)), this.boundingBox.expandByPoint(new Vector3(Q2, E2, t2)), this.boundingBox.getBoundingSphere(this.boundingSphere));
    };
    this.placeholderGeometry = C, this._materialThreeShaderIntegration = E, this._materialRaw = t, this.loadingAnimationEnabled = I.loadingAnimationEnabled ?? true, this.particleRevealEnabled = I.particleRevealEnabled ?? false, this.gaussTextures = [new DataTexture(null, -1, -1, RGIntegerFormat, UnsignedIntType, UVMapping, ClampToEdgeWrapping, ClampToEdgeWrapping, NearestFilter, NearestFilter, 0, NoColorSpace), new DataTexture(null, -1, -1, RGBAIntegerFormat, UnsignedIntType, UVMapping, ClampToEdgeWrapping, ClampToEdgeWrapping, NearestFilter, NearestFilter, 0, NoColorSpace)], this.gaussTextures[0].internalFormat = "RG32UI", this.gaussTextures[0].unpackAlignment = 1, this.gaussTextures[0].generateMipmaps = false, this.gaussTextures[0].needsUpdate = false, this.gaussTextures[1].internalFormat = "RGBA32UI", this.gaussTextures[1].unpackAlignment = 1, this.gaussTextures[1].generateMipmaps = false, this.gaussTextures[0].needsUpdate = false, this.shTexture = new DataTexture(null, -1, -1, "RGB", HalfFloatType, UVMapping, ClampToEdgeWrapping, ClampToEdgeWrapping, NearestFilter, NearestFilter, 0, NoColorSpace), this.shTexture.internalFormat = "RGB16F", this.shTexture.unpackAlignment = 1, this.shTexture.generateMipmaps = false, this.shTexture.needsUpdate = false, this.semanticsTexture = new DataTexture(null, -1, -1, "R", UnsignedByteType, UVMapping, ClampToEdgeWrapping, ClampToEdgeWrapping, NearestFilter, NearestFilter, 0, NoColorSpace), this.semanticsTexture.internalFormat = "R8UI", this.semanticsTexture.unpackAlignment = 1, this.semanticsTexture.generateMipmaps = false, this.semanticsTexture.needsUpdate = false, this.skyboxTexture = new CubeTexture(), this._skyboxMaterialThreeShaderIntegration = new ig(), this._skyboxMaterialRaw = new og();
    let o = new BufferGeometry();
    o.setAttribute("a_pos", new BufferAttribute(new Float32Array(Z), 3)), o.setDrawRange(0, 36), o.boundingSphere = new Sphere(new Vector3(0, 0, 0), 1), o.boundingBox = new Box3(new Vector3(-1, -1, -1), new Vector3(1, 1, 1)), this.skybox = new Mesh(o, B ? this._skyboxMaterialThreeShaderIntegration : this._skyboxMaterialRaw), this.skybox.frustumCulled = false, this.frustumCulled = true, I.loader != null ? (this.updateLoader(I.loader), this._source = I.loader.dataset) : I.source != null && (this.source = I.source);
    let e = new Matrix4(), i = new Vector3(), D = new Matrix4(), s = new Matrix4(), r = new Quaternion(), y = new Vector3(), h = new Vector3(), R = new Mesh(new PlaneGeometry(), new MeshNormalMaterial({ colorWrite: false, depthWrite: false }));
    R.renderOrder = -1 / 0, R.frustumCulled = false, R.scale.setScalar(1), this.add(R), R.onBeforeRender = (M, d, Y) => {
      if (!M.capabilities.isWebGL2) throw "LumaSplatsThree requires WebGL2";
      if (I.onBeforeRender != null && I.onBeforeRender(M, d, Y, this), e.copy(this.matrixWorld), e.scale(U), D.copy(Y.matrixWorldInverse).multiply(e), s.copy(D).invert(), s.decompose(i, r, h), y.set(0, 0, -1).applyQuaternion(r), !this.lumaSplatsWebGL) {
        this.geometry = C;
        return;
      }
      !this.preventSort && this.visible && this.lumaSplatsWebGL.requestSort(i.toArray(), y.toArray()), this.prepareGLObjects(M), this.lumaSplatsWebGL.tickAnimation(), this.lumaSplatsWebGL.semanticsMask = this.semanticsMask, this.lumaSplatsWebGL.loadingAnimation.enabled = this.loadingAnimationEnabled, this.lumaSplatsWebGL.loadingAnimation.particleRevealEnabled = this.particleRevealEnabled;
      let f = { modelViewMatrix: D, viewPosition: i, projectionMatrix: Y.projectionMatrix, far: Y.far }, QA = { gaussTexture0: this.gaussTextures[0], gaussTexture1: this.gaussTextures[1], shTexture: this.shTexture, semanticsTexture: this.semanticsTexture, skyboxTexture: this.skyboxTexture };
      if (this.material.updateUniformsAndDefines(this, this.lumaSplatsWebGL, M, d, f, QA), this.lumaSplatsWebGL.hasSkyboxTexture) {
        this.skybox.material.updateUniformsAndDefines(this, this.lumaSplatsWebGL, M, d, f, QA);
        let LA = (this.semanticsMask & 1) !== 0;
        this.skybox.visible = LA && !this.preventDraw;
      }
      this.instancedQuadGeometry && (this.preventDraw ? (this.instancedQuadGeometry.setDrawRange(0, 0), this.instancedQuadGeometry.instanceCount = 0) : (this.instancedQuadGeometry.instanceCount = this.lumaSplatsWebGL.numSplats - this.lumaSplatsWebGL.sortEnd + this.lumaSplatsWebGL.numVisible, this.instancedQuadGeometry.setDrawRange(0, 6))), this.lumaSplatsWebGL.sortAge++;
    };
  }
  get source() {
    return this._source;
  }
  set source(g) {
    JSON.stringify(g) !== JSON.stringify(this._source) && (this.updateLoader(g ? new T(g) : null), this._source = g);
  }
  get enableThreeShaderIntegration() {
    return this.material === this._materialThreeShaderIntegration;
  }
  set enableThreeShaderIntegration(g) {
    this.material = g ? this._materialThreeShaderIntegration : this._materialRaw, this.skybox.material = g ? this._skyboxMaterialThreeShaderIntegration : this._skyboxMaterialRaw;
  }
  prepareGLObjects(g) {
    let I = this.placeholderGeometry;
    if (!this.lumaSplatsWebGL) {
      this.geometry = I;
      return;
    }
    if (this.lumaSplatsWebGL.syncGpuResources(g.getContext()) && (g.state.activeTexture(WebGL2RenderingContext.TEXTURE0), g.state.unbindTexture(), g.state.activeTexture(WebGL2RenderingContext.TEXTURE1), g.state.unbindTexture(), g.state.activeTexture(WebGL2RenderingContext.TEXTURE2), g.state.unbindTexture(), g.state.activeTexture(WebGL2RenderingContext.TEXTURE3), g.state.unbindTexture()), this.lumaSplatsWebGL.glObjects) {
      if (g.properties.get(this.gaussTextures[0]).__webglTexture = this.lumaSplatsWebGL.glObjects.gaussTextures[0], g.properties.get(this.gaussTextures[1]).__webglTexture = this.lumaSplatsWebGL.glObjects.gaussTextures[1], g.properties.get(this.shTexture).__webglTexture = this.lumaSplatsWebGL.glObjects.shTexture, g.properties.get(this.semanticsTexture).__webglTexture = this.lumaSplatsWebGL.glObjects.semanticsTexture, g.properties.get(this.skyboxTexture).__webglTexture = this.lumaSplatsWebGL.glObjects.skyboxTexture, this.gaussTextures[0].needsUpdate = false, this.gaussTextures[1].needsUpdate = false, this.shTexture.needsUpdate = false, this.semanticsTexture.needsUpdate = false, this.skyboxTexture.needsUpdate = false, this.splatIndexAttribute == null) {
        let C = new GLBufferAttribute(this.lumaSplatsWebGL.glObjects.indexBuffer1, WebGL2RenderingContext.INT, 1, 4, this.lumaSplatsWebGL.loader.totalSplats);
        C.needsUpdate = true, C.isInstancedBufferAttribute = true, C.meshPerAttribute = 1, this.instancedQuadGeometry = new InstancedBufferGeometry(), this.instancedQuadGeometry.setAttribute("a0", new BufferAttribute(yA, 2)), this.instancedQuadGeometry.setDrawRange(0, 6), this.instancedQuadGeometry.setAttribute("a1", C), this.instancedQuadGeometry.boundingBox = this.boundingBox, this.instancedQuadGeometry.boundingSphere = this.boundingSphere, this.splatIndexAttribute = C;
      }
      this.splatIndexAttribute.buffer = this.lumaSplatsWebGL.glObjects.indexBuffer1;
    }
    this.geometry = this.instancedQuadGeometry ?? I;
  }
  setShaderHooks(g) {
    this._materialRaw.setShaderHooks(g), this._materialThreeShaderIntegration.setShaderHooks(g), this._skyboxMaterialRaw.setShaderHooks(g), this._skyboxMaterialThreeShaderIntegration.setShaderHooks(g);
  }
  captureCubemap(g, I = new Vector3(), B = 0.01, C = 1e3, Q = 128) {
    return new Promise((E, t) => {
      var _a;
      let o = this;
      for (; o.parent != null && (o = o.parent, !(o instanceof Scene)); ) ;
      let e = o instanceof Scene ? o.clone() : new Scene();
      e.environment = null, e.background = null;
      let i = new a({ loader: (_a = this.lumaSplatsWebGL) == null ? void 0 : _a.loader, enableThreeShaderIntegration: true, loadingAnimationEnabled: false });
      i.preventSort = true, i.onLoad = () => {
        i.prepareGLObjects(g), i.lumaSplatsWebGL.queueSort(I.toArray(), new Vector3(0, 0, -1).toArray(), () => {
          let s = new WebGLCubeRenderTarget(Q, { generateMipmaps: true, minFilter: LinearMipmapLinearFilter }), r = new CubeCamera(B, C, s);
          r.position.copy(I), e.clear(), e.add(r), e.add(i), r.update(g, e), i.dispose(), E(s.texture);
        });
      };
    });
  }
  updateLoader(g) {
    var _a;
    for (let I of this._loaderEventListeners) I.remove();
    (_a = this.lumaSplatsWebGL) == null ? void 0 : _a.dispose(), this.lumaSplatsWebGL = null, g && (this.lumaSplatsWebGL = new O(g, this), g.metaReady.then((I) => {
      var _a2;
      if (I.initialPose == null) throw "initialPose not provided";
      let B = new Matrix4().fromArray(I.initialPose);
      B = B.premultiply(new Matrix4().makeScale(U.x, U.y, U.z));
      let C = B.clone();
      C = C.premultiply(new Matrix4().makeScale(-1, -1, -1));
      let Q = new Vector3(), E = new Quaternion();
      C.decompose(new Vector3(), E, new Vector3()), B.decompose(Q, new Quaternion(), new Vector3());
      let t = new Matrix4().compose(Q, E, new Vector3(1, 1, 1));
      (_a2 = this.onInitialCameraTransform) == null ? void 0 : _a2.call(this, t);
    }), this.lumaSplatsWebGL.events.onLoad.addListener(() => {
      var _a2;
      (_a2 = this.onLoad) == null ? void 0 : _a2.call(this, this);
    }), g.skyboxReady.then((I) => {
      I && !this.addedSkybox && (this.add(this.skybox), this.addedSkybox = true);
    }), this.onPointsUpdate({ start: 0, end: g.cpuPtsCount, cpuPoints: g.cpuPoints }), this._loaderEventListeners = [g.events.updateCpuPoints.addListener(this.onPointsUpdate), g.events.progress.addListener((I) => {
      var _a2;
      (_a2 = this.onProgress) == null ? void 0 : _a2.call(this, I);
    })]);
  }
};
var Qg = SA(true);
var Eg = SA(false);
function SA(a2) {
  return class extends (a2 ? ShaderMaterial : RawShaderMaterial) {
    constructor() {
      let g = { s0: new Uniform(null), s1: new Uniform(null), s2: new Uniform(null), s3: new Uniform(null), view: new Uniform(new Matrix4()), proj: new Uniform(new Matrix4()), res2: new Uniform(new Vector2()), ires2: new Uniform(new Vector2()), inv_sqrt_scale_to_world: new Uniform(1), offset: new Uniform(0), size: new Uniform(0), zs_aa_ts: new Uniform(new Vector3()), scene_center: new Uniform(new Vector3()), cpos: new Uniform(new Vector3()), load_r: new Uniform(new Vector2()), reveal_r: new Uniform(new Vector2()), solid_r: new Uniform(new Vector2()), debug: new Uniform(0), ellip: new Uniform(0), semanticsMask: new Uniform(255) }, I = W(k, H, a2, true), B = { ...I.additionalUniforms, ...g };
      super({ glslVersion: I.glslVersion, vertexShader: I.vertexShader, fragmentShader: I.fragmentShader, defines: {}, uniforms: B, blending: CustomBlending, blendEquation: AddEquation, blendSrc: OneFactor, blendDst: OneMinusSrcAlphaFactor, transparent: true, depthWrite: false, depthTest: true, fog: a2, side: BackSide });
      this.usingShaderHooks = false;
      this.renderDepth = false;
      this._viewport = new Vector4();
      this.baseUniforms = g, this.uniforms = B;
    }
    updateUniformsAndDefines(g, I, B, C, Q, E) {
      let t = this.renderDepth, o = (g.semanticsMask != 255 || this.usingShaderHooks) && I.hasSemanticsTexture;
      this.setDefines({ hasSphericalHarmonics: I.hasSphericalHarmonicsTexture, hasSemantics: o, renderDepth: t });
      let e = this.blending;
      t ? this.blending = NoBlending : this.blending = CustomBlending, this.needsUpdate = this.needsUpdate || e !== this.blending;
      let i = I.shaderParams;
      I.glObjects && (this.uniforms.s0.value = E.gaussTexture0, this.uniforms.s1.value = E.gaussTexture1, !t && I.hasSphericalHarmonicsTexture && (this.uniforms.s2.value = E.shTexture), o && (this.uniforms.s3.value = E.semanticsTexture, this.uniforms.semanticsMask.value = I.semanticsMask), this.uniforms.view.value = Q.modelViewMatrix, this.uniforms.proj.value = Q.projectionMatrix, a2 && this.uniforms.inv_proj && this.uniforms.inv_proj.value.copy(Q.projectionMatrix).invert(), B.getCurrentViewport(this._viewport), this.uniforms.res2.value.set(this._viewport.width / 2, this._viewport.height / 2), this.uniforms.ires2.value.set(2 / this._viewport.width, 2 / this._viewport.height), this.uniforms.inv_sqrt_scale_to_world.value = 1 / Math.sqrt(I.loader.scaleToWorld), this.uniforms.offset.value = I.sortEnd, this.uniforms.size.value = I.loader.numSplats, this.uniforms.zs_aa_ts.value.set(Q.far, I.loader.antialias, Math.pow(i.tweakScale, 2)), this.uniforms.cpos.value.copy(Q.viewPosition), this.uniforms.scene_center.value.fromArray(I.loader.sceneCenter), this.uniforms.load_r.value.set(i.loadR2, 1 / (i.loadR2 - i.loadR1)), this.uniforms.reveal_r.value.set(i.revealR2, 1 / (i.revealR2 - i.revealR1)), this.uniforms.solid_r.value.set(i.solidR2, 1 / (i.solidR2 - i.solidR1)), this.uniforms.debug.value = i.debugView, this.uniforms.ellip.value = i.debugView > 0 ? 1 : 0);
    }
    setDefines(g) {
      let I = this.defines;
      g.hasSphericalHarmonics && I.HAVE_SH == null ? (I.HAVE_SH = "", this.needsUpdate = true) : !g.hasSphericalHarmonics && I.HAVE_SH != null && (delete I.HAVE_SH, this.needsUpdate = true), g.renderDepth && I.RD == null ? (I.RD = "", this.needsUpdate = true) : !g.renderDepth && I.RD != null && (delete I.RD, this.needsUpdate = true), g.hasSemantics && I.HAVE_SEMANTICS == null ? (I.HAVE_SEMANTICS = "", this.needsUpdate = true) : !g.hasSemantics && I.HAVE_SEMANTICS != null && (delete I.HAVE_SEMANTICS, this.needsUpdate = true);
    }
    setShaderHooks(g) {
      let I = W(k, H, a2, true, g);
      this.vertexShader = I.vertexShader, this.fragmentShader = I.fragmentShader, this.uniforms = { ...I.additionalUniforms, ...this.baseUniforms }, this.usingShaderHooks = g.vertexShaderHooks != null || g.fragmentShaderHooks != null, this.needsUpdate = true;
    }
  };
}
function W(a2, A, g, I, B) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j;
  let C = g ? { vertexShaderHooks: { additionalGlobals: [ShaderChunk.fog_pars_vertex, ShaderChunk.clipping_planes_pars_vertex, (_a = B == null ? void 0 : B.vertexShaderHooks) == null ? void 0 : _a.additionalGlobals, ((_b = B == null ? void 0 : B.vertexShaderHooks) == null ? void 0 : _b.onMainEnd) ? `void _three_onMainEnd${B.vertexShaderHooks.onMainEnd}` : void 0].join(`
`), additionalUniforms: { inv_proj: ["mat4", { value: new Matrix4() }], ...(_c = B == null ? void 0 : B.vertexShaderHooks) == null ? void 0 : _c.additionalUniforms }, getSplatColor: (_d = B == null ? void 0 : B.vertexShaderHooks) == null ? void 0 : _d.getSplatColor, onMainEnd: `
					() {
						${I ? `
								// inverse gl_Position back to view-space,
								// rather then just the center we now have
								// vertex positions in view-space
								vec4 mvPosition = inv_proj * gl_Position;
								mvPosition /= mvPosition.w;
							` : ""}

						#include <fog_vertex>
						#include <clipping_planes_vertex>
						${((_e = B == null ? void 0 : B.vertexShaderHooks) == null ? void 0 : _e.onMainEnd) ? "_three_onMainEnd()" : ""};
					}
				`, getSplatTransform: (_f = B == null ? void 0 : B.vertexShaderHooks) == null ? void 0 : _f.getSplatTransform }, fragmentShaderHooks: { additionalGlobals: [ShaderChunk.fog_pars_fragment, ShaderChunk.clipping_planes_pars_fragment, (_g = B == null ? void 0 : B.fragmentShaderHooks) == null ? void 0 : _g.additionalGlobals, ((_h = B == null ? void 0 : B.fragmentShaderHooks) == null ? void 0 : _h.getFragmentColor) ? `vec4 _three_getFragmentColor${B.fragmentShaderHooks.getFragmentColor}` : void 0].join(`
`), additionalUniforms: { ...(_i = B == null ? void 0 : B.fragmentShaderHooks) == null ? void 0 : _i.additionalUniforms }, getFragmentColor: `
					(vec4 fragColor) {
						#include <clipping_planes_fragment>

						fragColor.rgb = pow(fragColor.rgb, vec3(2.2));

						#define gl_FragColor fragColor
						#include <tonemapping_fragment>
						#include <colorspace_fragment>
						#include <fog_fragment>
						#undef gl_FragColor
						return ${((_j = B == null ? void 0 : B.fragmentShaderHooks) == null ? void 0 : _j.getFragmentColor) ? "_three_getFragmentColor(fragColor)" : "fragColor"};
					}
				` } } : B, Q = _(a2, A, C), E = g ? { ...UniformsLib.fog } : {}, t = C == null ? void 0 : C.vertexShaderHooks, o = C == null ? void 0 : C.fragmentShaderHooks;
  if (t == null ? void 0 : t.additionalUniforms) for (let h in t.additionalUniforms) E[h] = t == null ? void 0 : t.additionalUniforms[h][1];
  if (o == null ? void 0 : o.additionalUniforms) for (let h in o.additionalUniforms) E[h] = o == null ? void 0 : o.additionalUniforms[h][1];
  let e = /#version\s+([^\n]+)\n/, i = Q.fragmentShader.match(e), D = Q.vertexShader.match(e), s = Q.vertexShader.replace(e, ""), r = Q.fragmentShader.replace(e, "");
  return { glslVersion: ((i == null ? void 0 : i[1]) || (D == null ? void 0 : D[1])) ?? "300 es", vertexShader: s, fragmentShader: r, additionalUniforms: E };
}
var ig = MA(true);
var og = MA(false);
function MA(a2) {
  return class extends (a2 ? ShaderMaterial : RawShaderMaterial) {
    constructor() {
      let g = { s0: new Uniform(null), view: new Uniform(new Matrix4()), proj: new Uniform(new Matrix4()), radius: new Uniform(0), origin: new Uniform(new Vector3()), fade: new Uniform(0) }, I = W(V, z, a2, false), B = { ...I.additionalUniforms, ...g };
      super({ glslVersion: I.glslVersion, vertexShader: I.vertexShader, fragmentShader: I.fragmentShader, defines: { mvPosition: "vec3(0.0, 0.0, 1000.0)" }, uniforms: B, blending: NoBlending, transparent: false, depthWrite: false, depthTest: true, fog: a2, side: FrontSide });
      this.baseUniforms = g, this.uniforms = B;
    }
    updateUniformsAndDefines(g, I, B, C, Q, E) {
      if (!I.glObjects) return;
      this.uniforms.view.value.copy(Q.modelViewMatrix), this.uniforms.view.value.setPosition(0, 0, 0), this.uniforms.proj.value = Q.projectionMatrix;
      let o = I.shaderParams, e = (o.solidR1 + o.solidR2) / 2, i = I.loader.sceneRadius(), D = i * 0.9, s = Math.min(Math.max((e - D) / (i - D), 0), 1);
      this.uniforms.fade.value = s, this.uniforms.radius.value = I.loader.skybox.distance, this.uniforms.origin.value.fromArray(I.loader.skybox.origin), this.uniforms.s0.value = E.skyboxTexture;
    }
    setShaderHooks(g) {
      let I = W(V, z, a2, false, g);
      this.vertexShader = I.vertexShader, this.fragmentShader = I.fragmentShader, this.uniforms = { ...I.additionalUniforms, ...this.baseUniforms }, this.needsUpdate = true;
    }
  };
}
function CA(a2) {
  "use strict";
  let A = (a2 & 31744) >> 10, g = a2 & 1023;
  return (a2 >> 15 ? -1 : 1) * (A ? A === 31 ? g ? NaN : 1 / 0 : Math.pow(2, A - 15) * (1 + g / 1024) : 6103515625e-14 * (g / 1024));
}
export {
  T as LumaSplatsLoader,
  sA as LumaSplatsSemantics,
  GA as LumaSplatsThree,
  O as LumaSplatsWebGL,
  _ as getShaderCodeWithHooks,
  z as skyboxFragmentShader,
  V as skyboxVertexShader,
  Z as skyboxVertices,
  H as splatFragmentShader,
  k as splatVertexShader
};
//# sourceMappingURL=@lumaai_luma-web.js.map
