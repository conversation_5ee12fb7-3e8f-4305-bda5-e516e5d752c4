<template>
  <!-- <PERSON><PERSON> Backdrop -->
  <div
    v-if="show"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm p-4"
    @click.self="handleBackdropClick"
  >
    <!-- Modal Container -->
    <div
      class="bg-secondary rounded-lg shadow-xl flex flex-col justify-center items-center w-full max-w-md p-6 mx-4 transform transition-all duration-300 ease-out"
      :class="[
        show ? 'scale-100 opacity-100' : 'scale-95 opacity-0',
        isMobile ? 'max-w-sm' : 'lg:min-w-[400px]'
      ]"
      role="dialog"
      aria-modal="true"
      :aria-labelledby="title ? 'status-popup-title' : undefined"
      aria-describedby="status-popup-message"
    >
      <!-- Close Button -->
      <div
        v-if="showCloseButton"
        class="w-full flex justify-end mb-2"
      >
        <button
          @click="handleClose"
          class="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300"
          aria-label="Close popup"
        >
          <svg
            class="w-5 h-5"
            viewBox="0 0 27 27"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M20.25 20.25L6.75 6.75"
              stroke="var(--secondaryText)"
              stroke-width="1.575"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M20.25 6.75L6.75 20.25"
              stroke="var(--secondaryText)"
              stroke-width="1.575"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>

      <!-- Status Icon -->
      <div class="flex justify-center items-center mb-4">
        <!-- Success Icon -->
        <svg
          v-if="type === 'success'"
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 48 48"
          fill="none"
        >
          <path
            d="M0 24C0 10.7452 10.7452 0 24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24Z"
            fill="#0E9F6E"
          />
          <path
            d="M21.1554 32.3333C20.829 32.3349 20.515 32.1816 20.2807 31.9062L14.3774 24.9625C14.2598 24.8232 14.1659 24.6572 14.1011 24.474C14.0364 24.2908 14.0021 24.0939 14.0001 23.8946C13.9961 23.4921 14.1242 23.1041 14.3562 22.816C14.5882 22.528 14.9051 22.3635 15.2372 22.3586C15.5692 22.3538 15.8893 22.5091 16.1269 22.7903L21.1604 28.7085L31.8722 16.098C32.1102 15.8168 32.4305 15.6617 32.7628 15.6668C33.0951 15.6719 33.4122 15.8368 33.6442 16.1253C33.8762 16.4137 34.0041 16.8021 33.9999 17.2049C33.9957 17.6077 33.8596 17.992 33.6217 18.2732L22.0301 31.9062C21.7958 32.1816 21.4818 32.3349 21.1554 32.3333Z"
            fill="white"
          />
        </svg>

        <!-- Error Icon -->
        <svg
          v-else-if="type === 'error'"
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 48 48"
          fill="none"
        >
          <path
            d="M24 0C10.7452 0 0 10.7452 0 24C0 37.2548 10.7452 48 24 48C37.2548 48 48 37.2548 48 24C48 10.7452 37.2548 0 24 0Z"
            fill="#E02424"
          />
          <path
            d="M30 18L18 30M18 18L30 30"
            stroke="white"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>

        <!-- Warning Icon -->
        <svg
          v-else-if="type === 'warning'"
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 48 48"
          fill="none"
        >
          <path
            d="M24 0C10.7452 0 0 10.7452 0 24C0 37.2548 10.7452 48 24 48C37.2548 48 48 37.2548 48 24C48 10.7452 37.2548 0 24 0Z"
            fill="#F59E0B"
          />
          <path
            d="M24 16V24M24 32H24.01"
            stroke="white"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>

        <!-- Info Icon -->
        <svg
          v-else-if="type === 'info'"
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 48 48"
          fill="none"
        >
          <path
            d="M24 0C10.7452 0 0 10.7452 0 24C0 37.2548 10.7452 48 24 48C37.2548 48 48 37.2548 48 24C48 10.7452 37.2548 0 24 0Z"
            fill="#3B82F6"
          />
          <path
            d="M24 16V24M24 32H24.01"
            stroke="white"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>

      <!-- Content -->
      <div class="text-center w-full">
        <!-- Title -->
        <h3
          v-if="title"
          id="status-popup-title"
          class="text-secondaryText text-lg font-semibold mb-3"
        >
          {{ title }}
        </h3>

        <!-- Message -->
        <p
          id="status-popup-message"
          class="text-secondaryText text-base leading-relaxed mb-6"
          :class="{ 'mb-4': !buttonText }"
        >
          {{ message }}
        </p>

        <!-- Action Button -->
        <div
          v-if="buttonText"
          class="flex justify-center"
        >
          <button
            @click="handleButtonClick"
            class="px-6 py-2 bg-[#1c64f2] hover:bg-[#1a56db] active:bg-[#1e429f] text-white rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            {{ buttonText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    required: true,
    validator: (v) => ['success', 'error', 'warning', 'info'].includes(v),
  },
  title: {
    type: String,
    default: '',
  },
  message: {
    type: String,
    required: true,
  },
  buttonText: {
    type: String,
    default: 'Continue',
  },
  isMobile: {
    type: Boolean,
    default: false,
  },
  showCloseButton: {
    type: Boolean,
    default: true,
  },
  closeOnBackdrop: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['close', 'button-click']);

// Handle close button click
const handleClose = () => {
  emit('close');
};

// Handle backdrop click
const handleBackdropClick = () => {
  if (props.closeOnBackdrop) {
    emit('close');
  }
};

// Handle action button click
const handleButtonClick = () => {
  emit('button-click');
  // Also emit close by default unless parent handles it differently
  emit('close');
};
</script>

<style scoped>
/* Modal animations */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.modal-enter-to,
.modal-leave-from {
  opacity: 1;
  transform: scale(1);
}

/* Backdrop animations */
.backdrop-enter-active,
.backdrop-leave-active {
  transition: opacity 0.3s ease;
}

.backdrop-enter-from,
.backdrop-leave-to {
  opacity: 0;
}

.backdrop-enter-to,
.backdrop-leave-from {
  opacity: 1;
}
</style>
