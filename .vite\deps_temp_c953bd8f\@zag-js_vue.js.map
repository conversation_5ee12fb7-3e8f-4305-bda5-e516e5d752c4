{"version": 3, "sources": ["../../node_modules/@zag-js/vue/src/index.ts", "../../node_modules/@zag-js/vue/src/normalize-props.ts", "../../node_modules/@zag-js/vue/src/use-snapshot.ts", "../../node_modules/@zag-js/vue/src/use-actor.ts", "../../node_modules/@zag-js/vue/src/use-service.ts", "../../node_modules/@zag-js/vue/src/use-machine.ts"], "sourcesContent": ["export { mergeProps } from \"@zag-js/core\"\nexport type { ContextFrom, EventFrom, StateFrom } from \"@zag-js/core\"\nexport * from \"./normalize-props\"\nexport * from \"./use-actor\"\nexport * from \"./use-machine\"\nexport * from \"./use-snapshot\"\n", "import { createNormalizer } from \"@zag-js/types\"\nimport type * as Vue from \"vue\"\n\ntype ReservedProps = {\n  key?: string | number | symbol\n  ref?: Vue.VNodeRef\n}\n\ntype Attrs<T> = T & ReservedProps\n\nexport type PropTypes = Vue.NativeElements & {\n  element: Attrs<Vue.HTMLAttributes>\n  style: Vue.CSSProperties\n}\n\ntype Dict = Record<string, string>\n\nfunction toCase(txt: string) {\n  return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()\n}\n\nconst propMap: Record<string, string> = {\n  htmlFor: \"for\",\n  className: \"class\",\n  onDoubleClick: \"onDblclick\",\n  onChange: \"onInput\",\n  onFocus: \"onFocusin\",\n  onBlur: \"onFocusout\",\n  defaultValue: \"value\",\n  defaultChecked: \"checked\",\n}\n\nfunction toVueProp(prop: string) {\n  if (prop in propMap) return propMap[prop]\n\n  if (prop.startsWith(\"on\")) {\n    return `on${toCase(prop.substr(2))}`\n  }\n\n  return prop.toLowerCase()\n}\n\nexport const normalizeProps = createNormalizer<PropTypes>((props: Dict) => {\n  const normalized: Dict = {}\n  for (const key in props) {\n    const value = props[key]\n    if (key === \"children\") {\n      if (typeof value === \"string\") {\n        normalized[\"innerHTML\"] = value\n      } else if (process.env.NODE_ENV !== \"production\" && value != null) {\n        console.warn(\"[Vue Normalize Prop] : avoid passing non-primitive value as `children`\")\n      }\n    } else {\n      normalized[toVueProp(key)] = props[key]\n    }\n  }\n  return normalized\n})\n", "import type { Machine, StateMachine as S } from \"@zag-js/core\"\nimport { snapshot, subscribe } from \"@zag-js/store\"\nimport { onUnmounted, shallowRef, watch, watchEffect, type Ref, unref } from \"vue\"\nimport type { MachineOptions } from \"./types\"\n\nexport function useSnapshot<\n  TContext extends Record<string, any>,\n  TState extends S.StateSchema,\n  TEvent extends S.EventObject = S.AnyEventObject,\n>(\n  service: Machine<TContext, TState, TEvent>,\n  options?: MachineOptions<TContext, TState, TEvent>,\n): Ref<S.State<TContext, TState, TEvent>> {\n  //\n  const { actions, context } = options ?? {}\n\n  const state = shallowRef(service.state)\n\n  const unsubscribe = subscribe(service.state, () => {\n    state.value = snapshot(service.state) as any\n  })\n\n  onUnmounted(() => {\n    unsubscribe?.()\n  })\n\n  watchEffect(() => {\n    service.setOptions({ actions })\n  })\n\n  if (context) {\n    watch(\n      context,\n      (ctx) => {\n        service.setContext(unref(ctx))\n      },\n      { deep: true },\n    )\n  }\n\n  return state\n}\n", "import type { Machine, StateMachine as S } from \"@zag-js/core\"\nimport { useSnapshot } from \"./use-snapshot\"\n\nexport function useActor<\n  TContext extends Record<string, any>,\n  TState extends S.StateSchema,\n  TEvent extends S.EventObject = S.AnyEventObject,\n>(service: Machine<TContext, TState, TEvent>) {\n  const state = useSnapshot(service)\n  return [state, service.send] as const\n}\n", "import type { MachineSrc, StateMachine as S } from \"@zag-js/core\"\nimport { onBeforeUnmount, onMounted, unref } from \"vue\"\nimport type { MachineOptions } from \"./types\"\n\nexport function useService<\n  TContext extends Record<string, any>,\n  TState extends S.StateSchema,\n  TEvent extends S.EventObject = S.AnyEventObject,\n>(machine: MachineSrc<TContext, TState, TEvent>, options?: MachineOptions<TContext, TState, TEvent>) {\n  const { state: hydratedState, context } = options ?? {}\n\n  const service = typeof machine === \"function\" ? machine() : machine\n  if (context) service.setContext(unref(context))\n  service._created()\n\n  onMounted(() => {\n    service.start(hydratedState)\n\n    onBeforeUnmount(() => {\n      service.stop()\n    })\n  })\n\n  return service\n}\n", "import type { MachineSrc, StateMachine as S } from \"@zag-js/core\"\nimport type { MachineOptions } from \"./types\"\nimport { useService } from \"./use-service\"\nimport { useSnapshot } from \"./use-snapshot\"\n\nexport function useMachine<\n  TContext extends Record<string, any>,\n  TState extends S.StateSchema,\n  TEvent extends S.EventObject = S.AnyEventObject,\n>(machine: MachineSrc<TContext, TState, TEvent>, options?: MachineOptions<TContext, TState, TEvent>) {\n  const service = useService(machine, options)\n  const state = useSnapshot(service, options)\n  return [state, service.send, service] as const\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;ACiBA,SAAS,OAAO,KAAa;AAC3B,SAAO,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,OAAO,CAAC,EAAE,YAAY;AACjE;AAEA,IAAM,UAAkC;EACtC,SAAS;EACT,WAAW;EACX,eAAe;EACf,UAAU;EACV,SAAS;EACT,QAAQ;EACR,cAAc;EACd,gBAAgB;AAClB;AAEA,SAAS,UAAU,MAAc;AAC/B,MAAI,QAAQ,QAAS,QAAO,QAAQ,IAAI;AAExC,MAAI,KAAK,WAAW,IAAI,GAAG;AACzB,WAAO,KAAK,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC;EACpC;AAEA,SAAO,KAAK,YAAY;AAC1B;AAEO,IAAM,iBAAiB,iBAA4B,CAAC,UAAgB;AACzE,QAAM,aAAmB,CAAC;AAC1B,aAAW,OAAO,OAAO;AACvB,UAAM,QAAQ,MAAM,GAAG;AACvB,QAAI,QAAQ,YAAY;AACtB,UAAI,OAAO,UAAU,UAAU;AAC7B,mBAAW,WAAW,IAAI;MAC5B,WAAoD,SAAS,MAAM;AACjE,gBAAQ,KAAK,wEAAwE;MACvF;IACF,OAAO;AACL,iBAAW,UAAU,GAAG,CAAC,IAAI,MAAM,GAAG;IACxC;EACF;AACA,SAAO;AACT,CAAC;ACpDM,SAAS,YAKd,SACA,SACwC;AAExC,QAAM,EAAE,SAAS,QAAQ,IAAI,WAAW,CAAC;AAEzC,QAAM,QAAQ,WAAW,QAAQ,KAAK;AAEtC,QAAM,cAAc,UAAU,QAAQ,OAAO,MAAM;AACjD,UAAM,QAAQ,SAAS,QAAQ,KAAK;EACtC,CAAC;AAED,cAAY,MAAM;AAChB;EACF,CAAC;AAED,cAAY,MAAM;AAChB,YAAQ,WAAW,EAAE,QAAQ,CAAC;EAChC,CAAC;AAED,MAAI,SAAS;AACX;MACE;MACA,CAAC,QAAQ;AACP,gBAAQ,WAAW,MAAM,GAAG,CAAC;MAC/B;MACA,EAAE,MAAM,KAAK;IACf;EACF;AAEA,SAAO;AACT;ACtCO,SAAS,SAId,SAA4C;AAC5C,QAAM,QAAQ,YAAY,OAAO;AACjC,SAAO,CAAC,OAAO,QAAQ,IAAI;AAC7B;ACNO,SAAS,WAId,SAA+C,SAAoD;AACnG,QAAM,EAAE,OAAO,eAAe,QAAQ,IAAI,WAAW,CAAC;AAEtD,QAAM,UAAU,OAAO,YAAY,aAAa,QAAQ,IAAI;AAC5D,MAAI,QAAS,SAAQ,WAAWA,MAAM,OAAO,CAAC;AAC9C,UAAQ,SAAS;AAEjB,YAAU,MAAM;AACd,YAAQ,MAAM,aAAa;AAE3B,oBAAgB,MAAM;AACpB,cAAQ,KAAK;IACf,CAAC;EACH,CAAC;AAED,SAAO;AACT;ACnBO,SAAS,WAId,SAA+C,SAAoD;AACnG,QAAM,UAAU,WAAW,SAAS,OAAO;AAC3C,QAAM,QAAQ,YAAY,SAAS,OAAO;AAC1C,SAAO,CAAC,OAAO,QAAQ,MAAM,OAAO;AACtC;", "names": ["unref"]}