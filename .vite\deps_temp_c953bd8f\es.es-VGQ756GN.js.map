{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/es.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"Lunes\",\n  \"Martes\",\n  \"Miércoles\",\n  \"Jueves\",\n  \"Viernes\",\n  \"Sábado\",\n  \"Domingo\"\n];\nconst months = [\n  \"<PERSON><PERSON>\",\n  \"Febrero\",\n  \"Mar<PERSON>\",\n  \"A<PERSON><PERSON>\",\n  \"Mayo\",\n  \"Jun<PERSON>\",\n  \"Julio\",\n  \"Agosto\",\n  \"Septiembre\",\n  \"Octubre\",\n  \"Noviembre\",\n  \"Diciembre\"\n];\nconst years = \"Años\";\nconst year = \"Año\";\nconst month = \"Mes\";\nconst week = \"Semana\";\nconst day = \"Día\";\nconst today = \"Hoy\";\nconst noEvent = \"No hay evento\";\nconst allDay = \"Todo el día\";\nconst deleteEvent = \"Borrar\";\nconst createEvent = \"Crear un evento\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst es = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  es as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}