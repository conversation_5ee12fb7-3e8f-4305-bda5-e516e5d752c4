import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/is.es.js
var weekDays = [
  "Mánudagu<PERSON>",
  "<PERSON><PERSON>ð<PERSON>dagur",
  "<PERSON><PERSON><PERSON><PERSON>gur",
  "<PERSON><PERSON><PERSON>dagur",
  "<PERSON><PERSON><PERSON><PERSON>gu<PERSON>",
  "Laugardagur",
  "Sunnudagur"
];
var months = [
  "<PERSON><PERSON><PERSON>",
  "<PERSON>r<PERSON><PERSON>",
  "<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "Ágús<PERSON>",
  "September",
  "Október",
  "Nóvember",
  "Desember"
];
var years = "Ár";
var year = "Ár";
var month = "Mánuður";
var week = "Vika";
var day = "Dagur";
var today = "Í dag";
var noEvent = "Enginn atburður";
var allDay = "Allan daginn";
var deleteEvent = "Eyða";
var createEvent = "Búðu til viðburð";
var dateFormat = "dddd D MMMM YYYY";
var is = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  is as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/is.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=is.es-6VNWB3B6.js.map
