{"version": 3, "sources": ["../../node_modules/vue-cal/dist/vue-cal.es.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nimport { openBlock, createElementBlock, Fragment, renderList, normalizeClass, normalizeStyle, createVNode, Transition, withCtx, createElementVNode, renderSlot, toDisplayString, createCommentVNode, createTextVNode, resolveComponent, createBlock, resolveDynamicComponent, createSlots, withKeys, withModifiers, TransitionGroup, normalizeProps, mergeProps } from \"vue\";\nconst __variableDynamicImportRuntimeHelper = (glob, path, segs) => {\n  const v = glob[path];\n  if (v) {\n    return typeof v === \"function\" ? v() : Promise.resolve(v);\n  }\n  return new Promise((_, reject) => {\n    (typeof queueMicrotask === \"function\" ? queueMicrotask : setTimeout)(\n      reject.bind(\n        null,\n        new Error(\n          \"Unknown variable dynamic import: \" + path + (path.split(\"/\").length !== segs ? \". Note that variables only represent file names one level deep.\" : \"\")\n        )\n      )\n    );\n  });\n};\nlet now, todayDate, todayF, self;\nlet _dateObject = {};\nlet _timeObject = {};\nclass DateUtils {\n  constructor(texts, noPrototypes = false) {\n    __publicField(this, \"texts\", {});\n    /**\n     * Simply takes a Date and returns the associated time in minutes (sum of hours + minutes).\n     *\n     * @param {Date} date the JavaScript Date to extract minutes from.\n     * @return {Number} the number of minutes (total of hours plus minutes).\n     */\n    __publicField(this, \"dateToMinutes\", (date) => date.getHours() * 60 + date.getMinutes());\n    self = this;\n    this._texts = texts;\n    if (!noPrototypes && Date && !Date.prototype.addDays) this._initDatePrototypes();\n  }\n  _initDatePrototypes() {\n    Date.prototype.addDays = function(days) {\n      return self.addDays(this, days);\n    };\n    Date.prototype.subtractDays = function(days) {\n      return self.subtractDays(this, days);\n    };\n    Date.prototype.addHours = function(hours) {\n      return self.addHours(this, hours);\n    };\n    Date.prototype.subtractHours = function(hours) {\n      return self.subtractHours(this, hours);\n    };\n    Date.prototype.addMinutes = function(minutes) {\n      return self.addMinutes(this, minutes);\n    };\n    Date.prototype.subtractMinutes = function(minutes) {\n      return self.subtractMinutes(this, minutes);\n    };\n    Date.prototype.getWeek = function() {\n      return self.getWeek(this);\n    };\n    Date.prototype.isToday = function() {\n      return self.isToday(this);\n    };\n    Date.prototype.isLeapYear = function() {\n      return self.isLeapYear(this);\n    };\n    Date.prototype.format = function(format = \"YYYY-MM-DD\") {\n      return self.formatDate(this, format);\n    };\n    Date.prototype.formatTime = function(format = \"HH:mm\") {\n      return self.formatTime(this, format);\n    };\n  }\n  removePrototypes() {\n    delete Date.prototype.addDays;\n    delete Date.prototype.subtractDays;\n    delete Date.prototype.addHours;\n    delete Date.prototype.subtractHours;\n    delete Date.prototype.addMinutes;\n    delete Date.prototype.subtractMinutes;\n    delete Date.prototype.getWeek;\n    delete Date.prototype.isToday;\n    delete Date.prototype.isLeapYear;\n    delete Date.prototype.format;\n    delete Date.prototype.formatTime;\n  }\n  updateTexts(texts) {\n    this._texts = texts;\n  }\n  // Cache Today's date (to a maximum) for better isToday() performances. Formatted without leading 0.\n  // We still need to update Today's date when Today changes without page refresh.\n  _todayFormatted() {\n    if (todayDate !== (/* @__PURE__ */ new Date()).getDate()) {\n      now = /* @__PURE__ */ new Date();\n      todayDate = now.getDate();\n      todayF = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}`;\n    }\n    return todayF;\n  }\n  // UTILITIES.\n  // ====================================================================\n  addDays(date, days) {\n    const d = new Date(date.valueOf());\n    d.setDate(d.getDate() + days);\n    return d;\n  }\n  subtractDays(date, days) {\n    const d = new Date(date.valueOf());\n    d.setDate(d.getDate() - days);\n    return d;\n  }\n  addHours(date, hours) {\n    const d = new Date(date.valueOf());\n    d.setHours(d.getHours() + hours);\n    return d;\n  }\n  subtractHours(date, hours) {\n    const d = new Date(date.valueOf());\n    d.setHours(d.getHours() - hours);\n    return d;\n  }\n  addMinutes(date, minutes) {\n    const d = new Date(date.valueOf());\n    d.setMinutes(d.getMinutes() + minutes);\n    return d;\n  }\n  subtractMinutes(date, minutes) {\n    const d = new Date(date.valueOf());\n    d.setMinutes(d.getMinutes() - minutes);\n    return d;\n  }\n  getWeek(date) {\n    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));\n    const dayNum = d.getUTCDay() || 7;\n    d.setUTCDate(d.getUTCDate() + 4 - dayNum);\n    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));\n    return Math.ceil(((d - yearStart) / 864e5 + 1) / 7);\n  }\n  isToday(date) {\n    return `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}` === this._todayFormatted();\n  }\n  isLeapYear(date) {\n    const year = date.getFullYear();\n    return !(year % 400) || year % 100 && !(year % 4);\n  }\n  // Returns today if it's FirstDayOfWeek (Monday or Sunday) or previous FirstDayOfWeek otherwise.\n  getPreviousFirstDayOfWeek(date = null, weekStartsOnSunday) {\n    const prevFirstDayOfWeek = date && new Date(date.valueOf()) || /* @__PURE__ */ new Date();\n    const dayModifier = weekStartsOnSunday ? 7 : 6;\n    prevFirstDayOfWeek.setDate(prevFirstDayOfWeek.getDate() - (prevFirstDayOfWeek.getDay() + dayModifier) % 7);\n    return prevFirstDayOfWeek;\n  }\n  /**\n   * Converts a string to a Javascript Date object. If a Date object is passed, return it as is.\n   *\n   * @param {String | Date} date the string to convert to Date.\n   * @return {Date} the equivalent Javascript Date object.\n   */\n  stringToDate(date) {\n    if (date instanceof Date) return date;\n    if (date.length === 10) date += \" 00:00\";\n    return new Date(date.replace(/-/g, \"/\"));\n  }\n  /**\n   * Count the number of days this date range spans onto.\n   * E.g. countDays(2019-11-02 18:00, 2019-11-03 02:00) = 2\n   *\n   * @param {String | Date} start the start date\n   * @param {String | Date} end the end date\n   * @return {Integer} The number of days this date range involves\n   */\n  countDays(start, end) {\n    if (typeof start === \"string\") start = start.replace(/-/g, \"/\");\n    if (typeof end === \"string\") end = end.replace(/-/g, \"/\");\n    start = new Date(start).setHours(0, 0, 0, 0);\n    end = new Date(end).setHours(0, 0, 1, 0);\n    const timezoneDiffMs = (new Date(end).getTimezoneOffset() - new Date(start).getTimezoneOffset()) * 60 * 1e3;\n    return Math.ceil((end - start - timezoneDiffMs) / (24 * 3600 * 1e3));\n  }\n  /**\n   * Take 2 dates and check if within the same time step (useful in overlapping events).\n   *\n   * @return {Boolean} `true` if their time is included in the same time step,\n   *                   this means these 2 dates are very close.\n   */\n  datesInSameTimeStep(date1, date2, timeStep) {\n    return Math.abs(date1.getTime() - date2.getTime()) <= timeStep * 60 * 1e3;\n  }\n  // ====================================================================\n  // FORMATTERS.\n  // ====================================================================\n  /**\n   * Formats a date/time to the given format and returns the formatted string.\n   *\n   * @param {Date} date a JavaScript Date object to format.\n   * @param {String} format the wanted format.\n   * @param {Object} texts Optional: the localized texts object to override the vue-cal one in this._texts.\n   *                       This becomes useful when showing multiple instances with different languages,\n   *                       like in the documentation page.\n   * @return {String} the formatted date.\n   */\n  formatDate(date, format = \"YYYY-MM-DD\", texts = null) {\n    if (!texts) texts = this._texts;\n    if (!format) format = \"YYYY-MM-DD\";\n    if (format === \"YYYY-MM-DD\") return this.formatDateLite(date);\n    _dateObject = {};\n    _timeObject = {};\n    const dateObj = {\n      YYYY: () => this._hydrateDateObject(date, texts).YYYY,\n      YY: () => this._hydrateDateObject(date, texts).YY(),\n      M: () => this._hydrateDateObject(date, texts).M,\n      MM: () => this._hydrateDateObject(date, texts).MM(),\n      MMM: () => this._hydrateDateObject(date, texts).MMM(),\n      MMMM: () => this._hydrateDateObject(date, texts).MMMM(),\n      MMMMG: () => this._hydrateDateObject(date, texts).MMMMG(),\n      D: () => this._hydrateDateObject(date, texts).D,\n      DD: () => this._hydrateDateObject(date, texts).DD(),\n      S: () => this._hydrateDateObject(date, texts).S(),\n      d: () => this._hydrateDateObject(date, texts).d,\n      dd: () => this._hydrateDateObject(date, texts).dd(),\n      ddd: () => this._hydrateDateObject(date, texts).ddd(),\n      dddd: () => this._hydrateDateObject(date, texts).dddd(),\n      HH: () => this._hydrateTimeObject(date, texts).HH,\n      H: () => this._hydrateTimeObject(date, texts).H,\n      hh: () => this._hydrateTimeObject(date, texts).hh,\n      h: () => this._hydrateTimeObject(date, texts).h,\n      am: () => this._hydrateTimeObject(date, texts).am,\n      AM: () => this._hydrateTimeObject(date, texts).AM,\n      mm: () => this._hydrateTimeObject(date, texts).mm,\n      m: () => this._hydrateTimeObject(date, texts).m\n    };\n    return format.replace(/(\\{[a-zA-Z]+\\}|[a-zA-Z]+)/g, (m, contents) => {\n      const result = dateObj[contents.replace(/\\{|\\}/g, \"\")];\n      return result !== void 0 ? result() : contents;\n    });\n  }\n  // More performant function to convert a Date to `YYYY-MM-DD` formatted string only.\n  formatDateLite(date) {\n    const m = date.getMonth() + 1;\n    const d = date.getDate();\n    return `${date.getFullYear()}-${m < 10 ? \"0\" : \"\"}${m}-${d < 10 ? \"0\" : \"\"}${d}`;\n  }\n  /**\n   * Formats a time (from Date or number of mins) to the given format and returns the formatted string.\n   *\n   * @param {Date | Number} date a JavaScript Date object or a time in minutes.\n   * @param {String} format the wanted format.\n   * @param {Object} texts Optional: the localized texts object to override the vue-cal one in this._texts.\n   *                       This becomes useful when showing multiple instances with different languages,\n   *                       like in the documentation page.\n   * @param {Boolean} round if time is 23:59:59, rounds up to 24:00 for formatting only.\n   * @return {String} the formatted time.\n   */\n  formatTime(date, format = \"HH:mm\", texts = null, round = false) {\n    let shouldRound = false;\n    if (round) {\n      const [h, m, s] = [date.getHours(), date.getMinutes(), date.getSeconds()];\n      if (h + m + s === 23 + 59 + 59) shouldRound = true;\n    }\n    if (date instanceof Date && format === \"HH:mm\") return shouldRound ? \"24:00\" : this.formatTimeLite(date);\n    _timeObject = {};\n    if (!texts) texts = this._texts;\n    const timeObj = this._hydrateTimeObject(date, texts);\n    const formatted = format.replace(/(\\{[a-zA-Z]+\\}|[a-zA-Z]+)/g, (m, contents) => {\n      const result = timeObj[contents.replace(/\\{|\\}/g, \"\")];\n      return result !== void 0 ? result : contents;\n    });\n    return shouldRound ? formatted.replace(\"23:59\", \"24:00\") : formatted;\n  }\n  /**\n   * Formats a time to 'HH:mm' from a Date and returns the formatted string.\n   *\n   * @param {Date} date a JavaScript Date object to format.\n   * @return {String} the formatted time.\n   */\n  formatTimeLite(date) {\n    const h = date.getHours();\n    const m = date.getMinutes();\n    return `${(h < 10 ? \"0\" : \"\") + h}:${(m < 10 ? \"0\" : \"\") + m}`;\n  }\n  _nth(d) {\n    if (d > 3 && d < 21) return \"th\";\n    switch (d % 10) {\n      case 1:\n        return \"st\";\n      case 2:\n        return \"nd\";\n      case 3:\n        return \"rd\";\n      default:\n        return \"th\";\n    }\n  }\n  _hydrateDateObject(date, texts) {\n    if (_dateObject.D) return _dateObject;\n    const YYYY = date.getFullYear();\n    const M = date.getMonth() + 1;\n    const D = date.getDate();\n    const day = date.getDay();\n    const dayNumber = (day - 1 + 7) % 7;\n    _dateObject = {\n      // Year.\n      YYYY,\n      // 2019.\n      YY: () => YYYY.toString().substring(2),\n      // 19.\n      // Month.\n      M,\n      // 1 to 12.\n      MM: () => (M < 10 ? \"0\" : \"\") + M,\n      // 01 to 12.\n      MMM: () => texts.months[M - 1].substring(0, 3),\n      // Jan to Dec.\n      MMMM: () => texts.months[M - 1],\n      // January to December.\n      MMMMG: () => (texts.monthsGenitive || texts.months)[M - 1],\n      // January to December in genitive form (Greek...)\n      // Day.\n      D,\n      // 1 to 31.\n      DD: () => (D < 10 ? \"0\" : \"\") + D,\n      // 01 to 31.\n      S: () => this._nth(D),\n      // st, nd, rd, th.\n      // Day of the week.\n      d: dayNumber + 1,\n      // 1 to 7 with 7 = Sunday.\n      dd: () => texts.weekDays[dayNumber][0],\n      // M to S.\n      ddd: () => texts.weekDays[dayNumber].substr(0, 3),\n      // Mon to Sun.\n      dddd: () => texts.weekDays[dayNumber]\n      // Monday to Sunday.\n    };\n    return _dateObject;\n  }\n  _hydrateTimeObject(date, texts) {\n    if (_timeObject.am) return _timeObject;\n    let H, m;\n    if (date instanceof Date) {\n      H = date.getHours();\n      m = date.getMinutes();\n    } else {\n      H = Math.floor(date / 60);\n      m = Math.floor(date % 60);\n    }\n    const h = H % 12 ? H % 12 : 12;\n    const am = (texts || { am: \"am\", pm: \"pm\" })[H === 24 || H < 12 ? \"am\" : \"pm\"];\n    _timeObject = {\n      H,\n      h,\n      HH: (H < 10 ? \"0\" : \"\") + H,\n      hh: (h < 10 ? \"0\" : \"\") + h,\n      am,\n      AM: am.toUpperCase(),\n      m,\n      mm: (m < 10 ? \"0\" : \"\") + m\n    };\n    return _timeObject;\n  }\n  // ====================================================================\n}\nconst minutesInADay$2 = 24 * 60;\nclass CellUtils {\n  constructor(vuecal) {\n    __publicField(this, \"_vuecal\", null);\n    /**\n     * Select a cell and go to narrower view on double click or single click according to vuecalProps option.\n     *\n     * @param {Boolean} force Force switching to narrower view.\n     * @param {Date} date The selected cell date at the exact time where it was clicked (through cursor coords).\n     * @param {Integer} split The selected cell split if any.\n     */\n    __publicField(this, \"selectCell\", (force = false, date, split) => {\n      this._vuecal.$emit(\"cell-click\", split ? { date, split } : date);\n      if (this._vuecal.clickToNavigate || force) this._vuecal.switchToNarrowerView();\n      else if (this._vuecal.dblclickToNavigate && \"ontouchstart\" in window) {\n        this._vuecal.domEvents.dblTapACell.taps++;\n        setTimeout(() => this._vuecal.domEvents.dblTapACell.taps = 0, this._vuecal.domEvents.dblTapACell.timeout);\n        if (this._vuecal.domEvents.dblTapACell.taps >= 2) {\n          this._vuecal.domEvents.dblTapACell.taps = 0;\n          this._vuecal.switchToNarrowerView();\n          this._vuecal.$emit(\"cell-dblclick\", split ? { date, split } : date);\n        }\n      }\n    });\n    /**\n     * Select a cell and go to narrower view on enter.\n     *\n     * @param {Boolean} force Force switching to narrower view.\n     * @param {Date} date The selected cell date at the exact time where it was clicked (through cursor coords).\n     * @param {Integer} split The selected cell split if any.\n     */\n    __publicField(this, \"keyPressEnterCell\", (date, split) => {\n      this._vuecal.$emit(\"cell-keypress-enter\", split ? { date, split } : date);\n      this._vuecal.switchToNarrowerView();\n    });\n    /**\n     * Get the coordinates of the mouse cursor from the cells wrapper referential (`ref=\"cells\"`).\n     *\n     * @todo Cache bounding box & update it on resize.\n     * @param {Object} e the native DOM event object.\n     * @return {Object} containing { x: {Number}, y: {Number} }\n     */\n    __publicField(this, \"getPosition\", (e) => {\n      const { left, top } = this._vuecal.cellsEl.getBoundingClientRect();\n      const { clientX, clientY } = \"ontouchstart\" in window && e.touches ? e.touches[0] : e;\n      return { x: clientX - left, y: clientY - top };\n    });\n    /**\n     * Get the number of minutes from the top to the mouse cursor.\n     * Returns a constrained time between 0 and 24 * 60.\n     *\n     * @param {Object} e the native DOM event object.\n     * @return {Object} containing { minutes: {Number}, cursorCoords: { x: {Number}, y: {Number} } }\n     */\n    __publicField(this, \"minutesAtCursor\", (e) => {\n      let minutes = 0;\n      let cursorCoords = { x: 0, y: 0 };\n      const { timeStep, timeCellHeight, timeFrom } = this._vuecal.$props;\n      if (typeof e === \"number\") minutes = e;\n      else if (typeof e === \"object\") {\n        cursorCoords = this.getPosition(e);\n        minutes = Math.round(cursorCoords.y * timeStep / parseInt(timeCellHeight) + timeFrom);\n      }\n      return { minutes: Math.max(Math.min(minutes, minutesInADay$2), 0), cursorCoords };\n    });\n    this._vuecal = vuecal;\n  }\n}\nconst defaultEventDuration = 2;\nconst minutesInADay$1 = 24 * 60;\nlet ud;\nlet _cellOverlaps, _comparisonArray;\nclass EventUtils {\n  constructor(vuecal, dateUtils2) {\n    __publicField(this, \"_vuecal\", null);\n    __publicField(this, \"eventDefaults\", {\n      _eid: null,\n      start: \"\",\n      // Externally given formatted date & time or Date object.\n      startTimeMinutes: 0,\n      end: \"\",\n      // Externally given formatted date & time or Date object.\n      endTimeMinutes: 0,\n      title: \"\",\n      content: \"\",\n      background: false,\n      allDay: false,\n      segments: null,\n      repeat: null,\n      daysCount: 1,\n      deletable: true,\n      deleting: false,\n      titleEditable: true,\n      resizable: true,\n      resizing: false,\n      draggable: true,\n      dragging: false,\n      draggingStatic: false,\n      // Controls the CSS class of the static clone while dragging.\n      focused: false,\n      class: \"\"\n    });\n    this._vuecal = vuecal;\n    ud = dateUtils2;\n  }\n  /**\n   * Create an event at the given date and time, and allow overriding\n   * event attributes through the eventOptions object.\n   *\n   * @param {Date | String} dateTime The date and time of the new event start.\n   * @param {Number} duration the event duration in minutes.\n   * @param {Object} eventOptions some options to override the `eventDefaults` - optional.\n   */\n  createAnEvent(dateTime, duration, eventOptions) {\n    if (typeof dateTime === \"string\") dateTime = ud.stringToDate(dateTime);\n    if (!(dateTime instanceof Date)) return false;\n    const startTimeMinutes = ud.dateToMinutes(dateTime);\n    duration = duration * 1 || defaultEventDuration * 60;\n    const endTimeMinutes = startTimeMinutes + duration;\n    const end = ud.addMinutes(new Date(dateTime), duration);\n    if (eventOptions.end) {\n      if (typeof eventOptions.end === \"string\") eventOptions.end = ud.stringToDate(eventOptions.end);\n      eventOptions.endTimeMinutes = ud.dateToMinutes(eventOptions.end);\n    }\n    const event = {\n      ...this.eventDefaults,\n      _eid: `${this._vuecal._.uid}_${this._vuecal.eventIdIncrement++}`,\n      start: dateTime,\n      startTimeMinutes,\n      end,\n      endTimeMinutes,\n      segments: null,\n      ...eventOptions\n    };\n    if (typeof this._vuecal.onEventCreate === \"function\") {\n      if (!this._vuecal.onEventCreate(event, () => this.deleteAnEvent(event))) return;\n    }\n    if (event.startDateF !== event.endDateF) {\n      event.daysCount = ud.countDays(event.start, event.end);\n    }\n    this._vuecal.mutableEvents.push(event);\n    this._vuecal.addEventsToView([event]);\n    this._vuecal.emitWithEvent(\"event-create\", event);\n    this._vuecal.$emit(\"event-change\", { event: this._vuecal.cleanupEvent(event), originalEvent: null });\n    return event;\n  }\n  /**\n   * Add an event segment (= day) to a multiple-day event.\n   *\n   * @param {Object} e the multiple-day event to add segment in.\n   */\n  addEventSegment(e) {\n    if (!e.segments) {\n      e.segments = {};\n      e.segments[ud.formatDateLite(e.start)] = {\n        start: e.start,\n        startTimeMinutes: e.startTimeMinutes,\n        endTimeMinutes: minutesInADay$1,\n        isFirstDay: true,\n        isLastDay: false\n      };\n    }\n    const previousSegment = e.segments[ud.formatDateLite(e.end)];\n    if (previousSegment) {\n      previousSegment.isLastDay = false;\n      previousSegment.endTimeMinutes = minutesInADay$1;\n    }\n    const start = ud.addDays(e.end, 1);\n    const formattedDate = ud.formatDateLite(start);\n    start.setHours(0, 0, 0, 0);\n    e.segments[formattedDate] = {\n      start,\n      startTimeMinutes: 0,\n      endTimeMinutes: e.endTimeMinutes,\n      isFirstDay: false,\n      isLastDay: true\n    };\n    e.end = ud.addMinutes(start, e.endTimeMinutes);\n    e.daysCount = Object.keys(e.segments).length;\n    return formattedDate;\n  }\n  /**\n   * Remove an event segment (= day) from a multiple-day event.\n   *\n   * @param {Object} e the multiple-day event to remove segments from.\n   */\n  removeEventSegment(e) {\n    let segmentsCount = Object.keys(e.segments).length;\n    if (segmentsCount <= 1) return ud.formatDateLite(e.end);\n    delete e.segments[ud.formatDateLite(e.end)];\n    segmentsCount--;\n    const end = ud.subtractDays(e.end, 1);\n    const formattedDate = ud.formatDateLite(end);\n    const previousSegment = e.segments[formattedDate];\n    if (!segmentsCount) e.segments = null;\n    else if (previousSegment) {\n      previousSegment.isLastDay = true;\n      previousSegment.endTimeMinutes = e.endTimeMinutes;\n    } else ;\n    e.daysCount = segmentsCount || 1;\n    e.end = end;\n    return formattedDate;\n  }\n  /**\n   * Create 1 segment per day of the given event, but only within the current view.\n   * (It won't create segments for all the days in view that are not in the event!)\n   *\n   * An event segment is a piece of event per day that contains more day-specific data.\n   *\n   * @param {Object} e the multiple-day event to create segments for.\n   * @param {Date} viewStartDate the starting date of the view.\n   * @param {Date} viewEndDate the ending date of the view.\n   */\n  createEventSegments(e, viewStartDate, viewEndDate) {\n    const viewStartTimestamp = viewStartDate.getTime();\n    const viewEndTimestamp = viewEndDate.getTime();\n    let eventStart = e.start.getTime();\n    let eventEnd = e.end.getTime();\n    let repeatedEventStartFound = false;\n    let timestamp, end, eventStartAtMidnight;\n    if (!e.end.getHours() && !e.end.getMinutes()) eventEnd -= 1e3;\n    e.segments = {};\n    if (!e.repeat) {\n      timestamp = Math.max(viewStartTimestamp, eventStart);\n      end = Math.min(viewEndTimestamp, eventEnd);\n    } else {\n      timestamp = viewStartTimestamp;\n      end = Math.min(\n        viewEndTimestamp,\n        e.repeat.until ? ud.stringToDate(e.repeat.until).getTime() : viewEndTimestamp\n      );\n    }\n    while (timestamp <= end) {\n      let createSegment = false;\n      const nextMidnight = ud.addDays(new Date(timestamp), 1).setHours(0, 0, 0, 0);\n      let isFirstDay, isLastDay, start, formattedDate;\n      if (e.repeat) {\n        const tmpDate = new Date(timestamp);\n        const tmpDateFormatted = ud.formatDateLite(tmpDate);\n        if (repeatedEventStartFound || e.occurrences && e.occurrences[tmpDateFormatted]) {\n          if (!repeatedEventStartFound) {\n            eventStart = e.occurrences[tmpDateFormatted].start;\n            eventStartAtMidnight = new Date(eventStart).setHours(0, 0, 0, 0);\n            eventEnd = e.occurrences[tmpDateFormatted].end;\n          }\n          repeatedEventStartFound = true;\n          createSegment = true;\n        }\n        isFirstDay = timestamp === eventStartAtMidnight;\n        isLastDay = tmpDateFormatted === ud.formatDateLite(new Date(eventEnd));\n        start = new Date(isFirstDay ? eventStart : timestamp);\n        formattedDate = ud.formatDateLite(start);\n        if (isLastDay) repeatedEventStartFound = false;\n      } else {\n        createSegment = true;\n        isFirstDay = timestamp === eventStart;\n        isLastDay = end === eventEnd && nextMidnight > end;\n        start = isFirstDay ? e.start : new Date(timestamp);\n        formattedDate = ud.formatDateLite(isFirstDay ? e.start : start);\n      }\n      if (createSegment) {\n        e.segments[formattedDate] = {\n          start,\n          startTimeMinutes: isFirstDay ? e.startTimeMinutes : 0,\n          endTimeMinutes: isLastDay ? e.endTimeMinutes : minutesInADay$1,\n          isFirstDay,\n          isLastDay\n        };\n      }\n      timestamp = nextMidnight;\n    }\n    return e;\n  }\n  /**\n   * Delete an event.\n   *\n   * @param {Object} event the calendar event to delete.\n   */\n  deleteAnEvent(event) {\n    this._vuecal.emitWithEvent(\"event-delete\", event);\n    this._vuecal.mutableEvents = this._vuecal.mutableEvents.filter((e) => e._eid !== event._eid);\n    this._vuecal.view.events = this._vuecal.view.events.filter((e) => e._eid !== event._eid);\n  }\n  // EVENT OVERLAPS.\n  // ===================================================================\n  // Will recalculate all the overlaps of the current cell OR split.\n  // cellEvents will contain only the current split events if in a split.\n  checkCellOverlappingEvents(cellEvents, options) {\n    _comparisonArray = cellEvents.slice(0);\n    _cellOverlaps = {};\n    cellEvents.forEach((e) => {\n      _comparisonArray.shift();\n      if (!_cellOverlaps[e._eid]) _cellOverlaps[e._eid] = { overlaps: [], start: e.start, position: 0 };\n      _cellOverlaps[e._eid].position = 0;\n      _comparisonArray.forEach((e2) => {\n        if (!_cellOverlaps[e2._eid]) _cellOverlaps[e2._eid] = { overlaps: [], start: e2.start, position: 0 };\n        const eventIsInRange = this.eventInRange(e2, e.start, e.end);\n        const eventsInSameTimeStep = options.overlapsPerTimeStep ? ud.datesInSameTimeStep(e.start, e2.start, options.timeStep) : 1;\n        if (!e.background && !e.allDay && !e2.background && !e2.allDay && eventIsInRange && eventsInSameTimeStep) {\n          _cellOverlaps[e._eid].overlaps.push(e2._eid);\n          _cellOverlaps[e._eid].overlaps = [...new Set(_cellOverlaps[e._eid].overlaps)];\n          _cellOverlaps[e2._eid].overlaps.push(e._eid);\n          _cellOverlaps[e2._eid].overlaps = [...new Set(_cellOverlaps[e2._eid].overlaps)];\n          _cellOverlaps[e2._eid].position++;\n        } else {\n          let pos1, pos2;\n          if ((pos1 = (_cellOverlaps[e._eid] || { overlaps: [] }).overlaps.indexOf(e2._eid)) > -1) _cellOverlaps[e._eid].overlaps.splice(pos1, 1);\n          if ((pos2 = (_cellOverlaps[e2._eid] || { overlaps: [] }).overlaps.indexOf(e._eid)) > -1) _cellOverlaps[e2._eid].overlaps.splice(pos2, 1);\n          _cellOverlaps[e2._eid].position--;\n        }\n      });\n    });\n    let longestStreak = 0;\n    for (const id in _cellOverlaps) {\n      const item = _cellOverlaps[id];\n      const overlapsRow = item.overlaps.map((id2) => ({ id: id2, start: _cellOverlaps[id2].start }));\n      overlapsRow.push({ id, start: item.start });\n      overlapsRow.sort((a, b) => a.start < b.start ? -1 : a.start > b.start ? 1 : a.id > b.id ? -1 : 1);\n      item.position = overlapsRow.findIndex((e) => e.id === id);\n      longestStreak = Math.max(this.getOverlapsStreak(item, _cellOverlaps), longestStreak);\n    }\n    return [_cellOverlaps, longestStreak];\n  }\n  /**\n   * Overlaps streak is the longest horizontal set of simultaneous events.\n   * This is determining the width of each events in this streak.\n   * E.g. 3 overlapping events [1, 2, 3]; 1 overlaps 2 & 3; 2 & 3 don't overlap;\n   *      => streak = 2; each width = 50% not 33%.\n   *\n   * @param {Object} event The current event we are checking among all the events of the current cell.\n   * @param {Object} cellOverlaps An indexed array of all the events overlaps for the current cell.\n   * @return {Number} The number of simultaneous event for this event.\n   */\n  getOverlapsStreak(event, cellOverlaps = {}) {\n    let streak = event.overlaps.length + 1;\n    let removeFromStreak = [];\n    event.overlaps.forEach((id) => {\n      if (!removeFromStreak.includes(id)) {\n        const overlapsWithoutSelf = event.overlaps.filter((id2) => id2 !== id);\n        overlapsWithoutSelf.forEach((id3) => {\n          if (!cellOverlaps[id3].overlaps.includes(id)) removeFromStreak.push(id3);\n        });\n      }\n    });\n    removeFromStreak = [...new Set(removeFromStreak)];\n    streak -= removeFromStreak.length;\n    return streak;\n  }\n  /**\n   * Tells whether an event is in a given date range, even partially.\n   *\n   * @param {Object} event The event to test.\n   * @param {Date} start The start of range date object.\n   * @param {Date} end The end of range date object.\n   * @return {Boolean} true if in range, even partially.\n   */\n  eventInRange(event, start, end) {\n    if (event.allDay || !this._vuecal.time) {\n      const startTimestamp2 = new Date(event.start).setHours(0, 0, 0, 0);\n      const endTimestamp2 = new Date(event.end).setHours(23, 59, 0, 0);\n      return endTimestamp2 >= new Date(start).setHours(0, 0, 0, 0) && startTimestamp2 <= new Date(end).setHours(0, 0, 0, 0);\n    }\n    const startTimestamp = event.start.getTime();\n    const endTimestamp = event.end.getTime();\n    return startTimestamp < end.getTime() && endTimestamp > start.getTime();\n  }\n}\nconst _hoisted_1$5 = { class: \"vuecal__flex vuecal__weekdays-headings\" };\nconst _hoisted_2$3 = [\"onClick\"];\nconst _hoisted_3$3 = {\n  class: \"vuecal__flex weekday-label\",\n  grow: \"\"\n};\nconst _hoisted_4$3 = { class: \"full\" };\nconst _hoisted_5$3 = { class: \"small\" };\nconst _hoisted_6$2 = { class: \"xsmall\" };\nconst _hoisted_7$2 = { key: 0 };\nconst _hoisted_8$2 = {\n  key: 0,\n  class: \"vuecal__flex vuecal__split-days-headers\",\n  grow: \"\"\n};\nfunction render$5(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", _hoisted_1$5, [\n    (openBlock(true), createElementBlock(Fragment, null, renderList($options.headings, (heading, i) => {\n      return openBlock(), createElementBlock(Fragment, { key: i }, [\n        !heading.hide ? (openBlock(), createElementBlock(\"div\", {\n          key: 0,\n          class: normalizeClass([\"vuecal__flex vuecal__heading\", { today: heading.today, clickable: $options.cellHeadingsClickable }]),\n          style: normalizeStyle($options.weekdayCellStyles),\n          onClick: ($event) => $options.view.id === \"week\" && $options.selectCell(heading.date, $event),\n          onDblclick: _cache[0] || (_cache[0] = ($event) => $options.view.id === \"week\" && $options.vuecal.dblclickToNavigate && $props.switchToNarrowerView())\n        }, [\n          createVNode(Transition, {\n            name: `slide-fade--${$props.transitionDirection}`,\n            appear: $options.vuecal.transitions\n          }, {\n            default: withCtx(() => [\n              (openBlock(), createElementBlock(\"div\", {\n                class: \"vuecal__flex\",\n                column: \"\",\n                key: $options.vuecal.transitions ? `${i}-${heading.dayOfMonth}` : false\n              }, [\n                createElementVNode(\"div\", _hoisted_3$3, [\n                  renderSlot(_ctx.$slots, \"weekday-heading\", {\n                    heading: $options.cleanupHeading(heading),\n                    view: $options.view\n                  }, () => [\n                    createElementVNode(\"span\", _hoisted_4$3, toDisplayString(heading.full), 1),\n                    createElementVNode(\"span\", _hoisted_5$3, toDisplayString(heading.small), 1),\n                    createElementVNode(\"span\", _hoisted_6$2, toDisplayString(heading.xsmall), 1),\n                    heading.dayOfMonth ? (openBlock(), createElementBlock(\"span\", _hoisted_7$2, \" \" + toDisplayString(heading.dayOfMonth), 1)) : createCommentVNode(\"\", true)\n                  ])\n                ]),\n                $options.vuecal.hasSplits && $options.vuecal.stickySplitLabels ? (openBlock(), createElementBlock(\"div\", _hoisted_8$2, [\n                  (openBlock(true), createElementBlock(Fragment, null, renderList($options.vuecal.daySplits, (split, i2) => {\n                    return openBlock(), createElementBlock(\"div\", {\n                      class: normalizeClass([\"day-split-header\", split.class || false]),\n                      key: i2\n                    }, [\n                      renderSlot(_ctx.$slots, \"split-label\", {\n                        split,\n                        view: $options.view\n                      }, () => [\n                        createTextVNode(toDisplayString(split.label), 1)\n                      ])\n                    ], 2);\n                  }), 128))\n                ])) : createCommentVNode(\"\", true)\n              ]))\n            ]),\n            _: 2\n          }, 1032, [\"name\", \"appear\"])\n        ], 46, _hoisted_2$3)) : createCommentVNode(\"\", true)\n      ], 64);\n    }), 128))\n  ]);\n}\nconst _export_sfc = (sfc, props) => {\n  const target = sfc.__vccOpts || sfc;\n  for (const [key, val] of props) {\n    target[key] = val;\n  }\n  return target;\n};\nconst _sfc_main$5 = {\n  inject: [\"vuecal\", \"utils\", \"view\"],\n  props: {\n    transitionDirection: { type: String, default: \"right\" },\n    weekDays: { type: Array, default: () => [] },\n    switchToNarrowerView: { type: Function, default: () => {\n    } }\n  },\n  methods: {\n    selectCell(date, DOMEvent) {\n      if (date.getTime() !== this.view.selectedDate.getTime()) {\n        this.view.selectedDate = date;\n      }\n      this.utils.cell.selectCell(false, date, DOMEvent);\n    },\n    cleanupHeading: (heading) => ({\n      label: heading.full,\n      date: heading.date,\n      ...heading.today ? { today: heading.today } : {}\n    })\n  },\n  computed: {\n    headings() {\n      if (![\"month\", \"week\"].includes(this.view.id)) return [];\n      let todayFound = false;\n      const headings = this.weekDays.map((cell, i) => {\n        const date = this.utils.date.addDays(this.view.startDate, this.vuecal.startWeekOnSunday && this.vuecal.hideWeekends ? i - 1 : i);\n        return {\n          hide: cell.hide,\n          full: cell.label,\n          // If defined in i18n file, weekDaysShort overrides default truncation of\n          // week days when does not fit on screen or with small/xsmall options.\n          small: cell.short || cell.label.substr(0, 3),\n          xsmall: cell.short || cell.label.substr(0, 1),\n          // Only for week view.\n          ...this.view.id === \"week\" ? {\n            dayOfMonth: date.getDate(),\n            date,\n            today: !todayFound && this.utils.date.isToday(date) && !todayFound++\n          } : {}\n        };\n      });\n      return headings;\n    },\n    cellWidth() {\n      return 100 / (7 - this.weekDays.reduce((total, day) => total + day.hide, 0));\n    },\n    weekdayCellStyles() {\n      return {\n        ...this.vuecal.hideWeekdays.length ? { width: `${this.cellWidth}%` } : {}\n      };\n    },\n    cellHeadingsClickable() {\n      return this.view.id === \"week\" && (this.vuecal.clickToNavigate || this.vuecal.dblclickToNavigate);\n    }\n  }\n};\nconst WeekdaysHeadings = /* @__PURE__ */ _export_sfc(_sfc_main$5, [[\"render\", render$5]]);\nconst _hoisted_1$4 = { class: \"vuecal__header\" };\nconst _hoisted_2$2 = {\n  key: 0,\n  class: \"vuecal__flex vuecal__menu\",\n  role: \"tablist\",\n  \"aria-label\": \"Calendar views navigation\"\n};\nconst _hoisted_3$2 = [\"onDragenter\", \"onDragleave\", \"onClick\", \"aria-label\"];\nconst _hoisted_4$2 = {\n  key: 1,\n  class: \"vuecal__title-bar\"\n};\nconst _hoisted_5$2 = [\"aria-label\"];\nconst _hoisted_6$1 = {\n  class: \"vuecal__flex vuecal__title\",\n  grow: \"\"\n};\nconst _hoisted_7$1 = [\"aria-label\"];\nconst _hoisted_8$1 = {\n  key: 0,\n  class: \"vuecal__flex vuecal__split-days-headers\"\n};\nfunction render$4(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_weekdays_headings = resolveComponent(\"weekdays-headings\");\n  return openBlock(), createElementBlock(\"div\", _hoisted_1$4, [\n    !$props.options.hideViewSelector ? (openBlock(), createElementBlock(\"div\", _hoisted_2$2, [\n      (openBlock(true), createElementBlock(Fragment, null, renderList($props.viewProps.views, (v, id) => {\n        return openBlock(), createElementBlock(Fragment, { key: id }, [\n          v.enabled ? (openBlock(), createElementBlock(\"button\", {\n            key: 0,\n            class: normalizeClass([\"vuecal__view-btn\", { \"vuecal__view-btn--active\": $options.view.id === id, \"vuecal__view-btn--highlighted\": _ctx.highlightedControl === id }]),\n            type: \"button\",\n            onDragenter: ($event) => $props.editEvents.drag && $options.dnd && $options.dnd.viewSelectorDragEnter($event, id, _ctx.$data),\n            onDragleave: ($event) => $props.editEvents.drag && $options.dnd && $options.dnd.viewSelectorDragLeave($event, id, _ctx.$data),\n            onClick: ($event) => $options.switchView(id, null, true),\n            \"aria-label\": `${v.label} view`\n          }, toDisplayString(v.label), 43, _hoisted_3$2)) : createCommentVNode(\"\", true)\n        ], 64);\n      }), 128))\n    ])) : createCommentVNode(\"\", true),\n    !$props.options.hideTitleBar ? (openBlock(), createElementBlock(\"div\", _hoisted_4$2, [\n      createElementVNode(\"button\", {\n        class: normalizeClass([\"vuecal__arrow vuecal__arrow--prev\", { \"vuecal__arrow--highlighted\": _ctx.highlightedControl === \"previous\" }]),\n        type: \"button\",\n        onClick: _cache[0] || (_cache[0] = (...args) => $options.previous && $options.previous(...args)),\n        onDragenter: _cache[1] || (_cache[1] = ($event) => $props.editEvents.drag && $options.dnd && $options.dnd.viewSelectorDragEnter($event, \"previous\", _ctx.$data)),\n        onDragleave: _cache[2] || (_cache[2] = ($event) => $props.editEvents.drag && $options.dnd && $options.dnd.viewSelectorDragLeave($event, \"previous\", _ctx.$data)),\n        \"aria-label\": `Previous ${$options.view.id}`\n      }, [\n        renderSlot(_ctx.$slots, \"arrow-prev\")\n      ], 42, _hoisted_5$2),\n      createElementVNode(\"div\", _hoisted_6$1, [\n        createVNode(Transition, {\n          name: $props.options.transitions ? `slide-fade--${$options.transitionDirection}` : \"\"\n        }, {\n          default: withCtx(() => [\n            (openBlock(), createBlock(resolveDynamicComponent($options.broaderView ? \"button\" : \"span\"), {\n              type: !!$options.broaderView && \"button\",\n              key: `${$options.view.id}${$options.view.startDate.toString()}`,\n              onClick: _cache[3] || (_cache[3] = ($event) => !!$options.broaderView && $options.switchToBroaderView()),\n              \"aria-label\": !!$options.broaderView && `Go to ${$options.broaderView} view`\n            }, {\n              default: withCtx(() => [\n                renderSlot(_ctx.$slots, \"title\")\n              ]),\n              _: 3\n            }, 8, [\"type\", \"aria-label\"]))\n          ]),\n          _: 3\n        }, 8, [\"name\"])\n      ]),\n      $props.options.todayButton ? (openBlock(), createElementBlock(\"button\", {\n        key: 0,\n        class: normalizeClass([\"vuecal__today-btn\", { \"vuecal__today-btn--highlighted\": _ctx.highlightedControl === \"today\" }]),\n        type: \"button\",\n        onClick: _cache[4] || (_cache[4] = (...args) => $options.goToToday && $options.goToToday(...args)),\n        onDragenter: _cache[5] || (_cache[5] = ($event) => $props.editEvents.drag && $options.dnd && $options.dnd.viewSelectorDragEnter($event, \"today\", _ctx.$data)),\n        onDragleave: _cache[6] || (_cache[6] = ($event) => $props.editEvents.drag && $options.dnd && $options.dnd.viewSelectorDragLeave($event, \"today\", _ctx.$data)),\n        \"aria-label\": \"Today\"\n      }, [\n        renderSlot(_ctx.$slots, \"today-button\")\n      ], 34)) : createCommentVNode(\"\", true),\n      createElementVNode(\"button\", {\n        class: normalizeClass([\"vuecal__arrow vuecal__arrow--next\", { \"vuecal__arrow--highlighted\": _ctx.highlightedControl === \"next\" }]),\n        type: \"button\",\n        onClick: _cache[7] || (_cache[7] = (...args) => $options.next && $options.next(...args)),\n        onDragenter: _cache[8] || (_cache[8] = ($event) => $props.editEvents.drag && $options.dnd && $options.dnd.viewSelectorDragEnter($event, \"next\", _ctx.$data)),\n        onDragleave: _cache[9] || (_cache[9] = ($event) => $props.editEvents.drag && $options.dnd && $options.dnd.viewSelectorDragLeave($event, \"next\", _ctx.$data)),\n        \"aria-label\": `Next ${$options.view.id}`\n      }, [\n        renderSlot(_ctx.$slots, \"arrow-next\")\n      ], 42, _hoisted_7$1)\n    ])) : createCommentVNode(\"\", true),\n    $props.viewProps.weekDaysInHeader ? (openBlock(), createBlock(_component_weekdays_headings, {\n      key: 2,\n      \"week-days\": $props.weekDays,\n      \"transition-direction\": $options.transitionDirection,\n      \"switch-to-narrower-view\": $props.switchToNarrowerView\n    }, createSlots({ _: 2 }, [\n      _ctx.$slots[\"weekday-heading\"] ? {\n        name: \"weekday-heading\",\n        fn: withCtx(({ heading, view }) => [\n          renderSlot(_ctx.$slots, \"weekday-heading\", {\n            heading,\n            view\n          })\n        ]),\n        key: \"0\"\n      } : void 0,\n      _ctx.$slots[\"split-label\"] ? {\n        name: \"split-label\",\n        fn: withCtx(({ split }) => [\n          renderSlot(_ctx.$slots, \"split-label\", {\n            split,\n            view: $options.view\n          })\n        ]),\n        key: \"1\"\n      } : void 0\n    ]), 1032, [\"week-days\", \"transition-direction\", \"switch-to-narrower-view\"])) : createCommentVNode(\"\", true),\n    createVNode(Transition, {\n      name: `slide-fade--${$options.transitionDirection}`\n    }, {\n      default: withCtx(() => [\n        $options.showDaySplits ? (openBlock(), createElementBlock(\"div\", _hoisted_8$1, [\n          (openBlock(true), createElementBlock(Fragment, null, renderList($props.daySplits, (split, i) => {\n            return openBlock(), createElementBlock(\"div\", {\n              class: normalizeClass([\"day-split-header\", split.class || false]),\n              key: i\n            }, [\n              renderSlot(_ctx.$slots, \"split-label\", {\n                split,\n                view: $options.view.id\n              }, () => [\n                createTextVNode(toDisplayString(split.label), 1)\n              ])\n            ], 2);\n          }), 128))\n        ])) : createCommentVNode(\"\", true)\n      ]),\n      _: 3\n    }, 8, [\"name\"])\n  ]);\n}\nconst _sfc_main$4 = {\n  inject: [\"vuecal\", \"previous\", \"next\", \"switchView\", \"updateSelectedDate\", \"modules\", \"view\"],\n  components: { WeekdaysHeadings },\n  props: {\n    // Vuecal main component options (props).\n    options: { type: Object, default: () => ({}) },\n    editEvents: { type: Object, required: true },\n    hasSplits: { type: [Boolean, Number], default: false },\n    daySplits: { type: Array, default: () => [] },\n    viewProps: { type: Object, default: () => ({}) },\n    weekDays: { type: Array, default: () => [] },\n    switchToNarrowerView: { type: Function, default: () => {\n    } }\n  },\n  data: () => ({\n    highlightedControl: null\n  }),\n  methods: {\n    goToToday() {\n      this.updateSelectedDate(new Date((/* @__PURE__ */ new Date()).setHours(0, 0, 0, 0)));\n    },\n    switchToBroaderView() {\n      this.transitionDirection = \"left\";\n      if (this.broaderView) this.switchView(this.broaderView);\n    }\n  },\n  computed: {\n    transitionDirection: {\n      get() {\n        return this.vuecal.transitionDirection;\n      },\n      set(direction) {\n        this.vuecal.transitionDirection = direction;\n      }\n    },\n    broaderView() {\n      const { enabledViews } = this.vuecal;\n      return enabledViews[enabledViews.indexOf(this.view.id) - 1];\n    },\n    showDaySplits() {\n      return this.view.id === \"day\" && this.hasSplits && this.options.stickySplitLabels && !this.options.minSplitWidth;\n    },\n    // Drag & drop module.\n    dnd() {\n      return this.modules.dnd;\n    }\n  }\n};\nconst Header = /* @__PURE__ */ _export_sfc(_sfc_main$4, [[\"render\", render$4]]);\nconst _hoisted_1$3 = [\"draggable\"];\nfunction render$3(_ctx, _cache, $props, $setup, $data, $options) {\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass([\"vuecal__event\", $options.eventClasses]),\n    style: normalizeStyle($options.eventStyles),\n    tabindex: \"0\",\n    onFocus: _cache[4] || (_cache[4] = (...args) => $options.focusEvent && $options.focusEvent(...args)),\n    onKeypress: _cache[5] || (_cache[5] = withKeys(withModifiers((...args) => $options.onEnterKeypress && $options.onEnterKeypress(...args), [\"stop\"]), [\"enter\"])),\n    onMouseenter: _cache[6] || (_cache[6] = (...args) => $options.onMouseEnter && $options.onMouseEnter(...args)),\n    onMouseleave: _cache[7] || (_cache[7] = (...args) => $options.onMouseLeave && $options.onMouseLeave(...args)),\n    onTouchstart: _cache[8] || (_cache[8] = withModifiers((...args) => $options.onTouchStart && $options.onTouchStart(...args), [\"stop\"])),\n    onMousedown: _cache[9] || (_cache[9] = ($event) => $options.onMouseDown($event)),\n    onMouseup: _cache[10] || (_cache[10] = (...args) => $options.onMouseUp && $options.onMouseUp(...args)),\n    onTouchend: _cache[11] || (_cache[11] = (...args) => $options.onMouseUp && $options.onMouseUp(...args)),\n    onTouchmove: _cache[12] || (_cache[12] = (...args) => $options.onTouchMove && $options.onTouchMove(...args)),\n    onDblclick: _cache[13] || (_cache[13] = (...args) => $options.onDblClick && $options.onDblClick(...args)),\n    draggable: $options.draggable,\n    onDragstart: _cache[14] || (_cache[14] = ($event) => $options.draggable && $options.onDragStart($event)),\n    onDragend: _cache[15] || (_cache[15] = ($event) => $options.draggable && $options.onDragEnd())\n  }, [\n    $options.vuecal.editEvents.delete && $props.event.deletable ? (openBlock(), createElementBlock(\"div\", {\n      key: 0,\n      class: \"vuecal__event-delete\",\n      onClick: _cache[0] || (_cache[0] = withModifiers((...args) => $options.deleteEvent && $options.deleteEvent(...args), [\"stop\"])),\n      onTouchstart: _cache[1] || (_cache[1] = withModifiers((...args) => $options.touchDeleteEvent && $options.touchDeleteEvent(...args), [\"stop\"]))\n    }, toDisplayString($options.vuecal.texts.deleteEvent), 33)) : createCommentVNode(\"\", true),\n    renderSlot(_ctx.$slots, \"event\", {\n      event: $props.event,\n      view: $options.view.id\n    }),\n    $options.resizable ? (openBlock(), createElementBlock(\"div\", {\n      key: 1,\n      class: \"vuecal__event-resize-handle\",\n      contenteditable: \"false\",\n      onMousedown: _cache[2] || (_cache[2] = withModifiers((...args) => $options.onResizeHandleMouseDown && $options.onResizeHandleMouseDown(...args), [\"stop\", \"prevent\"])),\n      onTouchstart: _cache[3] || (_cache[3] = withModifiers((...args) => $options.onResizeHandleMouseDown && $options.onResizeHandleMouseDown(...args), [\"stop\", \"prevent\"]))\n    }, null, 32)) : createCommentVNode(\"\", true)\n  ], 46, _hoisted_1$3);\n}\nconst _sfc_main$3 = {\n  inject: [\"vuecal\", \"utils\", \"modules\", \"view\", \"domEvents\", \"editEvents\"],\n  props: {\n    cellFormattedDate: { type: String, default: \"\" },\n    event: { type: Object, default: () => ({}) },\n    cellEvents: { type: Array, default: () => [] },\n    overlaps: { type: Array, default: () => [] },\n    // If multiple simultaneous events, the events are placed from left to right from the\n    // one starting first to the last. (See utils/event.js > checkCellOverlappingEvents)\n    eventPosition: { type: Number, default: 0 },\n    overlapsStreak: { type: Number, default: 0 },\n    allDay: { type: Boolean, default: false }\n    // Is the event displayed in the all-day bar.\n  },\n  data: () => ({\n    // Event touch detection with 30px threshold.\n    touch: {\n      dragThreshold: 30,\n      // px.\n      startX: 0,\n      startY: 0,\n      // Detect if the event touch start + touch end was a drag or a tap.\n      // If it was a drag, don't call the event-click handler.\n      dragged: false\n    }\n  }),\n  methods: {\n    /**\n     * On event mousedown.\n     * Do not prevent propagation to trigger cell mousedown which highlights the cell if not highlighted.\n     */\n    onMouseDown(e, touch = false) {\n      if (\"ontouchstart\" in window && !touch) return false;\n      const { clickHoldAnEvent, focusAnEvent, resizeAnEvent, dragAnEvent } = this.domEvents;\n      if (focusAnEvent._eid === this.event._eid && clickHoldAnEvent._eid === this.event._eid) {\n        return true;\n      }\n      this.focusEvent();\n      clickHoldAnEvent._eid = null;\n      if (this.vuecal.editEvents.delete && this.event.deletable) {\n        clickHoldAnEvent.timeoutId = setTimeout(() => {\n          if (!resizeAnEvent._eid && !dragAnEvent._eid) {\n            clickHoldAnEvent._eid = this.event._eid;\n            this.event.deleting = true;\n          }\n        }, clickHoldAnEvent.timeout);\n      }\n    },\n    /**\n     * The mouseup handler is global (whole document) and initialized in index.vue on mounted.\n     * It handles the mouseup on cell, events, and everything.\n     * All mouseup on event, should be put there to avoid conflicts with other cases.\n     * This function is also called on touchend on the event.\n     */\n    onMouseUp(e) {\n      if (this.domEvents.focusAnEvent._eid === this.event._eid && !this.touch.dragged) {\n        this.domEvents.focusAnEvent.mousedUp = true;\n      }\n      this.touch.dragged = false;\n    },\n    onMouseEnter(e) {\n      e.preventDefault();\n      this.vuecal.emitWithEvent(\"event-mouse-enter\", this.event);\n    },\n    onMouseLeave(e) {\n      e.preventDefault();\n      this.vuecal.emitWithEvent(\"event-mouse-leave\", this.event);\n    },\n    // Detect if user taps on an event or drags it. If dragging, don't fire the event-click handler (if any).\n    onTouchMove(e) {\n      if (typeof this.vuecal.onEventClick !== \"function\") return;\n      const { clientX, clientY } = e.touches[0];\n      const { startX, startY, dragThreshold } = this.touch;\n      if (Math.abs(clientX - startX) > dragThreshold || Math.abs(clientY - startY) > dragThreshold) {\n        this.touch.dragged = true;\n      }\n    },\n    onTouchStart(e) {\n      this.touch.startX = e.touches[0].clientX;\n      this.touch.startY = e.touches[0].clientY;\n      this.onMouseDown(e, true);\n    },\n    onEnterKeypress(e) {\n      if (typeof this.vuecal.onEventClick === \"function\") return this.vuecal.onEventClick(this.event, e);\n    },\n    onDblClick(e) {\n      if (typeof this.vuecal.onEventDblclick === \"function\") return this.vuecal.onEventDblclick(this.event, e);\n    },\n    onDragStart(e) {\n      this.dnd && this.dnd.eventDragStart(e, this.event);\n    },\n    onDragEnd() {\n      this.dnd && this.dnd.eventDragEnd(this.event);\n    },\n    onResizeHandleMouseDown() {\n      this.focusEvent();\n      this.domEvents.dragAnEvent._eid = null;\n      this.domEvents.resizeAnEvent = Object.assign(this.domEvents.resizeAnEvent, {\n        _eid: this.event._eid,\n        start: (this.segment || this.event).start,\n        split: this.event.split || null,\n        segment: !!this.segment && this.utils.date.formatDateLite(this.segment.start),\n        originalEnd: new Date((this.segment || this.event).end),\n        originalEndTimeMinutes: this.event.endTimeMinutes\n      });\n      this.event.resizing = true;\n    },\n    deleteEvent(touch = false) {\n      if (\"ontouchstart\" in window && !touch) return false;\n      this.utils.event.deleteAnEvent(this.event);\n    },\n    touchDeleteEvent(event) {\n      this.deleteEvent(true);\n    },\n    cancelDeleteEvent() {\n      this.event.deleting = false;\n    },\n    focusEvent() {\n      const { focusAnEvent } = this.domEvents;\n      const focusedEvent = focusAnEvent._eid;\n      if (focusedEvent === this.event._eid) return;\n      else if (focusedEvent) {\n        const event = this.view.events.find((e) => e._eid === focusedEvent);\n        if (event) event.focused = false;\n      }\n      this.vuecal.cancelDelete();\n      this.vuecal.emitWithEvent(\"event-focus\", this.event);\n      focusAnEvent._eid = this.event._eid;\n      this.event.focused = true;\n    }\n  },\n  computed: {\n    eventDimensions() {\n      const { startTimeMinutes, endTimeMinutes } = this.segment || this.event;\n      let minutesFromTop = startTimeMinutes - this.vuecal.timeFrom;\n      const top = Math.max(Math.round(minutesFromTop * this.vuecal.timeCellHeight / this.vuecal.timeStep), 0);\n      minutesFromTop = Math.min(endTimeMinutes, this.vuecal.timeTo) - this.vuecal.timeFrom;\n      const bottom = Math.round(minutesFromTop * this.vuecal.timeCellHeight / this.vuecal.timeStep);\n      const height = Math.max(bottom - top, 5);\n      return { top, height };\n    },\n    eventStyles() {\n      if (this.event.allDay || !this.vuecal.time || !this.event.endTimeMinutes || this.view.id === \"month\" || this.allDay) return {};\n      let width = 100 / Math.min(this.overlaps.length + 1, this.overlapsStreak);\n      let left = 100 / (this.overlaps.length + 1) * this.eventPosition;\n      if (this.vuecal.minEventWidth && width < this.vuecal.minEventWidth) {\n        width = this.vuecal.minEventWidth;\n        left = (100 - this.vuecal.minEventWidth) / this.overlaps.length * this.eventPosition;\n      }\n      const { top, height } = this.eventDimensions;\n      return {\n        top: `${top}px`,\n        height: `${height}px`,\n        width: `${width}%`,\n        left: this.event.left && `${this.event.left}px` || `${left}%`\n      };\n    },\n    // Don't rely on global variables otherwise whenever it would change all the events would be redrawn.\n    eventClasses() {\n      const { isFirstDay, isLastDay } = this.segment || {};\n      return {\n        [this.event.class]: !!this.event.class,\n        \"vuecal__event--focus\": this.event.focused,\n        \"vuecal__event--resizing\": this.event.resizing,\n        \"vuecal__event--background\": this.event.background,\n        \"vuecal__event--deletable\": this.event.deleting,\n        \"vuecal__event--all-day\": this.event.allDay,\n        // Only apply the dragging class on the event copy that is being dragged.\n        \"vuecal__event--dragging\": !this.event.draggingStatic && this.event.dragging,\n        // Only apply the static class on the event original that remains static while a copy is being dragged.\n        // Sometimes when dragging fast the static class would get stuck and events stays invisible,\n        // So if dragging is false disable the static class as well.\n        \"vuecal__event--static\": this.event.dragging && this.event.draggingStatic,\n        // Multiple days events.\n        \"vuecal__event--multiple-days\": !!this.segment,\n        \"event-start\": this.segment && isFirstDay && !isLastDay,\n        \"event-middle\": this.segment && !isFirstDay && !isLastDay,\n        \"event-end\": this.segment && isLastDay && !isFirstDay\n      };\n    },\n    // When multiple-day events, a segment is a portion of event spanning on 1 day.\n    segment() {\n      return this.event.segments && this.event.segments[this.cellFormattedDate] || null;\n    },\n    draggable() {\n      const { draggable, background, daysCount } = this.event;\n      return this.vuecal.editEvents.drag && draggable && !background && daysCount === 1;\n    },\n    resizable() {\n      const { editEvents, time } = this.vuecal;\n      return editEvents.resize && this.event.resizable && time && !this.allDay && (!this.segment || this.segment && this.segment.isLastDay) && this.view.id !== \"month\";\n    },\n    // Drag & drop module.\n    dnd() {\n      return this.modules.dnd;\n    }\n  }\n};\nconst Event = /* @__PURE__ */ _export_sfc(_sfc_main$3, [[\"render\", render$3]]);\nconst _hoisted_1$2 = [\"data-split\", \"aria-label\", \"onTouchstart\", \"onMousedown\", \"onDragover\", \"onDrop\"];\nconst _hoisted_2$1 = {\n  key: 0,\n  class: \"cell-time-labels\"\n};\nconst _hoisted_3$1 = [\"innerHTML\"];\nconst _hoisted_4$1 = {\n  key: 2,\n  class: \"vuecal__cell-events\"\n};\nconst _hoisted_5$1 = [\"title\"];\nfunction render$2(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_event = resolveComponent(\"event\");\n  return openBlock(), createBlock(TransitionGroup, {\n    class: normalizeClass([\"vuecal__cell\", $options.cellClasses]),\n    name: `slide-fade--${$options.transitionDirection}`,\n    tag: \"div\",\n    appear: $props.options.transitions,\n    style: normalizeStyle($options.cellStyles)\n  }, {\n    default: withCtx(() => [\n      (openBlock(true), createElementBlock(Fragment, null, renderList($options.splitsCount ? $options.splits : 1, (split, i) => {\n        return openBlock(), createElementBlock(\"div\", {\n          class: normalizeClass([\"vuecal__flex vuecal__cell-content\", $options.splitsCount && $options.splitClasses(split)]),\n          key: $props.options.transitions ? `${$options.view.id}-${$props.data.content}-${i}` : i,\n          \"data-split\": $options.splitsCount ? split.id : false,\n          column: \"\",\n          tabindex: \"0\",\n          \"aria-label\": $props.data.content,\n          onFocus: _cache[0] || (_cache[0] = ($event) => $options.onCellFocus($event)),\n          onKeypress: _cache[1] || (_cache[1] = withKeys(($event) => $options.onCellkeyPressEnter($event), [\"enter\"])),\n          onTouchstart: ($event) => !$options.isDisabled && $options.onCellTouchStart($event, $options.splitsCount ? split.id : null),\n          onMousedown: ($event) => !$options.isDisabled && $options.onCellMouseDown($event, $options.splitsCount ? split.id : null),\n          onClick: _cache[2] || (_cache[2] = ($event) => !$options.isDisabled && $options.onCellClick($event)),\n          onDblclick: _cache[3] || (_cache[3] = ($event) => !$options.isDisabled && $options.onCellDblClick($event)),\n          onContextmenu: _cache[4] || (_cache[4] = ($event) => !$options.isDisabled && $props.options.cellContextmenu && $options.onCellContextMenu($event)),\n          onDragenter: _cache[5] || (_cache[5] = ($event) => !$options.isDisabled && $props.editEvents.drag && $options.dnd && $options.dnd.cellDragEnter($event, _ctx.$data, $props.data.startDate)),\n          onDragover: ($event) => !$options.isDisabled && $props.editEvents.drag && $options.dnd && $options.dnd.cellDragOver($event, _ctx.$data, $props.data.startDate, $options.splitsCount ? split.id : null),\n          onDragleave: _cache[6] || (_cache[6] = ($event) => !$options.isDisabled && $props.editEvents.drag && $options.dnd && $options.dnd.cellDragLeave($event, _ctx.$data, $props.data.startDate)),\n          onDrop: ($event) => !$options.isDisabled && $props.editEvents.drag && $options.dnd && $options.dnd.cellDragDrop($event, _ctx.$data, $props.data.startDate, $options.splitsCount ? split.id : null)\n        }, [\n          $props.options.showTimeInCells && $props.options.time && $options.isWeekOrDayView && !$props.allDay ? (openBlock(), createElementBlock(\"div\", _hoisted_2$1, [\n            (openBlock(true), createElementBlock(Fragment, null, renderList($options.vuecal.timeCells, (cell, i2) => {\n              return openBlock(), createElementBlock(\"span\", {\n                class: \"cell-time-label\",\n                key: i2\n              }, toDisplayString(cell.label), 1);\n            }), 128))\n          ])) : createCommentVNode(\"\", true),\n          $options.isWeekOrDayView && !$props.allDay && $options.specialHours.length ? (openBlock(true), createElementBlock(Fragment, { key: 1 }, renderList($options.specialHours, (block, i2) => {\n            return openBlock(), createElementBlock(\"div\", {\n              class: normalizeClass([\"vuecal__special-hours\", `vuecal__special-hours--day${block.day} ${block.class}`]),\n              style: normalizeStyle(`height: ${block.height}px;top: ${block.top}px`)\n            }, [\n              block.label ? (openBlock(), createElementBlock(\"div\", {\n                key: 0,\n                class: \"special-hours-label\",\n                innerHTML: block.label\n              }, null, 8, _hoisted_3$1)) : createCommentVNode(\"\", true)\n            ], 6);\n          }), 256)) : createCommentVNode(\"\", true),\n          renderSlot(_ctx.$slots, \"cell-content\", {\n            events: $options.events,\n            selectCell: ($event) => $options.selectCell($event, true),\n            split: $options.splitsCount ? split : false\n          }),\n          $options.eventsCount && ($options.isWeekOrDayView || $options.view.id === \"month\" && $props.options.eventsOnMonthView) ? (openBlock(), createElementBlock(\"div\", _hoisted_4$1, [\n            (openBlock(true), createElementBlock(Fragment, null, renderList($options.splitsCount ? split.events : $options.events, (event, j) => {\n              return openBlock(), createBlock(_component_event, {\n                key: j,\n                \"cell-formatted-date\": $props.data.formattedDate,\n                event,\n                \"all-day\": $props.allDay,\n                \"cell-events\": $options.splitsCount ? split.events : $options.events,\n                overlaps: (($options.splitsCount ? split.overlaps[event._eid] : _ctx.cellOverlaps[event._eid]) || []).overlaps,\n                \"event-position\": (($options.splitsCount ? split.overlaps[event._eid] : _ctx.cellOverlaps[event._eid]) || []).position,\n                \"overlaps-streak\": $options.splitsCount ? split.overlapsStreak : _ctx.cellOverlapsStreak\n              }, {\n                event: withCtx(({ event: event2, view }) => [\n                  renderSlot(_ctx.$slots, \"event\", {\n                    view,\n                    event: event2\n                  })\n                ]),\n                _: 2\n              }, 1032, [\"cell-formatted-date\", \"event\", \"all-day\", \"cell-events\", \"overlaps\", \"event-position\", \"overlaps-streak\"]);\n            }), 128))\n          ])) : createCommentVNode(\"\", true)\n        ], 42, _hoisted_1$2);\n      }), 128)),\n      $options.timelineVisible ? (openBlock(), createElementBlock(\"div\", {\n        class: \"vuecal__now-line\",\n        style: normalizeStyle(`top: ${$options.todaysTimePosition}px`),\n        key: $props.options.transitions ? `${$options.view.id}-now-line` : \"now-line\",\n        title: $options.utils.date.formatTime($options.vuecal.now)\n      }, null, 12, _hoisted_5$1)) : createCommentVNode(\"\", true)\n    ]),\n    _: 3\n  }, 8, [\"class\", \"name\", \"appear\", \"style\"]);\n}\nconst _sfc_main$2 = {\n  inject: [\"vuecal\", \"utils\", \"modules\", \"view\", \"domEvents\"],\n  components: { Event },\n  props: {\n    // Vue-cal main component options (props).\n    options: { type: Object, default: () => ({}) },\n    editEvents: { type: Object, required: true },\n    data: { type: Object, required: true },\n    cellSplits: { type: Array, default: () => [] },\n    minTimestamp: { type: [Number, null], default: null },\n    maxTimestamp: { type: [Number, null], default: null },\n    cellWidth: { type: [Number, Boolean], default: false },\n    allDay: { type: Boolean, default: false }\n  },\n  data: () => ({\n    cellOverlaps: {},\n    cellOverlapsStreak: 1,\n    // Largest amount of simultaneous events in cell.\n    // On mouse down, save the time at cursor so it can be reused on cell focus event\n    // where there is no cursor coords.\n    timeAtCursor: null,\n    highlighted: false,\n    // On event drag over.\n    highlightedSplit: null\n  }),\n  methods: {\n    getSplitAtCursor({ target }) {\n      const targetIsSplit = target.classList.contains(\"vuecal__cell-split\");\n      let split = targetIsSplit ? target : this.vuecal.findAncestor(target, \"vuecal__cell-split\");\n      if (split) {\n        split = split.attributes[\"data-split\"].value;\n        if (parseInt(split).toString() === split.toString()) split = parseInt(split);\n      }\n      return split || null;\n    },\n    splitClasses(split) {\n      return {\n        \"vuecal__cell-split\": true,\n        \"vuecal__cell-split--highlighted\": this.highlightedSplit === split.id,\n        [split.class]: !!split.class\n      };\n    },\n    checkCellOverlappingEvents() {\n      if (this.options.time && this.eventsCount && !this.splitsCount) {\n        if (this.eventsCount === 1) {\n          this.cellOverlaps = [];\n          this.cellOverlapsStreak = 1;\n        } else [this.cellOverlaps, this.cellOverlapsStreak] = this.utils.event.checkCellOverlappingEvents(this.events, this.options);\n      }\n    },\n    isDOMElementAnEvent(el) {\n      return this.vuecal.isDOMElementAnEvent(el);\n    },\n    /**\n     * Select a cell and go to narrower view on double click or single click according to vuecalProps option.\n     */\n    selectCell(DOMEvent, force = false) {\n      const split = this.splitsCount ? this.getSplitAtCursor(DOMEvent) : null;\n      this.utils.cell.selectCell(force, this.timeAtCursor, split);\n      this.timeAtCursor = null;\n    },\n    onCellkeyPressEnter(DOMEvent) {\n      if (!this.isSelected) this.onCellFocus(DOMEvent);\n      const split = this.splitsCount ? this.getSplitAtCursor(DOMEvent) : null;\n      this.utils.cell.keyPressEnterCell(this.timeAtCursor, split);\n      this.timeAtCursor = null;\n    },\n    /**\n     * On cell focus, from tab key or from mousedown, highlight the cell and emit\n     * the cell-focus event with the date of the cell start when focusing from tab or\n     * the date & time at cursor if click/touch.\n     */\n    onCellFocus(DOMEvent) {\n      if (!this.isSelected && !this.isDisabled) {\n        this.isSelected = this.data.startDate;\n        const split = this.splitsCount ? this.getSplitAtCursor(DOMEvent) : null;\n        const date = this.timeAtCursor || this.data.startDate;\n        this.vuecal.$emit(\"cell-focus\", split ? { date, split } : date);\n      }\n    },\n    onCellMouseDown(DOMEvent, split = null, touch = false) {\n      if (\"ontouchstart\" in window && !touch) return false;\n      if (!this.isSelected) this.onCellFocus(DOMEvent);\n      const { clickHoldACell, focusAnEvent } = this.domEvents;\n      this.domEvents.cancelClickEventCreation = false;\n      clickHoldACell.eventCreated = false;\n      this.timeAtCursor = new Date(this.data.startDate);\n      const { minutes, cursorCoords: { y } } = this.vuecal.minutesAtCursor(DOMEvent);\n      this.timeAtCursor.setMinutes(minutes);\n      const mouseDownOnEvent = this.isDOMElementAnEvent(DOMEvent.target);\n      if (!mouseDownOnEvent && focusAnEvent._eid) {\n        (this.view.events.find((e) => e._eid === focusAnEvent._eid) || {}).focused = false;\n      }\n      if (this.editEvents.create && !mouseDownOnEvent) this.setUpEventCreation(DOMEvent, y);\n    },\n    setUpEventCreation(DOMEvent, startCursorY) {\n      if (this.options.dragToCreateEvent && [\"week\", \"day\"].includes(this.view.id)) {\n        const { dragCreateAnEvent } = this.domEvents;\n        dragCreateAnEvent.startCursorY = startCursorY;\n        dragCreateAnEvent.split = this.splitsCount ? this.getSplitAtCursor(DOMEvent) : null;\n        dragCreateAnEvent.start = this.timeAtCursor;\n        if (this.options.snapToTime) {\n          let timeMinutes = this.timeAtCursor.getHours() * 60 + this.timeAtCursor.getMinutes();\n          const plusHalfSnapTime = timeMinutes + this.options.snapToTime / 2;\n          timeMinutes = plusHalfSnapTime - plusHalfSnapTime % this.options.snapToTime;\n          dragCreateAnEvent.start.setHours(0, timeMinutes, 0, 0);\n        }\n      } else if (this.options.cellClickHold && [\"month\", \"week\", \"day\"].includes(this.view.id)) {\n        this.setUpCellHoldTimer(DOMEvent);\n      }\n    },\n    // When click & holding a cell, and if allowed, set a timeout to create an event (can be cancelled).\n    setUpCellHoldTimer(DOMEvent) {\n      const { clickHoldACell } = this.domEvents;\n      clickHoldACell.cellId = `${this.vuecal._.uid}_${this.data.formattedDate}`;\n      clickHoldACell.split = this.splitsCount ? this.getSplitAtCursor(DOMEvent) : null;\n      clickHoldACell.timeoutId = setTimeout(() => {\n        if (clickHoldACell.cellId && !this.domEvents.cancelClickEventCreation) {\n          const { _eid } = this.utils.event.createAnEvent(\n            this.timeAtCursor,\n            null,\n            clickHoldACell.split ? { split: clickHoldACell.split } : {}\n          );\n          clickHoldACell.eventCreated = _eid;\n        }\n      }, clickHoldACell.timeout);\n    },\n    onCellTouchStart(DOMEvent, split = null) {\n      this.onCellMouseDown(DOMEvent, split, true);\n    },\n    onCellClick(DOMEvent) {\n      if (!this.isDOMElementAnEvent(DOMEvent.target)) this.selectCell(DOMEvent);\n    },\n    onCellDblClick(DOMEvent) {\n      const date = new Date(this.data.startDate);\n      date.setMinutes(this.vuecal.minutesAtCursor(DOMEvent).minutes);\n      const split = this.splitsCount ? this.getSplitAtCursor(DOMEvent) : null;\n      this.vuecal.$emit(\"cell-dblclick\", split ? { date, split } : date);\n      if (this.options.dblclickToNavigate) this.vuecal.switchToNarrowerView();\n    },\n    onCellContextMenu(DOMEvent) {\n      DOMEvent.stopPropagation();\n      DOMEvent.preventDefault();\n      const date = new Date(this.data.startDate);\n      const { cursorCoords, minutes } = this.vuecal.minutesAtCursor(DOMEvent);\n      date.setMinutes(minutes);\n      const split = this.splitsCount ? this.getSplitAtCursor(DOMEvent) : null;\n      this.vuecal.$emit(\"cell-contextmenu\", { date, ...cursorCoords, ...split || {}, e: DOMEvent });\n    }\n  },\n  computed: {\n    // Drag & drop module.\n    dnd() {\n      return this.modules.dnd;\n    },\n    nowInMinutes() {\n      return this.utils.date.dateToMinutes(this.vuecal.now);\n    },\n    isBeforeMinDate() {\n      return this.minTimestamp !== null && this.minTimestamp > this.data.endDate.getTime();\n    },\n    isAfterMaxDate() {\n      return this.maxTimestamp && this.maxTimestamp < this.data.startDate.getTime();\n    },\n    // Is the current cell disabled or not.\n    isDisabled() {\n      const { disableDays } = this.options;\n      const { isYearsOrYearView } = this.vuecal;\n      if (disableDays.length && disableDays.includes(this.data.formattedDate) && !isYearsOrYearView) return true;\n      return this.isBeforeMinDate || this.isAfterMaxDate;\n    },\n    // Is the current cell selected or not.\n    isSelected: {\n      get() {\n        let selected = false;\n        const { selectedDate } = this.view;\n        if (this.view.id === \"years\") {\n          selected = selectedDate.getFullYear() === this.data.startDate.getFullYear();\n        } else if (this.view.id === \"year\") {\n          selected = selectedDate.getFullYear() === this.data.startDate.getFullYear() && selectedDate.getMonth() === this.data.startDate.getMonth();\n        } else selected = selectedDate.getTime() === this.data.startDate.getTime();\n        return selected;\n      },\n      set(date) {\n        this.view.selectedDate = date;\n        this.vuecal.$emit(\"update:selected-date\", this.view.selectedDate);\n      }\n    },\n    // Cache result for performance.\n    isWeekOrDayView() {\n      return [\"week\", \"day\"].includes(this.view.id);\n    },\n    transitionDirection() {\n      return this.vuecal.transitionDirection;\n    },\n    specialHours() {\n      return this.data.specialHours.map((block) => {\n        let { from, to } = block;\n        from = Math.max(from, this.options.timeFrom);\n        to = Math.min(to, this.options.timeTo);\n        return {\n          ...block,\n          height: (to - from) * this.timeScale,\n          top: (from - this.options.timeFrom) * this.timeScale\n        };\n      });\n    },\n    events() {\n      const { startDate: cellStart, endDate: cellEnd } = this.data;\n      let events = [];\n      if (!([\"years\", \"year\"].includes(this.view.id) && !this.options.eventsCountOnYearView)) {\n        events = this.view.events.slice(0);\n        if (this.view.id === \"month\") {\n          events.push(...this.view.outOfScopeEvents);\n        }\n        events = events.filter((e) => this.utils.event.eventInRange(e, cellStart, cellEnd));\n        if (this.options.showAllDayEvents && this.view.id !== \"month\") events = events.filter((e) => !!e.allDay === this.allDay);\n        if (this.options.time && this.isWeekOrDayView && !this.allDay) {\n          const { timeFrom, timeTo } = this.options;\n          events = events.filter((e) => {\n            const segment = e.daysCount > 1 && e.segments[this.data.formattedDate] || {};\n            const singleDayInRange = e.daysCount === 1 && e.startTimeMinutes < timeTo && e.endTimeMinutes > timeFrom;\n            const multipleDayInRange = e.daysCount > 1 && (segment.startTimeMinutes < timeTo && segment.endTimeMinutes > timeFrom);\n            const recurrMultDayInRange = false;\n            return e.allDay || singleDayInRange || multipleDayInRange || recurrMultDayInRange;\n          });\n        }\n        if (this.options.time && this.isWeekOrDayView && !(this.options.showAllDayEvents && this.allDay)) {\n          events.sort((a, b) => a.start < b.start ? -1 : 1);\n        }\n        if (!this.cellSplits.length) this.$nextTick(this.checkCellOverlappingEvents);\n      }\n      return events;\n    },\n    eventsCount() {\n      return this.events.length;\n    },\n    splits() {\n      return this.cellSplits.map((item, i) => {\n        const events = this.events.filter((e) => e.split === item.id);\n        const [overlaps, streak] = this.utils.event.checkCellOverlappingEvents(events.filter((e) => !e.background && !e.allDay), this.options);\n        return {\n          ...item,\n          overlaps,\n          overlapsStreak: streak,\n          events\n        };\n      });\n    },\n    splitsCount() {\n      return this.splits.length;\n    },\n    cellClasses() {\n      return {\n        [this.data.class]: !!this.data.class,\n        \"vuecal__cell--current\": this.data.current,\n        // E.g. Current year in years view.\n        \"vuecal__cell--today\": this.data.today,\n        \"vuecal__cell--out-of-scope\": this.data.outOfScope,\n        \"vuecal__cell--before-min\": this.isDisabled && this.isBeforeMinDate,\n        \"vuecal__cell--after-max\": this.isDisabled && this.isAfterMaxDate,\n        \"vuecal__cell--disabled\": this.isDisabled,\n        \"vuecal__cell--selected\": this.isSelected,\n        \"vuecal__cell--highlighted\": this.highlighted,\n        \"vuecal__cell--has-splits\": this.splitsCount,\n        \"vuecal__cell--has-events\": this.eventsCount\n      };\n    },\n    cellStyles() {\n      return {\n        // cellWidth is only applied when hiding weekdays on month and week views.\n        ...this.cellWidth ? { width: `${this.cellWidth}%` } : {}\n      };\n    },\n    timelineVisible() {\n      const { time, timeTo } = this.options;\n      return this.data.today && this.isWeekOrDayView && time && !this.allDay && this.nowInMinutes <= timeTo;\n    },\n    todaysTimePosition() {\n      if (!this.data.today || !this.options.time) return;\n      const minutesFromTop = this.nowInMinutes - this.options.timeFrom;\n      return Math.round(minutesFromTop * this.timeScale);\n    },\n    timeScale() {\n      return this.options.timeCellHeight / this.options.timeStep;\n    }\n  }\n};\nconst Cell = /* @__PURE__ */ _export_sfc(_sfc_main$2, [[\"render\", render$2]]);\nconst _hoisted_1$1 = {\n  key: 0,\n  class: \"vuecal__all-day-text\",\n  style: { \"width\": \"3em\" }\n};\nfunction render$1(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_vuecal_cell = resolveComponent(\"vuecal-cell\");\n  return openBlock(), createElementBlock(\"div\", {\n    class: \"vuecal__flex vuecal__all-day\",\n    style: normalizeStyle($props.cellOrSplitMinWidth && { height: $props.height })\n  }, [\n    !$props.cellOrSplitMinWidth ? (openBlock(), createElementBlock(\"div\", _hoisted_1$1, [\n      createElementVNode(\"span\", null, toDisplayString($props.label), 1)\n    ])) : createCommentVNode(\"\", true),\n    createElementVNode(\"div\", {\n      class: normalizeClass([\"vuecal__flex vuecal__cells\", `${$options.view.id}-view`]),\n      grow: \"\",\n      style: normalizeStyle($props.cellOrSplitMinWidth ? `min-width: ${$props.cellOrSplitMinWidth}px` : \"\")\n    }, [\n      (openBlock(true), createElementBlock(Fragment, null, renderList($props.cells, (cell, i) => {\n        return openBlock(), createBlock(_component_vuecal_cell, {\n          key: i,\n          options: $props.options,\n          \"edit-events\": $options.editEvents,\n          data: cell,\n          \"all-day\": true,\n          \"cell-width\": $props.options.hideWeekdays.length && ($options.vuecal.isWeekView || $options.vuecal.isMonthView) && $options.vuecal.cellWidth,\n          \"min-timestamp\": $props.options.minTimestamp,\n          \"max-timestamp\": $props.options.maxTimestamp,\n          \"cell-splits\": $props.daySplits\n        }, {\n          event: withCtx(({ event, view }) => [\n            renderSlot(_ctx.$slots, \"event\", {\n              view,\n              event\n            })\n          ]),\n          _: 2\n        }, 1032, [\"options\", \"edit-events\", \"data\", \"cell-width\", \"min-timestamp\", \"max-timestamp\", \"cell-splits\"]);\n      }), 128))\n    ], 6)\n  ], 4);\n}\nconst _sfc_main$1 = {\n  inject: [\"vuecal\", \"view\", \"editEvents\"],\n  components: { \"vuecal-cell\": Cell },\n  props: {\n    // Vue-cal main component options (props).\n    options: { type: Object, required: true },\n    cells: { type: Array, required: true },\n    label: { type: String, required: true },\n    daySplits: { type: Array, default: () => [] },\n    shortEvents: { type: Boolean, default: true },\n    height: { type: String, default: \"\" },\n    cellOrSplitMinWidth: { type: Number, default: null }\n  },\n  computed: {\n    hasCellOrSplitWidth() {\n      return !!(this.options.minCellWidth || this.daySplits.length && this.options.minSplitWidth);\n    }\n  }\n};\nconst AllDayBar = /* @__PURE__ */ _export_sfc(_sfc_main$1, [[\"render\", render$1]]);\nconst _hoisted_1 = [\"lang\"];\nconst _hoisted_2 = { class: \"default\" };\nconst _hoisted_3 = {\n  key: 0,\n  class: \"vuecal__flex vuecal__body\",\n  grow: \"\"\n};\nconst _hoisted_4 = [\"onBlur\", \"innerHTML\"];\nconst _hoisted_5 = [\"innerHTML\"];\nconst _hoisted_6 = [\"innerHTML\"];\nconst _hoisted_7 = {\n  class: \"vuecal__flex\",\n  row: \"\",\n  grow: \"\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"vuecal__time-column\"\n};\nconst _hoisted_9 = { class: \"vuecal__time-cell-label\" };\nconst _hoisted_10 = {\n  key: 1,\n  class: \"vuecal__flex vuecal__week-numbers\",\n  column: \"\"\n};\nconst _hoisted_11 = [\"wrap\", \"column\"];\nconst _hoisted_12 = [\"onBlur\", \"innerHTML\"];\nconst _hoisted_13 = [\"innerHTML\"];\nconst _hoisted_14 = [\"innerHTML\"];\nconst _hoisted_15 = [\"wrap\"];\nconst _hoisted_16 = [\"innerHTML\"];\nconst _hoisted_17 = [\"innerHTML\"];\nconst _hoisted_18 = {\n  key: 2,\n  class: \"vuecal__cell-events-count\"\n};\nconst _hoisted_19 = {\n  key: 3,\n  class: \"vuecal__no-event\"\n};\nconst _hoisted_20 = [\"onBlur\", \"innerHTML\"];\nconst _hoisted_21 = [\"innerHTML\"];\nconst _hoisted_22 = {\n  key: 2,\n  class: \"vuecal__event-time\"\n};\nconst _hoisted_23 = { key: 0 };\nconst _hoisted_24 = {\n  key: 1,\n  class: \"days-to-end\"\n};\nconst _hoisted_25 = [\"innerHTML\"];\nconst _hoisted_26 = {\n  key: 0,\n  class: \"vuecal__scrollbar-check\"\n};\nfunction render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_vuecal_header = resolveComponent(\"vuecal-header\");\n  const _component_all_day_bar = resolveComponent(\"all-day-bar\");\n  const _component_weekdays_headings = resolveComponent(\"weekdays-headings\");\n  const _component_vuecal_cell = resolveComponent(\"vuecal-cell\");\n  return openBlock(), createElementBlock(\"div\", {\n    class: normalizeClass([\"vuecal__flex vuecal\", $options.cssClasses]),\n    column: \"\",\n    ref: \"vuecal\",\n    lang: $props.locale\n  }, [\n    createVNode(_component_vuecal_header, {\n      options: _ctx.$props,\n      \"edit-events\": $options.editEvents,\n      \"view-props\": { views: $options.views, weekDaysInHeader: $options.weekDaysInHeader },\n      \"week-days\": $options.weekDays,\n      \"has-splits\": $options.hasSplits,\n      \"day-splits\": $options.daySplits,\n      \"switch-to-narrower-view\": $options.switchToNarrowerView\n    }, createSlots({\n      \"arrow-prev\": withCtx(() => [\n        renderSlot(_ctx.$slots, \"arrow-prev\", {}, () => [\n          _cache[0] || (_cache[0] = createTextVNode(\" \")),\n          _cache[1] || (_cache[1] = createElementVNode(\"i\", { class: \"angle\" }, null, -1)),\n          _cache[2] || (_cache[2] = createTextVNode(\" \"))\n        ])\n      ]),\n      \"arrow-next\": withCtx(() => [\n        renderSlot(_ctx.$slots, \"arrow-next\", {}, () => [\n          _cache[3] || (_cache[3] = createTextVNode(\" \")),\n          _cache[4] || (_cache[4] = createElementVNode(\"i\", { class: \"angle\" }, null, -1)),\n          _cache[5] || (_cache[5] = createTextVNode(\" \"))\n        ])\n      ]),\n      \"today-button\": withCtx(() => [\n        renderSlot(_ctx.$slots, \"today-button\", {}, () => [\n          createElementVNode(\"span\", _hoisted_2, toDisplayString($data.texts.today), 1)\n        ])\n      ]),\n      title: withCtx(() => [\n        renderSlot(_ctx.$slots, \"title\", {\n          title: $options.viewTitle,\n          view: $data.view\n        }, () => [\n          createTextVNode(toDisplayString($options.viewTitle), 1)\n        ])\n      ]),\n      _: 2\n    }, [\n      _ctx.$slots[\"weekday-heading\"] ? {\n        name: \"weekday-heading\",\n        fn: withCtx(({ heading, view }) => [\n          renderSlot(_ctx.$slots, \"weekday-heading\", {\n            heading,\n            view\n          })\n        ]),\n        key: \"0\"\n      } : void 0,\n      _ctx.$slots[\"split-label\"] ? {\n        name: \"split-label\",\n        fn: withCtx(({ split }) => [\n          renderSlot(_ctx.$slots, \"split-label\", {\n            split,\n            view: $data.view.id\n          })\n        ]),\n        key: \"1\"\n      } : void 0\n    ]), 1032, [\"options\", \"edit-events\", \"view-props\", \"week-days\", \"has-splits\", \"day-splits\", \"switch-to-narrower-view\"]),\n    !$props.hideBody ? (openBlock(), createElementBlock(\"div\", _hoisted_3, [\n      createVNode(Transition, {\n        name: `slide-fade--${$data.transitionDirection}`,\n        appear: $props.transitions\n      }, {\n        default: withCtx(() => [\n          (openBlock(), createElementBlock(\"div\", {\n            class: \"vuecal__flex\",\n            style: { \"min-width\": \"100%\" },\n            key: $props.transitions ? $data.view.id : false,\n            column: \"\"\n          }, [\n            $props.showAllDayEvents && $options.hasTimeColumn && (!$options.cellOrSplitMinWidth || $options.isDayView && !$props.minSplitWidth) ? (openBlock(), createBlock(_component_all_day_bar, normalizeProps(mergeProps({ key: 0 }, $options.allDayBar)), {\n              event: withCtx(({ event, view }) => [\n                renderSlot(_ctx.$slots, \"event\", {\n                  view,\n                  event\n                }, () => [\n                  $options.editEvents.title && event.titleEditable ? (openBlock(), createElementBlock(\"div\", {\n                    key: 0,\n                    class: \"vuecal__event-title vuecal__event-title--edit\",\n                    contenteditable: \"\",\n                    onBlur: ($event) => $options.onEventTitleBlur($event, event),\n                    innerHTML: event.title\n                  }, null, 40, _hoisted_4)) : event.title ? (openBlock(), createElementBlock(\"div\", {\n                    key: 1,\n                    class: \"vuecal__event-title\",\n                    innerHTML: event.title\n                  }, null, 8, _hoisted_5)) : createCommentVNode(\"\", true),\n                  event.content && !$options.hasShortEvents && !$options.isShortMonthView ? (openBlock(), createElementBlock(\"div\", {\n                    key: 2,\n                    class: \"vuecal__event-content\",\n                    innerHTML: event.content\n                  }, null, 8, _hoisted_6)) : createCommentVNode(\"\", true)\n                ])\n              ]),\n              _: 3\n            }, 16)) : createCommentVNode(\"\", true),\n            createElementVNode(\"div\", {\n              class: normalizeClass([\"vuecal__bg\", { vuecal__flex: !$options.hasTimeColumn }]),\n              column: \"\"\n            }, [\n              createElementVNode(\"div\", _hoisted_7, [\n                $options.hasTimeColumn ? (openBlock(), createElementBlock(\"div\", _hoisted_8, [\n                  $props.showAllDayEvents && $options.cellOrSplitMinWidth && !($options.isDayView && !$props.minSplitWidth) ? (openBlock(), createElementBlock(\"div\", {\n                    key: 0,\n                    class: \"vuecal__all-day-text\",\n                    style: normalizeStyle({ height: $options.allDayBar.height })\n                  }, [\n                    createElementVNode(\"span\", null, toDisplayString($data.texts.allDay), 1)\n                  ], 4)) : createCommentVNode(\"\", true),\n                  (openBlock(true), createElementBlock(Fragment, null, renderList($options.timeCells, (cell, i) => {\n                    return openBlock(), createElementBlock(\"div\", {\n                      class: \"vuecal__time-cell\",\n                      key: i,\n                      style: normalizeStyle(`height: ${$props.timeCellHeight}px`)\n                    }, [\n                      renderSlot(_ctx.$slots, \"time-cell\", {\n                        hours: cell.hours,\n                        minutes: cell.minutes\n                      }, () => [\n                        _cache[6] || (_cache[6] = createElementVNode(\"span\", { class: \"vuecal__time-cell-line\" }, null, -1)),\n                        createElementVNode(\"span\", _hoisted_9, toDisplayString(cell.label), 1)\n                      ])\n                    ], 4);\n                  }), 128))\n                ])) : createCommentVNode(\"\", true),\n                $props.showWeekNumbers && $options.isMonthView ? (openBlock(), createElementBlock(\"div\", _hoisted_10, [\n                  (openBlock(), createElementBlock(Fragment, null, renderList(6, (i) => {\n                    return createElementVNode(\"div\", {\n                      class: \"vuecal__flex vuecal__week-number-cell\",\n                      key: i,\n                      grow: \"\"\n                    }, [\n                      renderSlot(_ctx.$slots, \"week-number-cell\", {\n                        week: $options.getWeekNumber(i - 1)\n                      }, () => [\n                        createTextVNode(toDisplayString($options.getWeekNumber(i - 1)), 1)\n                      ])\n                    ]);\n                  }), 64))\n                ])) : createCommentVNode(\"\", true),\n                createElementVNode(\"div\", {\n                  class: normalizeClass([\"vuecal__flex vuecal__cells\", `${$data.view.id}-view`]),\n                  grow: \"\",\n                  wrap: !$options.cellOrSplitMinWidth || !$options.isWeekView,\n                  column: !!$options.cellOrSplitMinWidth\n                }, [\n                  $options.cellOrSplitMinWidth && $options.isWeekView ? (openBlock(), createBlock(_component_weekdays_headings, {\n                    key: 0,\n                    \"transition-direction\": $data.transitionDirection,\n                    \"week-days\": $options.weekDays,\n                    \"switch-to-narrower-view\": $options.switchToNarrowerView,\n                    style: normalizeStyle($options.cellOrSplitMinWidth ? `min-width: ${$options.cellOrSplitMinWidth}px` : \"\")\n                  }, createSlots({ _: 2 }, [\n                    _ctx.$slots[\"weekday-heading\"] ? {\n                      name: \"weekday-heading\",\n                      fn: withCtx(({ heading, view }) => [\n                        renderSlot(_ctx.$slots, \"weekday-heading\", {\n                          heading,\n                          view\n                        })\n                      ]),\n                      key: \"0\"\n                    } : void 0,\n                    _ctx.$slots[\"split-label\"] ? {\n                      name: \"split-label\",\n                      fn: withCtx(({ split }) => [\n                        renderSlot(_ctx.$slots, \"split-label\", {\n                          split,\n                          view: $data.view.id\n                        })\n                      ]),\n                      key: \"1\"\n                    } : void 0\n                  ]), 1032, [\"transition-direction\", \"week-days\", \"switch-to-narrower-view\", \"style\"])) : $options.hasSplits && $props.stickySplitLabels && $props.minSplitWidth ? (openBlock(), createElementBlock(\"div\", {\n                    key: 1,\n                    class: \"vuecal__flex vuecal__split-days-headers\",\n                    style: normalizeStyle($options.cellOrSplitMinWidth ? `min-width: ${$options.cellOrSplitMinWidth}px` : \"\")\n                  }, [\n                    (openBlock(true), createElementBlock(Fragment, null, renderList($options.daySplits, (split, i) => {\n                      return openBlock(), createElementBlock(\"div\", {\n                        class: normalizeClass([\"day-split-header\", split.class || false]),\n                        key: i\n                      }, [\n                        renderSlot(_ctx.$slots, \"split-label\", {\n                          split,\n                          view: $data.view.id\n                        }, () => [\n                          createTextVNode(toDisplayString(split.label), 1)\n                        ])\n                      ], 2);\n                    }), 128))\n                  ], 4)) : createCommentVNode(\"\", true),\n                  $props.showAllDayEvents && $options.hasTimeColumn && ($options.isWeekView && $options.cellOrSplitMinWidth || $options.isDayView && $options.hasSplits && $props.minSplitWidth) ? (openBlock(), createBlock(_component_all_day_bar, normalizeProps(mergeProps({ key: 2 }, $options.allDayBar)), {\n                    event: withCtx(({ event, view }) => [\n                      renderSlot(_ctx.$slots, \"event\", {\n                        view,\n                        event\n                      }, () => [\n                        $options.editEvents.title && event.titleEditable ? (openBlock(), createElementBlock(\"div\", {\n                          key: 0,\n                          class: \"vuecal__event-title vuecal__event-title--edit\",\n                          contenteditable: \"\",\n                          onBlur: ($event) => $options.onEventTitleBlur($event, event),\n                          innerHTML: event.title\n                        }, null, 40, _hoisted_12)) : event.title ? (openBlock(), createElementBlock(\"div\", {\n                          key: 1,\n                          class: \"vuecal__event-title\",\n                          innerHTML: event.title\n                        }, null, 8, _hoisted_13)) : createCommentVNode(\"\", true),\n                        event.content && !$options.hasShortEvents && !$options.isShortMonthView ? (openBlock(), createElementBlock(\"div\", {\n                          key: 2,\n                          class: \"vuecal__event-content\",\n                          innerHTML: event.content\n                        }, null, 8, _hoisted_14)) : createCommentVNode(\"\", true)\n                      ])\n                    ]),\n                    _: 3\n                  }, 16)) : createCommentVNode(\"\", true),\n                  createElementVNode(\"div\", {\n                    class: \"vuecal__flex\",\n                    ref: (el) => $data.cellsEl = el,\n                    grow: \"\",\n                    wrap: !$options.cellOrSplitMinWidth || !$options.isWeekView,\n                    style: normalizeStyle($options.cellOrSplitMinWidth ? `min-width: ${$options.cellOrSplitMinWidth}px` : \"\")\n                  }, [\n                    (openBlock(true), createElementBlock(Fragment, null, renderList($options.viewCells, (cell, i) => {\n                      return openBlock(), createBlock(_component_vuecal_cell, {\n                        key: i,\n                        options: _ctx.$props,\n                        \"edit-events\": $options.editEvents,\n                        data: cell,\n                        \"cell-width\": $props.hideWeekdays.length && ($options.isWeekView || $options.isMonthView) && $options.cellWidth,\n                        \"min-timestamp\": $options.minTimestamp,\n                        \"max-timestamp\": $options.maxTimestamp,\n                        \"cell-splits\": $options.hasSplits && $options.daySplits || []\n                      }, {\n                        \"cell-content\": withCtx(({ events, split, selectCell }) => [\n                          renderSlot(_ctx.$slots, \"cell-content\", {\n                            cell,\n                            view: $data.view,\n                            goNarrower: selectCell,\n                            events\n                          }, () => [\n                            split && !$props.stickySplitLabels ? (openBlock(), createElementBlock(\"div\", {\n                              key: 0,\n                              class: \"split-label\",\n                              innerHTML: split.label\n                            }, null, 8, _hoisted_16)) : createCommentVNode(\"\", true),\n                            cell.content ? (openBlock(), createElementBlock(\"div\", {\n                              key: 1,\n                              class: \"vuecal__cell-date\",\n                              innerHTML: cell.content\n                            }, null, 8, _hoisted_17)) : createCommentVNode(\"\", true),\n                            ($options.isMonthView && !$props.eventsOnMonthView || $options.isYearsOrYearView && $props.eventsCountOnYearView) && events.length ? (openBlock(), createElementBlock(\"div\", _hoisted_18, [\n                              renderSlot(_ctx.$slots, \"events-count\", {\n                                view: $data.view,\n                                events\n                              }, () => [\n                                createTextVNode(toDisplayString(events.length), 1)\n                              ])\n                            ])) : createCommentVNode(\"\", true),\n                            !$options.cellOrSplitHasEvents(events, split) && $options.isWeekOrDayView ? (openBlock(), createElementBlock(\"div\", _hoisted_19, [\n                              renderSlot(_ctx.$slots, \"no-event\", {}, () => [\n                                createTextVNode(toDisplayString($data.texts.noEvent), 1)\n                              ])\n                            ])) : createCommentVNode(\"\", true)\n                          ])\n                        ]),\n                        event: withCtx(({ event, view }) => [\n                          renderSlot(_ctx.$slots, \"event\", {\n                            view,\n                            event\n                          }, () => [\n                            $options.editEvents.title && event.titleEditable ? (openBlock(), createElementBlock(\"div\", {\n                              key: 0,\n                              class: \"vuecal__event-title vuecal__event-title--edit\",\n                              contenteditable: \"\",\n                              onBlur: ($event) => $options.onEventTitleBlur($event, event),\n                              innerHTML: event.title\n                            }, null, 40, _hoisted_20)) : event.title ? (openBlock(), createElementBlock(\"div\", {\n                              key: 1,\n                              class: \"vuecal__event-title\",\n                              innerHTML: event.title\n                            }, null, 8, _hoisted_21)) : createCommentVNode(\"\", true),\n                            $props.time && !event.allDay && !($options.isMonthView && (event.allDay || $props.showAllDayEvents === \"short\")) && !$options.isShortMonthView ? (openBlock(), createElementBlock(\"div\", _hoisted_22, [\n                              createTextVNode(toDisplayString($data.utils.date.formatTime(event.start, $options.TimeFormat)), 1),\n                              event.endTimeMinutes ? (openBlock(), createElementBlock(\"span\", _hoisted_23, \" - \" + toDisplayString($data.utils.date.formatTime(event.end, $options.TimeFormat, null, true)), 1)) : createCommentVNode(\"\", true),\n                              event.daysCount > 1 && (event.segments[cell.formattedDate] || {}).isFirstDay ? (openBlock(), createElementBlock(\"small\", _hoisted_24, \" +\" + toDisplayString(event.daysCount - 1) + toDisplayString(($data.texts.day[0] || \"\").toLowerCase()), 1)) : createCommentVNode(\"\", true)\n                            ])) : createCommentVNode(\"\", true),\n                            event.content && !($options.isMonthView && event.allDay && $props.showAllDayEvents === \"short\") && !$options.isShortMonthView ? (openBlock(), createElementBlock(\"div\", {\n                              key: 3,\n                              class: \"vuecal__event-content\",\n                              innerHTML: event.content\n                            }, null, 8, _hoisted_25)) : createCommentVNode(\"\", true)\n                          ])\n                        ]),\n                        \"no-event\": withCtx(() => [\n                          renderSlot(_ctx.$slots, \"no-event\", {}, () => [\n                            createTextVNode(toDisplayString($data.texts.noEvent), 1)\n                          ])\n                        ]),\n                        _: 2\n                      }, 1032, [\"options\", \"edit-events\", \"data\", \"cell-width\", \"min-timestamp\", \"max-timestamp\", \"cell-splits\"]);\n                    }), 128))\n                  ], 12, _hoisted_15)\n                ], 10, _hoisted_11)\n              ])\n            ], 2)\n          ]))\n        ]),\n        _: 3\n      }, 8, [\"name\", \"appear\"]),\n      !$data.ready ? (openBlock(), createElementBlock(\"div\", _hoisted_26, _cache[7] || (_cache[7] = [\n        createElementVNode(\"div\", null, null, -1)\n      ]))) : createCommentVNode(\"\", true)\n    ])) : createCommentVNode(\"\", true)\n  ], 10, _hoisted_1);\n}\nconst minutesInADay = 24 * 60;\nconst textsDefaults = {\n  weekDays: Array(7).fill(\"\"),\n  weekDaysShort: [],\n  months: Array(12).fill(\"\"),\n  years: \"\",\n  year: \"\",\n  month: \"\",\n  week: \"\",\n  day: \"\",\n  today: \"\",\n  noEvent: \"\",\n  allDay: \"\",\n  deleteEvent: \"\",\n  createEvent: \"\",\n  dateFormat: \"dddd MMMM D, YYYY\",\n  am: \"am\",\n  pm: \"pm\"\n};\nconst validViews = [\"years\", \"year\", \"month\", \"week\", \"day\"];\nconst dateUtils = new DateUtils(textsDefaults);\nconst _sfc_main = {\n  name: \"vue-cal\",\n  components: { \"vuecal-cell\": Cell, \"vuecal-header\": Header, WeekdaysHeadings, AllDayBar },\n  // By Vue design, passing props loses the reactivity unless it's a method or reactive OBJECT.\n  provide() {\n    return {\n      vuecal: this,\n      utils: this.utils,\n      modules: this.modules,\n      // Methods.\n      previous: this.previous,\n      next: this.next,\n      switchView: this.switchView,\n      updateSelectedDate: this.updateSelectedDate,\n      editEvents: this.editEvents,\n      // Objects.\n      view: this.view,\n      domEvents: this.domEvents\n    };\n  },\n  props: {\n    activeView: { type: String, default: \"week\" },\n    // Only used if there are daySplits with minSplitWidth, to add the same height top spacer on time column.\n    allDayBarHeight: { type: [String, Number], default: \"25px\" },\n    cellClickHold: { type: Boolean, default: true },\n    cellContextmenu: { type: Boolean, default: false },\n    clickToNavigate: { type: Boolean, default: false },\n    dblclickToNavigate: { type: Boolean, default: true },\n    disableDatePrototypes: { type: Boolean, default: false },\n    disableDays: { type: Array, default: () => [] },\n    disableViews: { type: Array, default: () => [] },\n    dragToCreateEvent: { type: Boolean, default: true },\n    // Start a drag creation after dragging a certain amount of pixels.\n    // This prevents drag creation by mistake when you want to navigate.\n    dragToCreateThreshold: { type: Number, default: 15 },\n    editableEvents: { type: [Boolean, Object], default: false },\n    events: { type: Array, default: () => [] },\n    eventsCountOnYearView: { type: Boolean, default: false },\n    eventsOnMonthView: { type: [Boolean, String], default: false },\n    hideBody: { type: Boolean, default: false },\n    hideTitleBar: { type: Boolean, default: false },\n    hideViewSelector: { type: Boolean, default: false },\n    hideWeekdays: { type: Array, default: () => [] },\n    hideWeekends: { type: Boolean, default: false },\n    locale: { type: [String, Object], default: \"en\" },\n    maxDate: { type: [String, Date], default: \"\" },\n    minCellWidth: { type: Number, default: 0 },\n    minDate: { type: [String, Date], default: \"\" },\n    minEventWidth: { type: Number, default: 0 },\n    minSplitWidth: { type: Number, default: 0 },\n    onEventClick: { type: [Function, null], default: null },\n    onEventCreate: { type: [Function, null], default: null },\n    onEventDblclick: { type: [Function, null], default: null },\n    overlapsPerTimeStep: { type: Boolean, default: false },\n    resizeX: { type: Boolean, default: false },\n    selectedDate: { type: [String, Date], default: \"\" },\n    showAllDayEvents: { type: [Boolean, String], default: false },\n    showTimeInCells: { type: Boolean, default: false },\n    showWeekNumbers: { type: [Boolean, String], default: false },\n    snapToTime: { type: Number, default: 0 },\n    small: { type: Boolean, default: false },\n    specialHours: { type: Object, default: () => ({}) },\n    splitDays: { type: Array, default: () => [] },\n    startWeekOnSunday: { type: Boolean, default: false },\n    stickySplitLabels: { type: Boolean, default: false },\n    time: { type: Boolean, default: true },\n    timeCellHeight: { type: Number, default: 40 },\n    // In pixels.\n    timeFormat: { type: String, default: \"\" },\n    timeFrom: { type: Number, default: 0 },\n    // In minutes.\n    timeStep: { type: Number, default: 60 },\n    // In minutes.\n    timeTo: { type: Number, default: minutesInADay },\n    // In minutes.\n    todayButton: { type: Boolean, default: false },\n    transitions: { type: Boolean, default: true },\n    twelveHour: { type: Boolean, default: false },\n    watchRealTime: { type: Boolean, default: false },\n    // Expensive, so only trigger on demand.\n    xsmall: { type: Boolean, default: false }\n  },\n  data() {\n    return {\n      ready: false,\n      // Is vue-cal ready.\n      // Make texts reactive before a locale is loaded.\n      texts: { ...textsDefaults },\n      utils: {\n        // Remove prototypes ASAP if the user wants so.\n        date: (this.disableDatePrototypes ? dateUtils.removePrototypes() : false) || dateUtils,\n        cell: null,\n        // Note: Destructuring class method loses the `this` context and Vue Cal becomes inaccessible\n        // from the event utils function. Don't do:\n        // const { eventInRange, createEventSegments } = this.utils.event\n        event: null\n      },\n      modules: { dnd: null },\n      cellsEl: null,\n      // At any time this object will be filled with current view, visible events and selected date.\n      view: {\n        id: \"\",\n        title: \"\",\n        startDate: null,\n        endDate: null,\n        firstCellDate: null,\n        lastCellDate: null,\n        selectedDate: null,\n        // All the events are stored in the mutableEvents array, but subset of visible ones are passed\n        // Into the current view for fast lookup and manipulation.\n        events: []\n      },\n      eventIdIncrement: 1,\n      // Internal unique id.\n      // Preset at now date on load, but updated every minute if watchRealTime,\n      // or updated at least on each cells rerender, in order to keep Today's date accurate.\n      now: /* @__PURE__ */ new Date(),\n      // Useful when watchRealTime = true, 2 timeouts: 1 to snap to round minutes, then 1 every minute.\n      timeTickerIds: [null, null],\n      // All the possible events/cells interractions:\n      // e.g. focus, click, click & hold, resize, drag & drop (coming).\n      domEvents: {\n        resizeAnEvent: {\n          _eid: null,\n          // Only one at a time.\n          start: null,\n          split: null,\n          segment: null,\n          originalEndTimeMinutes: 0,\n          originalEnd: null,\n          end: null,\n          startCell: null,\n          endCell: null\n        },\n        dragAnEvent: {\n          // Only one at a time, only needed for vuecal dragging-event class.\n          _eid: null\n        },\n        dragCreateAnEvent: {\n          startCursorY: null,\n          start: null,\n          // The cell date where we start the drag.\n          split: null,\n          event: null\n        },\n        focusAnEvent: {\n          _eid: null,\n          // Only one at a time.\n          // Useful to detect a full click (mousedown + mouseup on same event).\n          // E.g. Only call onEventClick function (if any) on full click.\n          mousedUp: false\n        },\n        clickHoldAnEvent: {\n          _eid: null,\n          // Only one at a time.\n          timeout: 1200,\n          // Hold for 1.2s to delete an event.\n          timeoutId: null\n        },\n        dblTapACell: {\n          taps: 0,\n          timeout: 500\n          // Allowed latency between first and second click.\n        },\n        clickHoldACell: {\n          cellId: null,\n          split: null,\n          timeout: 1200,\n          // Hold for 1.2s to create an event.\n          timeoutId: null,\n          eventCreated: false\n        },\n        // A single click can trigger event creation if the user decides so.\n        // But prevent this to happen on click & hold, on event click and on resize event.\n        cancelClickEventCreation: false\n      },\n      // The events source of truth.\n      // An array of mutable events updated each time given external events array changes.\n      mutableEvents: [],\n      // Transition when switching view. left when going toward the past, right when going toward future.\n      transitionDirection: \"right\"\n    };\n  },\n  methods: {\n    /**\n     * Only import locale on demand to keep a small library weight.\n     *\n     * @param {String|Object} locale the language user whishes to have on vue-cal.\n     */\n    async loadLocale(locale) {\n      if (typeof this.locale === \"object\") {\n        this.texts = Object.assign({}, textsDefaults, locale);\n        this.utils.date.updateTexts(this.texts);\n        return;\n      }\n      const texts = await __variableDynamicImportRuntimeHelper(/* @__PURE__ */ Object.assign({ \"./i18n/ar.json\": () => import(\"./i18n/ar.es.js\"), \"./i18n/bg.json\": () => import(\"./i18n/bg.es.js\"), \"./i18n/bn.json\": () => import(\"./i18n/bn.es.js\"), \"./i18n/bs.json\": () => import(\"./i18n/bs.es.js\"), \"./i18n/ca.json\": () => import(\"./i18n/ca.es.js\"), \"./i18n/cs.json\": () => import(\"./i18n/cs.es.js\"), \"./i18n/da.json\": () => import(\"./i18n/da.es.js\"), \"./i18n/de.json\": () => import(\"./i18n/de.es.js\"), \"./i18n/el.json\": () => import(\"./i18n/el.es.js\"), \"./i18n/en.json\": () => import(\"./i18n/en.es.js\"), \"./i18n/es.json\": () => import(\"./i18n/es.es.js\"), \"./i18n/et.json\": () => import(\"./i18n/et.es.js\"), \"./i18n/fa.json\": () => import(\"./i18n/fa.es.js\"), \"./i18n/fi.json\": () => import(\"./i18n/fi.es.js\"), \"./i18n/fr.json\": () => import(\"./i18n/fr.es.js\"), \"./i18n/he.json\": () => import(\"./i18n/he.es.js\"), \"./i18n/hr.json\": () => import(\"./i18n/hr.es.js\"), \"./i18n/hu.json\": () => import(\"./i18n/hu.es.js\"), \"./i18n/id.json\": () => import(\"./i18n/id.es.js\"), \"./i18n/is.json\": () => import(\"./i18n/is.es.js\"), \"./i18n/it.json\": () => import(\"./i18n/it.es.js\"), \"./i18n/ja.json\": () => import(\"./i18n/ja.es.js\"), \"./i18n/ka.json\": () => import(\"./i18n/ka.es.js\"), \"./i18n/ko.json\": () => import(\"./i18n/ko.es.js\"), \"./i18n/lt.json\": () => import(\"./i18n/lt.es.js\"), \"./i18n/mn.json\": () => import(\"./i18n/mn.es.js\"), \"./i18n/nl.json\": () => import(\"./i18n/nl.es.js\"), \"./i18n/no.json\": () => import(\"./i18n/no.es.js\"), \"./i18n/pl.json\": () => import(\"./i18n/pl.es.js\"), \"./i18n/pt-br.json\": () => import(\"./i18n/pt-br.es.js\"), \"./i18n/pt-pt.json\": () => import(\"./i18n/pt-pt.es.js\"), \"./i18n/ro.json\": () => import(\"./i18n/ro.es.js\"), \"./i18n/ru.json\": () => import(\"./i18n/ru.es.js\"), \"./i18n/sk.json\": () => import(\"./i18n/sk.es.js\"), \"./i18n/sl.json\": () => import(\"./i18n/sl.es.js\"), \"./i18n/sq.json\": () => import(\"./i18n/sq.es.js\"), \"./i18n/sr.json\": () => import(\"./i18n/sr.es.js\"), \"./i18n/sv.json\": () => import(\"./i18n/sv.es.js\"), \"./i18n/tr.json\": () => import(\"./i18n/tr.es.js\"), \"./i18n/uk.json\": () => import(\"./i18n/uk.es.js\"), \"./i18n/vi.json\": () => import(\"./i18n/vi.es.js\"), \"./i18n/zh-cn.json\": () => import(\"./i18n/zh-cn.es.js\"), \"./i18n/zh-hk.json\": () => import(\"./i18n/zh-hk.es.js\") }), `./i18n/${locale}.json`, 3);\n      this.texts = Object.assign({}, textsDefaults, texts);\n      this.utils.date.updateTexts(this.texts);\n    },\n    /**\n     * Only import drag and drop module on demand to keep a small library weight.\n     */\n    loadDragAndDrop() {\n      import(\"./drag-and-drop.es.js\").then((response) => {\n        const { DragAndDrop } = response;\n        this.modules.dnd = new DragAndDrop(this);\n      }).catch(() => console.warn(\"Vue Cal: Missing drag & drop module.\"));\n    },\n    /**\n     * Checks that the given view is in the array of valid views or use 'week' otherwise.\n     * Then check the view is enabled or use the first enabled view instead.\n     * Raises error and warning if needed.\n     *\n     * @param {String} view The view to validate.\n     * @return {String} a valid view.\n     */\n    validateView(view) {\n      if (!validViews.includes(view)) {\n        console.error(`Vue Cal: invalid active-view parameter provided: \"${view}\".\nA valid view must be one of: ${validViews.join(\", \")}.`);\n        view = \"week\";\n      }\n      if (!this.enabledViews.includes(view)) {\n        console.warn(`Vue Cal: the provided active-view \"${view}\" is disabled. Using the \"${this.enabledViews[0]}\" view instead.`);\n        view = this.enabledViews[0];\n      }\n      return view;\n    },\n    /**\n     * On click/dblclick of a cell go to a narrower view if available.\n     * E.g. Click on month cell takes you to week view if not hidden, otherwise on day view if not hidden.\n     *\n     * @param {String | Date} date A starting date for the view, if none, fallbacks to the selected date,\n     *                             If also empty fallbacks to the current view start date.\n     */\n    switchToNarrowerView(date = null) {\n      this.transitionDirection = \"right\";\n      const view = this.enabledViews[this.enabledViews.indexOf(this.view.id) + 1];\n      if (view) this.switchView(view, date);\n    },\n    /**\n     * Switches to the specified view on view selector click, or programmatically form external call (via $refs).\n     * If a date is given, it will be selected and if the view does not contain it, it will go to that date.\n     *\n     * @param {String} view the view to go to. Among `years`, `year`, `month`, `week`, `day`.\n     * @param {String | Date} date A starting date for the view, if none, fallbacks to the selected date,\n     *                             If also empty fallbacks to the current view start date.\n     * @param {Boolean} fromViewSelector to know if the caller is the built-in view selector.\n     */\n    switchView(view, date = null, fromViewSelector = false) {\n      view = this.validateView(view);\n      const ud2 = this.utils.date;\n      const viewDateBeforeChange = this.view.startDate && this.view.startDate.getTime();\n      if (this.transitions && fromViewSelector) {\n        if (this.view.id === view) return;\n        const views = this.enabledViews;\n        this.transitionDirection = views.indexOf(this.view.id) > views.indexOf(view) ? \"left\" : \"right\";\n      }\n      const oldView = this.view.id;\n      this.view.events = [];\n      this.view.id = view;\n      this.view.firstCellDate = null;\n      this.view.lastCellDate = null;\n      if (!date) date = this.view.selectedDate || this.view.startDate;\n      switch (view) {\n        case \"years\": {\n          this.view.startDate = new Date(Math.floor(date.getFullYear() / 25) * 25 || 2e3, 0, 1);\n          this.view.endDate = new Date(this.view.startDate.getFullYear() + 25, 0, 1);\n          this.view.endDate.setSeconds(-1);\n          break;\n        }\n        case \"year\": {\n          this.view.startDate = new Date(date.getFullYear(), 0, 1);\n          this.view.endDate = new Date(date.getFullYear() + 1, 0, 1);\n          this.view.endDate.setSeconds(-1);\n          break;\n        }\n        case \"month\": {\n          this.view.startDate = new Date(date.getFullYear(), date.getMonth(), 1);\n          this.view.endDate = new Date(date.getFullYear(), date.getMonth() + 1, 1);\n          this.view.endDate.setSeconds(-1);\n          let startDate = new Date(this.view.startDate);\n          if (startDate.getDay() !== (this.startWeekOnSunday ? 0 : 1)) {\n            startDate = ud2.getPreviousFirstDayOfWeek(startDate, this.startWeekOnSunday);\n          }\n          this.view.firstCellDate = startDate;\n          this.view.lastCellDate = ud2.addDays(startDate, 41);\n          this.view.lastCellDate.setHours(23, 59, 59, 0);\n          if (this.hideWeekends) {\n            if ([0, 6].includes(this.view.firstCellDate.getDay())) {\n              const daysToAdd = this.view.firstCellDate.getDay() === 6 && !this.startWeekOnSunday ? 2 : 1;\n              this.view.firstCellDate = ud2.addDays(this.view.firstCellDate, daysToAdd);\n            }\n            if ([0, 6].includes(this.view.startDate.getDay())) {\n              const daysToAdd = this.view.startDate.getDay() === 6 ? 2 : 1;\n              this.view.startDate = ud2.addDays(this.view.startDate, daysToAdd);\n            }\n            if ([0, 6].includes(this.view.lastCellDate.getDay())) {\n              const daysToSubtract = this.view.lastCellDate.getDay() === 0 && !this.startWeekOnSunday ? 2 : 1;\n              this.view.lastCellDate = ud2.subtractDays(this.view.lastCellDate, daysToSubtract);\n            }\n            if ([0, 6].includes(this.view.endDate.getDay())) {\n              const daysToSubtract = this.view.endDate.getDay() === 0 ? 2 : 1;\n              this.view.endDate = ud2.subtractDays(this.view.endDate, daysToSubtract);\n            }\n          }\n          break;\n        }\n        case \"week\": {\n          date = ud2.getPreviousFirstDayOfWeek(date, this.startWeekOnSunday);\n          const weekDaysCount = this.hideWeekends ? 5 : 7;\n          this.view.startDate = this.hideWeekends && this.startWeekOnSunday ? ud2.addDays(date, 1) : date;\n          this.view.startDate.setHours(0, 0, 0, 0);\n          this.view.endDate = ud2.addDays(date, weekDaysCount);\n          this.view.endDate.setSeconds(-1);\n          break;\n        }\n        case \"day\": {\n          this.view.startDate = date;\n          this.view.startDate.setHours(0, 0, 0, 0);\n          this.view.endDate = new Date(date);\n          this.view.endDate.setHours(23, 59, 59, 0);\n          break;\n        }\n      }\n      this.addEventsToView();\n      const viewDate = this.view.startDate && this.view.startDate.getTime();\n      if (oldView === view && viewDate === viewDateBeforeChange) return;\n      this.$emit(\"update:activeView\", view);\n      if (this.ready) {\n        const startDate = this.view.startDate;\n        const params = {\n          view,\n          startDate,\n          endDate: this.view.endDate,\n          ...this.isMonthView ? {\n            firstCellDate: this.view.firstCellDate,\n            lastCellDate: this.view.lastCellDate,\n            outOfScopeEvents: this.view.outOfScopeEvents.map(this.cleanupEvent)\n          } : {},\n          events: this.view.events.map(this.cleanupEvent),\n          ...this.isWeekView ? { week: ud2.getWeek(this.startWeekOnSunday ? ud2.addDays(startDate, 1) : startDate) } : {}\n        };\n        this.$emit(\"view-change\", params);\n      }\n    },\n    /**\n     * Shorthand function for external call (via $refs).\n     */\n    previous() {\n      this.previousNext(false);\n    },\n    /**\n     * Shorthand function for external call (via $refs).\n     */\n    next() {\n      this.previousNext();\n    },\n    /**\n     * On click on previous or next arrow, update the calendar visible date range.\n     *\n     * @param {Boolean} next\n     */\n    previousNext(next = true) {\n      const ud2 = this.utils.date;\n      this.transitionDirection = next ? \"right\" : \"left\";\n      const modifier = next ? 1 : -1;\n      let firstCellDate = null;\n      const { startDate, id: viewId } = this.view;\n      switch (viewId) {\n        case \"years\":\n          firstCellDate = new Date(startDate.getFullYear() + 25 * modifier, 0, 1);\n          break;\n        case \"year\":\n          firstCellDate = new Date(startDate.getFullYear() + 1 * modifier, 1, 1);\n          break;\n        case \"month\":\n          firstCellDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1 * modifier, 1);\n          break;\n        case \"week\":\n          firstCellDate = ud2[next ? \"addDays\" : \"subtractDays\"](ud2.getPreviousFirstDayOfWeek(startDate, this.startWeekOnSunday), 7);\n          break;\n        case \"day\":\n          firstCellDate = ud2[next ? \"addDays\" : \"subtractDays\"](startDate, 1);\n          const weekDay = firstCellDate.getDay();\n          const weekDayIndex = this.startWeekOnSunday ? weekDay : (weekDay || 7) - 1;\n          const isDayHidden = this.weekDays[weekDayIndex].hide;\n          if (isDayHidden) {\n            const daysWithIndex = this.weekDays.map((day, i) => ({ ...day, i }));\n            let daysFromDate = 0;\n            if (next) {\n              [...daysWithIndex.slice(weekDayIndex), ...daysWithIndex].find((day) => {\n                daysFromDate++;\n                return !day.hide;\n              }).i;\n              daysFromDate--;\n            } else {\n              [...daysWithIndex, ...daysWithIndex.slice(0, weekDayIndex)].reverse().find((day) => {\n                daysFromDate++;\n                return !day.hide;\n              }).i;\n            }\n            firstCellDate = ud2[next ? \"addDays\" : \"subtractDays\"](firstCellDate, daysFromDate);\n          }\n          break;\n      }\n      if (firstCellDate) this.switchView(viewId, firstCellDate);\n    },\n    /**\n     * Add events (subset from mutableEvents) to the current view (in `this.view.events`).\n     * This is done for performance, so that all the cells have a quick lookup of only what's needed.\n     *\n     * @param {Array} events\n     */\n    addEventsToView(events = []) {\n      const ue = this.utils.event;\n      const { startDate, endDate, firstCellDate, lastCellDate } = this.view;\n      if (!events.length) this.view.events = [];\n      events = events.length ? events : [...this.mutableEvents];\n      if (!events || this.isYearsOrYearView && !this.eventsCountOnYearView) return;\n      let filteredEvents = events.filter((e) => ue.eventInRange(e, startDate, endDate));\n      if (!this.isYearsOrYearView && !(this.isMonthView && !this.eventsOnMonthView)) {\n        filteredEvents = filteredEvents.map((e) => {\n          return e.daysCount > 1 ? ue.createEventSegments(e, firstCellDate || startDate, lastCellDate || endDate) : e;\n        });\n      }\n      this.view.events.push(...filteredEvents);\n      if (this.isMonthView) {\n        this.view.outOfScopeEvents = [];\n        events.forEach((e) => {\n          if (ue.eventInRange(e, firstCellDate, startDate) || ue.eventInRange(e, endDate, lastCellDate)) {\n            if (!this.view.events.some((e2) => e2._eid === e._eid)) this.view.outOfScopeEvents.push(e);\n          }\n        });\n      }\n    },\n    /**\n     * find a DOM ancestor of a given DOM node `el` matching given class name.\n     *\n     * @param {Object} el a DOM node to find ancestor from.\n     * @param {String} Class the CSS class name of the ancestor.\n     * @return {Object} The matched DOM node or null if no match.\n     */\n    findAncestor(el, Class) {\n      while ((el = el.parentElement) && !el.classList.contains(Class)) {\n      }\n      return el;\n    },\n    /**\n     * Tells whether a clicked DOM node is or is within a calendar event.\n     *\n     * @param {Object} el a DOM node to check if event.\n     * @return {Boolean} true if the given DOM node is - or is in - an event.\n     */\n    isDOMElementAnEvent(el) {\n      return el.classList.contains(\"vuecal__event\") || this.findAncestor(el, \"vuecal__event\");\n    },\n    /**\n     * Capture mousemove anywhere in the page.\n     * If resizing an event was started earlier, this will update event end.\n     * If resizing was not started, this method is calculation is avoided with a premature return.\n     * Notes: Event resizing is started in cell component (onMouseDown) but place onMouseMove & onMouseUp\n     *        handlers in the single-instance parent for performance.\n     *\n     * @param {Object} e the native DOM event object.\n     */\n    onMouseMove(e) {\n      const { resizeAnEvent, dragAnEvent, dragCreateAnEvent } = this.domEvents;\n      if (resizeAnEvent._eid === null && dragAnEvent._eid === null && !dragCreateAnEvent.start) return;\n      e.preventDefault();\n      if (resizeAnEvent._eid) this.eventResizing(e);\n      else if (this.dragToCreateEvent && dragCreateAnEvent.start) this.eventDragCreation(e);\n    },\n    /**\n     * Capture mouseup anywhere in the page, not only on a cell or event.\n     * Then end up any resize, drag & drop, click & hold or event or cell.\n     * Notes: Mouseup can never cancel a click with preventDefault or stopPropagation,\n     *        But it always happens before the click event.\n     *\n     * @param {Object} e the native DOM event object.\n     */\n    onMouseUp(e) {\n      const {\n        focusAnEvent,\n        resizeAnEvent,\n        clickHoldAnEvent,\n        clickHoldACell,\n        dragCreateAnEvent\n      } = this.domEvents;\n      const { _eid: isClickHoldingEvent } = clickHoldAnEvent;\n      const { _eid: wasResizing } = resizeAnEvent;\n      let hasResized = false;\n      const { event: dragCreatedEvent, start: dragCreateStarted } = dragCreateAnEvent;\n      const mouseUpOnEvent = this.isDOMElementAnEvent(e.target);\n      const eventClicked = focusAnEvent.mousedUp;\n      focusAnEvent.mousedUp = false;\n      if (mouseUpOnEvent) this.domEvents.cancelClickEventCreation = true;\n      if (clickHoldACell.eventCreated) return;\n      if (wasResizing) {\n        const { originalEnd, originalEndTimeMinutes, endTimeMinutes } = resizeAnEvent;\n        const event = this.view.events.find((e2) => e2._eid === resizeAnEvent._eid);\n        hasResized = endTimeMinutes && endTimeMinutes !== originalEndTimeMinutes;\n        if (event && event.end.getTime() !== originalEnd.getTime()) {\n          const mutableEvent = this.mutableEvents.find((e2) => e2._eid === resizeAnEvent._eid);\n          mutableEvent.endTimeMinutes = event.endTimeMinutes;\n          mutableEvent.end = event.end;\n          const cleanEvent = this.cleanupEvent(event);\n          const originalEvent = {\n            ...this.cleanupEvent(event),\n            end: originalEnd,\n            endTimeMinutes: event.originalEndTimeMinutes\n          };\n          this.$emit(\"event-duration-change\", { event: cleanEvent, oldDate: resizeAnEvent.originalEnd, originalEvent });\n          this.$emit(\"event-change\", { event: cleanEvent, originalEvent });\n        }\n        if (event) event.resizing = false;\n        resizeAnEvent._eid = null;\n        resizeAnEvent.start = null;\n        resizeAnEvent.split = null;\n        resizeAnEvent.segment = null;\n        resizeAnEvent.originalEndTimeMinutes = null;\n        resizeAnEvent.originalEnd = null;\n        resizeAnEvent.endTimeMinutes = null;\n        resizeAnEvent.startCell = null;\n        resizeAnEvent.endCell = null;\n      } else if (dragCreateStarted) {\n        if (dragCreatedEvent) {\n          this.emitWithEvent(\"event-drag-create\", dragCreatedEvent);\n          dragCreateAnEvent.event.resizing = false;\n        }\n        dragCreateAnEvent.start = null;\n        dragCreateAnEvent.split = null;\n        dragCreateAnEvent.event = null;\n      }\n      if (!mouseUpOnEvent && !wasResizing) this.unfocusEvent();\n      if (clickHoldAnEvent.timeoutId && !isClickHoldingEvent) {\n        clearTimeout(clickHoldAnEvent.timeoutId);\n        clickHoldAnEvent.timeoutId = null;\n      }\n      if (clickHoldACell.timeoutId) {\n        clearTimeout(clickHoldACell.timeoutId);\n        clickHoldACell.timeoutId = null;\n      }\n      const eventClickHandler = typeof this.onEventClick === \"function\";\n      if (eventClicked && !hasResized && !isClickHoldingEvent && !dragCreatedEvent && eventClickHandler) {\n        let event = this.view.events.find((e2) => e2._eid === focusAnEvent._eid);\n        if (!event && this.isMonthView) event = this.view.outOfScopeEvents.find((e2) => e2._eid === focusAnEvent._eid);\n        return event && this.onEventClick(event, e);\n      }\n    },\n    /**\n     * Capture `escape` keypress when delete button is visible, and cancel deletion.\n     *\n     * @param {Object} e the native DOM event object.\n     */\n    onKeyUp(e) {\n      if (e.keyCode === 27) this.cancelDelete();\n    },\n    /**\n     * On mousemove while resising an event.\n     *\n     * @param {Object} e the native DOM event object.\n     */\n    eventResizing(e) {\n      const { resizeAnEvent } = this.domEvents;\n      const event = this.view.events.find((e2) => e2._eid === resizeAnEvent._eid) || { segments: {} };\n      const { minutes, cursorCoords } = this.minutesAtCursor(e);\n      const segment = event.segments && event.segments[resizeAnEvent.segment];\n      const { date: ud2, event: ue } = this.utils;\n      const newEndTimeMins = Math.max(minutes, this.timeFrom + 1, (segment || event).startTimeMinutes + 1);\n      event.endTimeMinutes = resizeAnEvent.endTimeMinutes = newEndTimeMins;\n      if (this.snapToTime) {\n        const plusHalfSnapTime = event.endTimeMinutes + this.snapToTime / 2;\n        event.endTimeMinutes = plusHalfSnapTime - plusHalfSnapTime % this.snapToTime;\n      }\n      if (segment) segment.endTimeMinutes = event.endTimeMinutes;\n      event.end.setHours(0, event.endTimeMinutes, event.endTimeMinutes === minutesInADay ? -1 : 0, 0);\n      if (this.resizeX && this.isWeekView) {\n        event.daysCount = ud2.countDays(event.start, event.end);\n        const cells = this.cellsEl;\n        const cellWidth = cells.offsetWidth / cells.childElementCount;\n        const endCell = Math.floor(cursorCoords.x / cellWidth);\n        if (resizeAnEvent.startCell === null) resizeAnEvent.startCell = endCell - (event.daysCount - 1);\n        if (resizeAnEvent.endCell !== endCell) {\n          resizeAnEvent.endCell = endCell;\n          const newEnd = ud2.addDays(event.start, endCell - resizeAnEvent.startCell);\n          const newDaysCount = Math.max(ud2.countDays(event.start, newEnd), 1);\n          if (newDaysCount !== event.daysCount) {\n            let lastSegmentFormattedDate = null;\n            if (newDaysCount > event.daysCount) lastSegmentFormattedDate = ue.addEventSegment(event);\n            else lastSegmentFormattedDate = ue.removeEventSegment(event);\n            resizeAnEvent.segment = lastSegmentFormattedDate;\n            event.endTimeMinutes += 1e-3;\n          }\n        }\n      }\n      this.$emit(\"event-resizing\", { _eid: event._eid, end: event.end, endTimeMinutes: event.endTimeMinutes });\n    },\n    /**\n     * On mousemove while dragging to create an event.\n     *\n     * @param {Object} e the native DOM event object.\n     */\n    eventDragCreation(e) {\n      const { dragCreateAnEvent } = this.domEvents;\n      const { start, startCursorY, split } = dragCreateAnEvent;\n      const timeAtCursor = new Date(start);\n      const { minutes, cursorCoords: { y } } = this.minutesAtCursor(e);\n      if (!dragCreateAnEvent.event && Math.abs(startCursorY - y) < this.dragToCreateThreshold) return;\n      if (!dragCreateAnEvent.event) {\n        dragCreateAnEvent.event = this.utils.event.createAnEvent(start, 1, { split });\n        if (!dragCreateAnEvent.event) {\n          dragCreateAnEvent.start = null;\n          dragCreateAnEvent.split = null;\n          dragCreateAnEvent.event = null;\n          return;\n        }\n        dragCreateAnEvent.event.resizing = true;\n      } else {\n        timeAtCursor.setHours(0, minutes, minutes === minutesInADay ? -1 : 0, 0);\n        if (this.snapToTime) {\n          let timeMinutes = timeAtCursor.getHours() * 60 + timeAtCursor.getMinutes();\n          const plusHalfSnapTime = timeMinutes + this.snapToTime / 2;\n          timeMinutes = plusHalfSnapTime - plusHalfSnapTime % this.snapToTime;\n          timeAtCursor.setHours(0, timeMinutes, 0, 0);\n        }\n        const dragFromBottom = start < timeAtCursor;\n        const { event } = dragCreateAnEvent;\n        event.start = dragFromBottom ? start : timeAtCursor;\n        event.end = dragFromBottom ? timeAtCursor : start;\n        event.startTimeMinutes = event.start.getHours() * 60 + event.start.getMinutes();\n        event.endTimeMinutes = event.end.getHours() * 60 + event.end.getMinutes();\n      }\n    },\n    /**\n     * Unfocus an event (e.g. when clicking outside of focused event).\n     */\n    unfocusEvent() {\n      const { focusAnEvent, clickHoldAnEvent } = this.domEvents;\n      const event = this.view.events.find((e) => e._eid === (focusAnEvent._eid || clickHoldAnEvent._eid));\n      focusAnEvent._eid = null;\n      clickHoldAnEvent._eid = null;\n      if (event) {\n        event.focused = false;\n        event.deleting = false;\n      }\n    },\n    /**\n     * Cancel an event deletion (e.g. when clicking outside of visible delete button).\n     */\n    cancelDelete() {\n      const { clickHoldAnEvent } = this.domEvents;\n      if (clickHoldAnEvent._eid) {\n        const event = this.view.events.find((e) => e._eid === clickHoldAnEvent._eid);\n        if (event) event.deleting = false;\n        clickHoldAnEvent._eid = null;\n        clickHoldAnEvent.timeoutId = null;\n      }\n    },\n    /**\n     * After editing an event title (if `this.editable`), save the new string into the event object\n     * and emit event to the outside world.\n     *\n     * @param {Object} e the native DOM event object.\n     * @param {Object} event the vue-cal event object.\n     */\n    onEventTitleBlur(e, event) {\n      if (event.title === e.target.innerHTML) return;\n      const oldTitle = event.title;\n      event.title = e.target.innerHTML;\n      const cleanEvent = this.cleanupEvent(event);\n      this.$emit(\"event-title-change\", { event: cleanEvent, oldTitle });\n      this.$emit(\"event-change\", { event: cleanEvent, originalEvent: { ...cleanEvent, title: oldTitle } });\n    },\n    /**\n     * The `mutableEvents` array of events is the source of truth.\n     * It is first populated from the `events` prop and every time the `events` prop changes.\n     * When the user updates an event through interractions, the event gets updated here.\n     * Notes: mutableEvents couldn't be a computed variable based on this.events, because we add\n     *        items to the array. (Cannot mutate props)\n     */\n    updateMutableEvents() {\n      const ud2 = this.utils.date;\n      this.mutableEvents = [];\n      this.events.forEach((event) => {\n        const start = typeof event.start === \"string\" ? ud2.stringToDate(event.start) : event.start;\n        const startDateF = ud2.formatDateLite(start);\n        const startTimeMinutes = ud2.dateToMinutes(start);\n        let end = null;\n        if (typeof event.end === \"string\" && event.end.includes(\"24:00\")) {\n          end = new Date(event.end.replace(\" 24:00\", \"\"));\n          end.setHours(23, 59, 59, 0);\n        } else end = typeof event.end === \"string\" ? ud2.stringToDate(event.end) : event.end;\n        let endDateF = ud2.formatDateLite(end);\n        let endTimeMinutes = ud2.dateToMinutes(end);\n        if (!endTimeMinutes || endTimeMinutes === minutesInADay) {\n          if (!this.time || typeof event.end === \"string\" && event.end.length === 10) {\n            end.setHours(23, 59, 59, 0);\n          } else end.setSeconds(end.getSeconds() - 1);\n          endDateF = ud2.formatDateLite(end);\n          endTimeMinutes = minutesInADay;\n        }\n        const multipleDays = startDateF !== endDateF;\n        event = Object.assign({ ...this.utils.event.eventDefaults }, event, {\n          // Keep the event ids scoped to this calendar instance.\n          _eid: `${this._.uid}_${this.eventIdIncrement++}`,\n          segments: multipleDays ? {} : null,\n          start,\n          startTimeMinutes,\n          end,\n          endTimeMinutes,\n          daysCount: multipleDays ? ud2.countDays(start, end) : 1,\n          class: event.class\n        });\n        this.mutableEvents.push(event);\n      });\n    },\n    /**\n     * Get the number of minutes from the top to the mouse cursor.\n     *\n     * @param {Object} e the native DOM event object.\n     * @return {Object} containing { minutes: {Number}, cursorCoords: { x: {Number}, y: {Number} } }\n     */\n    minutesAtCursor(e) {\n      return this.utils.cell.minutesAtCursor(e);\n    },\n    /**\n     * Creates a new event in vue-cal memory (in the mutableEvents array) starting at the given date & time.\n     * Proxy method to allow call from cell click & hold or external call (via $refs).\n     * Notes: Event duration is by default 2 hours. You can override the event end through eventOptions.\n     *\n     * @param {String | Date} dateTime date & time at which the event will start.\n     * @param {Number} duration the event duration in minutes.\n     * @param {Object} eventOptions an object of options to override the event creation defaults.\n     *                              (can be any key allowed in an event object)\n     * @return {Object} the created event.\n     */\n    createEvent(dateTime, duration, eventOptions = {}) {\n      return this.utils.event.createAnEvent(dateTime, duration, eventOptions);\n    },\n    /**\n     * Remove all the vue-cal private vars from the event (before returning it through $emit()).\n     *\n     * @param {Object} event the event object to cleanup.\n     */\n    cleanupEvent(event) {\n      event = { ...event };\n      const discardProps = [\n        \"segments\",\n        \"deletable\",\n        \"deleting\",\n        \"titleEditable\",\n        \"resizable\",\n        \"resizing\",\n        \"draggable\",\n        \"dragging\",\n        \"draggingStatic\",\n        \"focused\"\n      ];\n      discardProps.forEach((prop) => {\n        if (prop in event) delete event[prop];\n      });\n      if (!event.repeat) delete event.repeat;\n      return event;\n    },\n    /**\n     * Emits an event (custom DOM event) to the outside world.\n     * This event has an event name and a clean calendar event as a parameter.\n     *\n     * @param {String} eventName the name of the custom emitted event (e.g. `event-focus`).\n     * @param {Object} event the event to return to the outside world.\n     */\n    emitWithEvent(eventName, event) {\n      this.$emit(eventName, this.cleanupEvent(event));\n    },\n    /**\n     * Update the selected date:\n     * - on created, from given selectedDate prop\n     * - on click/dblClick of another cell\n     * - from external call (via $refs)\n     * - when the given selectedDate prop changes.\n     * If date is not in the view, the view will change to show it.\n     *\n     * @param {String | Date} date The date to select.\n     */\n    updateSelectedDate(date) {\n      if (date && typeof date === \"string\") date = this.utils.date.stringToDate(date);\n      else date = new Date(date);\n      if (date && date instanceof Date) {\n        const { selectedDate } = this.view;\n        if (selectedDate) this.transitionDirection = selectedDate.getTime() > date.getTime() ? \"left\" : \"right\";\n        date.setHours(0, 0, 0, 0);\n        if (!selectedDate || selectedDate.getTime() !== date.getTime()) this.view.selectedDate = date;\n        this.switchView(this.view.id);\n      }\n      this.$emit(\"update:selected-date\", this.view.selectedDate);\n    },\n    /**\n     * Double checks the week number is correct. Read below to understand!\n     * this is a wrapper around the `getWeek()` function for performance:\n     * As this is called multiple times from the template and cannot be in computed since there is\n     * a parameter, this wrapper function avoids the `getWeek()` function call 5 times out of 6\n     * using the computed `firstCellDateWeekNumber`.\n     *\n     * Reason why:\n     * Getting the week number is not that straightforward as there might be a 53rd week in the year.\n     * Whenever the year starts on a Thursday or any leap year starting on a Wednesday, this week will be 53.\n     *\n     * @param {Number} weekFromFirstCell Number from 0 to 6.\n     */\n    getWeekNumber(weekFromFirstCell) {\n      const ud2 = this.utils.date;\n      const firstCellWeekNumber = this.firstCellDateWeekNumber;\n      const currentWeekNumber = firstCellWeekNumber + weekFromFirstCell;\n      const modifier = this.startWeekOnSunday ? 1 : 0;\n      if (currentWeekNumber > 52) {\n        return ud2.getWeek(ud2.addDays(this.view.firstCellDate, 7 * weekFromFirstCell + modifier));\n      } else return currentWeekNumber;\n    },\n    /**\n     * Only if watchRealTime is true.\n     * Pull the current time from user machine every minute to keep vue-cal accurate even when idle.\n     * This will redraw the now line every minute and ensure that Today's date is always accurate.\n     */\n    timeTick() {\n      this.now = /* @__PURE__ */ new Date();\n      this.timeTickerIds[1] = setTimeout(this.timeTick, 60 * 1e3);\n    },\n    /**\n     * Updates the localized texts in use in the Date prototypes. (E.g. new Date().format())\n     * Callable from outside of Vue Cal.\n     */\n    updateDateTexts() {\n      this.utils.date.updateTexts(this.texts);\n    },\n    /**\n     * On Windows devices, the .vuecal__bg's vertical scrollbar takes space and pushes the content.\n     * This function will also push the weekdays-headings and all-day bar to have them properly aligned.\n     * The calculated style will be placed in the document head in a style tag so it's only done once\n     * (the scrollbar width never changes).\n     * Ref. https://github.com/antoniandre/vue-cal-v4/issues/221\n     */\n    alignWithScrollbar() {\n      if (document.getElementById(\"vuecal-align-with-scrollbar\")) return;\n      const bg = this.$refs.vuecal.getElementsByClassName(\"vuecal__scrollbar-check\")[0];\n      const scrollbarWidth = bg.offsetWidth - bg.children[0].offsetWidth;\n      if (scrollbarWidth) {\n        const style = document.createElement(\"style\");\n        style.id = \"vuecal-align-with-scrollbar\";\n        style.type = \"text/css\";\n        style.innerHTML = `.vuecal--view-with-time .vuecal__weekdays-headings,.vuecal--view-with-time .vuecal__all-day {padding-right: ${scrollbarWidth}px}`;\n        document.head.appendChild(style);\n      }\n    },\n    /**\n     * Tells wether there are events in the given cell or split and returns a Boolean.\n     * This function simplifies the template.\n     *\n     * @param {Array} events The cell events.\n     * @param {Object|Boolean} split The current split object if any or false.\n     * @return {Boolean} true if there are events, false otherwise.\n     */\n    cellOrSplitHasEvents(events, split = null) {\n      return events.length && (!split && events.length || split && events.some((e) => e.split === split.id));\n    }\n  },\n  created() {\n    this.utils.cell = new CellUtils(this);\n    this.utils.event = new EventUtils(this, this.utils.date);\n    this.loadLocale(this.locale);\n    if (this.editEvents.drag) this.loadDragAndDrop();\n    this.updateMutableEvents(this.events);\n    this.view.id = this.currentView;\n    if (this.selectedDate) this.updateSelectedDate(this.selectedDate);\n    else {\n      this.view.selectedDate = /* @__PURE__ */ new Date();\n      this.switchView(this.currentView);\n    }\n    if (this.time && this.watchRealTime) {\n      this.timeTickerIds[0] = setTimeout(this.timeTick, (60 - this.now.getSeconds()) * 1e3);\n    }\n  },\n  mounted() {\n    const ud2 = this.utils.date;\n    const hasTouch = \"ontouchstart\" in window;\n    const { resize, drag, create, delete: deletable, title } = this.editEvents;\n    const hasEventClickHandler = this.onEventClick && typeof this.onEventClick === \"function\";\n    if (resize || drag || create || deletable || title || hasEventClickHandler) {\n      window.addEventListener(hasTouch ? \"touchend\" : \"mouseup\", this.onMouseUp);\n    }\n    if (resize || drag || create && this.dragToCreateEvent) {\n      window.addEventListener(hasTouch ? \"touchmove\" : \"mousemove\", this.onMouseMove, { passive: false });\n    }\n    if (title) window.addEventListener(\"keyup\", this.onKeyUp);\n    if (hasTouch) {\n      this.$refs.vuecal.oncontextmenu = function(e) {\n        e.preventDefault();\n        e.stopPropagation();\n      };\n    }\n    if (!this.hideBody) this.alignWithScrollbar();\n    const startDate = this.view.startDate;\n    const params = {\n      view: this.view.id,\n      startDate,\n      endDate: this.view.endDate,\n      ...this.isMonthView ? { firstCellDate: this.view.firstCellDate, lastCellDate: this.view.lastCellDate } : {},\n      events: this.view.events.map(this.cleanupEvent),\n      ...this.isWeekView ? { week: ud2.getWeek(this.startWeekOnSunday ? ud2.addDays(startDate, 1) : startDate) } : {}\n    };\n    this.$emit(\"ready\", params);\n    this.ready = true;\n  },\n  beforeUnmount() {\n    const hasTouch = \"ontouchstart\" in window;\n    window.removeEventListener(hasTouch ? \"touchmove\" : \"mousemove\", this.onMouseMove, { passive: false });\n    window.removeEventListener(hasTouch ? \"touchend\" : \"mouseup\", this.onMouseUp);\n    window.removeEventListener(\"keyup\", this.onKeyUp);\n    if (this.timeTickerIds[0]) clearTimeout(this.timeTickerIds[0]);\n    if (this.timeTickerIds[1]) clearTimeout(this.timeTickerIds[1]);\n    this.timeTickerIds = [null, null];\n  },\n  computed: {\n    editEvents() {\n      if (this.editableEvents && typeof this.editableEvents === \"object\") {\n        return {\n          title: !!this.editableEvents.title,\n          drag: !!this.editableEvents.drag,\n          resize: !!this.editableEvents.resize,\n          create: !!this.editableEvents.create,\n          delete: !!this.editableEvents.delete\n        };\n      }\n      return {\n        title: !!this.editableEvents,\n        drag: !!this.editableEvents,\n        resize: !!this.editableEvents,\n        create: !!this.editableEvents,\n        delete: !!this.editableEvents\n      };\n    },\n    views() {\n      return {\n        years: { label: this.texts.years, enabled: !this.disableViews.includes(\"years\") },\n        year: { label: this.texts.year, enabled: !this.disableViews.includes(\"year\") },\n        month: { label: this.texts.month, enabled: !this.disableViews.includes(\"month\") },\n        week: { label: this.texts.week, enabled: !this.disableViews.includes(\"week\") },\n        day: { label: this.texts.day, enabled: !this.disableViews.includes(\"day\") }\n      };\n    },\n    currentView() {\n      return this.validateView(this.activeView);\n    },\n    enabledViews() {\n      return Object.keys(this.views).filter((view) => this.views[view].enabled);\n    },\n    hasTimeColumn() {\n      return this.time && this.isWeekOrDayView;\n    },\n    isShortMonthView() {\n      return this.isMonthView && this.eventsOnMonthView === \"short\";\n    },\n    firstCellDateWeekNumber() {\n      const ud2 = this.utils.date;\n      const date = this.view.firstCellDate;\n      return ud2.getWeek(this.startWeekOnSunday ? ud2.addDays(date, 1) : date);\n    },\n    // For week & day views.\n    timeCells() {\n      const timeCells = [];\n      for (let i = this.timeFrom, max = this.timeTo; i < max; i += this.timeStep) {\n        timeCells.push({\n          hours: Math.floor(i / 60),\n          minutes: i % 60,\n          label: this.utils.date.formatTime(i, this.TimeFormat),\n          // The texts (3rd param) are given on Vue Cal init.\n          value: i\n        });\n      }\n      return timeCells;\n    },\n    TimeFormat() {\n      return this.timeFormat || (this.twelveHour ? \"h:mm{am}\" : \"HH:mm\");\n    },\n    // Filter out the day splits that are hidden.\n    daySplits() {\n      return (this.splitDays.filter((item) => !item.hide) || []).map((item, i) => ({ ...item, id: item.id || i + 1 }));\n    },\n    // Whether the current view has days splits.\n    hasSplits() {\n      return this.daySplits.length && this.isWeekOrDayView;\n    },\n    hasShortEvents() {\n      return this.showAllDayEvents === \"short\";\n    },\n    // Returns the min cell width or the min split width if any.\n    cellOrSplitMinWidth() {\n      let minWidth = null;\n      if (this.hasSplits && this.minSplitWidth) minWidth = this.visibleDaysCount * this.minSplitWidth * this.daySplits.length;\n      else if (this.minCellWidth && this.isWeekView) minWidth = this.visibleDaysCount * this.minCellWidth;\n      return minWidth;\n    },\n    allDayBar() {\n      let height = this.allDayBarHeight || null;\n      if (height && !isNaN(height)) height += \"px\";\n      return {\n        cells: this.viewCells,\n        options: this.$props,\n        label: this.texts.allDay,\n        shortEvents: this.hasShortEvents,\n        daySplits: this.hasSplits && this.daySplits || [],\n        cellOrSplitMinWidth: this.cellOrSplitMinWidth,\n        height\n      };\n    },\n    minTimestamp() {\n      let date = null;\n      if (this.minDate && typeof this.minDate === \"string\") date = this.utils.date.stringToDate(this.minDate);\n      else if (this.minDate && this.minDate instanceof Date) date = this.minDate;\n      return date ? date.getTime() : null;\n    },\n    maxTimestamp() {\n      let date = null;\n      if (this.maxDate && typeof this.maxDate === \"string\") date = this.utils.date.stringToDate(this.maxDate);\n      else if (this.maxDate && this.maxDate instanceof Date) date = this.maxDate;\n      return date ? date.getTime() : null;\n    },\n    weekDays() {\n      let { weekDays, weekDaysShort = [] } = this.texts;\n      weekDays = weekDays.slice(0).map((day, i) => ({\n        label: day,\n        ...weekDaysShort.length ? { short: weekDaysShort[i] } : {},\n        hide: this.hideWeekends && i >= 5 || this.hideWeekdays.length && this.hideWeekdays.includes(i + 1)\n      }));\n      if (this.startWeekOnSunday) weekDays.unshift(weekDays.pop());\n      return weekDays;\n    },\n    weekDaysInHeader() {\n      return this.isMonthView || // hasSplits check is important here in case the user toggles the splits but keep minSplitWidth.\n      this.isWeekView && !this.minCellWidth && !(this.hasSplits && this.minSplitWidth);\n    },\n    months() {\n      return this.texts.months.map((month) => ({ label: month }));\n    },\n    // Validate and fill up the special hours object once for all at root level and not in cell.\n    specialDayHours() {\n      if (!this.specialHours || !Object.keys(this.specialHours).length) return {};\n      return Array(7).fill(\"\").map((cell, i) => {\n        let day = this.specialHours[i + 1] || [];\n        if (!Array.isArray(day)) day = [day];\n        cell = [];\n        day.forEach(({ from, to, class: Class, label }, j) => {\n          cell[j] = {\n            day: i + 1,\n            from: ![null, void 0].includes(from) ? from * 1 : null,\n            to: ![null, void 0].includes(to) ? to * 1 : null,\n            class: Class || \"\",\n            label: label || \"\"\n          };\n        });\n        return cell;\n      });\n    },\n    viewTitle() {\n      const ud2 = this.utils.date;\n      let title = \"\";\n      const date = this.view.startDate;\n      const year = date.getFullYear();\n      const month = date.getMonth();\n      switch (this.view.id) {\n        case \"years\": {\n          title = this.texts.years;\n          break;\n        }\n        case \"year\": {\n          title = year;\n          break;\n        }\n        case \"month\": {\n          title = `${this.months[month].label} ${year}`;\n          break;\n        }\n        case \"week\": {\n          const lastDayOfWeek = this.view.endDate;\n          const y1 = date.getFullYear();\n          let m1 = this.texts.months[date.getMonth()];\n          if (this.xsmall) m1 = m1.substring(0, 3);\n          let formattedMonthYear = `${m1} ${y1}`;\n          if (lastDayOfWeek.getMonth() !== date.getMonth()) {\n            const y2 = lastDayOfWeek.getFullYear();\n            let m2 = this.texts.months[lastDayOfWeek.getMonth()];\n            if (this.xsmall) m2 = m2.substring(0, 3);\n            if (y1 === y2) formattedMonthYear = `${m1} - ${m2} ${y1}`;\n            else {\n              if (this.small) formattedMonthYear = `${m1.substring(0, 3)} ${y1} - ${m2.substring(0, 3)} ${y2}`;\n              else formattedMonthYear = `${m1} ${y1} - ${m2} ${y2}`;\n            }\n          }\n          title = `${this.texts.week} ${ud2.getWeek(this.startWeekOnSunday ? ud2.addDays(date, 1) : date)} (${formattedMonthYear})`;\n          break;\n        }\n        case \"day\": {\n          title = this.utils.date.formatDate(date, this.texts.dateFormat, this.texts);\n          break;\n        }\n      }\n      return title;\n    },\n    viewCells() {\n      const ud2 = this.utils.date;\n      let cells = [];\n      let fromYear = null;\n      let todayFound = false;\n      if (!this.watchRealTime) this.now = /* @__PURE__ */ new Date();\n      const now2 = this.now;\n      switch (this.view.id) {\n        case \"years\": {\n          fromYear = this.view.startDate.getFullYear();\n          cells = Array.apply(null, Array(25)).map((cell, i) => {\n            const startDate = new Date(fromYear + i, 0, 1);\n            const endDate = new Date(fromYear + i + 1, 0, 1);\n            endDate.setSeconds(-1);\n            return {\n              startDate,\n              formattedDate: ud2.formatDateLite(startDate),\n              endDate,\n              content: fromYear + i,\n              current: fromYear + i === now2.getFullYear()\n            };\n          });\n          break;\n        }\n        case \"year\": {\n          fromYear = this.view.startDate.getFullYear();\n          cells = Array.apply(null, Array(12)).map((cell, i) => {\n            const startDate = new Date(fromYear, i, 1);\n            const endDate = new Date(fromYear, i + 1, 1);\n            endDate.setSeconds(-1);\n            return {\n              startDate,\n              formattedDate: ud2.formatDateLite(startDate),\n              endDate,\n              content: this.xsmall ? this.months[i].label.substr(0, 3) : this.months[i].label,\n              current: i === now2.getMonth() && fromYear === now2.getFullYear()\n            };\n          });\n          break;\n        }\n        case \"month\": {\n          const month = this.view.startDate.getMonth();\n          const firstCellDate = new Date(this.view.firstCellDate);\n          todayFound = false;\n          cells = Array.apply(null, Array(42)).map((cell, i) => {\n            const startDate = ud2.addDays(firstCellDate, i);\n            const endDate = new Date(startDate);\n            endDate.setHours(23, 59, 59, 0);\n            const isToday = !todayFound && ud2.isToday(startDate) && !todayFound++;\n            return {\n              startDate,\n              formattedDate: ud2.formatDateLite(startDate),\n              endDate,\n              content: startDate.getDate(),\n              today: isToday,\n              outOfScope: startDate.getMonth() !== month,\n              class: `vuecal__cell--day${startDate.getDay() || 7}`\n            };\n          });\n          if (this.hideWeekends || this.hideWeekdays.length) {\n            cells = cells.filter((cell) => {\n              const day = cell.startDate.getDay() || 7;\n              return !(this.hideWeekends && day >= 6 || this.hideWeekdays.length && this.hideWeekdays.includes(day));\n            });\n          }\n          break;\n        }\n        case \"week\": {\n          todayFound = false;\n          const firstDayOfWeek = this.view.startDate;\n          const weekDays = this.weekDays;\n          cells = weekDays.map((cell, i) => {\n            const startDate = ud2.addDays(firstDayOfWeek, this.startWeekOnSunday && this.hideWeekends ? i - 1 : i);\n            const endDate = new Date(startDate);\n            endDate.setHours(23, 59, 59, 0);\n            const dayOfWeek = (startDate.getDay() || 7) - 1;\n            return {\n              startDate,\n              formattedDate: ud2.formatDateLite(startDate),\n              endDate,\n              // To increase performance skip checking isToday if today already found.\n              today: !todayFound && ud2.isToday(startDate) && !todayFound++,\n              specialHours: this.specialDayHours[dayOfWeek] || []\n            };\n          }).filter((cell, i) => !weekDays[i].hide);\n          break;\n        }\n        case \"day\": {\n          const startDate = this.view.startDate;\n          const endDate = new Date(this.view.startDate);\n          endDate.setHours(23, 59, 59, 0);\n          const dayOfWeek = (startDate.getDay() || 7) - 1;\n          cells = [{\n            startDate,\n            formattedDate: ud2.formatDateLite(startDate),\n            endDate,\n            today: ud2.isToday(startDate),\n            specialHours: this.specialDayHours[dayOfWeek] || []\n          }];\n          break;\n        }\n      }\n      return cells;\n    },\n    // Only when hiding weekdays on month and week views.\n    visibleDaysCount() {\n      if (this.isDayView) return 1;\n      return 7 - this.weekDays.reduce((total, day) => total + day.hide, 0);\n    },\n    cellWidth() {\n      return 100 / this.visibleDaysCount;\n    },\n    cssClasses() {\n      const { resizeAnEvent, dragAnEvent, dragCreateAnEvent } = this.domEvents;\n      return {\n        [`vuecal--${this.view.id}-view`]: true,\n        [`vuecal--${this.locale}`]: this.locale,\n        \"vuecal--no-time\": !this.time,\n        \"vuecal--view-with-time\": this.hasTimeColumn,\n        \"vuecal--week-numbers\": this.showWeekNumbers && this.isMonthView,\n        \"vuecal--twelve-hour\": this.twelveHour,\n        \"vuecal--click-to-navigate\": this.clickToNavigate,\n        \"vuecal--hide-weekends\": this.hideWeekends,\n        \"vuecal--split-days\": this.hasSplits,\n        \"vuecal--sticky-split-labels\": this.hasSplits && this.stickySplitLabels,\n        \"vuecal--overflow-x\": this.minCellWidth && this.isWeekView || this.hasSplits && this.minSplitWidth,\n        \"vuecal--small\": this.small,\n        \"vuecal--xsmall\": this.xsmall,\n        \"vuecal--resizing-event\": resizeAnEvent._eid,\n        \"vuecal--drag-creating-event\": dragCreateAnEvent.event,\n        \"vuecal--dragging-event\": dragAnEvent._eid,\n        \"vuecal--events-on-month-view\": this.eventsOnMonthView,\n        \"vuecal--short-events\": this.isMonthView && this.eventsOnMonthView === \"short\",\n        \"vuecal--has-touch\": typeof window !== \"undefined\" && \"ontouchstart\" in window\n      };\n    },\n    isYearsOrYearView() {\n      return [\"years\", \"year\"].includes(this.view.id);\n    },\n    isYearsView() {\n      return this.view.id === \"years\";\n    },\n    isYearView() {\n      return this.view.id === \"year\";\n    },\n    isMonthView() {\n      return this.view.id === \"month\";\n    },\n    isWeekOrDayView() {\n      return [\"week\", \"day\"].includes(this.view.id);\n    },\n    isWeekView() {\n      return this.view.id === \"week\";\n    },\n    isDayView() {\n      return this.view.id === \"day\";\n    }\n  },\n  watch: {\n    events: {\n      // To be able to detect an event attribute change, it has to be first initialized with a value.\n      handler(events, oldEvents) {\n        this.updateMutableEvents(events);\n        this.addEventsToView();\n      },\n      deep: true\n    },\n    locale(locale) {\n      this.loadLocale(locale);\n    },\n    selectedDate(date) {\n      this.updateSelectedDate(date);\n    },\n    activeView(newVal) {\n      this.switchView(newVal);\n    }\n  }\n};\nconst index = /* @__PURE__ */ _export_sfc(_sfc_main, [[\"render\", render]]);\nexport {\n  index as default\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAI,YAAY,OAAO;AACvB,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU,gBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AAO7G,IAAM,uCAAuC,CAAC,MAAM,MAAM,SAAS;AACjE,QAAM,IAAI,KAAK,IAAI;AACnB,MAAI,GAAG;AACL,WAAO,OAAO,MAAM,aAAa,EAAE,IAAI,QAAQ,QAAQ,CAAC;AAAA,EAC1D;AACA,SAAO,IAAI,QAAQ,CAAC,GAAG,WAAW;AAChC,KAAC,OAAO,mBAAmB,aAAa,iBAAiB;AAAA,MACvD,OAAO;AAAA,QACL;AAAA,QACA,IAAI;AAAA,UACF,sCAAsC,QAAQ,KAAK,MAAM,GAAG,EAAE,WAAW,OAAO,oEAAoE;AAAA,QACtJ;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH;AACA,IAAI;AAAJ,IAAS;AAAT,IAAoB;AAApB,IAA4B;AAC5B,IAAI,cAAc,CAAC;AACnB,IAAI,cAAc,CAAC;AACnB,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,OAAO,eAAe,OAAO;AACvC,kBAAc,MAAM,SAAS,CAAC,CAAC;AAO/B,kBAAc,MAAM,iBAAiB,CAAC,SAAS,KAAK,SAAS,IAAI,KAAK,KAAK,WAAW,CAAC;AACvF,WAAO;AACP,SAAK,SAAS;AACd,QAAI,CAAC,gBAAgB,QAAQ,CAAC,KAAK,UAAU,QAAS,MAAK,oBAAoB;AAAA,EACjF;AAAA,EACA,sBAAsB;AACpB,SAAK,UAAU,UAAU,SAAS,MAAM;AACtC,aAAO,KAAK,QAAQ,MAAM,IAAI;AAAA,IAChC;AACA,SAAK,UAAU,eAAe,SAAS,MAAM;AAC3C,aAAO,KAAK,aAAa,MAAM,IAAI;AAAA,IACrC;AACA,SAAK,UAAU,WAAW,SAAS,OAAO;AACxC,aAAO,KAAK,SAAS,MAAM,KAAK;AAAA,IAClC;AACA,SAAK,UAAU,gBAAgB,SAAS,OAAO;AAC7C,aAAO,KAAK,cAAc,MAAM,KAAK;AAAA,IACvC;AACA,SAAK,UAAU,aAAa,SAAS,SAAS;AAC5C,aAAO,KAAK,WAAW,MAAM,OAAO;AAAA,IACtC;AACA,SAAK,UAAU,kBAAkB,SAAS,SAAS;AACjD,aAAO,KAAK,gBAAgB,MAAM,OAAO;AAAA,IAC3C;AACA,SAAK,UAAU,UAAU,WAAW;AAClC,aAAO,KAAK,QAAQ,IAAI;AAAA,IAC1B;AACA,SAAK,UAAU,UAAU,WAAW;AAClC,aAAO,KAAK,QAAQ,IAAI;AAAA,IAC1B;AACA,SAAK,UAAU,aAAa,WAAW;AACrC,aAAO,KAAK,WAAW,IAAI;AAAA,IAC7B;AACA,SAAK,UAAU,SAAS,SAAS,SAAS,cAAc;AACtD,aAAO,KAAK,WAAW,MAAM,MAAM;AAAA,IACrC;AACA,SAAK,UAAU,aAAa,SAAS,SAAS,SAAS;AACrD,aAAO,KAAK,WAAW,MAAM,MAAM;AAAA,IACrC;AAAA,EACF;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK,UAAU;AACtB,WAAO,KAAK,UAAU;AACtB,WAAO,KAAK,UAAU;AACtB,WAAO,KAAK,UAAU;AACtB,WAAO,KAAK,UAAU;AACtB,WAAO,KAAK,UAAU;AACtB,WAAO,KAAK,UAAU;AACtB,WAAO,KAAK,UAAU;AACtB,WAAO,KAAK,UAAU;AACtB,WAAO,KAAK,UAAU;AACtB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA,EAGA,kBAAkB;AAChB,QAAI,eAA+B,oBAAI,KAAK,GAAG,QAAQ,GAAG;AACxD,YAAsB,oBAAI,KAAK;AAC/B,kBAAY,IAAI,QAAQ;AACxB,eAAS,GAAG,IAAI,YAAY,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,IAAI,QAAQ,CAAC;AAAA,IAClE;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAGA,QAAQ,MAAM,MAAM;AAClB,UAAM,IAAI,IAAI,KAAK,KAAK,QAAQ,CAAC;AACjC,MAAE,QAAQ,EAAE,QAAQ,IAAI,IAAI;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,aAAa,MAAM,MAAM;AACvB,UAAM,IAAI,IAAI,KAAK,KAAK,QAAQ,CAAC;AACjC,MAAE,QAAQ,EAAE,QAAQ,IAAI,IAAI;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,SAAS,MAAM,OAAO;AACpB,UAAM,IAAI,IAAI,KAAK,KAAK,QAAQ,CAAC;AACjC,MAAE,SAAS,EAAE,SAAS,IAAI,KAAK;AAC/B,WAAO;AAAA,EACT;AAAA,EACA,cAAc,MAAM,OAAO;AACzB,UAAM,IAAI,IAAI,KAAK,KAAK,QAAQ,CAAC;AACjC,MAAE,SAAS,EAAE,SAAS,IAAI,KAAK;AAC/B,WAAO;AAAA,EACT;AAAA,EACA,WAAW,MAAM,SAAS;AACxB,UAAM,IAAI,IAAI,KAAK,KAAK,QAAQ,CAAC;AACjC,MAAE,WAAW,EAAE,WAAW,IAAI,OAAO;AACrC,WAAO;AAAA,EACT;AAAA,EACA,gBAAgB,MAAM,SAAS;AAC7B,UAAM,IAAI,IAAI,KAAK,KAAK,QAAQ,CAAC;AACjC,MAAE,WAAW,EAAE,WAAW,IAAI,OAAO;AACrC,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,MAAM;AACZ,UAAM,IAAI,IAAI,KAAK,KAAK,IAAI,KAAK,YAAY,GAAG,KAAK,SAAS,GAAG,KAAK,QAAQ,CAAC,CAAC;AAChF,UAAM,SAAS,EAAE,UAAU,KAAK;AAChC,MAAE,WAAW,EAAE,WAAW,IAAI,IAAI,MAAM;AACxC,UAAM,YAAY,IAAI,KAAK,KAAK,IAAI,EAAE,eAAe,GAAG,GAAG,CAAC,CAAC;AAC7D,WAAO,KAAK,OAAO,IAAI,aAAa,QAAQ,KAAK,CAAC;AAAA,EACpD;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,GAAG,KAAK,YAAY,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,KAAK,QAAQ,CAAC,OAAO,KAAK,gBAAgB;AAAA,EAC/F;AAAA,EACA,WAAW,MAAM;AACf,UAAM,OAAO,KAAK,YAAY;AAC9B,WAAO,EAAE,OAAO,QAAQ,OAAO,OAAO,EAAE,OAAO;AAAA,EACjD;AAAA;AAAA,EAEA,0BAA0B,OAAO,MAAM,oBAAoB;AACzD,UAAM,qBAAqB,QAAQ,IAAI,KAAK,KAAK,QAAQ,CAAC,KAAqB,oBAAI,KAAK;AACxF,UAAM,cAAc,qBAAqB,IAAI;AAC7C,uBAAmB,QAAQ,mBAAmB,QAAQ,KAAK,mBAAmB,OAAO,IAAI,eAAe,CAAC;AACzG,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,MAAM;AACjB,QAAI,gBAAgB,KAAM,QAAO;AACjC,QAAI,KAAK,WAAW,GAAI,SAAQ;AAChC,WAAO,IAAI,KAAK,KAAK,QAAQ,MAAM,GAAG,CAAC;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,UAAU,OAAO,KAAK;AACpB,QAAI,OAAO,UAAU,SAAU,SAAQ,MAAM,QAAQ,MAAM,GAAG;AAC9D,QAAI,OAAO,QAAQ,SAAU,OAAM,IAAI,QAAQ,MAAM,GAAG;AACxD,YAAQ,IAAI,KAAK,KAAK,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3C,UAAM,IAAI,KAAK,GAAG,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AACvC,UAAM,kBAAkB,IAAI,KAAK,GAAG,EAAE,kBAAkB,IAAI,IAAI,KAAK,KAAK,EAAE,kBAAkB,KAAK,KAAK;AACxG,WAAO,KAAK,MAAM,MAAM,QAAQ,mBAAmB,KAAK,OAAO,IAAI;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,OAAO,OAAO,UAAU;AAC1C,WAAO,KAAK,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,KAAK,WAAW,KAAK;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,WAAW,MAAM,SAAS,cAAc,QAAQ,MAAM;AACpD,QAAI,CAAC,MAAO,SAAQ,KAAK;AACzB,QAAI,CAAC,OAAQ,UAAS;AACtB,QAAI,WAAW,aAAc,QAAO,KAAK,eAAe,IAAI;AAC5D,kBAAc,CAAC;AACf,kBAAc,CAAC;AACf,UAAM,UAAU;AAAA,MACd,MAAM,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,MACjD,IAAI,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE,GAAG;AAAA,MAClD,GAAG,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,MAC9C,IAAI,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE,GAAG;AAAA,MAClD,KAAK,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE,IAAI;AAAA,MACpD,MAAM,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE,KAAK;AAAA,MACtD,OAAO,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE,MAAM;AAAA,MACxD,GAAG,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,MAC9C,IAAI,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE,GAAG;AAAA,MAClD,GAAG,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE,EAAE;AAAA,MAChD,GAAG,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,MAC9C,IAAI,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE,GAAG;AAAA,MAClD,KAAK,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE,IAAI;AAAA,MACpD,MAAM,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE,KAAK;AAAA,MACtD,IAAI,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,MAC/C,GAAG,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,MAC9C,IAAI,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,MAC/C,GAAG,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,MAC9C,IAAI,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,MAC/C,IAAI,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,MAC/C,IAAI,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,MAC/C,GAAG,MAAM,KAAK,mBAAmB,MAAM,KAAK,EAAE;AAAA,IAChD;AACA,WAAO,OAAO,QAAQ,8BAA8B,CAAC,GAAG,aAAa;AACnE,YAAM,SAAS,QAAQ,SAAS,QAAQ,UAAU,EAAE,CAAC;AACrD,aAAO,WAAW,SAAS,OAAO,IAAI;AAAA,IACxC,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,eAAe,MAAM;AACnB,UAAM,IAAI,KAAK,SAAS,IAAI;AAC5B,UAAM,IAAI,KAAK,QAAQ;AACvB,WAAO,GAAG,KAAK,YAAY,CAAC,IAAI,IAAI,KAAK,MAAM,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK,MAAM,EAAE,GAAG,CAAC;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,WAAW,MAAM,SAAS,SAAS,QAAQ,MAAM,QAAQ,OAAO;AAC9D,QAAI,cAAc;AAClB,QAAI,OAAO;AACT,YAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,SAAS,GAAG,KAAK,WAAW,GAAG,KAAK,WAAW,CAAC;AACxE,UAAI,IAAI,IAAI,MAAM,KAAK,KAAK,GAAI,eAAc;AAAA,IAChD;AACA,QAAI,gBAAgB,QAAQ,WAAW,QAAS,QAAO,cAAc,UAAU,KAAK,eAAe,IAAI;AACvG,kBAAc,CAAC;AACf,QAAI,CAAC,MAAO,SAAQ,KAAK;AACzB,UAAM,UAAU,KAAK,mBAAmB,MAAM,KAAK;AACnD,UAAM,YAAY,OAAO,QAAQ,8BAA8B,CAAC,GAAG,aAAa;AAC9E,YAAM,SAAS,QAAQ,SAAS,QAAQ,UAAU,EAAE,CAAC;AACrD,aAAO,WAAW,SAAS,SAAS;AAAA,IACtC,CAAC;AACD,WAAO,cAAc,UAAU,QAAQ,SAAS,OAAO,IAAI;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,MAAM;AACnB,UAAM,IAAI,KAAK,SAAS;AACxB,UAAM,IAAI,KAAK,WAAW;AAC1B,WAAO,IAAI,IAAI,KAAK,MAAM,MAAM,CAAC,KAAK,IAAI,KAAK,MAAM,MAAM,CAAC;AAAA,EAC9D;AAAA,EACA,KAAK,GAAG;AACN,QAAI,IAAI,KAAK,IAAI,GAAI,QAAO;AAC5B,YAAQ,IAAI,IAAI;AAAA,MACd,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,mBAAmB,MAAM,OAAO;AAC9B,QAAI,YAAY,EAAG,QAAO;AAC1B,UAAM,OAAO,KAAK,YAAY;AAC9B,UAAM,IAAI,KAAK,SAAS,IAAI;AAC5B,UAAM,IAAI,KAAK,QAAQ;AACvB,UAAM,MAAM,KAAK,OAAO;AACxB,UAAM,aAAa,MAAM,IAAI,KAAK;AAClC,kBAAc;AAAA;AAAA,MAEZ;AAAA;AAAA,MAEA,IAAI,MAAM,KAAK,SAAS,EAAE,UAAU,CAAC;AAAA;AAAA;AAAA,MAGrC;AAAA;AAAA,MAEA,IAAI,OAAO,IAAI,KAAK,MAAM,MAAM;AAAA;AAAA,MAEhC,KAAK,MAAM,MAAM,OAAO,IAAI,CAAC,EAAE,UAAU,GAAG,CAAC;AAAA;AAAA,MAE7C,MAAM,MAAM,MAAM,OAAO,IAAI,CAAC;AAAA;AAAA,MAE9B,OAAO,OAAO,MAAM,kBAAkB,MAAM,QAAQ,IAAI,CAAC;AAAA;AAAA;AAAA,MAGzD;AAAA;AAAA,MAEA,IAAI,OAAO,IAAI,KAAK,MAAM,MAAM;AAAA;AAAA,MAEhC,GAAG,MAAM,KAAK,KAAK,CAAC;AAAA;AAAA;AAAA,MAGpB,GAAG,YAAY;AAAA;AAAA,MAEf,IAAI,MAAM,MAAM,SAAS,SAAS,EAAE,CAAC;AAAA;AAAA,MAErC,KAAK,MAAM,MAAM,SAAS,SAAS,EAAE,OAAO,GAAG,CAAC;AAAA;AAAA,MAEhD,MAAM,MAAM,MAAM,SAAS,SAAS;AAAA;AAAA,IAEtC;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB,MAAM,OAAO;AAC9B,QAAI,YAAY,GAAI,QAAO;AAC3B,QAAI,GAAG;AACP,QAAI,gBAAgB,MAAM;AACxB,UAAI,KAAK,SAAS;AAClB,UAAI,KAAK,WAAW;AAAA,IACtB,OAAO;AACL,UAAI,KAAK,MAAM,OAAO,EAAE;AACxB,UAAI,KAAK,MAAM,OAAO,EAAE;AAAA,IAC1B;AACA,UAAM,IAAI,IAAI,KAAK,IAAI,KAAK;AAC5B,UAAM,MAAM,SAAS,EAAE,IAAI,MAAM,IAAI,KAAK,GAAG,MAAM,MAAM,IAAI,KAAK,OAAO,IAAI;AAC7E,kBAAc;AAAA,MACZ;AAAA,MACA;AAAA,MACA,KAAK,IAAI,KAAK,MAAM,MAAM;AAAA,MAC1B,KAAK,IAAI,KAAK,MAAM,MAAM;AAAA,MAC1B;AAAA,MACA,IAAI,GAAG,YAAY;AAAA,MACnB;AAAA,MACA,KAAK,IAAI,KAAK,MAAM,MAAM;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAAA;AAEF;AACA,IAAM,kBAAkB,KAAK;AAC7B,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,QAAQ;AAClB,kBAAc,MAAM,WAAW,IAAI;AAQnC,kBAAc,MAAM,cAAc,CAAC,QAAQ,OAAO,MAAM,UAAU;AAChE,WAAK,QAAQ,MAAM,cAAc,QAAQ,EAAE,MAAM,MAAM,IAAI,IAAI;AAC/D,UAAI,KAAK,QAAQ,mBAAmB,MAAO,MAAK,QAAQ,qBAAqB;AAAA,eACpE,KAAK,QAAQ,sBAAsB,kBAAkB,QAAQ;AACpE,aAAK,QAAQ,UAAU,YAAY;AACnC,mBAAW,MAAM,KAAK,QAAQ,UAAU,YAAY,OAAO,GAAG,KAAK,QAAQ,UAAU,YAAY,OAAO;AACxG,YAAI,KAAK,QAAQ,UAAU,YAAY,QAAQ,GAAG;AAChD,eAAK,QAAQ,UAAU,YAAY,OAAO;AAC1C,eAAK,QAAQ,qBAAqB;AAClC,eAAK,QAAQ,MAAM,iBAAiB,QAAQ,EAAE,MAAM,MAAM,IAAI,IAAI;AAAA,QACpE;AAAA,MACF;AAAA,IACF,CAAC;AAQD,kBAAc,MAAM,qBAAqB,CAAC,MAAM,UAAU;AACxD,WAAK,QAAQ,MAAM,uBAAuB,QAAQ,EAAE,MAAM,MAAM,IAAI,IAAI;AACxE,WAAK,QAAQ,qBAAqB;AAAA,IACpC,CAAC;AAQD,kBAAc,MAAM,eAAe,CAAC,MAAM;AACxC,YAAM,EAAE,MAAM,IAAI,IAAI,KAAK,QAAQ,QAAQ,sBAAsB;AACjE,YAAM,EAAE,SAAS,QAAQ,IAAI,kBAAkB,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI;AACpF,aAAO,EAAE,GAAG,UAAU,MAAM,GAAG,UAAU,IAAI;AAAA,IAC/C,CAAC;AAQD,kBAAc,MAAM,mBAAmB,CAAC,MAAM;AAC5C,UAAI,UAAU;AACd,UAAI,eAAe,EAAE,GAAG,GAAG,GAAG,EAAE;AAChC,YAAM,EAAE,UAAU,gBAAgB,SAAS,IAAI,KAAK,QAAQ;AAC5D,UAAI,OAAO,MAAM,SAAU,WAAU;AAAA,eAC5B,OAAO,MAAM,UAAU;AAC9B,uBAAe,KAAK,YAAY,CAAC;AACjC,kBAAU,KAAK,MAAM,aAAa,IAAI,WAAW,SAAS,cAAc,IAAI,QAAQ;AAAA,MACtF;AACA,aAAO,EAAE,SAAS,KAAK,IAAI,KAAK,IAAI,SAAS,eAAe,GAAG,CAAC,GAAG,aAAa;AAAA,IAClF,CAAC;AACD,SAAK,UAAU;AAAA,EACjB;AACF;AACA,IAAM,uBAAuB;AAC7B,IAAM,kBAAkB,KAAK;AAC7B,IAAI;AACJ,IAAI;AAAJ,IAAmB;AACnB,IAAM,aAAN,MAAiB;AAAA,EACf,YAAY,QAAQ,YAAY;AAC9B,kBAAc,MAAM,WAAW,IAAI;AACnC,kBAAc,MAAM,iBAAiB;AAAA,MACnC,MAAM;AAAA,MACN,OAAO;AAAA;AAAA,MAEP,kBAAkB;AAAA,MAClB,KAAK;AAAA;AAAA,MAEL,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,WAAW;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,MACV,eAAe;AAAA,MACf,WAAW;AAAA,MACX,UAAU;AAAA,MACV,WAAW;AAAA,MACX,UAAU;AAAA,MACV,gBAAgB;AAAA;AAAA,MAEhB,SAAS;AAAA,MACT,OAAO;AAAA,IACT,CAAC;AACD,SAAK,UAAU;AACf,SAAK;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,UAAU,UAAU,cAAc;AAC9C,QAAI,OAAO,aAAa,SAAU,YAAW,GAAG,aAAa,QAAQ;AACrE,QAAI,EAAE,oBAAoB,MAAO,QAAO;AACxC,UAAM,mBAAmB,GAAG,cAAc,QAAQ;AAClD,eAAW,WAAW,KAAK,uBAAuB;AAClD,UAAM,iBAAiB,mBAAmB;AAC1C,UAAM,MAAM,GAAG,WAAW,IAAI,KAAK,QAAQ,GAAG,QAAQ;AACtD,QAAI,aAAa,KAAK;AACpB,UAAI,OAAO,aAAa,QAAQ,SAAU,cAAa,MAAM,GAAG,aAAa,aAAa,GAAG;AAC7F,mBAAa,iBAAiB,GAAG,cAAc,aAAa,GAAG;AAAA,IACjE;AACA,UAAM,QAAQ;AAAA,MACZ,GAAG,KAAK;AAAA,MACR,MAAM,GAAG,KAAK,QAAQ,EAAE,GAAG,IAAI,KAAK,QAAQ,kBAAkB;AAAA,MAC9D,OAAO;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV,GAAG;AAAA,IACL;AACA,QAAI,OAAO,KAAK,QAAQ,kBAAkB,YAAY;AACpD,UAAI,CAAC,KAAK,QAAQ,cAAc,OAAO,MAAM,KAAK,cAAc,KAAK,CAAC,EAAG;AAAA,IAC3E;AACA,QAAI,MAAM,eAAe,MAAM,UAAU;AACvC,YAAM,YAAY,GAAG,UAAU,MAAM,OAAO,MAAM,GAAG;AAAA,IACvD;AACA,SAAK,QAAQ,cAAc,KAAK,KAAK;AACrC,SAAK,QAAQ,gBAAgB,CAAC,KAAK,CAAC;AACpC,SAAK,QAAQ,cAAc,gBAAgB,KAAK;AAChD,SAAK,QAAQ,MAAM,gBAAgB,EAAE,OAAO,KAAK,QAAQ,aAAa,KAAK,GAAG,eAAe,KAAK,CAAC;AACnG,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,GAAG;AACjB,QAAI,CAAC,EAAE,UAAU;AACf,QAAE,WAAW,CAAC;AACd,QAAE,SAAS,GAAG,eAAe,EAAE,KAAK,CAAC,IAAI;AAAA,QACvC,OAAO,EAAE;AAAA,QACT,kBAAkB,EAAE;AAAA,QACpB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,WAAW;AAAA,MACb;AAAA,IACF;AACA,UAAM,kBAAkB,EAAE,SAAS,GAAG,eAAe,EAAE,GAAG,CAAC;AAC3D,QAAI,iBAAiB;AACnB,sBAAgB,YAAY;AAC5B,sBAAgB,iBAAiB;AAAA,IACnC;AACA,UAAM,QAAQ,GAAG,QAAQ,EAAE,KAAK,CAAC;AACjC,UAAM,gBAAgB,GAAG,eAAe,KAAK;AAC7C,UAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,MAAE,SAAS,aAAa,IAAI;AAAA,MAC1B;AAAA,MACA,kBAAkB;AAAA,MAClB,gBAAgB,EAAE;AAAA,MAClB,YAAY;AAAA,MACZ,WAAW;AAAA,IACb;AACA,MAAE,MAAM,GAAG,WAAW,OAAO,EAAE,cAAc;AAC7C,MAAE,YAAY,OAAO,KAAK,EAAE,QAAQ,EAAE;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,GAAG;AACpB,QAAI,gBAAgB,OAAO,KAAK,EAAE,QAAQ,EAAE;AAC5C,QAAI,iBAAiB,EAAG,QAAO,GAAG,eAAe,EAAE,GAAG;AACtD,WAAO,EAAE,SAAS,GAAG,eAAe,EAAE,GAAG,CAAC;AAC1C;AACA,UAAM,MAAM,GAAG,aAAa,EAAE,KAAK,CAAC;AACpC,UAAM,gBAAgB,GAAG,eAAe,GAAG;AAC3C,UAAM,kBAAkB,EAAE,SAAS,aAAa;AAChD,QAAI,CAAC,cAAe,GAAE,WAAW;AAAA,aACxB,iBAAiB;AACxB,sBAAgB,YAAY;AAC5B,sBAAgB,iBAAiB,EAAE;AAAA,IACrC,MAAO;AACP,MAAE,YAAY,iBAAiB;AAC/B,MAAE,MAAM;AACR,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,oBAAoB,GAAG,eAAe,aAAa;AACjD,UAAM,qBAAqB,cAAc,QAAQ;AACjD,UAAM,mBAAmB,YAAY,QAAQ;AAC7C,QAAI,aAAa,EAAE,MAAM,QAAQ;AACjC,QAAI,WAAW,EAAE,IAAI,QAAQ;AAC7B,QAAI,0BAA0B;AAC9B,QAAI,WAAW,KAAK;AACpB,QAAI,CAAC,EAAE,IAAI,SAAS,KAAK,CAAC,EAAE,IAAI,WAAW,EAAG,aAAY;AAC1D,MAAE,WAAW,CAAC;AACd,QAAI,CAAC,EAAE,QAAQ;AACb,kBAAY,KAAK,IAAI,oBAAoB,UAAU;AACnD,YAAM,KAAK,IAAI,kBAAkB,QAAQ;AAAA,IAC3C,OAAO;AACL,kBAAY;AACZ,YAAM,KAAK;AAAA,QACT;AAAA,QACA,EAAE,OAAO,QAAQ,GAAG,aAAa,EAAE,OAAO,KAAK,EAAE,QAAQ,IAAI;AAAA,MAC/D;AAAA,IACF;AACA,WAAO,aAAa,KAAK;AACvB,UAAI,gBAAgB;AACpB,YAAM,eAAe,GAAG,QAAQ,IAAI,KAAK,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AAC3E,UAAI,YAAY,WAAW,OAAO;AAClC,UAAI,EAAE,QAAQ;AACZ,cAAM,UAAU,IAAI,KAAK,SAAS;AAClC,cAAM,mBAAmB,GAAG,eAAe,OAAO;AAClD,YAAI,2BAA2B,EAAE,eAAe,EAAE,YAAY,gBAAgB,GAAG;AAC/E,cAAI,CAAC,yBAAyB;AAC5B,yBAAa,EAAE,YAAY,gBAAgB,EAAE;AAC7C,mCAAuB,IAAI,KAAK,UAAU,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AAC/D,uBAAW,EAAE,YAAY,gBAAgB,EAAE;AAAA,UAC7C;AACA,oCAA0B;AAC1B,0BAAgB;AAAA,QAClB;AACA,qBAAa,cAAc;AAC3B,oBAAY,qBAAqB,GAAG,eAAe,IAAI,KAAK,QAAQ,CAAC;AACrE,gBAAQ,IAAI,KAAK,aAAa,aAAa,SAAS;AACpD,wBAAgB,GAAG,eAAe,KAAK;AACvC,YAAI,UAAW,2BAA0B;AAAA,MAC3C,OAAO;AACL,wBAAgB;AAChB,qBAAa,cAAc;AAC3B,oBAAY,QAAQ,YAAY,eAAe;AAC/C,gBAAQ,aAAa,EAAE,QAAQ,IAAI,KAAK,SAAS;AACjD,wBAAgB,GAAG,eAAe,aAAa,EAAE,QAAQ,KAAK;AAAA,MAChE;AACA,UAAI,eAAe;AACjB,UAAE,SAAS,aAAa,IAAI;AAAA,UAC1B;AAAA,UACA,kBAAkB,aAAa,EAAE,mBAAmB;AAAA,UACpD,gBAAgB,YAAY,EAAE,iBAAiB;AAAA,UAC/C;AAAA,UACA;AAAA,QACF;AAAA,MACF;AACA,kBAAY;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,OAAO;AACnB,SAAK,QAAQ,cAAc,gBAAgB,KAAK;AAChD,SAAK,QAAQ,gBAAgB,KAAK,QAAQ,cAAc,OAAO,CAAC,MAAM,EAAE,SAAS,MAAM,IAAI;AAC3F,SAAK,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO,OAAO,CAAC,MAAM,EAAE,SAAS,MAAM,IAAI;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,2BAA2B,YAAY,SAAS;AAC9C,uBAAmB,WAAW,MAAM,CAAC;AACrC,oBAAgB,CAAC;AACjB,eAAW,QAAQ,CAAC,MAAM;AACxB,uBAAiB,MAAM;AACvB,UAAI,CAAC,cAAc,EAAE,IAAI,EAAG,eAAc,EAAE,IAAI,IAAI,EAAE,UAAU,CAAC,GAAG,OAAO,EAAE,OAAO,UAAU,EAAE;AAChG,oBAAc,EAAE,IAAI,EAAE,WAAW;AACjC,uBAAiB,QAAQ,CAAC,OAAO;AAC/B,YAAI,CAAC,cAAc,GAAG,IAAI,EAAG,eAAc,GAAG,IAAI,IAAI,EAAE,UAAU,CAAC,GAAG,OAAO,GAAG,OAAO,UAAU,EAAE;AACnG,cAAM,iBAAiB,KAAK,aAAa,IAAI,EAAE,OAAO,EAAE,GAAG;AAC3D,cAAM,uBAAuB,QAAQ,sBAAsB,GAAG,oBAAoB,EAAE,OAAO,GAAG,OAAO,QAAQ,QAAQ,IAAI;AACzH,YAAI,CAAC,EAAE,cAAc,CAAC,EAAE,UAAU,CAAC,GAAG,cAAc,CAAC,GAAG,UAAU,kBAAkB,sBAAsB;AACxG,wBAAc,EAAE,IAAI,EAAE,SAAS,KAAK,GAAG,IAAI;AAC3C,wBAAc,EAAE,IAAI,EAAE,WAAW,CAAC,GAAG,IAAI,IAAI,cAAc,EAAE,IAAI,EAAE,QAAQ,CAAC;AAC5E,wBAAc,GAAG,IAAI,EAAE,SAAS,KAAK,EAAE,IAAI;AAC3C,wBAAc,GAAG,IAAI,EAAE,WAAW,CAAC,GAAG,IAAI,IAAI,cAAc,GAAG,IAAI,EAAE,QAAQ,CAAC;AAC9E,wBAAc,GAAG,IAAI,EAAE;AAAA,QACzB,OAAO;AACL,cAAI,MAAM;AACV,eAAK,QAAQ,cAAc,EAAE,IAAI,KAAK,EAAE,UAAU,CAAC,EAAE,GAAG,SAAS,QAAQ,GAAG,IAAI,KAAK,GAAI,eAAc,EAAE,IAAI,EAAE,SAAS,OAAO,MAAM,CAAC;AACtI,eAAK,QAAQ,cAAc,GAAG,IAAI,KAAK,EAAE,UAAU,CAAC,EAAE,GAAG,SAAS,QAAQ,EAAE,IAAI,KAAK,GAAI,eAAc,GAAG,IAAI,EAAE,SAAS,OAAO,MAAM,CAAC;AACvI,wBAAc,GAAG,IAAI,EAAE;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,QAAI,gBAAgB;AACpB,eAAW,MAAM,eAAe;AAC9B,YAAM,OAAO,cAAc,EAAE;AAC7B,YAAM,cAAc,KAAK,SAAS,IAAI,CAAC,SAAS,EAAE,IAAI,KAAK,OAAO,cAAc,GAAG,EAAE,MAAM,EAAE;AAC7F,kBAAY,KAAK,EAAE,IAAI,OAAO,KAAK,MAAM,CAAC;AAC1C,kBAAY,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,QAAQ,KAAK,EAAE,QAAQ,EAAE,QAAQ,IAAI,EAAE,KAAK,EAAE,KAAK,KAAK,CAAC;AAChG,WAAK,WAAW,YAAY,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;AACxD,sBAAgB,KAAK,IAAI,KAAK,kBAAkB,MAAM,aAAa,GAAG,aAAa;AAAA,IACrF;AACA,WAAO,CAAC,eAAe,aAAa;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,OAAO,eAAe,CAAC,GAAG;AAC1C,QAAI,SAAS,MAAM,SAAS,SAAS;AACrC,QAAI,mBAAmB,CAAC;AACxB,UAAM,SAAS,QAAQ,CAAC,OAAO;AAC7B,UAAI,CAAC,iBAAiB,SAAS,EAAE,GAAG;AAClC,cAAM,sBAAsB,MAAM,SAAS,OAAO,CAAC,QAAQ,QAAQ,EAAE;AACrE,4BAAoB,QAAQ,CAAC,QAAQ;AACnC,cAAI,CAAC,aAAa,GAAG,EAAE,SAAS,SAAS,EAAE,EAAG,kBAAiB,KAAK,GAAG;AAAA,QACzE,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,uBAAmB,CAAC,GAAG,IAAI,IAAI,gBAAgB,CAAC;AAChD,cAAU,iBAAiB;AAC3B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAAa,OAAO,OAAO,KAAK;AAC9B,QAAI,MAAM,UAAU,CAAC,KAAK,QAAQ,MAAM;AACtC,YAAM,kBAAkB,IAAI,KAAK,MAAM,KAAK,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AACjE,YAAM,gBAAgB,IAAI,KAAK,MAAM,GAAG,EAAE,SAAS,IAAI,IAAI,GAAG,CAAC;AAC/D,aAAO,iBAAiB,IAAI,KAAK,KAAK,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC,KAAK,mBAAmB,IAAI,KAAK,GAAG,EAAE,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,IACtH;AACA,UAAM,iBAAiB,MAAM,MAAM,QAAQ;AAC3C,UAAM,eAAe,MAAM,IAAI,QAAQ;AACvC,WAAO,iBAAiB,IAAI,QAAQ,KAAK,eAAe,MAAM,QAAQ;AAAA,EACxE;AACF;AACA,IAAM,eAAe,EAAE,OAAO,yCAAyC;AACvE,IAAM,eAAe,CAAC,SAAS;AAC/B,IAAM,eAAe;AAAA,EACnB,OAAO;AAAA,EACP,MAAM;AACR;AACA,IAAM,eAAe,EAAE,OAAO,OAAO;AACrC,IAAM,eAAe,EAAE,OAAO,QAAQ;AACtC,IAAM,eAAe,EAAE,OAAO,SAAS;AACvC,IAAM,eAAe,EAAE,KAAK,EAAE;AAC9B,IAAM,eAAe;AAAA,EACnB,KAAK;AAAA,EACL,OAAO;AAAA,EACP,MAAM;AACR;AACA,SAAS,SAAS,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC/D,SAAO,UAAU,GAAG,mBAAmB,OAAO,cAAc;AAAA,KACzD,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,SAAS,UAAU,CAAC,SAAS,MAAM;AACjG,aAAO,UAAU,GAAG,mBAAmB,UAAU,EAAE,KAAK,EAAE,GAAG;AAAA,QAC3D,CAAC,QAAQ,QAAQ,UAAU,GAAG,mBAAmB,OAAO;AAAA,UACtD,KAAK;AAAA,UACL,OAAO,eAAe,CAAC,gCAAgC,EAAE,OAAO,QAAQ,OAAO,WAAW,SAAS,sBAAsB,CAAC,CAAC;AAAA,UAC3H,OAAO,eAAe,SAAS,iBAAiB;AAAA,UAChD,SAAS,CAAC,WAAW,SAAS,KAAK,OAAO,UAAU,SAAS,WAAW,QAAQ,MAAM,MAAM;AAAA,UAC5F,YAAY,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,SAAS,KAAK,OAAO,UAAU,SAAS,OAAO,sBAAsB,OAAO,qBAAqB;AAAA,QACrJ,GAAG;AAAA,UACD,YAAY,YAAY;AAAA,YACtB,MAAM,eAAe,OAAO,mBAAmB;AAAA,YAC/C,QAAQ,SAAS,OAAO;AAAA,UAC1B,GAAG;AAAA,YACD,SAAS,QAAQ,MAAM;AAAA,eACpB,UAAU,GAAG,mBAAmB,OAAO;AAAA,gBACtC,OAAO;AAAA,gBACP,QAAQ;AAAA,gBACR,KAAK,SAAS,OAAO,cAAc,GAAG,CAAC,IAAI,QAAQ,UAAU,KAAK;AAAA,cACpE,GAAG;AAAA,gBACD,gBAAmB,OAAO,cAAc;AAAA,kBACtC,WAAW,KAAK,QAAQ,mBAAmB;AAAA,oBACzC,SAAS,SAAS,eAAe,OAAO;AAAA,oBACxC,MAAM,SAAS;AAAA,kBACjB,GAAG,MAAM;AAAA,oBACP,gBAAmB,QAAQ,cAAc,gBAAgB,QAAQ,IAAI,GAAG,CAAC;AAAA,oBACzE,gBAAmB,QAAQ,cAAc,gBAAgB,QAAQ,KAAK,GAAG,CAAC;AAAA,oBAC1E,gBAAmB,QAAQ,cAAc,gBAAgB,QAAQ,MAAM,GAAG,CAAC;AAAA,oBAC3E,QAAQ,cAAc,UAAU,GAAG,mBAAmB,QAAQ,cAAc,MAAM,gBAAgB,QAAQ,UAAU,GAAG,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,kBAC1J,CAAC;AAAA,gBACH,CAAC;AAAA,gBACD,SAAS,OAAO,aAAa,SAAS,OAAO,qBAAqB,UAAU,GAAG,mBAAmB,OAAO,cAAc;AAAA,mBACpH,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,SAAS,OAAO,WAAW,CAAC,OAAO,OAAO;AACxG,2BAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,sBAC5C,OAAO,eAAe,CAAC,oBAAoB,MAAM,SAAS,KAAK,CAAC;AAAA,sBAChE,KAAK;AAAA,oBACP,GAAG;AAAA,sBACD,WAAW,KAAK,QAAQ,eAAe;AAAA,wBACrC;AAAA,wBACA,MAAM,SAAS;AAAA,sBACjB,GAAG,MAAM;AAAA,wBACP,gBAAgB,gBAAgB,MAAM,KAAK,GAAG,CAAC;AAAA,sBACjD,CAAC;AAAA,oBACH,GAAG,CAAC;AAAA,kBACN,CAAC,GAAG,GAAG;AAAA,gBACT,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,cACnC,CAAC;AAAA,YACH,CAAC;AAAA,YACD,GAAG;AAAA,UACL,GAAG,MAAM,CAAC,QAAQ,QAAQ,CAAC;AAAA,QAC7B,GAAG,IAAI,YAAY,KAAK,mBAAmB,IAAI,IAAI;AAAA,MACrD,GAAG,EAAE;AAAA,IACP,CAAC,GAAG,GAAG;AAAA,EACT,CAAC;AACH;AACA,IAAM,cAAc,CAAC,KAAK,UAAU;AAClC,QAAM,SAAS,IAAI,aAAa;AAChC,aAAW,CAAC,KAAK,GAAG,KAAK,OAAO;AAC9B,WAAO,GAAG,IAAI;AAAA,EAChB;AACA,SAAO;AACT;AACA,IAAM,cAAc;AAAA,EAClB,QAAQ,CAAC,UAAU,SAAS,MAAM;AAAA,EAClC,OAAO;AAAA,IACL,qBAAqB,EAAE,MAAM,QAAQ,SAAS,QAAQ;AAAA,IACtD,UAAU,EAAE,MAAM,OAAO,SAAS,MAAM,CAAC,EAAE;AAAA,IAC3C,sBAAsB,EAAE,MAAM,UAAU,SAAS,MAAM;AAAA,IACvD,EAAE;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACP,WAAW,MAAM,UAAU;AACzB,UAAI,KAAK,QAAQ,MAAM,KAAK,KAAK,aAAa,QAAQ,GAAG;AACvD,aAAK,KAAK,eAAe;AAAA,MAC3B;AACA,WAAK,MAAM,KAAK,WAAW,OAAO,MAAM,QAAQ;AAAA,IAClD;AAAA,IACA,gBAAgB,CAAC,aAAa;AAAA,MAC5B,OAAO,QAAQ;AAAA,MACf,MAAM,QAAQ;AAAA,MACd,GAAG,QAAQ,QAAQ,EAAE,OAAO,QAAQ,MAAM,IAAI,CAAC;AAAA,IACjD;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,WAAW;AACT,UAAI,CAAC,CAAC,SAAS,MAAM,EAAE,SAAS,KAAK,KAAK,EAAE,EAAG,QAAO,CAAC;AACvD,UAAI,aAAa;AACjB,YAAM,WAAW,KAAK,SAAS,IAAI,CAAC,MAAM,MAAM;AAC9C,cAAM,OAAO,KAAK,MAAM,KAAK,QAAQ,KAAK,KAAK,WAAW,KAAK,OAAO,qBAAqB,KAAK,OAAO,eAAe,IAAI,IAAI,CAAC;AAC/H,eAAO;AAAA,UACL,MAAM,KAAK;AAAA,UACX,MAAM,KAAK;AAAA;AAAA;AAAA,UAGX,OAAO,KAAK,SAAS,KAAK,MAAM,OAAO,GAAG,CAAC;AAAA,UAC3C,QAAQ,KAAK,SAAS,KAAK,MAAM,OAAO,GAAG,CAAC;AAAA;AAAA,UAE5C,GAAG,KAAK,KAAK,OAAO,SAAS;AAAA,YAC3B,YAAY,KAAK,QAAQ;AAAA,YACzB;AAAA,YACA,OAAO,CAAC,cAAc,KAAK,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC;AAAA,UAC1D,IAAI,CAAC;AAAA,QACP;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AAAA,IACA,YAAY;AACV,aAAO,OAAO,IAAI,KAAK,SAAS,OAAO,CAAC,OAAO,QAAQ,QAAQ,IAAI,MAAM,CAAC;AAAA,IAC5E;AAAA,IACA,oBAAoB;AAClB,aAAO;AAAA,QACL,GAAG,KAAK,OAAO,aAAa,SAAS,EAAE,OAAO,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,wBAAwB;AACtB,aAAO,KAAK,KAAK,OAAO,WAAW,KAAK,OAAO,mBAAmB,KAAK,OAAO;AAAA,IAChF;AAAA,EACF;AACF;AACA,IAAM,mBAAmC,YAAY,aAAa,CAAC,CAAC,UAAU,QAAQ,CAAC,CAAC;AACxF,IAAM,eAAe,EAAE,OAAO,iBAAiB;AAC/C,IAAM,eAAe;AAAA,EACnB,KAAK;AAAA,EACL,OAAO;AAAA,EACP,MAAM;AAAA,EACN,cAAc;AAChB;AACA,IAAM,eAAe,CAAC,eAAe,eAAe,WAAW,YAAY;AAC3E,IAAM,eAAe;AAAA,EACnB,KAAK;AAAA,EACL,OAAO;AACT;AACA,IAAM,eAAe,CAAC,YAAY;AAClC,IAAM,eAAe;AAAA,EACnB,OAAO;AAAA,EACP,MAAM;AACR;AACA,IAAM,eAAe,CAAC,YAAY;AAClC,IAAM,eAAe;AAAA,EACnB,KAAK;AAAA,EACL,OAAO;AACT;AACA,SAAS,SAAS,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC/D,QAAM,+BAA+B,iBAAiB,mBAAmB;AACzE,SAAO,UAAU,GAAG,mBAAmB,OAAO,cAAc;AAAA,IAC1D,CAAC,OAAO,QAAQ,oBAAoB,UAAU,GAAG,mBAAmB,OAAO,cAAc;AAAA,OACtF,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,OAAO,UAAU,OAAO,CAAC,GAAG,OAAO;AACjG,eAAO,UAAU,GAAG,mBAAmB,UAAU,EAAE,KAAK,GAAG,GAAG;AAAA,UAC5D,EAAE,WAAW,UAAU,GAAG,mBAAmB,UAAU;AAAA,YACrD,KAAK;AAAA,YACL,OAAO,eAAe,CAAC,oBAAoB,EAAE,4BAA4B,SAAS,KAAK,OAAO,IAAI,iCAAiC,KAAK,uBAAuB,GAAG,CAAC,CAAC;AAAA,YACpK,MAAM;AAAA,YACN,aAAa,CAAC,WAAW,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,IAAI,sBAAsB,QAAQ,IAAI,KAAK,KAAK;AAAA,YAC5H,aAAa,CAAC,WAAW,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,IAAI,sBAAsB,QAAQ,IAAI,KAAK,KAAK;AAAA,YAC5H,SAAS,CAAC,WAAW,SAAS,WAAW,IAAI,MAAM,IAAI;AAAA,YACvD,cAAc,GAAG,EAAE,KAAK;AAAA,UAC1B,GAAG,gBAAgB,EAAE,KAAK,GAAG,IAAI,YAAY,KAAK,mBAAmB,IAAI,IAAI;AAAA,QAC/E,GAAG,EAAE;AAAA,MACP,CAAC,GAAG,GAAG;AAAA,IACT,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,IACjC,CAAC,OAAO,QAAQ,gBAAgB,UAAU,GAAG,mBAAmB,OAAO,cAAc;AAAA,MACnF,gBAAmB,UAAU;AAAA,QAC3B,OAAO,eAAe,CAAC,qCAAqC,EAAE,8BAA8B,KAAK,uBAAuB,WAAW,CAAC,CAAC;AAAA,QACrI,MAAM;AAAA,QACN,SAAS,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,IAAI,SAAS,SAAS,YAAY,SAAS,SAAS,GAAG,IAAI;AAAA,QAC9F,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,IAAI,sBAAsB,QAAQ,YAAY,KAAK,KAAK;AAAA,QAC9J,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,IAAI,sBAAsB,QAAQ,YAAY,KAAK,KAAK;AAAA,QAC9J,cAAc,YAAY,SAAS,KAAK,EAAE;AAAA,MAC5C,GAAG;AAAA,QACD,WAAW,KAAK,QAAQ,YAAY;AAAA,MACtC,GAAG,IAAI,YAAY;AAAA,MACnB,gBAAmB,OAAO,cAAc;AAAA,QACtC,YAAY,YAAY;AAAA,UACtB,MAAM,OAAO,QAAQ,cAAc,eAAe,SAAS,mBAAmB,KAAK;AAAA,QACrF,GAAG;AAAA,UACD,SAAS,QAAQ,MAAM;AAAA,aACpB,UAAU,GAAG,YAAY,wBAAwB,SAAS,cAAc,WAAW,MAAM,GAAG;AAAA,cAC3F,MAAM,CAAC,CAAC,SAAS,eAAe;AAAA,cAChC,KAAK,GAAG,SAAS,KAAK,EAAE,GAAG,SAAS,KAAK,UAAU,SAAS,CAAC;AAAA,cAC7D,SAAS,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,SAAS,eAAe,SAAS,oBAAoB;AAAA,cACtG,cAAc,CAAC,CAAC,SAAS,eAAe,SAAS,SAAS,WAAW;AAAA,YACvE,GAAG;AAAA,cACD,SAAS,QAAQ,MAAM;AAAA,gBACrB,WAAW,KAAK,QAAQ,OAAO;AAAA,cACjC,CAAC;AAAA,cACD,GAAG;AAAA,YACL,GAAG,GAAG,CAAC,QAAQ,YAAY,CAAC;AAAA,UAC9B,CAAC;AAAA,UACD,GAAG;AAAA,QACL,GAAG,GAAG,CAAC,MAAM,CAAC;AAAA,MAChB,CAAC;AAAA,MACD,OAAO,QAAQ,eAAe,UAAU,GAAG,mBAAmB,UAAU;AAAA,QACtE,KAAK;AAAA,QACL,OAAO,eAAe,CAAC,qBAAqB,EAAE,kCAAkC,KAAK,uBAAuB,QAAQ,CAAC,CAAC;AAAA,QACtH,MAAM;AAAA,QACN,SAAS,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,IAAI,SAAS,SAAS,aAAa,SAAS,UAAU,GAAG,IAAI;AAAA,QAChG,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,IAAI,sBAAsB,QAAQ,SAAS,KAAK,KAAK;AAAA,QAC3J,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,IAAI,sBAAsB,QAAQ,SAAS,KAAK,KAAK;AAAA,QAC3J,cAAc;AAAA,MAChB,GAAG;AAAA,QACD,WAAW,KAAK,QAAQ,cAAc;AAAA,MACxC,GAAG,EAAE,KAAK,mBAAmB,IAAI,IAAI;AAAA,MACrC,gBAAmB,UAAU;AAAA,QAC3B,OAAO,eAAe,CAAC,qCAAqC,EAAE,8BAA8B,KAAK,uBAAuB,OAAO,CAAC,CAAC;AAAA,QACjI,MAAM;AAAA,QACN,SAAS,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,IAAI,SAAS,SAAS,QAAQ,SAAS,KAAK,GAAG,IAAI;AAAA,QACtF,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,IAAI,sBAAsB,QAAQ,QAAQ,KAAK,KAAK;AAAA,QAC1J,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,IAAI,sBAAsB,QAAQ,QAAQ,KAAK,KAAK;AAAA,QAC1J,cAAc,QAAQ,SAAS,KAAK,EAAE;AAAA,MACxC,GAAG;AAAA,QACD,WAAW,KAAK,QAAQ,YAAY;AAAA,MACtC,GAAG,IAAI,YAAY;AAAA,IACrB,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,IACjC,OAAO,UAAU,oBAAoB,UAAU,GAAG,YAAY,8BAA8B;AAAA,MAC1F,KAAK;AAAA,MACL,aAAa,OAAO;AAAA,MACpB,wBAAwB,SAAS;AAAA,MACjC,2BAA2B,OAAO;AAAA,IACpC,GAAG,YAAY,EAAE,GAAG,EAAE,GAAG;AAAA,MACvB,KAAK,OAAO,iBAAiB,IAAI;AAAA,QAC/B,MAAM;AAAA,QACN,IAAI,QAAQ,CAAC,EAAE,SAAS,KAAK,MAAM;AAAA,UACjC,WAAW,KAAK,QAAQ,mBAAmB;AAAA,YACzC;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,QACD,KAAK;AAAA,MACP,IAAI;AAAA,MACJ,KAAK,OAAO,aAAa,IAAI;AAAA,QAC3B,MAAM;AAAA,QACN,IAAI,QAAQ,CAAC,EAAE,MAAM,MAAM;AAAA,UACzB,WAAW,KAAK,QAAQ,eAAe;AAAA,YACrC;AAAA,YACA,MAAM,SAAS;AAAA,UACjB,CAAC;AAAA,QACH,CAAC;AAAA,QACD,KAAK;AAAA,MACP,IAAI;AAAA,IACN,CAAC,GAAG,MAAM,CAAC,aAAa,wBAAwB,yBAAyB,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,IAC1G,YAAY,YAAY;AAAA,MACtB,MAAM,eAAe,SAAS,mBAAmB;AAAA,IACnD,GAAG;AAAA,MACD,SAAS,QAAQ,MAAM;AAAA,QACrB,SAAS,iBAAiB,UAAU,GAAG,mBAAmB,OAAO,cAAc;AAAA,WAC5E,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,OAAO,WAAW,CAAC,OAAO,MAAM;AAC9F,mBAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,cAC5C,OAAO,eAAe,CAAC,oBAAoB,MAAM,SAAS,KAAK,CAAC;AAAA,cAChE,KAAK;AAAA,YACP,GAAG;AAAA,cACD,WAAW,KAAK,QAAQ,eAAe;AAAA,gBACrC;AAAA,gBACA,MAAM,SAAS,KAAK;AAAA,cACtB,GAAG,MAAM;AAAA,gBACP,gBAAgB,gBAAgB,MAAM,KAAK,GAAG,CAAC;AAAA,cACjD,CAAC;AAAA,YACH,GAAG,CAAC;AAAA,UACN,CAAC,GAAG,GAAG;AAAA,QACT,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,MACnC,CAAC;AAAA,MACD,GAAG;AAAA,IACL,GAAG,GAAG,CAAC,MAAM,CAAC;AAAA,EAChB,CAAC;AACH;AACA,IAAM,cAAc;AAAA,EAClB,QAAQ,CAAC,UAAU,YAAY,QAAQ,cAAc,sBAAsB,WAAW,MAAM;AAAA,EAC5F,YAAY,EAAE,iBAAiB;AAAA,EAC/B,OAAO;AAAA;AAAA,IAEL,SAAS,EAAE,MAAM,QAAQ,SAAS,OAAO,CAAC,GAAG;AAAA,IAC7C,YAAY,EAAE,MAAM,QAAQ,UAAU,KAAK;AAAA,IAC3C,WAAW,EAAE,MAAM,CAAC,SAAS,MAAM,GAAG,SAAS,MAAM;AAAA,IACrD,WAAW,EAAE,MAAM,OAAO,SAAS,MAAM,CAAC,EAAE;AAAA,IAC5C,WAAW,EAAE,MAAM,QAAQ,SAAS,OAAO,CAAC,GAAG;AAAA,IAC/C,UAAU,EAAE,MAAM,OAAO,SAAS,MAAM,CAAC,EAAE;AAAA,IAC3C,sBAAsB,EAAE,MAAM,UAAU,SAAS,MAAM;AAAA,IACvD,EAAE;AAAA,EACJ;AAAA,EACA,MAAM,OAAO;AAAA,IACX,oBAAoB;AAAA,EACtB;AAAA,EACA,SAAS;AAAA,IACP,YAAY;AACV,WAAK,mBAAmB,IAAI,MAAsB,oBAAI,KAAK,GAAG,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AAAA,IACrF;AAAA,IACA,sBAAsB;AACpB,WAAK,sBAAsB;AAC3B,UAAI,KAAK,YAAa,MAAK,WAAW,KAAK,WAAW;AAAA,IACxD;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,qBAAqB;AAAA,MACnB,MAAM;AACJ,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA,MACA,IAAI,WAAW;AACb,aAAK,OAAO,sBAAsB;AAAA,MACpC;AAAA,IACF;AAAA,IACA,cAAc;AACZ,YAAM,EAAE,aAAa,IAAI,KAAK;AAC9B,aAAO,aAAa,aAAa,QAAQ,KAAK,KAAK,EAAE,IAAI,CAAC;AAAA,IAC5D;AAAA,IACA,gBAAgB;AACd,aAAO,KAAK,KAAK,OAAO,SAAS,KAAK,aAAa,KAAK,QAAQ,qBAAqB,CAAC,KAAK,QAAQ;AAAA,IACrG;AAAA;AAAA,IAEA,MAAM;AACJ,aAAO,KAAK,QAAQ;AAAA,IACtB;AAAA,EACF;AACF;AACA,IAAM,SAAyB,YAAY,aAAa,CAAC,CAAC,UAAU,QAAQ,CAAC,CAAC;AAC9E,IAAM,eAAe,CAAC,WAAW;AACjC,SAAS,SAAS,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC/D,SAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,IAC5C,OAAO,eAAe,CAAC,iBAAiB,SAAS,YAAY,CAAC;AAAA,IAC9D,OAAO,eAAe,SAAS,WAAW;AAAA,IAC1C,UAAU;AAAA,IACV,SAAS,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,IAAI,SAAS,SAAS,cAAc,SAAS,WAAW,GAAG,IAAI;AAAA,IAClG,YAAY,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,SAAS,cAAc,IAAI,SAAS,SAAS,mBAAmB,SAAS,gBAAgB,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC;AAAA,IAC7J,cAAc,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,IAAI,SAAS,SAAS,gBAAgB,SAAS,aAAa,GAAG,IAAI;AAAA,IAC3G,cAAc,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,IAAI,SAAS,SAAS,gBAAgB,SAAS,aAAa,GAAG,IAAI;AAAA,IAC3G,cAAc,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,cAAc,IAAI,SAAS,SAAS,gBAAgB,SAAS,aAAa,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC;AAAA,IACpI,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,SAAS,YAAY,MAAM;AAAA,IAC9E,WAAW,OAAO,EAAE,MAAM,OAAO,EAAE,IAAI,IAAI,SAAS,SAAS,aAAa,SAAS,UAAU,GAAG,IAAI;AAAA,IACpG,YAAY,OAAO,EAAE,MAAM,OAAO,EAAE,IAAI,IAAI,SAAS,SAAS,aAAa,SAAS,UAAU,GAAG,IAAI;AAAA,IACrG,aAAa,OAAO,EAAE,MAAM,OAAO,EAAE,IAAI,IAAI,SAAS,SAAS,eAAe,SAAS,YAAY,GAAG,IAAI;AAAA,IAC1G,YAAY,OAAO,EAAE,MAAM,OAAO,EAAE,IAAI,IAAI,SAAS,SAAS,cAAc,SAAS,WAAW,GAAG,IAAI;AAAA,IACvG,WAAW,SAAS;AAAA,IACpB,aAAa,OAAO,EAAE,MAAM,OAAO,EAAE,IAAI,CAAC,WAAW,SAAS,aAAa,SAAS,YAAY,MAAM;AAAA,IACtG,WAAW,OAAO,EAAE,MAAM,OAAO,EAAE,IAAI,CAAC,WAAW,SAAS,aAAa,SAAS,UAAU;AAAA,EAC9F,GAAG;AAAA,IACD,SAAS,OAAO,WAAW,UAAU,OAAO,MAAM,aAAa,UAAU,GAAG,mBAAmB,OAAO;AAAA,MACpG,KAAK;AAAA,MACL,OAAO;AAAA,MACP,SAAS,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,cAAc,IAAI,SAAS,SAAS,eAAe,SAAS,YAAY,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC;AAAA,MAC7H,cAAc,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,cAAc,IAAI,SAAS,SAAS,oBAAoB,SAAS,iBAAiB,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC;AAAA,IAC9I,GAAG,gBAAgB,SAAS,OAAO,MAAM,WAAW,GAAG,EAAE,KAAK,mBAAmB,IAAI,IAAI;AAAA,IACzF,WAAW,KAAK,QAAQ,SAAS;AAAA,MAC/B,OAAO,OAAO;AAAA,MACd,MAAM,SAAS,KAAK;AAAA,IACtB,CAAC;AAAA,IACD,SAAS,aAAa,UAAU,GAAG,mBAAmB,OAAO;AAAA,MAC3D,KAAK;AAAA,MACL,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,cAAc,IAAI,SAAS,SAAS,2BAA2B,SAAS,wBAAwB,GAAG,IAAI,GAAG,CAAC,QAAQ,SAAS,CAAC;AAAA,MACpK,cAAc,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,cAAc,IAAI,SAAS,SAAS,2BAA2B,SAAS,wBAAwB,GAAG,IAAI,GAAG,CAAC,QAAQ,SAAS,CAAC;AAAA,IACvK,GAAG,MAAM,EAAE,KAAK,mBAAmB,IAAI,IAAI;AAAA,EAC7C,GAAG,IAAI,YAAY;AACrB;AACA,IAAM,cAAc;AAAA,EAClB,QAAQ,CAAC,UAAU,SAAS,WAAW,QAAQ,aAAa,YAAY;AAAA,EACxE,OAAO;AAAA,IACL,mBAAmB,EAAE,MAAM,QAAQ,SAAS,GAAG;AAAA,IAC/C,OAAO,EAAE,MAAM,QAAQ,SAAS,OAAO,CAAC,GAAG;AAAA,IAC3C,YAAY,EAAE,MAAM,OAAO,SAAS,MAAM,CAAC,EAAE;AAAA,IAC7C,UAAU,EAAE,MAAM,OAAO,SAAS,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA,IAG3C,eAAe,EAAE,MAAM,QAAQ,SAAS,EAAE;AAAA,IAC1C,gBAAgB,EAAE,MAAM,QAAQ,SAAS,EAAE;AAAA,IAC3C,QAAQ,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA;AAAA,EAE1C;AAAA,EACA,MAAM,OAAO;AAAA;AAAA,IAEX,OAAO;AAAA,MACL,eAAe;AAAA;AAAA,MAEf,QAAQ;AAAA,MACR,QAAQ;AAAA;AAAA;AAAA,MAGR,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA,IAKP,YAAY,GAAG,QAAQ,OAAO;AAC5B,UAAI,kBAAkB,UAAU,CAAC,MAAO,QAAO;AAC/C,YAAM,EAAE,kBAAkB,cAAc,eAAe,YAAY,IAAI,KAAK;AAC5E,UAAI,aAAa,SAAS,KAAK,MAAM,QAAQ,iBAAiB,SAAS,KAAK,MAAM,MAAM;AACtF,eAAO;AAAA,MACT;AACA,WAAK,WAAW;AAChB,uBAAiB,OAAO;AACxB,UAAI,KAAK,OAAO,WAAW,UAAU,KAAK,MAAM,WAAW;AACzD,yBAAiB,YAAY,WAAW,MAAM;AAC5C,cAAI,CAAC,cAAc,QAAQ,CAAC,YAAY,MAAM;AAC5C,6BAAiB,OAAO,KAAK,MAAM;AACnC,iBAAK,MAAM,WAAW;AAAA,UACxB;AAAA,QACF,GAAG,iBAAiB,OAAO;AAAA,MAC7B;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,UAAU,GAAG;AACX,UAAI,KAAK,UAAU,aAAa,SAAS,KAAK,MAAM,QAAQ,CAAC,KAAK,MAAM,SAAS;AAC/E,aAAK,UAAU,aAAa,WAAW;AAAA,MACzC;AACA,WAAK,MAAM,UAAU;AAAA,IACvB;AAAA,IACA,aAAa,GAAG;AACd,QAAE,eAAe;AACjB,WAAK,OAAO,cAAc,qBAAqB,KAAK,KAAK;AAAA,IAC3D;AAAA,IACA,aAAa,GAAG;AACd,QAAE,eAAe;AACjB,WAAK,OAAO,cAAc,qBAAqB,KAAK,KAAK;AAAA,IAC3D;AAAA;AAAA,IAEA,YAAY,GAAG;AACb,UAAI,OAAO,KAAK,OAAO,iBAAiB,WAAY;AACpD,YAAM,EAAE,SAAS,QAAQ,IAAI,EAAE,QAAQ,CAAC;AACxC,YAAM,EAAE,QAAQ,QAAQ,cAAc,IAAI,KAAK;AAC/C,UAAI,KAAK,IAAI,UAAU,MAAM,IAAI,iBAAiB,KAAK,IAAI,UAAU,MAAM,IAAI,eAAe;AAC5F,aAAK,MAAM,UAAU;AAAA,MACvB;AAAA,IACF;AAAA,IACA,aAAa,GAAG;AACd,WAAK,MAAM,SAAS,EAAE,QAAQ,CAAC,EAAE;AACjC,WAAK,MAAM,SAAS,EAAE,QAAQ,CAAC,EAAE;AACjC,WAAK,YAAY,GAAG,IAAI;AAAA,IAC1B;AAAA,IACA,gBAAgB,GAAG;AACjB,UAAI,OAAO,KAAK,OAAO,iBAAiB,WAAY,QAAO,KAAK,OAAO,aAAa,KAAK,OAAO,CAAC;AAAA,IACnG;AAAA,IACA,WAAW,GAAG;AACZ,UAAI,OAAO,KAAK,OAAO,oBAAoB,WAAY,QAAO,KAAK,OAAO,gBAAgB,KAAK,OAAO,CAAC;AAAA,IACzG;AAAA,IACA,YAAY,GAAG;AACb,WAAK,OAAO,KAAK,IAAI,eAAe,GAAG,KAAK,KAAK;AAAA,IACnD;AAAA,IACA,YAAY;AACV,WAAK,OAAO,KAAK,IAAI,aAAa,KAAK,KAAK;AAAA,IAC9C;AAAA,IACA,0BAA0B;AACxB,WAAK,WAAW;AAChB,WAAK,UAAU,YAAY,OAAO;AAClC,WAAK,UAAU,gBAAgB,OAAO,OAAO,KAAK,UAAU,eAAe;AAAA,QACzE,MAAM,KAAK,MAAM;AAAA,QACjB,QAAQ,KAAK,WAAW,KAAK,OAAO;AAAA,QACpC,OAAO,KAAK,MAAM,SAAS;AAAA,QAC3B,SAAS,CAAC,CAAC,KAAK,WAAW,KAAK,MAAM,KAAK,eAAe,KAAK,QAAQ,KAAK;AAAA,QAC5E,aAAa,IAAI,MAAM,KAAK,WAAW,KAAK,OAAO,GAAG;AAAA,QACtD,wBAAwB,KAAK,MAAM;AAAA,MACrC,CAAC;AACD,WAAK,MAAM,WAAW;AAAA,IACxB;AAAA,IACA,YAAY,QAAQ,OAAO;AACzB,UAAI,kBAAkB,UAAU,CAAC,MAAO,QAAO;AAC/C,WAAK,MAAM,MAAM,cAAc,KAAK,KAAK;AAAA,IAC3C;AAAA,IACA,iBAAiB,OAAO;AACtB,WAAK,YAAY,IAAI;AAAA,IACvB;AAAA,IACA,oBAAoB;AAClB,WAAK,MAAM,WAAW;AAAA,IACxB;AAAA,IACA,aAAa;AACX,YAAM,EAAE,aAAa,IAAI,KAAK;AAC9B,YAAM,eAAe,aAAa;AAClC,UAAI,iBAAiB,KAAK,MAAM,KAAM;AAAA,eAC7B,cAAc;AACrB,cAAM,QAAQ,KAAK,KAAK,OAAO,KAAK,CAAC,MAAM,EAAE,SAAS,YAAY;AAClE,YAAI,MAAO,OAAM,UAAU;AAAA,MAC7B;AACA,WAAK,OAAO,aAAa;AACzB,WAAK,OAAO,cAAc,eAAe,KAAK,KAAK;AACnD,mBAAa,OAAO,KAAK,MAAM;AAC/B,WAAK,MAAM,UAAU;AAAA,IACvB;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,kBAAkB;AAChB,YAAM,EAAE,kBAAkB,eAAe,IAAI,KAAK,WAAW,KAAK;AAClE,UAAI,iBAAiB,mBAAmB,KAAK,OAAO;AACpD,YAAM,MAAM,KAAK,IAAI,KAAK,MAAM,iBAAiB,KAAK,OAAO,iBAAiB,KAAK,OAAO,QAAQ,GAAG,CAAC;AACtG,uBAAiB,KAAK,IAAI,gBAAgB,KAAK,OAAO,MAAM,IAAI,KAAK,OAAO;AAC5E,YAAM,SAAS,KAAK,MAAM,iBAAiB,KAAK,OAAO,iBAAiB,KAAK,OAAO,QAAQ;AAC5F,YAAM,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC;AACvC,aAAO,EAAE,KAAK,OAAO;AAAA,IACvB;AAAA,IACA,cAAc;AACZ,UAAI,KAAK,MAAM,UAAU,CAAC,KAAK,OAAO,QAAQ,CAAC,KAAK,MAAM,kBAAkB,KAAK,KAAK,OAAO,WAAW,KAAK,OAAQ,QAAO,CAAC;AAC7H,UAAI,QAAQ,MAAM,KAAK,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,cAAc;AACxE,UAAI,OAAO,OAAO,KAAK,SAAS,SAAS,KAAK,KAAK;AACnD,UAAI,KAAK,OAAO,iBAAiB,QAAQ,KAAK,OAAO,eAAe;AAClE,gBAAQ,KAAK,OAAO;AACpB,gBAAQ,MAAM,KAAK,OAAO,iBAAiB,KAAK,SAAS,SAAS,KAAK;AAAA,MACzE;AACA,YAAM,EAAE,KAAK,OAAO,IAAI,KAAK;AAC7B,aAAO;AAAA,QACL,KAAK,GAAG,GAAG;AAAA,QACX,QAAQ,GAAG,MAAM;AAAA,QACjB,OAAO,GAAG,KAAK;AAAA,QACf,MAAM,KAAK,MAAM,QAAQ,GAAG,KAAK,MAAM,IAAI,QAAQ,GAAG,IAAI;AAAA,MAC5D;AAAA,IACF;AAAA;AAAA,IAEA,eAAe;AACb,YAAM,EAAE,YAAY,UAAU,IAAI,KAAK,WAAW,CAAC;AACnD,aAAO;AAAA,QACL,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,MAAM;AAAA,QACjC,wBAAwB,KAAK,MAAM;AAAA,QACnC,2BAA2B,KAAK,MAAM;AAAA,QACtC,6BAA6B,KAAK,MAAM;AAAA,QACxC,4BAA4B,KAAK,MAAM;AAAA,QACvC,0BAA0B,KAAK,MAAM;AAAA;AAAA,QAErC,2BAA2B,CAAC,KAAK,MAAM,kBAAkB,KAAK,MAAM;AAAA;AAAA;AAAA;AAAA,QAIpE,yBAAyB,KAAK,MAAM,YAAY,KAAK,MAAM;AAAA;AAAA,QAE3D,gCAAgC,CAAC,CAAC,KAAK;AAAA,QACvC,eAAe,KAAK,WAAW,cAAc,CAAC;AAAA,QAC9C,gBAAgB,KAAK,WAAW,CAAC,cAAc,CAAC;AAAA,QAChD,aAAa,KAAK,WAAW,aAAa,CAAC;AAAA,MAC7C;AAAA,IACF;AAAA;AAAA,IAEA,UAAU;AACR,aAAO,KAAK,MAAM,YAAY,KAAK,MAAM,SAAS,KAAK,iBAAiB,KAAK;AAAA,IAC/E;AAAA,IACA,YAAY;AACV,YAAM,EAAE,WAAW,YAAY,UAAU,IAAI,KAAK;AAClD,aAAO,KAAK,OAAO,WAAW,QAAQ,aAAa,CAAC,cAAc,cAAc;AAAA,IAClF;AAAA,IACA,YAAY;AACV,YAAM,EAAE,YAAY,KAAK,IAAI,KAAK;AAClC,aAAO,WAAW,UAAU,KAAK,MAAM,aAAa,QAAQ,CAAC,KAAK,WAAW,CAAC,KAAK,WAAW,KAAK,WAAW,KAAK,QAAQ,cAAc,KAAK,KAAK,OAAO;AAAA,IAC5J;AAAA;AAAA,IAEA,MAAM;AACJ,aAAO,KAAK,QAAQ;AAAA,IACtB;AAAA,EACF;AACF;AACA,IAAM,QAAwB,YAAY,aAAa,CAAC,CAAC,UAAU,QAAQ,CAAC,CAAC;AAC7E,IAAM,eAAe,CAAC,cAAc,cAAc,gBAAgB,eAAe,cAAc,QAAQ;AACvG,IAAM,eAAe;AAAA,EACnB,KAAK;AAAA,EACL,OAAO;AACT;AACA,IAAM,eAAe,CAAC,WAAW;AACjC,IAAM,eAAe;AAAA,EACnB,KAAK;AAAA,EACL,OAAO;AACT;AACA,IAAM,eAAe,CAAC,OAAO;AAC7B,SAAS,SAAS,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC/D,QAAM,mBAAmB,iBAAiB,OAAO;AACjD,SAAO,UAAU,GAAG,YAAY,iBAAiB;AAAA,IAC/C,OAAO,eAAe,CAAC,gBAAgB,SAAS,WAAW,CAAC;AAAA,IAC5D,MAAM,eAAe,SAAS,mBAAmB;AAAA,IACjD,KAAK;AAAA,IACL,QAAQ,OAAO,QAAQ;AAAA,IACvB,OAAO,eAAe,SAAS,UAAU;AAAA,EAC3C,GAAG;AAAA,IACD,SAAS,QAAQ,MAAM;AAAA,OACpB,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,SAAS,cAAc,SAAS,SAAS,GAAG,CAAC,OAAO,MAAM;AACxH,eAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,UAC5C,OAAO,eAAe,CAAC,qCAAqC,SAAS,eAAe,SAAS,aAAa,KAAK,CAAC,CAAC;AAAA,UACjH,KAAK,OAAO,QAAQ,cAAc,GAAG,SAAS,KAAK,EAAE,IAAI,OAAO,KAAK,OAAO,IAAI,CAAC,KAAK;AAAA,UACtF,cAAc,SAAS,cAAc,MAAM,KAAK;AAAA,UAChD,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,cAAc,OAAO,KAAK;AAAA,UAC1B,SAAS,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,SAAS,YAAY,MAAM;AAAA,UAC1E,YAAY,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,SAAS,CAAC,WAAW,SAAS,oBAAoB,MAAM,GAAG,CAAC,OAAO,CAAC;AAAA,UAC1G,cAAc,CAAC,WAAW,CAAC,SAAS,cAAc,SAAS,iBAAiB,QAAQ,SAAS,cAAc,MAAM,KAAK,IAAI;AAAA,UAC1H,aAAa,CAAC,WAAW,CAAC,SAAS,cAAc,SAAS,gBAAgB,QAAQ,SAAS,cAAc,MAAM,KAAK,IAAI;AAAA,UACxH,SAAS,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,cAAc,SAAS,YAAY,MAAM;AAAA,UAClG,YAAY,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,cAAc,SAAS,eAAe,MAAM;AAAA,UACxG,eAAe,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,cAAc,OAAO,QAAQ,mBAAmB,SAAS,kBAAkB,MAAM;AAAA,UAChJ,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,cAAc,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,IAAI,cAAc,QAAQ,KAAK,OAAO,OAAO,KAAK,SAAS;AAAA,UACzL,YAAY,CAAC,WAAW,CAAC,SAAS,cAAc,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,IAAI,aAAa,QAAQ,KAAK,OAAO,OAAO,KAAK,WAAW,SAAS,cAAc,MAAM,KAAK,IAAI;AAAA,UACrM,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,cAAc,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,IAAI,cAAc,QAAQ,KAAK,OAAO,OAAO,KAAK,SAAS;AAAA,UACzL,QAAQ,CAAC,WAAW,CAAC,SAAS,cAAc,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,IAAI,aAAa,QAAQ,KAAK,OAAO,OAAO,KAAK,WAAW,SAAS,cAAc,MAAM,KAAK,IAAI;AAAA,QACnM,GAAG;AAAA,UACD,OAAO,QAAQ,mBAAmB,OAAO,QAAQ,QAAQ,SAAS,mBAAmB,CAAC,OAAO,UAAU,UAAU,GAAG,mBAAmB,OAAO,cAAc;AAAA,aACzJ,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,SAAS,OAAO,WAAW,CAAC,MAAM,OAAO;AACvG,qBAAO,UAAU,GAAG,mBAAmB,QAAQ;AAAA,gBAC7C,OAAO;AAAA,gBACP,KAAK;AAAA,cACP,GAAG,gBAAgB,KAAK,KAAK,GAAG,CAAC;AAAA,YACnC,CAAC,GAAG,GAAG;AAAA,UACT,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,UACjC,SAAS,mBAAmB,CAAC,OAAO,UAAU,SAAS,aAAa,UAAU,UAAU,IAAI,GAAG,mBAAmB,UAAU,EAAE,KAAK,EAAE,GAAG,WAAW,SAAS,cAAc,CAAC,OAAO,OAAO;AACvL,mBAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,cAC5C,OAAO,eAAe,CAAC,yBAAyB,6BAA6B,MAAM,GAAG,IAAI,MAAM,KAAK,EAAE,CAAC;AAAA,cACxG,OAAO,eAAe,WAAW,MAAM,MAAM,WAAW,MAAM,GAAG,IAAI;AAAA,YACvE,GAAG;AAAA,cACD,MAAM,SAAS,UAAU,GAAG,mBAAmB,OAAO;AAAA,gBACpD,KAAK;AAAA,gBACL,OAAO;AAAA,gBACP,WAAW,MAAM;AAAA,cACnB,GAAG,MAAM,GAAG,YAAY,KAAK,mBAAmB,IAAI,IAAI;AAAA,YAC1D,GAAG,CAAC;AAAA,UACN,CAAC,GAAG,GAAG,KAAK,mBAAmB,IAAI,IAAI;AAAA,UACvC,WAAW,KAAK,QAAQ,gBAAgB;AAAA,YACtC,QAAQ,SAAS;AAAA,YACjB,YAAY,CAAC,WAAW,SAAS,WAAW,QAAQ,IAAI;AAAA,YACxD,OAAO,SAAS,cAAc,QAAQ;AAAA,UACxC,CAAC;AAAA,UACD,SAAS,gBAAgB,SAAS,mBAAmB,SAAS,KAAK,OAAO,WAAW,OAAO,QAAQ,sBAAsB,UAAU,GAAG,mBAAmB,OAAO,cAAc;AAAA,aAC5K,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,SAAS,cAAc,MAAM,SAAS,SAAS,QAAQ,CAAC,OAAO,MAAM;AACnI,qBAAO,UAAU,GAAG,YAAY,kBAAkB;AAAA,gBAChD,KAAK;AAAA,gBACL,uBAAuB,OAAO,KAAK;AAAA,gBACnC;AAAA,gBACA,WAAW,OAAO;AAAA,gBAClB,eAAe,SAAS,cAAc,MAAM,SAAS,SAAS;AAAA,gBAC9D,YAAY,SAAS,cAAc,MAAM,SAAS,MAAM,IAAI,IAAI,KAAK,aAAa,MAAM,IAAI,MAAM,CAAC,GAAG;AAAA,gBACtG,oBAAoB,SAAS,cAAc,MAAM,SAAS,MAAM,IAAI,IAAI,KAAK,aAAa,MAAM,IAAI,MAAM,CAAC,GAAG;AAAA,gBAC9G,mBAAmB,SAAS,cAAc,MAAM,iBAAiB,KAAK;AAAA,cACxE,GAAG;AAAA,gBACD,OAAO,QAAQ,CAAC,EAAE,OAAO,QAAQ,KAAK,MAAM;AAAA,kBAC1C,WAAW,KAAK,QAAQ,SAAS;AAAA,oBAC/B;AAAA,oBACA,OAAO;AAAA,kBACT,CAAC;AAAA,gBACH,CAAC;AAAA,gBACD,GAAG;AAAA,cACL,GAAG,MAAM,CAAC,uBAAuB,SAAS,WAAW,eAAe,YAAY,kBAAkB,iBAAiB,CAAC;AAAA,YACtH,CAAC,GAAG,GAAG;AAAA,UACT,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,QACnC,GAAG,IAAI,YAAY;AAAA,MACrB,CAAC,GAAG,GAAG;AAAA,MACP,SAAS,mBAAmB,UAAU,GAAG,mBAAmB,OAAO;AAAA,QACjE,OAAO;AAAA,QACP,OAAO,eAAe,QAAQ,SAAS,kBAAkB,IAAI;AAAA,QAC7D,KAAK,OAAO,QAAQ,cAAc,GAAG,SAAS,KAAK,EAAE,cAAc;AAAA,QACnE,OAAO,SAAS,MAAM,KAAK,WAAW,SAAS,OAAO,GAAG;AAAA,MAC3D,GAAG,MAAM,IAAI,YAAY,KAAK,mBAAmB,IAAI,IAAI;AAAA,IAC3D,CAAC;AAAA,IACD,GAAG;AAAA,EACL,GAAG,GAAG,CAAC,SAAS,QAAQ,UAAU,OAAO,CAAC;AAC5C;AACA,IAAM,cAAc;AAAA,EAClB,QAAQ,CAAC,UAAU,SAAS,WAAW,QAAQ,WAAW;AAAA,EAC1D,YAAY,EAAE,MAAM;AAAA,EACpB,OAAO;AAAA;AAAA,IAEL,SAAS,EAAE,MAAM,QAAQ,SAAS,OAAO,CAAC,GAAG;AAAA,IAC7C,YAAY,EAAE,MAAM,QAAQ,UAAU,KAAK;AAAA,IAC3C,MAAM,EAAE,MAAM,QAAQ,UAAU,KAAK;AAAA,IACrC,YAAY,EAAE,MAAM,OAAO,SAAS,MAAM,CAAC,EAAE;AAAA,IAC7C,cAAc,EAAE,MAAM,CAAC,QAAQ,IAAI,GAAG,SAAS,KAAK;AAAA,IACpD,cAAc,EAAE,MAAM,CAAC,QAAQ,IAAI,GAAG,SAAS,KAAK;AAAA,IACpD,WAAW,EAAE,MAAM,CAAC,QAAQ,OAAO,GAAG,SAAS,MAAM;AAAA,IACrD,QAAQ,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,EAC1C;AAAA,EACA,MAAM,OAAO;AAAA,IACX,cAAc,CAAC;AAAA,IACf,oBAAoB;AAAA;AAAA;AAAA;AAAA,IAIpB,cAAc;AAAA,IACd,aAAa;AAAA;AAAA,IAEb,kBAAkB;AAAA,EACpB;AAAA,EACA,SAAS;AAAA,IACP,iBAAiB,EAAE,OAAO,GAAG;AAC3B,YAAM,gBAAgB,OAAO,UAAU,SAAS,oBAAoB;AACpE,UAAI,QAAQ,gBAAgB,SAAS,KAAK,OAAO,aAAa,QAAQ,oBAAoB;AAC1F,UAAI,OAAO;AACT,gBAAQ,MAAM,WAAW,YAAY,EAAE;AACvC,YAAI,SAAS,KAAK,EAAE,SAAS,MAAM,MAAM,SAAS,EAAG,SAAQ,SAAS,KAAK;AAAA,MAC7E;AACA,aAAO,SAAS;AAAA,IAClB;AAAA,IACA,aAAa,OAAO;AAClB,aAAO;AAAA,QACL,sBAAsB;AAAA,QACtB,mCAAmC,KAAK,qBAAqB,MAAM;AAAA,QACnE,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM;AAAA,MACzB;AAAA,IACF;AAAA,IACA,6BAA6B;AAC3B,UAAI,KAAK,QAAQ,QAAQ,KAAK,eAAe,CAAC,KAAK,aAAa;AAC9D,YAAI,KAAK,gBAAgB,GAAG;AAC1B,eAAK,eAAe,CAAC;AACrB,eAAK,qBAAqB;AAAA,QAC5B,MAAO,EAAC,KAAK,cAAc,KAAK,kBAAkB,IAAI,KAAK,MAAM,MAAM,2BAA2B,KAAK,QAAQ,KAAK,OAAO;AAAA,MAC7H;AAAA,IACF;AAAA,IACA,oBAAoB,IAAI;AACtB,aAAO,KAAK,OAAO,oBAAoB,EAAE;AAAA,IAC3C;AAAA;AAAA;AAAA;AAAA,IAIA,WAAW,UAAU,QAAQ,OAAO;AAClC,YAAM,QAAQ,KAAK,cAAc,KAAK,iBAAiB,QAAQ,IAAI;AACnE,WAAK,MAAM,KAAK,WAAW,OAAO,KAAK,cAAc,KAAK;AAC1D,WAAK,eAAe;AAAA,IACtB;AAAA,IACA,oBAAoB,UAAU;AAC5B,UAAI,CAAC,KAAK,WAAY,MAAK,YAAY,QAAQ;AAC/C,YAAM,QAAQ,KAAK,cAAc,KAAK,iBAAiB,QAAQ,IAAI;AACnE,WAAK,MAAM,KAAK,kBAAkB,KAAK,cAAc,KAAK;AAC1D,WAAK,eAAe;AAAA,IACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,YAAY,UAAU;AACpB,UAAI,CAAC,KAAK,cAAc,CAAC,KAAK,YAAY;AACxC,aAAK,aAAa,KAAK,KAAK;AAC5B,cAAM,QAAQ,KAAK,cAAc,KAAK,iBAAiB,QAAQ,IAAI;AACnE,cAAM,OAAO,KAAK,gBAAgB,KAAK,KAAK;AAC5C,aAAK,OAAO,MAAM,cAAc,QAAQ,EAAE,MAAM,MAAM,IAAI,IAAI;AAAA,MAChE;AAAA,IACF;AAAA,IACA,gBAAgB,UAAU,QAAQ,MAAM,QAAQ,OAAO;AACrD,UAAI,kBAAkB,UAAU,CAAC,MAAO,QAAO;AAC/C,UAAI,CAAC,KAAK,WAAY,MAAK,YAAY,QAAQ;AAC/C,YAAM,EAAE,gBAAgB,aAAa,IAAI,KAAK;AAC9C,WAAK,UAAU,2BAA2B;AAC1C,qBAAe,eAAe;AAC9B,WAAK,eAAe,IAAI,KAAK,KAAK,KAAK,SAAS;AAChD,YAAM,EAAE,SAAS,cAAc,EAAE,EAAE,EAAE,IAAI,KAAK,OAAO,gBAAgB,QAAQ;AAC7E,WAAK,aAAa,WAAW,OAAO;AACpC,YAAM,mBAAmB,KAAK,oBAAoB,SAAS,MAAM;AACjE,UAAI,CAAC,oBAAoB,aAAa,MAAM;AAC1C,SAAC,KAAK,KAAK,OAAO,KAAK,CAAC,MAAM,EAAE,SAAS,aAAa,IAAI,KAAK,CAAC,GAAG,UAAU;AAAA,MAC/E;AACA,UAAI,KAAK,WAAW,UAAU,CAAC,iBAAkB,MAAK,mBAAmB,UAAU,CAAC;AAAA,IACtF;AAAA,IACA,mBAAmB,UAAU,cAAc;AACzC,UAAI,KAAK,QAAQ,qBAAqB,CAAC,QAAQ,KAAK,EAAE,SAAS,KAAK,KAAK,EAAE,GAAG;AAC5E,cAAM,EAAE,kBAAkB,IAAI,KAAK;AACnC,0BAAkB,eAAe;AACjC,0BAAkB,QAAQ,KAAK,cAAc,KAAK,iBAAiB,QAAQ,IAAI;AAC/E,0BAAkB,QAAQ,KAAK;AAC/B,YAAI,KAAK,QAAQ,YAAY;AAC3B,cAAI,cAAc,KAAK,aAAa,SAAS,IAAI,KAAK,KAAK,aAAa,WAAW;AACnF,gBAAM,mBAAmB,cAAc,KAAK,QAAQ,aAAa;AACjE,wBAAc,mBAAmB,mBAAmB,KAAK,QAAQ;AACjE,4BAAkB,MAAM,SAAS,GAAG,aAAa,GAAG,CAAC;AAAA,QACvD;AAAA,MACF,WAAW,KAAK,QAAQ,iBAAiB,CAAC,SAAS,QAAQ,KAAK,EAAE,SAAS,KAAK,KAAK,EAAE,GAAG;AACxF,aAAK,mBAAmB,QAAQ;AAAA,MAClC;AAAA,IACF;AAAA;AAAA,IAEA,mBAAmB,UAAU;AAC3B,YAAM,EAAE,eAAe,IAAI,KAAK;AAChC,qBAAe,SAAS,GAAG,KAAK,OAAO,EAAE,GAAG,IAAI,KAAK,KAAK,aAAa;AACvE,qBAAe,QAAQ,KAAK,cAAc,KAAK,iBAAiB,QAAQ,IAAI;AAC5E,qBAAe,YAAY,WAAW,MAAM;AAC1C,YAAI,eAAe,UAAU,CAAC,KAAK,UAAU,0BAA0B;AACrE,gBAAM,EAAE,KAAK,IAAI,KAAK,MAAM,MAAM;AAAA,YAChC,KAAK;AAAA,YACL;AAAA,YACA,eAAe,QAAQ,EAAE,OAAO,eAAe,MAAM,IAAI,CAAC;AAAA,UAC5D;AACA,yBAAe,eAAe;AAAA,QAChC;AAAA,MACF,GAAG,eAAe,OAAO;AAAA,IAC3B;AAAA,IACA,iBAAiB,UAAU,QAAQ,MAAM;AACvC,WAAK,gBAAgB,UAAU,OAAO,IAAI;AAAA,IAC5C;AAAA,IACA,YAAY,UAAU;AACpB,UAAI,CAAC,KAAK,oBAAoB,SAAS,MAAM,EAAG,MAAK,WAAW,QAAQ;AAAA,IAC1E;AAAA,IACA,eAAe,UAAU;AACvB,YAAM,OAAO,IAAI,KAAK,KAAK,KAAK,SAAS;AACzC,WAAK,WAAW,KAAK,OAAO,gBAAgB,QAAQ,EAAE,OAAO;AAC7D,YAAM,QAAQ,KAAK,cAAc,KAAK,iBAAiB,QAAQ,IAAI;AACnE,WAAK,OAAO,MAAM,iBAAiB,QAAQ,EAAE,MAAM,MAAM,IAAI,IAAI;AACjE,UAAI,KAAK,QAAQ,mBAAoB,MAAK,OAAO,qBAAqB;AAAA,IACxE;AAAA,IACA,kBAAkB,UAAU;AAC1B,eAAS,gBAAgB;AACzB,eAAS,eAAe;AACxB,YAAM,OAAO,IAAI,KAAK,KAAK,KAAK,SAAS;AACzC,YAAM,EAAE,cAAc,QAAQ,IAAI,KAAK,OAAO,gBAAgB,QAAQ;AACtE,WAAK,WAAW,OAAO;AACvB,YAAM,QAAQ,KAAK,cAAc,KAAK,iBAAiB,QAAQ,IAAI;AACnE,WAAK,OAAO,MAAM,oBAAoB,EAAE,MAAM,GAAG,cAAc,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC;AAAA,IAC9F;AAAA,EACF;AAAA,EACA,UAAU;AAAA;AAAA,IAER,MAAM;AACJ,aAAO,KAAK,QAAQ;AAAA,IACtB;AAAA,IACA,eAAe;AACb,aAAO,KAAK,MAAM,KAAK,cAAc,KAAK,OAAO,GAAG;AAAA,IACtD;AAAA,IACA,kBAAkB;AAChB,aAAO,KAAK,iBAAiB,QAAQ,KAAK,eAAe,KAAK,KAAK,QAAQ,QAAQ;AAAA,IACrF;AAAA,IACA,iBAAiB;AACf,aAAO,KAAK,gBAAgB,KAAK,eAAe,KAAK,KAAK,UAAU,QAAQ;AAAA,IAC9E;AAAA;AAAA,IAEA,aAAa;AACX,YAAM,EAAE,YAAY,IAAI,KAAK;AAC7B,YAAM,EAAE,kBAAkB,IAAI,KAAK;AACnC,UAAI,YAAY,UAAU,YAAY,SAAS,KAAK,KAAK,aAAa,KAAK,CAAC,kBAAmB,QAAO;AACtG,aAAO,KAAK,mBAAmB,KAAK;AAAA,IACtC;AAAA;AAAA,IAEA,YAAY;AAAA,MACV,MAAM;AACJ,YAAI,WAAW;AACf,cAAM,EAAE,aAAa,IAAI,KAAK;AAC9B,YAAI,KAAK,KAAK,OAAO,SAAS;AAC5B,qBAAW,aAAa,YAAY,MAAM,KAAK,KAAK,UAAU,YAAY;AAAA,QAC5E,WAAW,KAAK,KAAK,OAAO,QAAQ;AAClC,qBAAW,aAAa,YAAY,MAAM,KAAK,KAAK,UAAU,YAAY,KAAK,aAAa,SAAS,MAAM,KAAK,KAAK,UAAU,SAAS;AAAA,QAC1I,MAAO,YAAW,aAAa,QAAQ,MAAM,KAAK,KAAK,UAAU,QAAQ;AACzE,eAAO;AAAA,MACT;AAAA,MACA,IAAI,MAAM;AACR,aAAK,KAAK,eAAe;AACzB,aAAK,OAAO,MAAM,wBAAwB,KAAK,KAAK,YAAY;AAAA,MAClE;AAAA,IACF;AAAA;AAAA,IAEA,kBAAkB;AAChB,aAAO,CAAC,QAAQ,KAAK,EAAE,SAAS,KAAK,KAAK,EAAE;AAAA,IAC9C;AAAA,IACA,sBAAsB;AACpB,aAAO,KAAK,OAAO;AAAA,IACrB;AAAA,IACA,eAAe;AACb,aAAO,KAAK,KAAK,aAAa,IAAI,CAAC,UAAU;AAC3C,YAAI,EAAE,MAAM,GAAG,IAAI;AACnB,eAAO,KAAK,IAAI,MAAM,KAAK,QAAQ,QAAQ;AAC3C,aAAK,KAAK,IAAI,IAAI,KAAK,QAAQ,MAAM;AACrC,eAAO;AAAA,UACL,GAAG;AAAA,UACH,SAAS,KAAK,QAAQ,KAAK;AAAA,UAC3B,MAAM,OAAO,KAAK,QAAQ,YAAY,KAAK;AAAA,QAC7C;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,SAAS;AACP,YAAM,EAAE,WAAW,WAAW,SAAS,QAAQ,IAAI,KAAK;AACxD,UAAI,SAAS,CAAC;AACd,UAAI,EAAE,CAAC,SAAS,MAAM,EAAE,SAAS,KAAK,KAAK,EAAE,KAAK,CAAC,KAAK,QAAQ,wBAAwB;AACtF,iBAAS,KAAK,KAAK,OAAO,MAAM,CAAC;AACjC,YAAI,KAAK,KAAK,OAAO,SAAS;AAC5B,iBAAO,KAAK,GAAG,KAAK,KAAK,gBAAgB;AAAA,QAC3C;AACA,iBAAS,OAAO,OAAO,CAAC,MAAM,KAAK,MAAM,MAAM,aAAa,GAAG,WAAW,OAAO,CAAC;AAClF,YAAI,KAAK,QAAQ,oBAAoB,KAAK,KAAK,OAAO,QAAS,UAAS,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,KAAK,MAAM;AACvH,YAAI,KAAK,QAAQ,QAAQ,KAAK,mBAAmB,CAAC,KAAK,QAAQ;AAC7D,gBAAM,EAAE,UAAU,OAAO,IAAI,KAAK;AAClC,mBAAS,OAAO,OAAO,CAAC,MAAM;AAC5B,kBAAM,UAAU,EAAE,YAAY,KAAK,EAAE,SAAS,KAAK,KAAK,aAAa,KAAK,CAAC;AAC3E,kBAAM,mBAAmB,EAAE,cAAc,KAAK,EAAE,mBAAmB,UAAU,EAAE,iBAAiB;AAChG,kBAAM,qBAAqB,EAAE,YAAY,MAAM,QAAQ,mBAAmB,UAAU,QAAQ,iBAAiB;AAC7G,kBAAM,uBAAuB;AAC7B,mBAAO,EAAE,UAAU,oBAAoB,sBAAsB;AAAA,UAC/D,CAAC;AAAA,QACH;AACA,YAAI,KAAK,QAAQ,QAAQ,KAAK,mBAAmB,EAAE,KAAK,QAAQ,oBAAoB,KAAK,SAAS;AAChG,iBAAO,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,QAAQ,KAAK,CAAC;AAAA,QAClD;AACA,YAAI,CAAC,KAAK,WAAW,OAAQ,MAAK,UAAU,KAAK,0BAA0B;AAAA,MAC7E;AACA,aAAO;AAAA,IACT;AAAA,IACA,cAAc;AACZ,aAAO,KAAK,OAAO;AAAA,IACrB;AAAA,IACA,SAAS;AACP,aAAO,KAAK,WAAW,IAAI,CAAC,MAAM,MAAM;AACtC,cAAM,SAAS,KAAK,OAAO,OAAO,CAAC,MAAM,EAAE,UAAU,KAAK,EAAE;AAC5D,cAAM,CAAC,UAAU,MAAM,IAAI,KAAK,MAAM,MAAM,2BAA2B,OAAO,OAAO,CAAC,MAAM,CAAC,EAAE,cAAc,CAAC,EAAE,MAAM,GAAG,KAAK,OAAO;AACrI,eAAO;AAAA,UACL,GAAG;AAAA,UACH;AAAA,UACA,gBAAgB;AAAA,UAChB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACA,cAAc;AACZ,aAAO,KAAK,OAAO;AAAA,IACrB;AAAA,IACA,cAAc;AACZ,aAAO;AAAA,QACL,CAAC,KAAK,KAAK,KAAK,GAAG,CAAC,CAAC,KAAK,KAAK;AAAA,QAC/B,yBAAyB,KAAK,KAAK;AAAA;AAAA,QAEnC,uBAAuB,KAAK,KAAK;AAAA,QACjC,8BAA8B,KAAK,KAAK;AAAA,QACxC,4BAA4B,KAAK,cAAc,KAAK;AAAA,QACpD,2BAA2B,KAAK,cAAc,KAAK;AAAA,QACnD,0BAA0B,KAAK;AAAA,QAC/B,0BAA0B,KAAK;AAAA,QAC/B,6BAA6B,KAAK;AAAA,QAClC,4BAA4B,KAAK;AAAA,QACjC,4BAA4B,KAAK;AAAA,MACnC;AAAA,IACF;AAAA,IACA,aAAa;AACX,aAAO;AAAA;AAAA,QAEL,GAAG,KAAK,YAAY,EAAE,OAAO,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC;AAAA,MACzD;AAAA,IACF;AAAA,IACA,kBAAkB;AAChB,YAAM,EAAE,MAAM,OAAO,IAAI,KAAK;AAC9B,aAAO,KAAK,KAAK,SAAS,KAAK,mBAAmB,QAAQ,CAAC,KAAK,UAAU,KAAK,gBAAgB;AAAA,IACjG;AAAA,IACA,qBAAqB;AACnB,UAAI,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,QAAQ,KAAM;AAC5C,YAAM,iBAAiB,KAAK,eAAe,KAAK,QAAQ;AACxD,aAAO,KAAK,MAAM,iBAAiB,KAAK,SAAS;AAAA,IACnD;AAAA,IACA,YAAY;AACV,aAAO,KAAK,QAAQ,iBAAiB,KAAK,QAAQ;AAAA,IACpD;AAAA,EACF;AACF;AACA,IAAM,OAAuB,YAAY,aAAa,CAAC,CAAC,UAAU,QAAQ,CAAC,CAAC;AAC5E,IAAM,eAAe;AAAA,EACnB,KAAK;AAAA,EACL,OAAO;AAAA,EACP,OAAO,EAAE,SAAS,MAAM;AAC1B;AACA,SAAS,SAAS,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC/D,QAAM,yBAAyB,iBAAiB,aAAa;AAC7D,SAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,IAC5C,OAAO;AAAA,IACP,OAAO,eAAe,OAAO,uBAAuB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAAA,EAC/E,GAAG;AAAA,IACD,CAAC,OAAO,uBAAuB,UAAU,GAAG,mBAAmB,OAAO,cAAc;AAAA,MAClF,gBAAmB,QAAQ,MAAM,gBAAgB,OAAO,KAAK,GAAG,CAAC;AAAA,IACnE,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,IACjC,gBAAmB,OAAO;AAAA,MACxB,OAAO,eAAe,CAAC,8BAA8B,GAAG,SAAS,KAAK,EAAE,OAAO,CAAC;AAAA,MAChF,MAAM;AAAA,MACN,OAAO,eAAe,OAAO,sBAAsB,cAAc,OAAO,mBAAmB,OAAO,EAAE;AAAA,IACtG,GAAG;AAAA,OACA,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,OAAO,OAAO,CAAC,MAAM,MAAM;AACzF,eAAO,UAAU,GAAG,YAAY,wBAAwB;AAAA,UACtD,KAAK;AAAA,UACL,SAAS,OAAO;AAAA,UAChB,eAAe,SAAS;AAAA,UACxB,MAAM;AAAA,UACN,WAAW;AAAA,UACX,cAAc,OAAO,QAAQ,aAAa,WAAW,SAAS,OAAO,cAAc,SAAS,OAAO,gBAAgB,SAAS,OAAO;AAAA,UACnI,iBAAiB,OAAO,QAAQ;AAAA,UAChC,iBAAiB,OAAO,QAAQ;AAAA,UAChC,eAAe,OAAO;AAAA,QACxB,GAAG;AAAA,UACD,OAAO,QAAQ,CAAC,EAAE,OAAO,KAAK,MAAM;AAAA,YAClC,WAAW,KAAK,QAAQ,SAAS;AAAA,cAC/B;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH,CAAC;AAAA,UACD,GAAG;AAAA,QACL,GAAG,MAAM,CAAC,WAAW,eAAe,QAAQ,cAAc,iBAAiB,iBAAiB,aAAa,CAAC;AAAA,MAC5G,CAAC,GAAG,GAAG;AAAA,IACT,GAAG,CAAC;AAAA,EACN,GAAG,CAAC;AACN;AACA,IAAM,cAAc;AAAA,EAClB,QAAQ,CAAC,UAAU,QAAQ,YAAY;AAAA,EACvC,YAAY,EAAE,eAAe,KAAK;AAAA,EAClC,OAAO;AAAA;AAAA,IAEL,SAAS,EAAE,MAAM,QAAQ,UAAU,KAAK;AAAA,IACxC,OAAO,EAAE,MAAM,OAAO,UAAU,KAAK;AAAA,IACrC,OAAO,EAAE,MAAM,QAAQ,UAAU,KAAK;AAAA,IACtC,WAAW,EAAE,MAAM,OAAO,SAAS,MAAM,CAAC,EAAE;AAAA,IAC5C,aAAa,EAAE,MAAM,SAAS,SAAS,KAAK;AAAA,IAC5C,QAAQ,EAAE,MAAM,QAAQ,SAAS,GAAG;AAAA,IACpC,qBAAqB,EAAE,MAAM,QAAQ,SAAS,KAAK;AAAA,EACrD;AAAA,EACA,UAAU;AAAA,IACR,sBAAsB;AACpB,aAAO,CAAC,EAAE,KAAK,QAAQ,gBAAgB,KAAK,UAAU,UAAU,KAAK,QAAQ;AAAA,IAC/E;AAAA,EACF;AACF;AACA,IAAM,YAA4B,YAAY,aAAa,CAAC,CAAC,UAAU,QAAQ,CAAC,CAAC;AACjF,IAAM,aAAa,CAAC,MAAM;AAC1B,IAAM,aAAa,EAAE,OAAO,UAAU;AACtC,IAAM,aAAa;AAAA,EACjB,KAAK;AAAA,EACL,OAAO;AAAA,EACP,MAAM;AACR;AACA,IAAM,aAAa,CAAC,UAAU,WAAW;AACzC,IAAM,aAAa,CAAC,WAAW;AAC/B,IAAM,aAAa,CAAC,WAAW;AAC/B,IAAM,aAAa;AAAA,EACjB,OAAO;AAAA,EACP,KAAK;AAAA,EACL,MAAM;AACR;AACA,IAAM,aAAa;AAAA,EACjB,KAAK;AAAA,EACL,OAAO;AACT;AACA,IAAM,aAAa,EAAE,OAAO,0BAA0B;AACtD,IAAM,cAAc;AAAA,EAClB,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAM,cAAc,CAAC,QAAQ,QAAQ;AACrC,IAAM,cAAc,CAAC,UAAU,WAAW;AAC1C,IAAM,cAAc,CAAC,WAAW;AAChC,IAAM,cAAc,CAAC,WAAW;AAChC,IAAM,cAAc,CAAC,MAAM;AAC3B,IAAM,cAAc,CAAC,WAAW;AAChC,IAAM,cAAc,CAAC,WAAW;AAChC,IAAM,cAAc;AAAA,EAClB,KAAK;AAAA,EACL,OAAO;AACT;AACA,IAAM,cAAc;AAAA,EAClB,KAAK;AAAA,EACL,OAAO;AACT;AACA,IAAM,cAAc,CAAC,UAAU,WAAW;AAC1C,IAAM,cAAc,CAAC,WAAW;AAChC,IAAM,cAAc;AAAA,EAClB,KAAK;AAAA,EACL,OAAO;AACT;AACA,IAAM,cAAc,EAAE,KAAK,EAAE;AAC7B,IAAM,cAAc;AAAA,EAClB,KAAK;AAAA,EACL,OAAO;AACT;AACA,IAAM,cAAc,CAAC,WAAW;AAChC,IAAM,cAAc;AAAA,EAClB,KAAK;AAAA,EACL,OAAO;AACT;AACA,SAAS,OAAO,MAAM,QAAQ,QAAQ,QAAQ,OAAO,UAAU;AAC7D,QAAM,2BAA2B,iBAAiB,eAAe;AACjE,QAAM,yBAAyB,iBAAiB,aAAa;AAC7D,QAAM,+BAA+B,iBAAiB,mBAAmB;AACzE,QAAM,yBAAyB,iBAAiB,aAAa;AAC7D,SAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,IAC5C,OAAO,eAAe,CAAC,uBAAuB,SAAS,UAAU,CAAC;AAAA,IAClE,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM,OAAO;AAAA,EACf,GAAG;AAAA,IACD,YAAY,0BAA0B;AAAA,MACpC,SAAS,KAAK;AAAA,MACd,eAAe,SAAS;AAAA,MACxB,cAAc,EAAE,OAAO,SAAS,OAAO,kBAAkB,SAAS,iBAAiB;AAAA,MACnF,aAAa,SAAS;AAAA,MACtB,cAAc,SAAS;AAAA,MACvB,cAAc,SAAS;AAAA,MACvB,2BAA2B,SAAS;AAAA,IACtC,GAAG,YAAY;AAAA,MACb,cAAc,QAAQ,MAAM;AAAA,QAC1B,WAAW,KAAK,QAAQ,cAAc,CAAC,GAAG,MAAM;AAAA,UAC9C,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,gBAAgB,GAAG;AAAA,UAC7C,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,gBAAmB,KAAK,EAAE,OAAO,QAAQ,GAAG,MAAM,EAAE;AAAA,UAC9E,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,gBAAgB,GAAG;AAAA,QAC/C,CAAC;AAAA,MACH,CAAC;AAAA,MACD,cAAc,QAAQ,MAAM;AAAA,QAC1B,WAAW,KAAK,QAAQ,cAAc,CAAC,GAAG,MAAM;AAAA,UAC9C,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,gBAAgB,GAAG;AAAA,UAC7C,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,gBAAmB,KAAK,EAAE,OAAO,QAAQ,GAAG,MAAM,EAAE;AAAA,UAC9E,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,gBAAgB,GAAG;AAAA,QAC/C,CAAC;AAAA,MACH,CAAC;AAAA,MACD,gBAAgB,QAAQ,MAAM;AAAA,QAC5B,WAAW,KAAK,QAAQ,gBAAgB,CAAC,GAAG,MAAM;AAAA,UAChD,gBAAmB,QAAQ,YAAY,gBAAgB,MAAM,MAAM,KAAK,GAAG,CAAC;AAAA,QAC9E,CAAC;AAAA,MACH,CAAC;AAAA,MACD,OAAO,QAAQ,MAAM;AAAA,QACnB,WAAW,KAAK,QAAQ,SAAS;AAAA,UAC/B,OAAO,SAAS;AAAA,UAChB,MAAM,MAAM;AAAA,QACd,GAAG,MAAM;AAAA,UACP,gBAAgB,gBAAgB,SAAS,SAAS,GAAG,CAAC;AAAA,QACxD,CAAC;AAAA,MACH,CAAC;AAAA,MACD,GAAG;AAAA,IACL,GAAG;AAAA,MACD,KAAK,OAAO,iBAAiB,IAAI;AAAA,QAC/B,MAAM;AAAA,QACN,IAAI,QAAQ,CAAC,EAAE,SAAS,KAAK,MAAM;AAAA,UACjC,WAAW,KAAK,QAAQ,mBAAmB;AAAA,YACzC;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH,CAAC;AAAA,QACD,KAAK;AAAA,MACP,IAAI;AAAA,MACJ,KAAK,OAAO,aAAa,IAAI;AAAA,QAC3B,MAAM;AAAA,QACN,IAAI,QAAQ,CAAC,EAAE,MAAM,MAAM;AAAA,UACzB,WAAW,KAAK,QAAQ,eAAe;AAAA,YACrC;AAAA,YACA,MAAM,MAAM,KAAK;AAAA,UACnB,CAAC;AAAA,QACH,CAAC;AAAA,QACD,KAAK;AAAA,MACP,IAAI;AAAA,IACN,CAAC,GAAG,MAAM,CAAC,WAAW,eAAe,cAAc,aAAa,cAAc,cAAc,yBAAyB,CAAC;AAAA,IACtH,CAAC,OAAO,YAAY,UAAU,GAAG,mBAAmB,OAAO,YAAY;AAAA,MACrE,YAAY,YAAY;AAAA,QACtB,MAAM,eAAe,MAAM,mBAAmB;AAAA,QAC9C,QAAQ,OAAO;AAAA,MACjB,GAAG;AAAA,QACD,SAAS,QAAQ,MAAM;AAAA,WACpB,UAAU,GAAG,mBAAmB,OAAO;AAAA,YACtC,OAAO;AAAA,YACP,OAAO,EAAE,aAAa,OAAO;AAAA,YAC7B,KAAK,OAAO,cAAc,MAAM,KAAK,KAAK;AAAA,YAC1C,QAAQ;AAAA,UACV,GAAG;AAAA,YACD,OAAO,oBAAoB,SAAS,kBAAkB,CAAC,SAAS,uBAAuB,SAAS,aAAa,CAAC,OAAO,kBAAkB,UAAU,GAAG,YAAY,wBAAwB,eAAe,WAAW,EAAE,KAAK,EAAE,GAAG,SAAS,SAAS,CAAC,GAAG;AAAA,cAClP,OAAO,QAAQ,CAAC,EAAE,OAAO,KAAK,MAAM;AAAA,gBAClC,WAAW,KAAK,QAAQ,SAAS;AAAA,kBAC/B;AAAA,kBACA;AAAA,gBACF,GAAG,MAAM;AAAA,kBACP,SAAS,WAAW,SAAS,MAAM,iBAAiB,UAAU,GAAG,mBAAmB,OAAO;AAAA,oBACzF,KAAK;AAAA,oBACL,OAAO;AAAA,oBACP,iBAAiB;AAAA,oBACjB,QAAQ,CAAC,WAAW,SAAS,iBAAiB,QAAQ,KAAK;AAAA,oBAC3D,WAAW,MAAM;AAAA,kBACnB,GAAG,MAAM,IAAI,UAAU,KAAK,MAAM,SAAS,UAAU,GAAG,mBAAmB,OAAO;AAAA,oBAChF,KAAK;AAAA,oBACL,OAAO;AAAA,oBACP,WAAW,MAAM;AAAA,kBACnB,GAAG,MAAM,GAAG,UAAU,KAAK,mBAAmB,IAAI,IAAI;AAAA,kBACtD,MAAM,WAAW,CAAC,SAAS,kBAAkB,CAAC,SAAS,oBAAoB,UAAU,GAAG,mBAAmB,OAAO;AAAA,oBAChH,KAAK;AAAA,oBACL,OAAO;AAAA,oBACP,WAAW,MAAM;AAAA,kBACnB,GAAG,MAAM,GAAG,UAAU,KAAK,mBAAmB,IAAI,IAAI;AAAA,gBACxD,CAAC;AAAA,cACH,CAAC;AAAA,cACD,GAAG;AAAA,YACL,GAAG,EAAE,KAAK,mBAAmB,IAAI,IAAI;AAAA,YACrC,gBAAmB,OAAO;AAAA,cACxB,OAAO,eAAe,CAAC,cAAc,EAAE,cAAc,CAAC,SAAS,cAAc,CAAC,CAAC;AAAA,cAC/E,QAAQ;AAAA,YACV,GAAG;AAAA,cACD,gBAAmB,OAAO,YAAY;AAAA,gBACpC,SAAS,iBAAiB,UAAU,GAAG,mBAAmB,OAAO,YAAY;AAAA,kBAC3E,OAAO,oBAAoB,SAAS,uBAAuB,EAAE,SAAS,aAAa,CAAC,OAAO,kBAAkB,UAAU,GAAG,mBAAmB,OAAO;AAAA,oBAClJ,KAAK;AAAA,oBACL,OAAO;AAAA,oBACP,OAAO,eAAe,EAAE,QAAQ,SAAS,UAAU,OAAO,CAAC;AAAA,kBAC7D,GAAG;AAAA,oBACD,gBAAmB,QAAQ,MAAM,gBAAgB,MAAM,MAAM,MAAM,GAAG,CAAC;AAAA,kBACzE,GAAG,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,mBACnC,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,SAAS,WAAW,CAAC,MAAM,MAAM;AAC/F,2BAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,sBAC5C,OAAO;AAAA,sBACP,KAAK;AAAA,sBACL,OAAO,eAAe,WAAW,OAAO,cAAc,IAAI;AAAA,oBAC5D,GAAG;AAAA,sBACD,WAAW,KAAK,QAAQ,aAAa;AAAA,wBACnC,OAAO,KAAK;AAAA,wBACZ,SAAS,KAAK;AAAA,sBAChB,GAAG,MAAM;AAAA,wBACP,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,gBAAmB,QAAQ,EAAE,OAAO,yBAAyB,GAAG,MAAM,EAAE;AAAA,wBAClG,gBAAmB,QAAQ,YAAY,gBAAgB,KAAK,KAAK,GAAG,CAAC;AAAA,sBACvE,CAAC;AAAA,oBACH,GAAG,CAAC;AAAA,kBACN,CAAC,GAAG,GAAG;AAAA,gBACT,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,gBACjC,OAAO,mBAAmB,SAAS,eAAe,UAAU,GAAG,mBAAmB,OAAO,aAAa;AAAA,mBACnG,UAAU,GAAG,mBAAmB,UAAU,MAAM,WAAW,GAAG,CAAC,MAAM;AACpE,2BAAO,gBAAmB,OAAO;AAAA,sBAC/B,OAAO;AAAA,sBACP,KAAK;AAAA,sBACL,MAAM;AAAA,oBACR,GAAG;AAAA,sBACD,WAAW,KAAK,QAAQ,oBAAoB;AAAA,wBAC1C,MAAM,SAAS,cAAc,IAAI,CAAC;AAAA,sBACpC,GAAG,MAAM;AAAA,wBACP,gBAAgB,gBAAgB,SAAS,cAAc,IAAI,CAAC,CAAC,GAAG,CAAC;AAAA,sBACnE,CAAC;AAAA,oBACH,CAAC;AAAA,kBACH,CAAC,GAAG,EAAE;AAAA,gBACR,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,gBACjC,gBAAmB,OAAO;AAAA,kBACxB,OAAO,eAAe,CAAC,8BAA8B,GAAG,MAAM,KAAK,EAAE,OAAO,CAAC;AAAA,kBAC7E,MAAM;AAAA,kBACN,MAAM,CAAC,SAAS,uBAAuB,CAAC,SAAS;AAAA,kBACjD,QAAQ,CAAC,CAAC,SAAS;AAAA,gBACrB,GAAG;AAAA,kBACD,SAAS,uBAAuB,SAAS,cAAc,UAAU,GAAG,YAAY,8BAA8B;AAAA,oBAC5G,KAAK;AAAA,oBACL,wBAAwB,MAAM;AAAA,oBAC9B,aAAa,SAAS;AAAA,oBACtB,2BAA2B,SAAS;AAAA,oBACpC,OAAO,eAAe,SAAS,sBAAsB,cAAc,SAAS,mBAAmB,OAAO,EAAE;AAAA,kBAC1G,GAAG,YAAY,EAAE,GAAG,EAAE,GAAG;AAAA,oBACvB,KAAK,OAAO,iBAAiB,IAAI;AAAA,sBAC/B,MAAM;AAAA,sBACN,IAAI,QAAQ,CAAC,EAAE,SAAS,KAAK,MAAM;AAAA,wBACjC,WAAW,KAAK,QAAQ,mBAAmB;AAAA,0BACzC;AAAA,0BACA;AAAA,wBACF,CAAC;AAAA,sBACH,CAAC;AAAA,sBACD,KAAK;AAAA,oBACP,IAAI;AAAA,oBACJ,KAAK,OAAO,aAAa,IAAI;AAAA,sBAC3B,MAAM;AAAA,sBACN,IAAI,QAAQ,CAAC,EAAE,MAAM,MAAM;AAAA,wBACzB,WAAW,KAAK,QAAQ,eAAe;AAAA,0BACrC;AAAA,0BACA,MAAM,MAAM,KAAK;AAAA,wBACnB,CAAC;AAAA,sBACH,CAAC;AAAA,sBACD,KAAK;AAAA,oBACP,IAAI;AAAA,kBACN,CAAC,GAAG,MAAM,CAAC,wBAAwB,aAAa,2BAA2B,OAAO,CAAC,KAAK,SAAS,aAAa,OAAO,qBAAqB,OAAO,iBAAiB,UAAU,GAAG,mBAAmB,OAAO;AAAA,oBACvM,KAAK;AAAA,oBACL,OAAO;AAAA,oBACP,OAAO,eAAe,SAAS,sBAAsB,cAAc,SAAS,mBAAmB,OAAO,EAAE;AAAA,kBAC1G,GAAG;AAAA,qBACA,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,SAAS,WAAW,CAAC,OAAO,MAAM;AAChG,6BAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,wBAC5C,OAAO,eAAe,CAAC,oBAAoB,MAAM,SAAS,KAAK,CAAC;AAAA,wBAChE,KAAK;AAAA,sBACP,GAAG;AAAA,wBACD,WAAW,KAAK,QAAQ,eAAe;AAAA,0BACrC;AAAA,0BACA,MAAM,MAAM,KAAK;AAAA,wBACnB,GAAG,MAAM;AAAA,0BACP,gBAAgB,gBAAgB,MAAM,KAAK,GAAG,CAAC;AAAA,wBACjD,CAAC;AAAA,sBACH,GAAG,CAAC;AAAA,oBACN,CAAC,GAAG,GAAG;AAAA,kBACT,GAAG,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,kBACpC,OAAO,oBAAoB,SAAS,kBAAkB,SAAS,cAAc,SAAS,uBAAuB,SAAS,aAAa,SAAS,aAAa,OAAO,kBAAkB,UAAU,GAAG,YAAY,wBAAwB,eAAe,WAAW,EAAE,KAAK,EAAE,GAAG,SAAS,SAAS,CAAC,GAAG;AAAA,oBAC7R,OAAO,QAAQ,CAAC,EAAE,OAAO,KAAK,MAAM;AAAA,sBAClC,WAAW,KAAK,QAAQ,SAAS;AAAA,wBAC/B;AAAA,wBACA;AAAA,sBACF,GAAG,MAAM;AAAA,wBACP,SAAS,WAAW,SAAS,MAAM,iBAAiB,UAAU,GAAG,mBAAmB,OAAO;AAAA,0BACzF,KAAK;AAAA,0BACL,OAAO;AAAA,0BACP,iBAAiB;AAAA,0BACjB,QAAQ,CAAC,WAAW,SAAS,iBAAiB,QAAQ,KAAK;AAAA,0BAC3D,WAAW,MAAM;AAAA,wBACnB,GAAG,MAAM,IAAI,WAAW,KAAK,MAAM,SAAS,UAAU,GAAG,mBAAmB,OAAO;AAAA,0BACjF,KAAK;AAAA,0BACL,OAAO;AAAA,0BACP,WAAW,MAAM;AAAA,wBACnB,GAAG,MAAM,GAAG,WAAW,KAAK,mBAAmB,IAAI,IAAI;AAAA,wBACvD,MAAM,WAAW,CAAC,SAAS,kBAAkB,CAAC,SAAS,oBAAoB,UAAU,GAAG,mBAAmB,OAAO;AAAA,0BAChH,KAAK;AAAA,0BACL,OAAO;AAAA,0BACP,WAAW,MAAM;AAAA,wBACnB,GAAG,MAAM,GAAG,WAAW,KAAK,mBAAmB,IAAI,IAAI;AAAA,sBACzD,CAAC;AAAA,oBACH,CAAC;AAAA,oBACD,GAAG;AAAA,kBACL,GAAG,EAAE,KAAK,mBAAmB,IAAI,IAAI;AAAA,kBACrC,gBAAmB,OAAO;AAAA,oBACxB,OAAO;AAAA,oBACP,KAAK,CAAC,OAAO,MAAM,UAAU;AAAA,oBAC7B,MAAM;AAAA,oBACN,MAAM,CAAC,SAAS,uBAAuB,CAAC,SAAS;AAAA,oBACjD,OAAO,eAAe,SAAS,sBAAsB,cAAc,SAAS,mBAAmB,OAAO,EAAE;AAAA,kBAC1G,GAAG;AAAA,qBACA,UAAU,IAAI,GAAG,mBAAmB,UAAU,MAAM,WAAW,SAAS,WAAW,CAAC,MAAM,MAAM;AAC/F,6BAAO,UAAU,GAAG,YAAY,wBAAwB;AAAA,wBACtD,KAAK;AAAA,wBACL,SAAS,KAAK;AAAA,wBACd,eAAe,SAAS;AAAA,wBACxB,MAAM;AAAA,wBACN,cAAc,OAAO,aAAa,WAAW,SAAS,cAAc,SAAS,gBAAgB,SAAS;AAAA,wBACtG,iBAAiB,SAAS;AAAA,wBAC1B,iBAAiB,SAAS;AAAA,wBAC1B,eAAe,SAAS,aAAa,SAAS,aAAa,CAAC;AAAA,sBAC9D,GAAG;AAAA,wBACD,gBAAgB,QAAQ,CAAC,EAAE,QAAQ,OAAO,WAAW,MAAM;AAAA,0BACzD,WAAW,KAAK,QAAQ,gBAAgB;AAAA,4BACtC;AAAA,4BACA,MAAM,MAAM;AAAA,4BACZ,YAAY;AAAA,4BACZ;AAAA,0BACF,GAAG,MAAM;AAAA,4BACP,SAAS,CAAC,OAAO,qBAAqB,UAAU,GAAG,mBAAmB,OAAO;AAAA,8BAC3E,KAAK;AAAA,8BACL,OAAO;AAAA,8BACP,WAAW,MAAM;AAAA,4BACnB,GAAG,MAAM,GAAG,WAAW,KAAK,mBAAmB,IAAI,IAAI;AAAA,4BACvD,KAAK,WAAW,UAAU,GAAG,mBAAmB,OAAO;AAAA,8BACrD,KAAK;AAAA,8BACL,OAAO;AAAA,8BACP,WAAW,KAAK;AAAA,4BAClB,GAAG,MAAM,GAAG,WAAW,KAAK,mBAAmB,IAAI,IAAI;AAAA,6BACtD,SAAS,eAAe,CAAC,OAAO,qBAAqB,SAAS,qBAAqB,OAAO,0BAA0B,OAAO,UAAU,UAAU,GAAG,mBAAmB,OAAO,aAAa;AAAA,8BACxL,WAAW,KAAK,QAAQ,gBAAgB;AAAA,gCACtC,MAAM,MAAM;AAAA,gCACZ;AAAA,8BACF,GAAG,MAAM;AAAA,gCACP,gBAAgB,gBAAgB,OAAO,MAAM,GAAG,CAAC;AAAA,8BACnD,CAAC;AAAA,4BACH,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,4BACjC,CAAC,SAAS,qBAAqB,QAAQ,KAAK,KAAK,SAAS,mBAAmB,UAAU,GAAG,mBAAmB,OAAO,aAAa;AAAA,8BAC/H,WAAW,KAAK,QAAQ,YAAY,CAAC,GAAG,MAAM;AAAA,gCAC5C,gBAAgB,gBAAgB,MAAM,MAAM,OAAO,GAAG,CAAC;AAAA,8BACzD,CAAC;AAAA,4BACH,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,0BACnC,CAAC;AAAA,wBACH,CAAC;AAAA,wBACD,OAAO,QAAQ,CAAC,EAAE,OAAO,KAAK,MAAM;AAAA,0BAClC,WAAW,KAAK,QAAQ,SAAS;AAAA,4BAC/B;AAAA,4BACA;AAAA,0BACF,GAAG,MAAM;AAAA,4BACP,SAAS,WAAW,SAAS,MAAM,iBAAiB,UAAU,GAAG,mBAAmB,OAAO;AAAA,8BACzF,KAAK;AAAA,8BACL,OAAO;AAAA,8BACP,iBAAiB;AAAA,8BACjB,QAAQ,CAAC,WAAW,SAAS,iBAAiB,QAAQ,KAAK;AAAA,8BAC3D,WAAW,MAAM;AAAA,4BACnB,GAAG,MAAM,IAAI,WAAW,KAAK,MAAM,SAAS,UAAU,GAAG,mBAAmB,OAAO;AAAA,8BACjF,KAAK;AAAA,8BACL,OAAO;AAAA,8BACP,WAAW,MAAM;AAAA,4BACnB,GAAG,MAAM,GAAG,WAAW,KAAK,mBAAmB,IAAI,IAAI;AAAA,4BACvD,OAAO,QAAQ,CAAC,MAAM,UAAU,EAAE,SAAS,gBAAgB,MAAM,UAAU,OAAO,qBAAqB,aAAa,CAAC,SAAS,oBAAoB,UAAU,GAAG,mBAAmB,OAAO,aAAa;AAAA,8BACpM,gBAAgB,gBAAgB,MAAM,MAAM,KAAK,WAAW,MAAM,OAAO,SAAS,UAAU,CAAC,GAAG,CAAC;AAAA,8BACjG,MAAM,kBAAkB,UAAU,GAAG,mBAAmB,QAAQ,aAAa,QAAQ,gBAAgB,MAAM,MAAM,KAAK,WAAW,MAAM,KAAK,SAAS,YAAY,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,8BAChN,MAAM,YAAY,MAAM,MAAM,SAAS,KAAK,aAAa,KAAK,CAAC,GAAG,cAAc,UAAU,GAAG,mBAAmB,SAAS,aAAa,OAAO,gBAAgB,MAAM,YAAY,CAAC,IAAI,iBAAiB,MAAM,MAAM,IAAI,CAAC,KAAK,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,4BAClR,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,4BACjC,MAAM,WAAW,EAAE,SAAS,eAAe,MAAM,UAAU,OAAO,qBAAqB,YAAY,CAAC,SAAS,oBAAoB,UAAU,GAAG,mBAAmB,OAAO;AAAA,8BACtK,KAAK;AAAA,8BACL,OAAO;AAAA,8BACP,WAAW,MAAM;AAAA,4BACnB,GAAG,MAAM,GAAG,WAAW,KAAK,mBAAmB,IAAI,IAAI;AAAA,0BACzD,CAAC;AAAA,wBACH,CAAC;AAAA,wBACD,YAAY,QAAQ,MAAM;AAAA,0BACxB,WAAW,KAAK,QAAQ,YAAY,CAAC,GAAG,MAAM;AAAA,4BAC5C,gBAAgB,gBAAgB,MAAM,MAAM,OAAO,GAAG,CAAC;AAAA,0BACzD,CAAC;AAAA,wBACH,CAAC;AAAA,wBACD,GAAG;AAAA,sBACL,GAAG,MAAM,CAAC,WAAW,eAAe,QAAQ,cAAc,iBAAiB,iBAAiB,aAAa,CAAC;AAAA,oBAC5G,CAAC,GAAG,GAAG;AAAA,kBACT,GAAG,IAAI,WAAW;AAAA,gBACpB,GAAG,IAAI,WAAW;AAAA,cACpB,CAAC;AAAA,YACH,GAAG,CAAC;AAAA,UACN,CAAC;AAAA,QACH,CAAC;AAAA,QACD,GAAG;AAAA,MACL,GAAG,GAAG,CAAC,QAAQ,QAAQ,CAAC;AAAA,MACxB,CAAC,MAAM,SAAS,UAAU,GAAG,mBAAmB,OAAO,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI;AAAA,QAC5F,gBAAmB,OAAO,MAAM,MAAM,EAAE;AAAA,MAC1C,EAAE,KAAK,mBAAmB,IAAI,IAAI;AAAA,IACpC,CAAC,KAAK,mBAAmB,IAAI,IAAI;AAAA,EACnC,GAAG,IAAI,UAAU;AACnB;AACA,IAAM,gBAAgB,KAAK;AAC3B,IAAM,gBAAgB;AAAA,EACpB,UAAU,MAAM,CAAC,EAAE,KAAK,EAAE;AAAA,EAC1B,eAAe,CAAC;AAAA,EAChB,QAAQ,MAAM,EAAE,EAAE,KAAK,EAAE;AAAA,EACzB,OAAO;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,KAAK;AAAA,EACL,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,IAAI;AAAA,EACJ,IAAI;AACN;AACA,IAAM,aAAa,CAAC,SAAS,QAAQ,SAAS,QAAQ,KAAK;AAC3D,IAAM,YAAY,IAAI,UAAU,aAAa;AAC7C,IAAM,YAAY;AAAA,EAChB,MAAM;AAAA,EACN,YAAY,EAAE,eAAe,MAAM,iBAAiB,QAAQ,kBAAkB,UAAU;AAAA;AAAA,EAExF,UAAU;AACR,WAAO;AAAA,MACL,QAAQ;AAAA,MACR,OAAO,KAAK;AAAA,MACZ,SAAS,KAAK;AAAA;AAAA,MAEd,UAAU,KAAK;AAAA,MACf,MAAM,KAAK;AAAA,MACX,YAAY,KAAK;AAAA,MACjB,oBAAoB,KAAK;AAAA,MACzB,YAAY,KAAK;AAAA;AAAA,MAEjB,MAAM,KAAK;AAAA,MACX,WAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,YAAY,EAAE,MAAM,QAAQ,SAAS,OAAO;AAAA;AAAA,IAE5C,iBAAiB,EAAE,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,OAAO;AAAA,IAC3D,eAAe,EAAE,MAAM,SAAS,SAAS,KAAK;AAAA,IAC9C,iBAAiB,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IACjD,iBAAiB,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IACjD,oBAAoB,EAAE,MAAM,SAAS,SAAS,KAAK;AAAA,IACnD,uBAAuB,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IACvD,aAAa,EAAE,MAAM,OAAO,SAAS,MAAM,CAAC,EAAE;AAAA,IAC9C,cAAc,EAAE,MAAM,OAAO,SAAS,MAAM,CAAC,EAAE;AAAA,IAC/C,mBAAmB,EAAE,MAAM,SAAS,SAAS,KAAK;AAAA;AAAA;AAAA,IAGlD,uBAAuB,EAAE,MAAM,QAAQ,SAAS,GAAG;AAAA,IACnD,gBAAgB,EAAE,MAAM,CAAC,SAAS,MAAM,GAAG,SAAS,MAAM;AAAA,IAC1D,QAAQ,EAAE,MAAM,OAAO,SAAS,MAAM,CAAC,EAAE;AAAA,IACzC,uBAAuB,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IACvD,mBAAmB,EAAE,MAAM,CAAC,SAAS,MAAM,GAAG,SAAS,MAAM;AAAA,IAC7D,UAAU,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IAC1C,cAAc,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IAC9C,kBAAkB,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IAClD,cAAc,EAAE,MAAM,OAAO,SAAS,MAAM,CAAC,EAAE;AAAA,IAC/C,cAAc,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IAC9C,QAAQ,EAAE,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,KAAK;AAAA,IAChD,SAAS,EAAE,MAAM,CAAC,QAAQ,IAAI,GAAG,SAAS,GAAG;AAAA,IAC7C,cAAc,EAAE,MAAM,QAAQ,SAAS,EAAE;AAAA,IACzC,SAAS,EAAE,MAAM,CAAC,QAAQ,IAAI,GAAG,SAAS,GAAG;AAAA,IAC7C,eAAe,EAAE,MAAM,QAAQ,SAAS,EAAE;AAAA,IAC1C,eAAe,EAAE,MAAM,QAAQ,SAAS,EAAE;AAAA,IAC1C,cAAc,EAAE,MAAM,CAAC,UAAU,IAAI,GAAG,SAAS,KAAK;AAAA,IACtD,eAAe,EAAE,MAAM,CAAC,UAAU,IAAI,GAAG,SAAS,KAAK;AAAA,IACvD,iBAAiB,EAAE,MAAM,CAAC,UAAU,IAAI,GAAG,SAAS,KAAK;AAAA,IACzD,qBAAqB,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IACrD,SAAS,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IACzC,cAAc,EAAE,MAAM,CAAC,QAAQ,IAAI,GAAG,SAAS,GAAG;AAAA,IAClD,kBAAkB,EAAE,MAAM,CAAC,SAAS,MAAM,GAAG,SAAS,MAAM;AAAA,IAC5D,iBAAiB,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IACjD,iBAAiB,EAAE,MAAM,CAAC,SAAS,MAAM,GAAG,SAAS,MAAM;AAAA,IAC3D,YAAY,EAAE,MAAM,QAAQ,SAAS,EAAE;AAAA,IACvC,OAAO,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IACvC,cAAc,EAAE,MAAM,QAAQ,SAAS,OAAO,CAAC,GAAG;AAAA,IAClD,WAAW,EAAE,MAAM,OAAO,SAAS,MAAM,CAAC,EAAE;AAAA,IAC5C,mBAAmB,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IACnD,mBAAmB,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IACnD,MAAM,EAAE,MAAM,SAAS,SAAS,KAAK;AAAA,IACrC,gBAAgB,EAAE,MAAM,QAAQ,SAAS,GAAG;AAAA;AAAA,IAE5C,YAAY,EAAE,MAAM,QAAQ,SAAS,GAAG;AAAA,IACxC,UAAU,EAAE,MAAM,QAAQ,SAAS,EAAE;AAAA;AAAA,IAErC,UAAU,EAAE,MAAM,QAAQ,SAAS,GAAG;AAAA;AAAA,IAEtC,QAAQ,EAAE,MAAM,QAAQ,SAAS,cAAc;AAAA;AAAA,IAE/C,aAAa,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IAC7C,aAAa,EAAE,MAAM,SAAS,SAAS,KAAK;AAAA,IAC5C,YAAY,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,IAC5C,eAAe,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA;AAAA,IAE/C,QAAQ,EAAE,MAAM,SAAS,SAAS,MAAM;AAAA,EAC1C;AAAA,EACA,OAAO;AACL,WAAO;AAAA,MACL,OAAO;AAAA;AAAA;AAAA,MAGP,OAAO,EAAE,GAAG,cAAc;AAAA,MAC1B,OAAO;AAAA;AAAA,QAEL,OAAO,KAAK,wBAAwB,UAAU,iBAAiB,IAAI,UAAU;AAAA,QAC7E,MAAM;AAAA;AAAA;AAAA;AAAA,QAIN,OAAO;AAAA,MACT;AAAA,MACA,SAAS,EAAE,KAAK,KAAK;AAAA,MACrB,SAAS;AAAA;AAAA,MAET,MAAM;AAAA,QACJ,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,WAAW;AAAA,QACX,SAAS;AAAA,QACT,eAAe;AAAA,QACf,cAAc;AAAA,QACd,cAAc;AAAA;AAAA;AAAA,QAGd,QAAQ,CAAC;AAAA,MACX;AAAA,MACA,kBAAkB;AAAA;AAAA;AAAA;AAAA,MAIlB,KAAqB,oBAAI,KAAK;AAAA;AAAA,MAE9B,eAAe,CAAC,MAAM,IAAI;AAAA;AAAA;AAAA,MAG1B,WAAW;AAAA,QACT,eAAe;AAAA,UACb,MAAM;AAAA;AAAA,UAEN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,SAAS;AAAA,UACT,wBAAwB;AAAA,UACxB,aAAa;AAAA,UACb,KAAK;AAAA,UACL,WAAW;AAAA,UACX,SAAS;AAAA,QACX;AAAA,QACA,aAAa;AAAA;AAAA,UAEX,MAAM;AAAA,QACR;AAAA,QACA,mBAAmB;AAAA,UACjB,cAAc;AAAA,UACd,OAAO;AAAA;AAAA,UAEP,OAAO;AAAA,UACP,OAAO;AAAA,QACT;AAAA,QACA,cAAc;AAAA,UACZ,MAAM;AAAA;AAAA;AAAA;AAAA,UAIN,UAAU;AAAA,QACZ;AAAA,QACA,kBAAkB;AAAA,UAChB,MAAM;AAAA;AAAA,UAEN,SAAS;AAAA;AAAA,UAET,WAAW;AAAA,QACb;AAAA,QACA,aAAa;AAAA,UACX,MAAM;AAAA,UACN,SAAS;AAAA;AAAA,QAEX;AAAA,QACA,gBAAgB;AAAA,UACd,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,SAAS;AAAA;AAAA,UAET,WAAW;AAAA,UACX,cAAc;AAAA,QAChB;AAAA;AAAA;AAAA,QAGA,0BAA0B;AAAA,MAC5B;AAAA;AAAA;AAAA,MAGA,eAAe,CAAC;AAAA;AAAA,MAEhB,qBAAqB;AAAA,IACvB;AAAA,EACF;AAAA,EACA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMP,MAAM,WAAW,QAAQ;AACvB,UAAI,OAAO,KAAK,WAAW,UAAU;AACnC,aAAK,QAAQ,OAAO,OAAO,CAAC,GAAG,eAAe,MAAM;AACpD,aAAK,MAAM,KAAK,YAAY,KAAK,KAAK;AACtC;AAAA,MACF;AACA,YAAM,QAAQ,MAAM,qCAAqD,OAAO,OAAO,EAAE,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,qBAAqB,MAAM,OAAO,wBAAoB,GAAG,qBAAqB,MAAM,OAAO,wBAAoB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,kBAAkB,MAAM,OAAO,qBAAiB,GAAG,qBAAqB,MAAM,OAAO,wBAAoB,GAAG,qBAAqB,MAAM,OAAO,wBAAoB,EAAE,CAAC,GAAG,UAAU,MAAM,SAAS,CAAC;AAC/xE,WAAK,QAAQ,OAAO,OAAO,CAAC,GAAG,eAAe,KAAK;AACnD,WAAK,MAAM,KAAK,YAAY,KAAK,KAAK;AAAA,IACxC;AAAA;AAAA;AAAA;AAAA,IAIA,kBAAkB;AAChB,aAAO,gCAAuB,EAAE,KAAK,CAAC,aAAa;AACjD,cAAM,EAAE,YAAY,IAAI;AACxB,aAAK,QAAQ,MAAM,IAAI,YAAY,IAAI;AAAA,MACzC,CAAC,EAAE,MAAM,MAAM,QAAQ,KAAK,sCAAsC,CAAC;AAAA,IACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,aAAa,MAAM;AACjB,UAAI,CAAC,WAAW,SAAS,IAAI,GAAG;AAC9B,gBAAQ,MAAM,qDAAqD,IAAI;AAAA,+BAChD,WAAW,KAAK,IAAI,CAAC,GAAG;AAC/C,eAAO;AAAA,MACT;AACA,UAAI,CAAC,KAAK,aAAa,SAAS,IAAI,GAAG;AACrC,gBAAQ,KAAK,sCAAsC,IAAI,6BAA6B,KAAK,aAAa,CAAC,CAAC,iBAAiB;AACzH,eAAO,KAAK,aAAa,CAAC;AAAA,MAC5B;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,qBAAqB,OAAO,MAAM;AAChC,WAAK,sBAAsB;AAC3B,YAAM,OAAO,KAAK,aAAa,KAAK,aAAa,QAAQ,KAAK,KAAK,EAAE,IAAI,CAAC;AAC1E,UAAI,KAAM,MAAK,WAAW,MAAM,IAAI;AAAA,IACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,WAAW,MAAM,OAAO,MAAM,mBAAmB,OAAO;AACtD,aAAO,KAAK,aAAa,IAAI;AAC7B,YAAM,MAAM,KAAK,MAAM;AACvB,YAAM,uBAAuB,KAAK,KAAK,aAAa,KAAK,KAAK,UAAU,QAAQ;AAChF,UAAI,KAAK,eAAe,kBAAkB;AACxC,YAAI,KAAK,KAAK,OAAO,KAAM;AAC3B,cAAM,QAAQ,KAAK;AACnB,aAAK,sBAAsB,MAAM,QAAQ,KAAK,KAAK,EAAE,IAAI,MAAM,QAAQ,IAAI,IAAI,SAAS;AAAA,MAC1F;AACA,YAAM,UAAU,KAAK,KAAK;AAC1B,WAAK,KAAK,SAAS,CAAC;AACpB,WAAK,KAAK,KAAK;AACf,WAAK,KAAK,gBAAgB;AAC1B,WAAK,KAAK,eAAe;AACzB,UAAI,CAAC,KAAM,QAAO,KAAK,KAAK,gBAAgB,KAAK,KAAK;AACtD,cAAQ,MAAM;AAAA,QACZ,KAAK,SAAS;AACZ,eAAK,KAAK,YAAY,IAAI,KAAK,KAAK,MAAM,KAAK,YAAY,IAAI,EAAE,IAAI,MAAM,KAAK,GAAG,CAAC;AACpF,eAAK,KAAK,UAAU,IAAI,KAAK,KAAK,KAAK,UAAU,YAAY,IAAI,IAAI,GAAG,CAAC;AACzE,eAAK,KAAK,QAAQ,WAAW,EAAE;AAC/B;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,eAAK,KAAK,YAAY,IAAI,KAAK,KAAK,YAAY,GAAG,GAAG,CAAC;AACvD,eAAK,KAAK,UAAU,IAAI,KAAK,KAAK,YAAY,IAAI,GAAG,GAAG,CAAC;AACzD,eAAK,KAAK,QAAQ,WAAW,EAAE;AAC/B;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,eAAK,KAAK,YAAY,IAAI,KAAK,KAAK,YAAY,GAAG,KAAK,SAAS,GAAG,CAAC;AACrE,eAAK,KAAK,UAAU,IAAI,KAAK,KAAK,YAAY,GAAG,KAAK,SAAS,IAAI,GAAG,CAAC;AACvE,eAAK,KAAK,QAAQ,WAAW,EAAE;AAC/B,cAAI,YAAY,IAAI,KAAK,KAAK,KAAK,SAAS;AAC5C,cAAI,UAAU,OAAO,OAAO,KAAK,oBAAoB,IAAI,IAAI;AAC3D,wBAAY,IAAI,0BAA0B,WAAW,KAAK,iBAAiB;AAAA,UAC7E;AACA,eAAK,KAAK,gBAAgB;AAC1B,eAAK,KAAK,eAAe,IAAI,QAAQ,WAAW,EAAE;AAClD,eAAK,KAAK,aAAa,SAAS,IAAI,IAAI,IAAI,CAAC;AAC7C,cAAI,KAAK,cAAc;AACrB,gBAAI,CAAC,GAAG,CAAC,EAAE,SAAS,KAAK,KAAK,cAAc,OAAO,CAAC,GAAG;AACrD,oBAAM,YAAY,KAAK,KAAK,cAAc,OAAO,MAAM,KAAK,CAAC,KAAK,oBAAoB,IAAI;AAC1F,mBAAK,KAAK,gBAAgB,IAAI,QAAQ,KAAK,KAAK,eAAe,SAAS;AAAA,YAC1E;AACA,gBAAI,CAAC,GAAG,CAAC,EAAE,SAAS,KAAK,KAAK,UAAU,OAAO,CAAC,GAAG;AACjD,oBAAM,YAAY,KAAK,KAAK,UAAU,OAAO,MAAM,IAAI,IAAI;AAC3D,mBAAK,KAAK,YAAY,IAAI,QAAQ,KAAK,KAAK,WAAW,SAAS;AAAA,YAClE;AACA,gBAAI,CAAC,GAAG,CAAC,EAAE,SAAS,KAAK,KAAK,aAAa,OAAO,CAAC,GAAG;AACpD,oBAAM,iBAAiB,KAAK,KAAK,aAAa,OAAO,MAAM,KAAK,CAAC,KAAK,oBAAoB,IAAI;AAC9F,mBAAK,KAAK,eAAe,IAAI,aAAa,KAAK,KAAK,cAAc,cAAc;AAAA,YAClF;AACA,gBAAI,CAAC,GAAG,CAAC,EAAE,SAAS,KAAK,KAAK,QAAQ,OAAO,CAAC,GAAG;AAC/C,oBAAM,iBAAiB,KAAK,KAAK,QAAQ,OAAO,MAAM,IAAI,IAAI;AAC9D,mBAAK,KAAK,UAAU,IAAI,aAAa,KAAK,KAAK,SAAS,cAAc;AAAA,YACxE;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,iBAAO,IAAI,0BAA0B,MAAM,KAAK,iBAAiB;AACjE,gBAAM,gBAAgB,KAAK,eAAe,IAAI;AAC9C,eAAK,KAAK,YAAY,KAAK,gBAAgB,KAAK,oBAAoB,IAAI,QAAQ,MAAM,CAAC,IAAI;AAC3F,eAAK,KAAK,UAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AACvC,eAAK,KAAK,UAAU,IAAI,QAAQ,MAAM,aAAa;AACnD,eAAK,KAAK,QAAQ,WAAW,EAAE;AAC/B;AAAA,QACF;AAAA,QACA,KAAK,OAAO;AACV,eAAK,KAAK,YAAY;AACtB,eAAK,KAAK,UAAU,SAAS,GAAG,GAAG,GAAG,CAAC;AACvC,eAAK,KAAK,UAAU,IAAI,KAAK,IAAI;AACjC,eAAK,KAAK,QAAQ,SAAS,IAAI,IAAI,IAAI,CAAC;AACxC;AAAA,QACF;AAAA,MACF;AACA,WAAK,gBAAgB;AACrB,YAAM,WAAW,KAAK,KAAK,aAAa,KAAK,KAAK,UAAU,QAAQ;AACpE,UAAI,YAAY,QAAQ,aAAa,qBAAsB;AAC3D,WAAK,MAAM,qBAAqB,IAAI;AACpC,UAAI,KAAK,OAAO;AACd,cAAM,YAAY,KAAK,KAAK;AAC5B,cAAM,SAAS;AAAA,UACb;AAAA,UACA;AAAA,UACA,SAAS,KAAK,KAAK;AAAA,UACnB,GAAG,KAAK,cAAc;AAAA,YACpB,eAAe,KAAK,KAAK;AAAA,YACzB,cAAc,KAAK,KAAK;AAAA,YACxB,kBAAkB,KAAK,KAAK,iBAAiB,IAAI,KAAK,YAAY;AAAA,UACpE,IAAI,CAAC;AAAA,UACL,QAAQ,KAAK,KAAK,OAAO,IAAI,KAAK,YAAY;AAAA,UAC9C,GAAG,KAAK,aAAa,EAAE,MAAM,IAAI,QAAQ,KAAK,oBAAoB,IAAI,QAAQ,WAAW,CAAC,IAAI,SAAS,EAAE,IAAI,CAAC;AAAA,QAChH;AACA,aAAK,MAAM,eAAe,MAAM;AAAA,MAClC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAIA,WAAW;AACT,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA,IAIA,OAAO;AACL,WAAK,aAAa;AAAA,IACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,aAAa,OAAO,MAAM;AACxB,YAAM,MAAM,KAAK,MAAM;AACvB,WAAK,sBAAsB,OAAO,UAAU;AAC5C,YAAM,WAAW,OAAO,IAAI;AAC5B,UAAI,gBAAgB;AACpB,YAAM,EAAE,WAAW,IAAI,OAAO,IAAI,KAAK;AACvC,cAAQ,QAAQ;AAAA,QACd,KAAK;AACH,0BAAgB,IAAI,KAAK,UAAU,YAAY,IAAI,KAAK,UAAU,GAAG,CAAC;AACtE;AAAA,QACF,KAAK;AACH,0BAAgB,IAAI,KAAK,UAAU,YAAY,IAAI,IAAI,UAAU,GAAG,CAAC;AACrE;AAAA,QACF,KAAK;AACH,0BAAgB,IAAI,KAAK,UAAU,YAAY,GAAG,UAAU,SAAS,IAAI,IAAI,UAAU,CAAC;AACxF;AAAA,QACF,KAAK;AACH,0BAAgB,IAAI,OAAO,YAAY,cAAc,EAAE,IAAI,0BAA0B,WAAW,KAAK,iBAAiB,GAAG,CAAC;AAC1H;AAAA,QACF,KAAK;AACH,0BAAgB,IAAI,OAAO,YAAY,cAAc,EAAE,WAAW,CAAC;AACnE,gBAAM,UAAU,cAAc,OAAO;AACrC,gBAAM,eAAe,KAAK,oBAAoB,WAAW,WAAW,KAAK;AACzE,gBAAM,cAAc,KAAK,SAAS,YAAY,EAAE;AAChD,cAAI,aAAa;AACf,kBAAM,gBAAgB,KAAK,SAAS,IAAI,CAAC,KAAK,OAAO,EAAE,GAAG,KAAK,EAAE,EAAE;AACnE,gBAAI,eAAe;AACnB,gBAAI,MAAM;AACR,eAAC,GAAG,cAAc,MAAM,YAAY,GAAG,GAAG,aAAa,EAAE,KAAK,CAAC,QAAQ;AACrE;AACA,uBAAO,CAAC,IAAI;AAAA,cACd,CAAC,EAAE;AACH;AAAA,YACF,OAAO;AACL,eAAC,GAAG,eAAe,GAAG,cAAc,MAAM,GAAG,YAAY,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAClF;AACA,uBAAO,CAAC,IAAI;AAAA,cACd,CAAC,EAAE;AAAA,YACL;AACA,4BAAgB,IAAI,OAAO,YAAY,cAAc,EAAE,eAAe,YAAY;AAAA,UACpF;AACA;AAAA,MACJ;AACA,UAAI,cAAe,MAAK,WAAW,QAAQ,aAAa;AAAA,IAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,gBAAgB,SAAS,CAAC,GAAG;AAC3B,YAAM,KAAK,KAAK,MAAM;AACtB,YAAM,EAAE,WAAW,SAAS,eAAe,aAAa,IAAI,KAAK;AACjE,UAAI,CAAC,OAAO,OAAQ,MAAK,KAAK,SAAS,CAAC;AACxC,eAAS,OAAO,SAAS,SAAS,CAAC,GAAG,KAAK,aAAa;AACxD,UAAI,CAAC,UAAU,KAAK,qBAAqB,CAAC,KAAK,sBAAuB;AACtE,UAAI,iBAAiB,OAAO,OAAO,CAAC,MAAM,GAAG,aAAa,GAAG,WAAW,OAAO,CAAC;AAChF,UAAI,CAAC,KAAK,qBAAqB,EAAE,KAAK,eAAe,CAAC,KAAK,oBAAoB;AAC7E,yBAAiB,eAAe,IAAI,CAAC,MAAM;AACzC,iBAAO,EAAE,YAAY,IAAI,GAAG,oBAAoB,GAAG,iBAAiB,WAAW,gBAAgB,OAAO,IAAI;AAAA,QAC5G,CAAC;AAAA,MACH;AACA,WAAK,KAAK,OAAO,KAAK,GAAG,cAAc;AACvC,UAAI,KAAK,aAAa;AACpB,aAAK,KAAK,mBAAmB,CAAC;AAC9B,eAAO,QAAQ,CAAC,MAAM;AACpB,cAAI,GAAG,aAAa,GAAG,eAAe,SAAS,KAAK,GAAG,aAAa,GAAG,SAAS,YAAY,GAAG;AAC7F,gBAAI,CAAC,KAAK,KAAK,OAAO,KAAK,CAAC,OAAO,GAAG,SAAS,EAAE,IAAI,EAAG,MAAK,KAAK,iBAAiB,KAAK,CAAC;AAAA,UAC3F;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,aAAa,IAAI,OAAO;AACtB,cAAQ,KAAK,GAAG,kBAAkB,CAAC,GAAG,UAAU,SAAS,KAAK,GAAG;AAAA,MACjE;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,oBAAoB,IAAI;AACtB,aAAO,GAAG,UAAU,SAAS,eAAe,KAAK,KAAK,aAAa,IAAI,eAAe;AAAA,IACxF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,YAAY,GAAG;AACb,YAAM,EAAE,eAAe,aAAa,kBAAkB,IAAI,KAAK;AAC/D,UAAI,cAAc,SAAS,QAAQ,YAAY,SAAS,QAAQ,CAAC,kBAAkB,MAAO;AAC1F,QAAE,eAAe;AACjB,UAAI,cAAc,KAAM,MAAK,cAAc,CAAC;AAAA,eACnC,KAAK,qBAAqB,kBAAkB,MAAO,MAAK,kBAAkB,CAAC;AAAA,IACtF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,UAAU,GAAG;AACX,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,YAAM,EAAE,MAAM,oBAAoB,IAAI;AACtC,YAAM,EAAE,MAAM,YAAY,IAAI;AAC9B,UAAI,aAAa;AACjB,YAAM,EAAE,OAAO,kBAAkB,OAAO,kBAAkB,IAAI;AAC9D,YAAM,iBAAiB,KAAK,oBAAoB,EAAE,MAAM;AACxD,YAAM,eAAe,aAAa;AAClC,mBAAa,WAAW;AACxB,UAAI,eAAgB,MAAK,UAAU,2BAA2B;AAC9D,UAAI,eAAe,aAAc;AACjC,UAAI,aAAa;AACf,cAAM,EAAE,aAAa,wBAAwB,eAAe,IAAI;AAChE,cAAM,QAAQ,KAAK,KAAK,OAAO,KAAK,CAAC,OAAO,GAAG,SAAS,cAAc,IAAI;AAC1E,qBAAa,kBAAkB,mBAAmB;AAClD,YAAI,SAAS,MAAM,IAAI,QAAQ,MAAM,YAAY,QAAQ,GAAG;AAC1D,gBAAM,eAAe,KAAK,cAAc,KAAK,CAAC,OAAO,GAAG,SAAS,cAAc,IAAI;AACnF,uBAAa,iBAAiB,MAAM;AACpC,uBAAa,MAAM,MAAM;AACzB,gBAAM,aAAa,KAAK,aAAa,KAAK;AAC1C,gBAAM,gBAAgB;AAAA,YACpB,GAAG,KAAK,aAAa,KAAK;AAAA,YAC1B,KAAK;AAAA,YACL,gBAAgB,MAAM;AAAA,UACxB;AACA,eAAK,MAAM,yBAAyB,EAAE,OAAO,YAAY,SAAS,cAAc,aAAa,cAAc,CAAC;AAC5G,eAAK,MAAM,gBAAgB,EAAE,OAAO,YAAY,cAAc,CAAC;AAAA,QACjE;AACA,YAAI,MAAO,OAAM,WAAW;AAC5B,sBAAc,OAAO;AACrB,sBAAc,QAAQ;AACtB,sBAAc,QAAQ;AACtB,sBAAc,UAAU;AACxB,sBAAc,yBAAyB;AACvC,sBAAc,cAAc;AAC5B,sBAAc,iBAAiB;AAC/B,sBAAc,YAAY;AAC1B,sBAAc,UAAU;AAAA,MAC1B,WAAW,mBAAmB;AAC5B,YAAI,kBAAkB;AACpB,eAAK,cAAc,qBAAqB,gBAAgB;AACxD,4BAAkB,MAAM,WAAW;AAAA,QACrC;AACA,0BAAkB,QAAQ;AAC1B,0BAAkB,QAAQ;AAC1B,0BAAkB,QAAQ;AAAA,MAC5B;AACA,UAAI,CAAC,kBAAkB,CAAC,YAAa,MAAK,aAAa;AACvD,UAAI,iBAAiB,aAAa,CAAC,qBAAqB;AACtD,qBAAa,iBAAiB,SAAS;AACvC,yBAAiB,YAAY;AAAA,MAC/B;AACA,UAAI,eAAe,WAAW;AAC5B,qBAAa,eAAe,SAAS;AACrC,uBAAe,YAAY;AAAA,MAC7B;AACA,YAAM,oBAAoB,OAAO,KAAK,iBAAiB;AACvD,UAAI,gBAAgB,CAAC,cAAc,CAAC,uBAAuB,CAAC,oBAAoB,mBAAmB;AACjG,YAAI,QAAQ,KAAK,KAAK,OAAO,KAAK,CAAC,OAAO,GAAG,SAAS,aAAa,IAAI;AACvE,YAAI,CAAC,SAAS,KAAK,YAAa,SAAQ,KAAK,KAAK,iBAAiB,KAAK,CAAC,OAAO,GAAG,SAAS,aAAa,IAAI;AAC7G,eAAO,SAAS,KAAK,aAAa,OAAO,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,QAAQ,GAAG;AACT,UAAI,EAAE,YAAY,GAAI,MAAK,aAAa;AAAA,IAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,cAAc,GAAG;AACf,YAAM,EAAE,cAAc,IAAI,KAAK;AAC/B,YAAM,QAAQ,KAAK,KAAK,OAAO,KAAK,CAAC,OAAO,GAAG,SAAS,cAAc,IAAI,KAAK,EAAE,UAAU,CAAC,EAAE;AAC9F,YAAM,EAAE,SAAS,aAAa,IAAI,KAAK,gBAAgB,CAAC;AACxD,YAAM,UAAU,MAAM,YAAY,MAAM,SAAS,cAAc,OAAO;AACtE,YAAM,EAAE,MAAM,KAAK,OAAO,GAAG,IAAI,KAAK;AACtC,YAAM,iBAAiB,KAAK,IAAI,SAAS,KAAK,WAAW,IAAI,WAAW,OAAO,mBAAmB,CAAC;AACnG,YAAM,iBAAiB,cAAc,iBAAiB;AACtD,UAAI,KAAK,YAAY;AACnB,cAAM,mBAAmB,MAAM,iBAAiB,KAAK,aAAa;AAClE,cAAM,iBAAiB,mBAAmB,mBAAmB,KAAK;AAAA,MACpE;AACA,UAAI,QAAS,SAAQ,iBAAiB,MAAM;AAC5C,YAAM,IAAI,SAAS,GAAG,MAAM,gBAAgB,MAAM,mBAAmB,gBAAgB,KAAK,GAAG,CAAC;AAC9F,UAAI,KAAK,WAAW,KAAK,YAAY;AACnC,cAAM,YAAY,IAAI,UAAU,MAAM,OAAO,MAAM,GAAG;AACtD,cAAM,QAAQ,KAAK;AACnB,cAAM,YAAY,MAAM,cAAc,MAAM;AAC5C,cAAM,UAAU,KAAK,MAAM,aAAa,IAAI,SAAS;AACrD,YAAI,cAAc,cAAc,KAAM,eAAc,YAAY,WAAW,MAAM,YAAY;AAC7F,YAAI,cAAc,YAAY,SAAS;AACrC,wBAAc,UAAU;AACxB,gBAAM,SAAS,IAAI,QAAQ,MAAM,OAAO,UAAU,cAAc,SAAS;AACzE,gBAAM,eAAe,KAAK,IAAI,IAAI,UAAU,MAAM,OAAO,MAAM,GAAG,CAAC;AACnE,cAAI,iBAAiB,MAAM,WAAW;AACpC,gBAAI,2BAA2B;AAC/B,gBAAI,eAAe,MAAM,UAAW,4BAA2B,GAAG,gBAAgB,KAAK;AAAA,gBAClF,4BAA2B,GAAG,mBAAmB,KAAK;AAC3D,0BAAc,UAAU;AACxB,kBAAM,kBAAkB;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM,kBAAkB,EAAE,MAAM,MAAM,MAAM,KAAK,MAAM,KAAK,gBAAgB,MAAM,eAAe,CAAC;AAAA,IACzG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,kBAAkB,GAAG;AACnB,YAAM,EAAE,kBAAkB,IAAI,KAAK;AACnC,YAAM,EAAE,OAAO,cAAc,MAAM,IAAI;AACvC,YAAM,eAAe,IAAI,KAAK,KAAK;AACnC,YAAM,EAAE,SAAS,cAAc,EAAE,EAAE,EAAE,IAAI,KAAK,gBAAgB,CAAC;AAC/D,UAAI,CAAC,kBAAkB,SAAS,KAAK,IAAI,eAAe,CAAC,IAAI,KAAK,sBAAuB;AACzF,UAAI,CAAC,kBAAkB,OAAO;AAC5B,0BAAkB,QAAQ,KAAK,MAAM,MAAM,cAAc,OAAO,GAAG,EAAE,MAAM,CAAC;AAC5E,YAAI,CAAC,kBAAkB,OAAO;AAC5B,4BAAkB,QAAQ;AAC1B,4BAAkB,QAAQ;AAC1B,4BAAkB,QAAQ;AAC1B;AAAA,QACF;AACA,0BAAkB,MAAM,WAAW;AAAA,MACrC,OAAO;AACL,qBAAa,SAAS,GAAG,SAAS,YAAY,gBAAgB,KAAK,GAAG,CAAC;AACvE,YAAI,KAAK,YAAY;AACnB,cAAI,cAAc,aAAa,SAAS,IAAI,KAAK,aAAa,WAAW;AACzE,gBAAM,mBAAmB,cAAc,KAAK,aAAa;AACzD,wBAAc,mBAAmB,mBAAmB,KAAK;AACzD,uBAAa,SAAS,GAAG,aAAa,GAAG,CAAC;AAAA,QAC5C;AACA,cAAM,iBAAiB,QAAQ;AAC/B,cAAM,EAAE,MAAM,IAAI;AAClB,cAAM,QAAQ,iBAAiB,QAAQ;AACvC,cAAM,MAAM,iBAAiB,eAAe;AAC5C,cAAM,mBAAmB,MAAM,MAAM,SAAS,IAAI,KAAK,MAAM,MAAM,WAAW;AAC9E,cAAM,iBAAiB,MAAM,IAAI,SAAS,IAAI,KAAK,MAAM,IAAI,WAAW;AAAA,MAC1E;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAIA,eAAe;AACb,YAAM,EAAE,cAAc,iBAAiB,IAAI,KAAK;AAChD,YAAM,QAAQ,KAAK,KAAK,OAAO,KAAK,CAAC,MAAM,EAAE,UAAU,aAAa,QAAQ,iBAAiB,KAAK;AAClG,mBAAa,OAAO;AACpB,uBAAiB,OAAO;AACxB,UAAI,OAAO;AACT,cAAM,UAAU;AAChB,cAAM,WAAW;AAAA,MACnB;AAAA,IACF;AAAA;AAAA;AAAA;AAAA,IAIA,eAAe;AACb,YAAM,EAAE,iBAAiB,IAAI,KAAK;AAClC,UAAI,iBAAiB,MAAM;AACzB,cAAM,QAAQ,KAAK,KAAK,OAAO,KAAK,CAAC,MAAM,EAAE,SAAS,iBAAiB,IAAI;AAC3E,YAAI,MAAO,OAAM,WAAW;AAC5B,yBAAiB,OAAO;AACxB,yBAAiB,YAAY;AAAA,MAC/B;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,iBAAiB,GAAG,OAAO;AACzB,UAAI,MAAM,UAAU,EAAE,OAAO,UAAW;AACxC,YAAM,WAAW,MAAM;AACvB,YAAM,QAAQ,EAAE,OAAO;AACvB,YAAM,aAAa,KAAK,aAAa,KAAK;AAC1C,WAAK,MAAM,sBAAsB,EAAE,OAAO,YAAY,SAAS,CAAC;AAChE,WAAK,MAAM,gBAAgB,EAAE,OAAO,YAAY,eAAe,EAAE,GAAG,YAAY,OAAO,SAAS,EAAE,CAAC;AAAA,IACrG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,sBAAsB;AACpB,YAAM,MAAM,KAAK,MAAM;AACvB,WAAK,gBAAgB,CAAC;AACtB,WAAK,OAAO,QAAQ,CAAC,UAAU;AAC7B,cAAM,QAAQ,OAAO,MAAM,UAAU,WAAW,IAAI,aAAa,MAAM,KAAK,IAAI,MAAM;AACtF,cAAM,aAAa,IAAI,eAAe,KAAK;AAC3C,cAAM,mBAAmB,IAAI,cAAc,KAAK;AAChD,YAAI,MAAM;AACV,YAAI,OAAO,MAAM,QAAQ,YAAY,MAAM,IAAI,SAAS,OAAO,GAAG;AAChE,gBAAM,IAAI,KAAK,MAAM,IAAI,QAAQ,UAAU,EAAE,CAAC;AAC9C,cAAI,SAAS,IAAI,IAAI,IAAI,CAAC;AAAA,QAC5B,MAAO,OAAM,OAAO,MAAM,QAAQ,WAAW,IAAI,aAAa,MAAM,GAAG,IAAI,MAAM;AACjF,YAAI,WAAW,IAAI,eAAe,GAAG;AACrC,YAAI,iBAAiB,IAAI,cAAc,GAAG;AAC1C,YAAI,CAAC,kBAAkB,mBAAmB,eAAe;AACvD,cAAI,CAAC,KAAK,QAAQ,OAAO,MAAM,QAAQ,YAAY,MAAM,IAAI,WAAW,IAAI;AAC1E,gBAAI,SAAS,IAAI,IAAI,IAAI,CAAC;AAAA,UAC5B,MAAO,KAAI,WAAW,IAAI,WAAW,IAAI,CAAC;AAC1C,qBAAW,IAAI,eAAe,GAAG;AACjC,2BAAiB;AAAA,QACnB;AACA,cAAM,eAAe,eAAe;AACpC,gBAAQ,OAAO,OAAO,EAAE,GAAG,KAAK,MAAM,MAAM,cAAc,GAAG,OAAO;AAAA;AAAA,UAElE,MAAM,GAAG,KAAK,EAAE,GAAG,IAAI,KAAK,kBAAkB;AAAA,UAC9C,UAAU,eAAe,CAAC,IAAI;AAAA,UAC9B;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,WAAW,eAAe,IAAI,UAAU,OAAO,GAAG,IAAI;AAAA,UACtD,OAAO,MAAM;AAAA,QACf,CAAC;AACD,aAAK,cAAc,KAAK,KAAK;AAAA,MAC/B,CAAC;AAAA,IACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,gBAAgB,GAAG;AACjB,aAAO,KAAK,MAAM,KAAK,gBAAgB,CAAC;AAAA,IAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYA,YAAY,UAAU,UAAU,eAAe,CAAC,GAAG;AACjD,aAAO,KAAK,MAAM,MAAM,cAAc,UAAU,UAAU,YAAY;AAAA,IACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,aAAa,OAAO;AAClB,cAAQ,EAAE,GAAG,MAAM;AACnB,YAAM,eAAe;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AACA,mBAAa,QAAQ,CAAC,SAAS;AAC7B,YAAI,QAAQ,MAAO,QAAO,MAAM,IAAI;AAAA,MACtC,CAAC;AACD,UAAI,CAAC,MAAM,OAAQ,QAAO,MAAM;AAChC,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,cAAc,WAAW,OAAO;AAC9B,WAAK,MAAM,WAAW,KAAK,aAAa,KAAK,CAAC;AAAA,IAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWA,mBAAmB,MAAM;AACvB,UAAI,QAAQ,OAAO,SAAS,SAAU,QAAO,KAAK,MAAM,KAAK,aAAa,IAAI;AAAA,UACzE,QAAO,IAAI,KAAK,IAAI;AACzB,UAAI,QAAQ,gBAAgB,MAAM;AAChC,cAAM,EAAE,aAAa,IAAI,KAAK;AAC9B,YAAI,aAAc,MAAK,sBAAsB,aAAa,QAAQ,IAAI,KAAK,QAAQ,IAAI,SAAS;AAChG,aAAK,SAAS,GAAG,GAAG,GAAG,CAAC;AACxB,YAAI,CAAC,gBAAgB,aAAa,QAAQ,MAAM,KAAK,QAAQ,EAAG,MAAK,KAAK,eAAe;AACzF,aAAK,WAAW,KAAK,KAAK,EAAE;AAAA,MAC9B;AACA,WAAK,MAAM,wBAAwB,KAAK,KAAK,YAAY;AAAA,IAC3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAcA,cAAc,mBAAmB;AAC/B,YAAM,MAAM,KAAK,MAAM;AACvB,YAAM,sBAAsB,KAAK;AACjC,YAAM,oBAAoB,sBAAsB;AAChD,YAAM,WAAW,KAAK,oBAAoB,IAAI;AAC9C,UAAI,oBAAoB,IAAI;AAC1B,eAAO,IAAI,QAAQ,IAAI,QAAQ,KAAK,KAAK,eAAe,IAAI,oBAAoB,QAAQ,CAAC;AAAA,MAC3F,MAAO,QAAO;AAAA,IAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,WAAW;AACT,WAAK,MAAsB,oBAAI,KAAK;AACpC,WAAK,cAAc,CAAC,IAAI,WAAW,KAAK,UAAU,KAAK,GAAG;AAAA,IAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,kBAAkB;AAChB,WAAK,MAAM,KAAK,YAAY,KAAK,KAAK;AAAA,IACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQA,qBAAqB;AACnB,UAAI,SAAS,eAAe,6BAA6B,EAAG;AAC5D,YAAM,KAAK,KAAK,MAAM,OAAO,uBAAuB,yBAAyB,EAAE,CAAC;AAChF,YAAM,iBAAiB,GAAG,cAAc,GAAG,SAAS,CAAC,EAAE;AACvD,UAAI,gBAAgB;AAClB,cAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,cAAM,KAAK;AACX,cAAM,OAAO;AACb,cAAM,YAAY,+GAA+G,cAAc;AAC/I,iBAAS,KAAK,YAAY,KAAK;AAAA,MACjC;AAAA,IACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IASA,qBAAqB,QAAQ,QAAQ,MAAM;AACzC,aAAO,OAAO,WAAW,CAAC,SAAS,OAAO,UAAU,SAAS,OAAO,KAAK,CAAC,MAAM,EAAE,UAAU,MAAM,EAAE;AAAA,IACtG;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,MAAM,OAAO,IAAI,UAAU,IAAI;AACpC,SAAK,MAAM,QAAQ,IAAI,WAAW,MAAM,KAAK,MAAM,IAAI;AACvD,SAAK,WAAW,KAAK,MAAM;AAC3B,QAAI,KAAK,WAAW,KAAM,MAAK,gBAAgB;AAC/C,SAAK,oBAAoB,KAAK,MAAM;AACpC,SAAK,KAAK,KAAK,KAAK;AACpB,QAAI,KAAK,aAAc,MAAK,mBAAmB,KAAK,YAAY;AAAA,SAC3D;AACH,WAAK,KAAK,eAA+B,oBAAI,KAAK;AAClD,WAAK,WAAW,KAAK,WAAW;AAAA,IAClC;AACA,QAAI,KAAK,QAAQ,KAAK,eAAe;AACnC,WAAK,cAAc,CAAC,IAAI,WAAW,KAAK,WAAW,KAAK,KAAK,IAAI,WAAW,KAAK,GAAG;AAAA,IACtF;AAAA,EACF;AAAA,EACA,UAAU;AACR,UAAM,MAAM,KAAK,MAAM;AACvB,UAAM,WAAW,kBAAkB;AACnC,UAAM,EAAE,QAAQ,MAAM,QAAQ,QAAQ,WAAW,MAAM,IAAI,KAAK;AAChE,UAAM,uBAAuB,KAAK,gBAAgB,OAAO,KAAK,iBAAiB;AAC/E,QAAI,UAAU,QAAQ,UAAU,aAAa,SAAS,sBAAsB;AAC1E,aAAO,iBAAiB,WAAW,aAAa,WAAW,KAAK,SAAS;AAAA,IAC3E;AACA,QAAI,UAAU,QAAQ,UAAU,KAAK,mBAAmB;AACtD,aAAO,iBAAiB,WAAW,cAAc,aAAa,KAAK,aAAa,EAAE,SAAS,MAAM,CAAC;AAAA,IACpG;AACA,QAAI,MAAO,QAAO,iBAAiB,SAAS,KAAK,OAAO;AACxD,QAAI,UAAU;AACZ,WAAK,MAAM,OAAO,gBAAgB,SAAS,GAAG;AAC5C,UAAE,eAAe;AACjB,UAAE,gBAAgB;AAAA,MACpB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,SAAU,MAAK,mBAAmB;AAC5C,UAAM,YAAY,KAAK,KAAK;AAC5B,UAAM,SAAS;AAAA,MACb,MAAM,KAAK,KAAK;AAAA,MAChB;AAAA,MACA,SAAS,KAAK,KAAK;AAAA,MACnB,GAAG,KAAK,cAAc,EAAE,eAAe,KAAK,KAAK,eAAe,cAAc,KAAK,KAAK,aAAa,IAAI,CAAC;AAAA,MAC1G,QAAQ,KAAK,KAAK,OAAO,IAAI,KAAK,YAAY;AAAA,MAC9C,GAAG,KAAK,aAAa,EAAE,MAAM,IAAI,QAAQ,KAAK,oBAAoB,IAAI,QAAQ,WAAW,CAAC,IAAI,SAAS,EAAE,IAAI,CAAC;AAAA,IAChH;AACA,SAAK,MAAM,SAAS,MAAM;AAC1B,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,gBAAgB;AACd,UAAM,WAAW,kBAAkB;AACnC,WAAO,oBAAoB,WAAW,cAAc,aAAa,KAAK,aAAa,EAAE,SAAS,MAAM,CAAC;AACrG,WAAO,oBAAoB,WAAW,aAAa,WAAW,KAAK,SAAS;AAC5E,WAAO,oBAAoB,SAAS,KAAK,OAAO;AAChD,QAAI,KAAK,cAAc,CAAC,EAAG,cAAa,KAAK,cAAc,CAAC,CAAC;AAC7D,QAAI,KAAK,cAAc,CAAC,EAAG,cAAa,KAAK,cAAc,CAAC,CAAC;AAC7D,SAAK,gBAAgB,CAAC,MAAM,IAAI;AAAA,EAClC;AAAA,EACA,UAAU;AAAA,IACR,aAAa;AACX,UAAI,KAAK,kBAAkB,OAAO,KAAK,mBAAmB,UAAU;AAClE,eAAO;AAAA,UACL,OAAO,CAAC,CAAC,KAAK,eAAe;AAAA,UAC7B,MAAM,CAAC,CAAC,KAAK,eAAe;AAAA,UAC5B,QAAQ,CAAC,CAAC,KAAK,eAAe;AAAA,UAC9B,QAAQ,CAAC,CAAC,KAAK,eAAe;AAAA,UAC9B,QAAQ,CAAC,CAAC,KAAK,eAAe;AAAA,QAChC;AAAA,MACF;AACA,aAAO;AAAA,QACL,OAAO,CAAC,CAAC,KAAK;AAAA,QACd,MAAM,CAAC,CAAC,KAAK;AAAA,QACb,QAAQ,CAAC,CAAC,KAAK;AAAA,QACf,QAAQ,CAAC,CAAC,KAAK;AAAA,QACf,QAAQ,CAAC,CAAC,KAAK;AAAA,MACjB;AAAA,IACF;AAAA,IACA,QAAQ;AACN,aAAO;AAAA,QACL,OAAO,EAAE,OAAO,KAAK,MAAM,OAAO,SAAS,CAAC,KAAK,aAAa,SAAS,OAAO,EAAE;AAAA,QAChF,MAAM,EAAE,OAAO,KAAK,MAAM,MAAM,SAAS,CAAC,KAAK,aAAa,SAAS,MAAM,EAAE;AAAA,QAC7E,OAAO,EAAE,OAAO,KAAK,MAAM,OAAO,SAAS,CAAC,KAAK,aAAa,SAAS,OAAO,EAAE;AAAA,QAChF,MAAM,EAAE,OAAO,KAAK,MAAM,MAAM,SAAS,CAAC,KAAK,aAAa,SAAS,MAAM,EAAE;AAAA,QAC7E,KAAK,EAAE,OAAO,KAAK,MAAM,KAAK,SAAS,CAAC,KAAK,aAAa,SAAS,KAAK,EAAE;AAAA,MAC5E;AAAA,IACF;AAAA,IACA,cAAc;AACZ,aAAO,KAAK,aAAa,KAAK,UAAU;AAAA,IAC1C;AAAA,IACA,eAAe;AACb,aAAO,OAAO,KAAK,KAAK,KAAK,EAAE,OAAO,CAAC,SAAS,KAAK,MAAM,IAAI,EAAE,OAAO;AAAA,IAC1E;AAAA,IACA,gBAAgB;AACd,aAAO,KAAK,QAAQ,KAAK;AAAA,IAC3B;AAAA,IACA,mBAAmB;AACjB,aAAO,KAAK,eAAe,KAAK,sBAAsB;AAAA,IACxD;AAAA,IACA,0BAA0B;AACxB,YAAM,MAAM,KAAK,MAAM;AACvB,YAAM,OAAO,KAAK,KAAK;AACvB,aAAO,IAAI,QAAQ,KAAK,oBAAoB,IAAI,QAAQ,MAAM,CAAC,IAAI,IAAI;AAAA,IACzE;AAAA;AAAA,IAEA,YAAY;AACV,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,KAAK,UAAU,MAAM,KAAK,QAAQ,IAAI,KAAK,KAAK,KAAK,UAAU;AAC1E,kBAAU,KAAK;AAAA,UACb,OAAO,KAAK,MAAM,IAAI,EAAE;AAAA,UACxB,SAAS,IAAI;AAAA,UACb,OAAO,KAAK,MAAM,KAAK,WAAW,GAAG,KAAK,UAAU;AAAA;AAAA,UAEpD,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT;AAAA,IACA,aAAa;AACX,aAAO,KAAK,eAAe,KAAK,aAAa,aAAa;AAAA,IAC5D;AAAA;AAAA,IAEA,YAAY;AACV,cAAQ,KAAK,UAAU,OAAO,CAAC,SAAS,CAAC,KAAK,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,OAAO,EAAE,GAAG,MAAM,IAAI,KAAK,MAAM,IAAI,EAAE,EAAE;AAAA,IACjH;AAAA;AAAA,IAEA,YAAY;AACV,aAAO,KAAK,UAAU,UAAU,KAAK;AAAA,IACvC;AAAA,IACA,iBAAiB;AACf,aAAO,KAAK,qBAAqB;AAAA,IACnC;AAAA;AAAA,IAEA,sBAAsB;AACpB,UAAI,WAAW;AACf,UAAI,KAAK,aAAa,KAAK,cAAe,YAAW,KAAK,mBAAmB,KAAK,gBAAgB,KAAK,UAAU;AAAA,eACxG,KAAK,gBAAgB,KAAK,WAAY,YAAW,KAAK,mBAAmB,KAAK;AACvF,aAAO;AAAA,IACT;AAAA,IACA,YAAY;AACV,UAAI,SAAS,KAAK,mBAAmB;AACrC,UAAI,UAAU,CAAC,MAAM,MAAM,EAAG,WAAU;AACxC,aAAO;AAAA,QACL,OAAO,KAAK;AAAA,QACZ,SAAS,KAAK;AAAA,QACd,OAAO,KAAK,MAAM;AAAA,QAClB,aAAa,KAAK;AAAA,QAClB,WAAW,KAAK,aAAa,KAAK,aAAa,CAAC;AAAA,QAChD,qBAAqB,KAAK;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAAA,IACA,eAAe;AACb,UAAI,OAAO;AACX,UAAI,KAAK,WAAW,OAAO,KAAK,YAAY,SAAU,QAAO,KAAK,MAAM,KAAK,aAAa,KAAK,OAAO;AAAA,eAC7F,KAAK,WAAW,KAAK,mBAAmB,KAAM,QAAO,KAAK;AACnE,aAAO,OAAO,KAAK,QAAQ,IAAI;AAAA,IACjC;AAAA,IACA,eAAe;AACb,UAAI,OAAO;AACX,UAAI,KAAK,WAAW,OAAO,KAAK,YAAY,SAAU,QAAO,KAAK,MAAM,KAAK,aAAa,KAAK,OAAO;AAAA,eAC7F,KAAK,WAAW,KAAK,mBAAmB,KAAM,QAAO,KAAK;AACnE,aAAO,OAAO,KAAK,QAAQ,IAAI;AAAA,IACjC;AAAA,IACA,WAAW;AACT,UAAI,EAAE,UAAU,gBAAgB,CAAC,EAAE,IAAI,KAAK;AAC5C,iBAAW,SAAS,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,OAAO;AAAA,QAC5C,OAAO;AAAA,QACP,GAAG,cAAc,SAAS,EAAE,OAAO,cAAc,CAAC,EAAE,IAAI,CAAC;AAAA,QACzD,MAAM,KAAK,gBAAgB,KAAK,KAAK,KAAK,aAAa,UAAU,KAAK,aAAa,SAAS,IAAI,CAAC;AAAA,MACnG,EAAE;AACF,UAAI,KAAK,kBAAmB,UAAS,QAAQ,SAAS,IAAI,CAAC;AAC3D,aAAO;AAAA,IACT;AAAA,IACA,mBAAmB;AACjB,aAAO,KAAK;AAAA,MACZ,KAAK,cAAc,CAAC,KAAK,gBAAgB,EAAE,KAAK,aAAa,KAAK;AAAA,IACpE;AAAA,IACA,SAAS;AACP,aAAO,KAAK,MAAM,OAAO,IAAI,CAAC,WAAW,EAAE,OAAO,MAAM,EAAE;AAAA,IAC5D;AAAA;AAAA,IAEA,kBAAkB;AAChB,UAAI,CAAC,KAAK,gBAAgB,CAAC,OAAO,KAAK,KAAK,YAAY,EAAE,OAAQ,QAAO,CAAC;AAC1E,aAAO,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,MAAM,MAAM;AACxC,YAAI,MAAM,KAAK,aAAa,IAAI,CAAC,KAAK,CAAC;AACvC,YAAI,CAAC,MAAM,QAAQ,GAAG,EAAG,OAAM,CAAC,GAAG;AACnC,eAAO,CAAC;AACR,YAAI,QAAQ,CAAC,EAAE,MAAM,IAAI,OAAO,OAAO,MAAM,GAAG,MAAM;AACpD,eAAK,CAAC,IAAI;AAAA,YACR,KAAK,IAAI;AAAA,YACT,MAAM,CAAC,CAAC,MAAM,MAAM,EAAE,SAAS,IAAI,IAAI,OAAO,IAAI;AAAA,YAClD,IAAI,CAAC,CAAC,MAAM,MAAM,EAAE,SAAS,EAAE,IAAI,KAAK,IAAI;AAAA,YAC5C,OAAO,SAAS;AAAA,YAChB,OAAO,SAAS;AAAA,UAClB;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,YAAY;AACV,YAAM,MAAM,KAAK,MAAM;AACvB,UAAI,QAAQ;AACZ,YAAM,OAAO,KAAK,KAAK;AACvB,YAAM,OAAO,KAAK,YAAY;AAC9B,YAAM,QAAQ,KAAK,SAAS;AAC5B,cAAQ,KAAK,KAAK,IAAI;AAAA,QACpB,KAAK,SAAS;AACZ,kBAAQ,KAAK,MAAM;AACnB;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,kBAAQ;AACR;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,kBAAQ,GAAG,KAAK,OAAO,KAAK,EAAE,KAAK,IAAI,IAAI;AAC3C;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,gBAAM,gBAAgB,KAAK,KAAK;AAChC,gBAAM,KAAK,KAAK,YAAY;AAC5B,cAAI,KAAK,KAAK,MAAM,OAAO,KAAK,SAAS,CAAC;AAC1C,cAAI,KAAK,OAAQ,MAAK,GAAG,UAAU,GAAG,CAAC;AACvC,cAAI,qBAAqB,GAAG,EAAE,IAAI,EAAE;AACpC,cAAI,cAAc,SAAS,MAAM,KAAK,SAAS,GAAG;AAChD,kBAAM,KAAK,cAAc,YAAY;AACrC,gBAAI,KAAK,KAAK,MAAM,OAAO,cAAc,SAAS,CAAC;AACnD,gBAAI,KAAK,OAAQ,MAAK,GAAG,UAAU,GAAG,CAAC;AACvC,gBAAI,OAAO,GAAI,sBAAqB,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE;AAAA,iBAClD;AACH,kBAAI,KAAK,MAAO,sBAAqB,GAAG,GAAG,UAAU,GAAG,CAAC,CAAC,IAAI,EAAE,MAAM,GAAG,UAAU,GAAG,CAAC,CAAC,IAAI,EAAE;AAAA,kBACzF,sBAAqB,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;AAAA,YACrD;AAAA,UACF;AACA,kBAAQ,GAAG,KAAK,MAAM,IAAI,IAAI,IAAI,QAAQ,KAAK,oBAAoB,IAAI,QAAQ,MAAM,CAAC,IAAI,IAAI,CAAC,KAAK,kBAAkB;AACtH;AAAA,QACF;AAAA,QACA,KAAK,OAAO;AACV,kBAAQ,KAAK,MAAM,KAAK,WAAW,MAAM,KAAK,MAAM,YAAY,KAAK,KAAK;AAC1E;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,IACA,YAAY;AACV,YAAM,MAAM,KAAK,MAAM;AACvB,UAAI,QAAQ,CAAC;AACb,UAAI,WAAW;AACf,UAAI,aAAa;AACjB,UAAI,CAAC,KAAK,cAAe,MAAK,MAAsB,oBAAI,KAAK;AAC7D,YAAM,OAAO,KAAK;AAClB,cAAQ,KAAK,KAAK,IAAI;AAAA,QACpB,KAAK,SAAS;AACZ,qBAAW,KAAK,KAAK,UAAU,YAAY;AAC3C,kBAAQ,MAAM,MAAM,MAAM,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,MAAM;AACpD,kBAAM,YAAY,IAAI,KAAK,WAAW,GAAG,GAAG,CAAC;AAC7C,kBAAM,UAAU,IAAI,KAAK,WAAW,IAAI,GAAG,GAAG,CAAC;AAC/C,oBAAQ,WAAW,EAAE;AACrB,mBAAO;AAAA,cACL;AAAA,cACA,eAAe,IAAI,eAAe,SAAS;AAAA,cAC3C;AAAA,cACA,SAAS,WAAW;AAAA,cACpB,SAAS,WAAW,MAAM,KAAK,YAAY;AAAA,YAC7C;AAAA,UACF,CAAC;AACD;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,qBAAW,KAAK,KAAK,UAAU,YAAY;AAC3C,kBAAQ,MAAM,MAAM,MAAM,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,MAAM;AACpD,kBAAM,YAAY,IAAI,KAAK,UAAU,GAAG,CAAC;AACzC,kBAAM,UAAU,IAAI,KAAK,UAAU,IAAI,GAAG,CAAC;AAC3C,oBAAQ,WAAW,EAAE;AACrB,mBAAO;AAAA,cACL;AAAA,cACA,eAAe,IAAI,eAAe,SAAS;AAAA,cAC3C;AAAA,cACA,SAAS,KAAK,SAAS,KAAK,OAAO,CAAC,EAAE,MAAM,OAAO,GAAG,CAAC,IAAI,KAAK,OAAO,CAAC,EAAE;AAAA,cAC1E,SAAS,MAAM,KAAK,SAAS,KAAK,aAAa,KAAK,YAAY;AAAA,YAClE;AAAA,UACF,CAAC;AACD;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,gBAAM,QAAQ,KAAK,KAAK,UAAU,SAAS;AAC3C,gBAAM,gBAAgB,IAAI,KAAK,KAAK,KAAK,aAAa;AACtD,uBAAa;AACb,kBAAQ,MAAM,MAAM,MAAM,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,MAAM;AACpD,kBAAM,YAAY,IAAI,QAAQ,eAAe,CAAC;AAC9C,kBAAM,UAAU,IAAI,KAAK,SAAS;AAClC,oBAAQ,SAAS,IAAI,IAAI,IAAI,CAAC;AAC9B,kBAAM,UAAU,CAAC,cAAc,IAAI,QAAQ,SAAS,KAAK,CAAC;AAC1D,mBAAO;AAAA,cACL;AAAA,cACA,eAAe,IAAI,eAAe,SAAS;AAAA,cAC3C;AAAA,cACA,SAAS,UAAU,QAAQ;AAAA,cAC3B,OAAO;AAAA,cACP,YAAY,UAAU,SAAS,MAAM;AAAA,cACrC,OAAO,oBAAoB,UAAU,OAAO,KAAK,CAAC;AAAA,YACpD;AAAA,UACF,CAAC;AACD,cAAI,KAAK,gBAAgB,KAAK,aAAa,QAAQ;AACjD,oBAAQ,MAAM,OAAO,CAAC,SAAS;AAC7B,oBAAM,MAAM,KAAK,UAAU,OAAO,KAAK;AACvC,qBAAO,EAAE,KAAK,gBAAgB,OAAO,KAAK,KAAK,aAAa,UAAU,KAAK,aAAa,SAAS,GAAG;AAAA,YACtG,CAAC;AAAA,UACH;AACA;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,uBAAa;AACb,gBAAM,iBAAiB,KAAK,KAAK;AACjC,gBAAM,WAAW,KAAK;AACtB,kBAAQ,SAAS,IAAI,CAAC,MAAM,MAAM;AAChC,kBAAM,YAAY,IAAI,QAAQ,gBAAgB,KAAK,qBAAqB,KAAK,eAAe,IAAI,IAAI,CAAC;AACrG,kBAAM,UAAU,IAAI,KAAK,SAAS;AAClC,oBAAQ,SAAS,IAAI,IAAI,IAAI,CAAC;AAC9B,kBAAM,aAAa,UAAU,OAAO,KAAK,KAAK;AAC9C,mBAAO;AAAA,cACL;AAAA,cACA,eAAe,IAAI,eAAe,SAAS;AAAA,cAC3C;AAAA;AAAA,cAEA,OAAO,CAAC,cAAc,IAAI,QAAQ,SAAS,KAAK,CAAC;AAAA,cACjD,cAAc,KAAK,gBAAgB,SAAS,KAAK,CAAC;AAAA,YACpD;AAAA,UACF,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,IAAI;AACxC;AAAA,QACF;AAAA,QACA,KAAK,OAAO;AACV,gBAAM,YAAY,KAAK,KAAK;AAC5B,gBAAM,UAAU,IAAI,KAAK,KAAK,KAAK,SAAS;AAC5C,kBAAQ,SAAS,IAAI,IAAI,IAAI,CAAC;AAC9B,gBAAM,aAAa,UAAU,OAAO,KAAK,KAAK;AAC9C,kBAAQ,CAAC;AAAA,YACP;AAAA,YACA,eAAe,IAAI,eAAe,SAAS;AAAA,YAC3C;AAAA,YACA,OAAO,IAAI,QAAQ,SAAS;AAAA,YAC5B,cAAc,KAAK,gBAAgB,SAAS,KAAK,CAAC;AAAA,UACpD,CAAC;AACD;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA,IAEA,mBAAmB;AACjB,UAAI,KAAK,UAAW,QAAO;AAC3B,aAAO,IAAI,KAAK,SAAS,OAAO,CAAC,OAAO,QAAQ,QAAQ,IAAI,MAAM,CAAC;AAAA,IACrE;AAAA,IACA,YAAY;AACV,aAAO,MAAM,KAAK;AAAA,IACpB;AAAA,IACA,aAAa;AACX,YAAM,EAAE,eAAe,aAAa,kBAAkB,IAAI,KAAK;AAC/D,aAAO;AAAA,QACL,CAAC,WAAW,KAAK,KAAK,EAAE,OAAO,GAAG;AAAA,QAClC,CAAC,WAAW,KAAK,MAAM,EAAE,GAAG,KAAK;AAAA,QACjC,mBAAmB,CAAC,KAAK;AAAA,QACzB,0BAA0B,KAAK;AAAA,QAC/B,wBAAwB,KAAK,mBAAmB,KAAK;AAAA,QACrD,uBAAuB,KAAK;AAAA,QAC5B,6BAA6B,KAAK;AAAA,QAClC,yBAAyB,KAAK;AAAA,QAC9B,sBAAsB,KAAK;AAAA,QAC3B,+BAA+B,KAAK,aAAa,KAAK;AAAA,QACtD,sBAAsB,KAAK,gBAAgB,KAAK,cAAc,KAAK,aAAa,KAAK;AAAA,QACrF,iBAAiB,KAAK;AAAA,QACtB,kBAAkB,KAAK;AAAA,QACvB,0BAA0B,cAAc;AAAA,QACxC,+BAA+B,kBAAkB;AAAA,QACjD,0BAA0B,YAAY;AAAA,QACtC,gCAAgC,KAAK;AAAA,QACrC,wBAAwB,KAAK,eAAe,KAAK,sBAAsB;AAAA,QACvE,qBAAqB,OAAO,WAAW,eAAe,kBAAkB;AAAA,MAC1E;AAAA,IACF;AAAA,IACA,oBAAoB;AAClB,aAAO,CAAC,SAAS,MAAM,EAAE,SAAS,KAAK,KAAK,EAAE;AAAA,IAChD;AAAA,IACA,cAAc;AACZ,aAAO,KAAK,KAAK,OAAO;AAAA,IAC1B;AAAA,IACA,aAAa;AACX,aAAO,KAAK,KAAK,OAAO;AAAA,IAC1B;AAAA,IACA,cAAc;AACZ,aAAO,KAAK,KAAK,OAAO;AAAA,IAC1B;AAAA,IACA,kBAAkB;AAChB,aAAO,CAAC,QAAQ,KAAK,EAAE,SAAS,KAAK,KAAK,EAAE;AAAA,IAC9C;AAAA,IACA,aAAa;AACX,aAAO,KAAK,KAAK,OAAO;AAAA,IAC1B;AAAA,IACA,YAAY;AACV,aAAO,KAAK,KAAK,OAAO;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL,QAAQ;AAAA;AAAA,MAEN,QAAQ,QAAQ,WAAW;AACzB,aAAK,oBAAoB,MAAM;AAC/B,aAAK,gBAAgB;AAAA,MACvB;AAAA,MACA,MAAM;AAAA,IACR;AAAA,IACA,OAAO,QAAQ;AACb,WAAK,WAAW,MAAM;AAAA,IACxB;AAAA,IACA,aAAa,MAAM;AACjB,WAAK,mBAAmB,IAAI;AAAA,IAC9B;AAAA,IACA,WAAW,QAAQ;AACjB,WAAK,WAAW,MAAM;AAAA,IACxB;AAAA,EACF;AACF;AACA,IAAM,QAAwB,YAAY,WAAW,CAAC,CAAC,UAAU,MAAM,CAAC,CAAC;", "names": []}