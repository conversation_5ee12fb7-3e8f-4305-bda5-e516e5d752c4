import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/cs.es.js
var weekDays = [
  "Pondělí",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "Čtvrtek",
  "Pátek",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>"
];
var months = [
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "B<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "Červen",
  "Červenec",
  "Srpen",
  "<PERSON><PERSON><PERSON><PERSON>",
  "Říjen",
  "Listopad",
  "Prosinec"
];
var years = "Roky";
var year = "Rok";
var month = "Měsíc";
var week = "Týden";
var day = "Den";
var today = "Dnes";
var noEvent = "Bez událostí";
var allDay = "Celý den";
var deleteEvent = "Odstranit";
var createEvent = "Vytvořit událost";
var dateFormat = "dddd D. MMMM YYYY";
var cs = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  cs as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/cs.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=cs.es-LMUTE6GM.js.map
