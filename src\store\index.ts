import { defineStore } from 'pinia';
import { onAuthStateChanged, isSignInWithEmailLink, signInWithEmailLink, onIdTokenChanged } from 'firebase/auth';
import type {User} from 'firebase/auth';
import { auth } from '../firebaseConfig';
import { PostRequestWithHeaders } from '../helpers/apihelper';
import { getCookie, setCookie } from '../helpers/domhelper';
import router from '../router';
import { menuListTop } from '../config/Organizationsidebar';
import type { CreateUserInput, userObjResponse, FcmTokenPayload } from '@/types/user';
import type { RoleResponse } from '@/types/organization';

const api_url = import.meta.env.VITE_API_URL;

interface UserState {
  auth: User | null;
  user_data: userObjResponse | null;
  current_org: null | string;
  sidebarOpen: boolean;
  headerFlag: boolean;
  user_role: RoleResponse | null;
  isMobile:boolean;
  fcmToken:Array<string> | null;
}

interface UpdateUserPayload {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone_number?: string;
  profilePicture?: string;
}

export const UserStore = defineStore({
  id: 'User',
  state: ():UserState => ({
    auth: null,
    user_data: null,
    current_org: null,
    sidebarOpen: false,
    headerFlag: false,
    user_role: null,
    isMobile: false,
    fcmToken: null,
  }),

  actions: {
    updateUser (payload: UpdateUserPayload) {
      if (this.user_data) {
        this.user_data = { ...this.user_data, ...payload };
      }
    },
    async callbackFunctionMonitorChanges () {
      if (window.innerWidth < 768) {
        this.isMobile = true;
      } else {
        this.isMobile = false;
      }
    },
    async CreateUser (values:CreateUserInput) {
      if (!values) {
        return Promise.reject(new Error("Values are required"));
      }

      const idToken = getCookie("accessToken");

      return PostRequestWithHeaders({
        url: `${api_url}/user/CreateUser`,
        headers: { Authorization: `Bearer ${idToken}` },
        body: {
          uid: values.uid, // Include uid in request
          first_name: values.first_name,
          last_name: values.last_name,
          email: values.email,
          role: values.role,
        },
      });
    },
    async sendVerificationEmail (user:User) {
      if (!user) {
        return Promise.reject(new Error("User is required"));
      }
      const idToken = getCookie("accessToken");

      return PostRequestWithHeaders({
        url: `${api_url}/user/SendEmailVerification`,
        headers: { Authorization: `Bearer ${idToken}` },
        body: {
          email: user.email,
        },
      });
    },
    async sendPasswordResetEmail (email:String) {
      if (!email) {
        return Promise.reject(new Error("Email is required"));
      }
      return PostRequestWithHeaders({
        url: `${api_url}/user/ResetPassword`,
        body: {
          email,
        },
      });
    },
    verifyAuth () {
      if (
        getCookie('accessToken').length === 0 ||
        getCookie('organization').length === 0 ||
        getCookie('organization') === null ||
        getCookie('accessToken') === null
      ) {
        return true;
      }
      return false;
    },

    toggleSidebar () {
      this.sidebarOpen = !this.sidebarOpen;
      console.log(this.sidebarOpen);
    },

    GetAuth ():Promise<void> {
      return new Promise((resolve, reject) => {
        console.log("getauth");
        onAuthStateChanged(auth, (user) => {
          console.log("onAuthStateChanged");

          if (user) {
            this.auth = user as User;
            this.setupTokenRefresh(user);
            this.current_org = getCookie('organization') as string;
            if (getCookie('organization')) {
              this.GetUserRole();
            }
            this.GetOrganizationDetails()
              .then(() => {
                resolve();
              })
              .catch((error) => {
                console.error('Error in GetOrganizationDetails:', error);
                reject(error);
              });
          } else {
            console.log('No authenticated user');
            this.auth = null;
            this.clearAuthCookies();
            window.location.href = '/login';
          }
        });
      });
    },

    setupTokenRefresh (user:User) {
      onIdTokenChanged(auth, (newUserCred) => {
        if (newUserCred) {
          this.refreshAndSetTokens(newUserCred);
        }
      }, (error) => {
        console.log(error);
      });

      this.refreshAndSetTokens(user);
    },

    refreshAndSetTokens (user:User):Promise<void> {
      return new Promise((resolve, reject) => {
        console.log("user---------------------");
        console.log(user);
        user.getIdToken(false)
          .then((idToken:string) => {
            const expirationDays = 30;
            setCookie('accessToken', idToken, expirationDays);
            setCookie('refreshToken', user.refreshToken, expirationDays);
            resolve();
          })
          .catch((error) => {
            console.error('Error refreshing token:', error);
            this.handleAuthError(error);
            reject(error);
          });
      });
    },

    clearAuthCookies () {
      setCookie('accessToken', '', -1);
      setCookie('refreshToken', '', -1);
      setCookie('organization', '', -1);
    },

    handleAuthError (error:Error):void {
      console.error('Authentication error:', error);
      this.auth = null;
      this.clearAuthCookies();
      router.push('/login');
    },

    GetOrganizationDetails ():Promise<void> {
      return new Promise((resolve, reject) => {
        if (this.auth){
          PostRequestWithHeaders({url: `${api_url}/user/GetUserDetails`, body: {uid: this.auth.uid}}).then((res) => {
            const userData  = res as userObjResponse;
            if (!getCookie('organization')){
              if (userData.organization_id[0]){
                setCookie('organization', userData.organization_id[0]._id, 30);
                this.GetUserRole();
                resolve();
              } else {
              // Handle case where no organization is found
              }
            }
            this.user_data = userData;
            resolve();
          }).catch((error) => {
            reject(error);
          });
        } else {
          reject();
        }
      });
    },

    async GetUserRole () {
      return new Promise((resolve, reject) => {
        if (getCookie('organization') !== undefined){
          PostRequestWithHeaders({url: `${api_url}/user/GetUserRole`}).then((res) => {
            const userData  = res as RoleResponse;
            if (userData) {
              const currentPath = router.currentRoute.value.fullPath.toLowerCase().replace(/^\/|\/$/g, '');
              const matchedMenuItem = menuListTop.find((item) => {
                const menuItemPath = item.route.toLowerCase().replace(/^\/|\/$/g, '');
                return currentPath.startsWith(menuItemPath);
              });
              if (matchedMenuItem && !matchedMenuItem.roles.includes(userData.userrole.role)) {
                router.push('/sessions');
              }
            }
            this.user_role = userData;
          }).then((res) => {
            resolve(res);
          })
            .catch((error) => {
              reject(error);
            });
        }
      });
    },

    async UpdateFcmToken (payload:FcmTokenPayload) {
      return new Promise((resolve, reject) => {
        if (getCookie('organization') !== undefined){
          PostRequestWithHeaders({
            url: `${api_url}/user/fcmToken`,
            body: payload,
          }).then((res) => {
            const userData = res as userObjResponse;
            this.fcmToken = userData.fcmToken ||null;
            resolve(res);
          }).catch((error) => {
            reject(error);
          });
        }
      });
    },

    SSO ():Promise<void> {
      return new Promise((resolve, reject) => {
        const urlParams = new URLSearchParams(window.location.search);
        const email = urlParams.get('email') as string;
        if (isSignInWithEmailLink(auth, window.location.href)) {
          signInWithEmailLink(auth, email, window.location.href)
            .then((user) => {
              this.refreshAndSetTokens(user.user).then(() => {
                resolve();
              });
            })
            .catch((error) => {
              reject(error);
            });
        }
      });
    },
  },
});
