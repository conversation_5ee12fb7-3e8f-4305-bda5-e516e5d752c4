// vite.config.js
import { defineConfig, loadEnv } from "file:///D:/sathana/Github/Dashboard-API-Frontend/node_modules/vite/dist/node/index.js";
import vue from "file:///D:/sathana/Github/Dashboard-API-Frontend/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { readFileSync, writeFileSync } from "node:fs";
import { resolve } from "node:path";
import path from "path";
import { createHtmlPlugin } from "file:///D:/sathana/Github/Dashboard-API-Frontend/node_modules/vite-plugin-html/dist/index.mjs";
import vueDevTools from "file:///D:/sathana/Github/Dashboard-API-Frontend/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
var __vite_injected_original_dirname = "D:\\sathana\\Github\\Dashboard-API-Frontend";
var vite_config_default = ({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };
  return defineConfig({
    plugins: [
      vue(),
      vueDevTools(),
      createHtmlPlugin({}),
      // Custom plugin to handle service worker
      {
        name: "firebase-service-worker",
        // Use writeBundle instead of closeBundle
        writeBundle() {
          const swTemplate = readFileSync(
            resolve(__vite_injected_original_dirname, "public/firebase-messaging-sw.js"),
            "utf-8"
          );
          const processedSW = swTemplate.replace("__FIREBASE_API_KEY__", process.env.VITE_FIREBASE_API_KEY || "").replace("__FIREBASE_AUTH_DOMAIN__", process.env.VITE_FIREBASE_AUTH_DOMAIN || "").replace("__FIREBASE_DATABASE_URL__", process.env.VITE_FIREBASE_DATABASE_URL || "").replace("__FIREBASE_PROJECT_ID__", process.env.VITE_FIREBASE_PROJECT_ID || "").replace("__FIREBASE_STORAGE_BUCKET__", process.env.VITE_FIREBASE_STORAGE_BUCKET || "").replace("__FIREBASE_APP_ID__", process.env.VITE_FIREBASE_APP_ID || "").replace("__FIREBASE_MESSAGING_SENDER_ID__", process.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "");
          try {
            writeFileSync(resolve(__vite_injected_original_dirname, "dist/firebase-messaging-sw.js"), processedSW);
            console.log("Firebase service worker processed and written successfully");
          } catch (error) {
            console.error("Failed to write Firebase service worker:", error);
          }
        }
      }
    ],
    esbuild: {
      drop: ["console", "debugger"]
    },
    resolve: {
      alias: {
        "@": path.resolve(__vite_injected_original_dirname, "./src")
      }
    }
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
