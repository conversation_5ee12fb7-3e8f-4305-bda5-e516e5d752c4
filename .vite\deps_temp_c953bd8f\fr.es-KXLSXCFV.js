import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/fr.es.js
var weekDays = [
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>"
];
var months = [
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "Septembre",
  "Octobre",
  "Novembre",
  "Décembre"
];
var years = "Années";
var year = "Année";
var month = "Mo<PERSON>";
var week = "Sema<PERSON>";
var day = "Jour";
var today = "Aujourd'hui";
var noEvent = "Aucun événement";
var allDay = "Jour entier";
var deleteEvent = "Supprimer";
var createEvent = "Créer un événement";
var dateFormat = "dddd D MMMM YYYY";
var fr = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  fr as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/fr.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=fr.es-KXLSXCFV.js.map
