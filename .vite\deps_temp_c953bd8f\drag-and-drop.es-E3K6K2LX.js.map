{"version": 3, "sources": ["../../node_modules/vue-cal/dist/drag-and-drop.es.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst holdOverTimeout = 800;\nlet changeViewTimeout = null;\nlet pressPrevOrNextInterval = null;\nlet viewBeforeDrag = { id: null, date: null };\nlet viewChanged = false;\nlet cancelViewChange = true;\nlet dragOverCell = { el: null, cell: null, timeout: null };\nconst dragging = {\n  _eid: null,\n  fromVueCal: null,\n  toVueCal: null\n};\nconst DragAndDrop = class {\n  constructor(vuecal) {\n    __publicField(this, \"_vuecal\");\n    this._vuecal = vuecal;\n  }\n  /**\n   * When click and drag an event the cursor can be anywhere in the event,\n   * when dropping the event, we need to subtract the cursor position in the event.\n   *\n   * @param {Object} e The associated DOM event.\n   */\n  _getEventStart(e) {\n    const { timeStep, timeCellHeight, timeFrom, utils } = this._vuecal;\n    let { y } = utils.cell.getPosition(e);\n    y -= e.dataTransfer.getData(\"cursor-grab-at\") * 1;\n    return Math.round(y * timeStep / parseInt(timeCellHeight) + timeFrom);\n  }\n  /**\n   * On drop, update the event start and end date directly into the event.\n   *\n   * @param {Object} e The associated DOM event.\n   * @param {Object} event The event being dragged.\n   * @param {Object} transferData The transfer data from the HTML5 dragging event.\n   * @param {Date} cellDate The hovered cell starting date.\n   */\n  _updateEventStartEnd(e, event, transferData, cellDate) {\n    const eventDuration = transferData.duration * 1 || event.endTimeMinutes - event.startTimeMinutes;\n    let startTimeMinutes = Math.max(this._getEventStart(e), 0);\n    if (this._vuecal.snapToTime) {\n      const plusHalfSnapTime = startTimeMinutes + this._vuecal.snapToTime / 2;\n      startTimeMinutes = plusHalfSnapTime - plusHalfSnapTime % this._vuecal.snapToTime;\n    }\n    event.startTimeMinutes = startTimeMinutes;\n    event.start = new Date(new Date(cellDate).setMinutes(startTimeMinutes));\n    event.endTimeMinutes = Math.min(startTimeMinutes + eventDuration, 24 * 60);\n    event.end = new Date(new Date(cellDate).setMinutes(event.endTimeMinutes));\n  }\n  /**\n   * On event drag start, only possible if editableEvent is true.\n   * /!\\ This is using the native HTML5 drag & drop, not supported on touch devices.\n   *\n   * @param {Object} e The associated DOM event.\n   * @param {Object} event The event being dragged.\n   */\n  eventDragStart(e, event) {\n    if (e.target.nodeType === 3) return e.preventDefault();\n    e.dataTransfer.dropEffect = \"move\";\n    e.dataTransfer.setData(\"event\", JSON.stringify(event));\n    e.dataTransfer.setData(\"cursor-grab-at\", e.offsetY);\n    const { clickHoldAnEvent } = this._vuecal.domEvents;\n    setTimeout(() => {\n      clickHoldAnEvent._eid = null;\n      clearTimeout(clickHoldAnEvent.timeoutId);\n      event.deleting = false;\n    }, 0);\n    this._vuecal.domEvents.dragAnEvent._eid = event._eid;\n    dragging._eid = event._eid;\n    dragging.fromVueCal = this._vuecal._.uid;\n    event.dragging = true;\n    setTimeout(() => event.draggingStatic = true, 0);\n    viewChanged = false;\n    viewBeforeDrag = { id: this._vuecal.view.id, date: this._vuecal.view.startDate };\n    cancelViewChange = true;\n  }\n  /**\n   * On event drag end, when releasing the event.\n   *\n   * @param {Object} event The event being dragged.\n   */\n  eventDragEnd(event) {\n    this._vuecal.domEvents.dragAnEvent._eid = null;\n    dragging._eid = null;\n    event.dragging = false;\n    event.draggingStatic = false;\n    const { fromVueCal, toVueCal } = dragging;\n    if (toVueCal && fromVueCal !== toVueCal) this._vuecal.utils.event.deleteAnEvent(event);\n    dragging.fromVueCal = null;\n    dragging.toVueCal = null;\n    if (viewChanged && cancelViewChange && viewBeforeDrag.id) this._vuecal.switchView(viewBeforeDrag.id, viewBeforeDrag.date, true);\n  }\n  /**\n   * On cell/split enter with a dragging event.\n   * Highlight the cell, and if on `years`, `year`, `month` view,\n   * set a timer to go deeper on drag hold over this cell.\n   *\n   * @param {Object} e The associated DOM event.\n   * @param {Object} cell The cell component's $data.\n   * @param {Date} cellDate The hovered cell starting date.\n   */\n  cellDragEnter(e, cell, cellDate) {\n    const target = e.currentTarget;\n    if (e.currentTarget.contains(e.relatedTarget)) return;\n    if (target === dragOverCell.el || !target.className.includes(\"vuecal__cell-content\")) return false;\n    if (dragOverCell.el) dragOverCell.cell.highlighted = false;\n    dragOverCell = { el: target, cell, timeout: clearTimeout(dragOverCell.timeout) };\n    cell.highlighted = true;\n    if ([\"years\", \"year\", \"month\"].includes(this._vuecal.view.id)) {\n      dragOverCell.timeout = setTimeout(() => this._vuecal.switchToNarrowerView(cellDate), 2e3);\n    }\n  }\n  /**\n   * On cell/split drag over, highlight the cell being hovered,\n   * Useful when starting to drag event on the same cell/split it's in.\n   * Warning: This is fired repeatedly as long as you stay over this cell/split.\n   *\n   * @param {Object} e The associated DOM event.\n   * @param {Object} cell The cell component's $data.\n   * @param {Date} cellDate The hovered cell starting date.\n   * @param {Number|String} split The optional split being hovered if any.\n   */\n  cellDragOver(e, cell, cellDate, split) {\n    e.preventDefault();\n    cell.highlighted = true;\n    if (split || split === 0) cell.highlightedSplit = split;\n  }\n  /**\n   * When event drag leaves a cell/split.\n   * Remove the cell/split highlighted state.\n   * Warning: cell dragleave event happens AFTER another cell dragenter!\n   *\n   * @param {Object} e The associated DOM event.\n   * @param {Object} cell The cell component's $data.\n   */\n  cellDragLeave(e, cell) {\n    e.preventDefault();\n    if (e.currentTarget.contains(e.relatedTarget)) return;\n    cell.highlightedSplit = false;\n    if (dragOverCell.cell === cell) {\n      clearTimeout(dragOverCell.timeout);\n      dragOverCell = { el: null, cell: null, timeout: null };\n      cell.highlighted = false;\n    }\n  }\n  /**\n   * On successful event drop into a cell/split.\n   * Change the event start and end time and remove the event dragging state\n   * and cell/split highlighted state.\n   *\n   * @param {Object} e The associated DOM event.\n   * @param {Object} cell The cell component's $data.\n   * @param {Date} cellDate The hovered cell starting date.\n   * @param {Number|String} split The optional split being dropped into, if any.\n   */\n  cellDragDrop(e, cell, cellDate, split) {\n    e.preventDefault();\n    clearTimeout(dragOverCell.timeout);\n    dragOverCell = { el: null, cell: null, timeout: null };\n    const transferData = JSON.parse(e.dataTransfer.getData(\"event\") || \"{}\");\n    let event, addToView;\n    if (dragging.fromVueCal !== this._vuecal._.uid) {\n      const { _eid, start, end, duration, ...cleanTransferData } = transferData;\n      event = this._vuecal.utils.event.createAnEvent(cellDate, duration, { ...cleanTransferData, split });\n    } else {\n      event = this._vuecal.view.events.find((evt) => evt._eid === dragging._eid);\n      if (!event) {\n        event = this._vuecal.mutableEvents.find((evt) => evt._eid === dragging._eid);\n        addToView = !!event;\n      }\n      if (!event) {\n        const duration = transferData.endTimeMinutes - transferData.startTimeMinutes;\n        const { start, end, ...cleanTransferData } = transferData;\n        event = this._vuecal.utils.event.createAnEvent(cellDate, duration, { ...cleanTransferData, split });\n      }\n    }\n    const { start: oldDate, split: oldSplit } = event;\n    this._updateEventStartEnd(e, event, transferData, cellDate);\n    if (addToView) this._vuecal.addEventsToView([event]);\n    event.dragging = false;\n    if (split || split === 0) event.split = split;\n    cell.highlighted = false;\n    cell.highlightedSplit = null;\n    cancelViewChange = false;\n    dragging.toVueCal = this._vuecal._.uid;\n    const params = {\n      event: this._vuecal.cleanupEvent(event),\n      oldDate,\n      newDate: event.start,\n      ...(split || split === 0) && { oldSplit, newSplit: split },\n      originalEvent: this._vuecal.cleanupEvent(transferData),\n      external: !dragging.fromVueCal\n      // If external event, not coming from any Vue Cal.\n    };\n    this._vuecal.$emit(\"event-drop\", params);\n    this._vuecal.$emit(\"event-change\", { event: params.event, originalEvent: params.originalEvent });\n    setTimeout(() => {\n      if (dragging._eid) this.eventDragEnd(event);\n    }, 300);\n  }\n  /**\n   * On drag enter on a view button or on today, prev & next buttons.\n   * Sets a highlighted state on the hovered button, and go to requested view.\n   *\n   * @param {Object} e The associated DOM event.\n   * @param {String} id The id of the header element being hovered. One of:\n   *                    previous, next, today, years, year, month, week, day.\n   * @param {Object} headerData The header component's $data.\n   */\n  viewSelectorDragEnter(e, id, headerData) {\n    if (e.currentTarget.contains(e.relatedTarget)) return;\n    headerData.highlightedControl = id;\n    clearTimeout(changeViewTimeout);\n    changeViewTimeout = setTimeout(() => {\n      if ([\"previous\", \"next\"].includes(id)) {\n        this._vuecal[id]();\n        clearInterval(pressPrevOrNextInterval);\n        pressPrevOrNextInterval = setInterval(this._vuecal[id], holdOverTimeout);\n      } else if (id === \"today\") {\n        clearInterval(pressPrevOrNextInterval);\n        let viewId;\n        if (this._vuecal.view.id.includes(\"year\")) {\n          viewId = this._vuecal.enabledViews.filter((view) => !view.includes(\"year\"))[0];\n        }\n        this._vuecal.switchView(viewId || this._vuecal.view.id, new Date((/* @__PURE__ */ new Date()).setHours(0, 0, 0, 0)), true);\n      } else this._vuecal.switchView(id, null, true);\n      viewChanged = true;\n    }, holdOverTimeout);\n  }\n  /**\n   * On drag leave on a view button or on today, prev & next buttons.\n   * Removes the highlighted state on the hovered button, and cancel the timer to\n   * go to the requested view.\n   *\n   * @param {Object} e The associated DOM event.\n   * @param {String} id The id of the header element being hovered. One of:\n   *                    previous, next, today, years, year, month, week, day.\n   * @param {Object} headerData The header component's $data.\n   */\n  viewSelectorDragLeave(e, id, headerData) {\n    if (e.currentTarget.contains(e.relatedTarget)) return;\n    if (headerData.highlightedControl === id) {\n      headerData.highlightedControl = null;\n      if (changeViewTimeout) changeViewTimeout = clearTimeout(changeViewTimeout);\n      if (pressPrevOrNextInterval) pressPrevOrNextInterval = clearInterval(pressPrevOrNextInterval);\n    }\n  }\n};\nexport {\n  DragAndDrop\n};\n"], "mappings": ";;;AAAA,IAAI,YAAY,OAAO;AACvB,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,gBAAgB,CAAC,KAAK,KAAK,UAAU,gBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK,KAAK;AAM7G,IAAM,kBAAkB;AACxB,IAAI,oBAAoB;AACxB,IAAI,0BAA0B;AAC9B,IAAI,iBAAiB,EAAE,IAAI,MAAM,MAAM,KAAK;AAC5C,IAAI,cAAc;AAClB,IAAI,mBAAmB;AACvB,IAAI,eAAe,EAAE,IAAI,MAAM,MAAM,MAAM,SAAS,KAAK;AACzD,IAAM,WAAW;AAAA,EACf,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,UAAU;AACZ;AACA,IAAM,cAAc,MAAM;AAAA,EACxB,YAAY,QAAQ;AAClB,kBAAc,MAAM,SAAS;AAC7B,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,GAAG;AAChB,UAAM,EAAE,UAAU,gBAAgB,UAAU,MAAM,IAAI,KAAK;AAC3D,QAAI,EAAE,EAAE,IAAI,MAAM,KAAK,YAAY,CAAC;AACpC,SAAK,EAAE,aAAa,QAAQ,gBAAgB,IAAI;AAChD,WAAO,KAAK,MAAM,IAAI,WAAW,SAAS,cAAc,IAAI,QAAQ;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,qBAAqB,GAAG,OAAO,cAAc,UAAU;AACrD,UAAM,gBAAgB,aAAa,WAAW,KAAK,MAAM,iBAAiB,MAAM;AAChF,QAAI,mBAAmB,KAAK,IAAI,KAAK,eAAe,CAAC,GAAG,CAAC;AACzD,QAAI,KAAK,QAAQ,YAAY;AAC3B,YAAM,mBAAmB,mBAAmB,KAAK,QAAQ,aAAa;AACtE,yBAAmB,mBAAmB,mBAAmB,KAAK,QAAQ;AAAA,IACxE;AACA,UAAM,mBAAmB;AACzB,UAAM,QAAQ,IAAI,KAAK,IAAI,KAAK,QAAQ,EAAE,WAAW,gBAAgB,CAAC;AACtE,UAAM,iBAAiB,KAAK,IAAI,mBAAmB,eAAe,KAAK,EAAE;AACzE,UAAM,MAAM,IAAI,KAAK,IAAI,KAAK,QAAQ,EAAE,WAAW,MAAM,cAAc,CAAC;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,GAAG,OAAO;AACvB,QAAI,EAAE,OAAO,aAAa,EAAG,QAAO,EAAE,eAAe;AACrD,MAAE,aAAa,aAAa;AAC5B,MAAE,aAAa,QAAQ,SAAS,KAAK,UAAU,KAAK,CAAC;AACrD,MAAE,aAAa,QAAQ,kBAAkB,EAAE,OAAO;AAClD,UAAM,EAAE,iBAAiB,IAAI,KAAK,QAAQ;AAC1C,eAAW,MAAM;AACf,uBAAiB,OAAO;AACxB,mBAAa,iBAAiB,SAAS;AACvC,YAAM,WAAW;AAAA,IACnB,GAAG,CAAC;AACJ,SAAK,QAAQ,UAAU,YAAY,OAAO,MAAM;AAChD,aAAS,OAAO,MAAM;AACtB,aAAS,aAAa,KAAK,QAAQ,EAAE;AACrC,UAAM,WAAW;AACjB,eAAW,MAAM,MAAM,iBAAiB,MAAM,CAAC;AAC/C,kBAAc;AACd,qBAAiB,EAAE,IAAI,KAAK,QAAQ,KAAK,IAAI,MAAM,KAAK,QAAQ,KAAK,UAAU;AAC/E,uBAAmB;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,OAAO;AAClB,SAAK,QAAQ,UAAU,YAAY,OAAO;AAC1C,aAAS,OAAO;AAChB,UAAM,WAAW;AACjB,UAAM,iBAAiB;AACvB,UAAM,EAAE,YAAY,SAAS,IAAI;AACjC,QAAI,YAAY,eAAe,SAAU,MAAK,QAAQ,MAAM,MAAM,cAAc,KAAK;AACrF,aAAS,aAAa;AACtB,aAAS,WAAW;AACpB,QAAI,eAAe,oBAAoB,eAAe,GAAI,MAAK,QAAQ,WAAW,eAAe,IAAI,eAAe,MAAM,IAAI;AAAA,EAChI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc,GAAG,MAAM,UAAU;AAC/B,UAAM,SAAS,EAAE;AACjB,QAAI,EAAE,cAAc,SAAS,EAAE,aAAa,EAAG;AAC/C,QAAI,WAAW,aAAa,MAAM,CAAC,OAAO,UAAU,SAAS,sBAAsB,EAAG,QAAO;AAC7F,QAAI,aAAa,GAAI,cAAa,KAAK,cAAc;AACrD,mBAAe,EAAE,IAAI,QAAQ,MAAM,SAAS,aAAa,aAAa,OAAO,EAAE;AAC/E,SAAK,cAAc;AACnB,QAAI,CAAC,SAAS,QAAQ,OAAO,EAAE,SAAS,KAAK,QAAQ,KAAK,EAAE,GAAG;AAC7D,mBAAa,UAAU,WAAW,MAAM,KAAK,QAAQ,qBAAqB,QAAQ,GAAG,GAAG;AAAA,IAC1F;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,aAAa,GAAG,MAAM,UAAU,OAAO;AACrC,MAAE,eAAe;AACjB,SAAK,cAAc;AACnB,QAAI,SAAS,UAAU,EAAG,MAAK,mBAAmB;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,GAAG,MAAM;AACrB,MAAE,eAAe;AACjB,QAAI,EAAE,cAAc,SAAS,EAAE,aAAa,EAAG;AAC/C,SAAK,mBAAmB;AACxB,QAAI,aAAa,SAAS,MAAM;AAC9B,mBAAa,aAAa,OAAO;AACjC,qBAAe,EAAE,IAAI,MAAM,MAAM,MAAM,SAAS,KAAK;AACrD,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,aAAa,GAAG,MAAM,UAAU,OAAO;AACrC,MAAE,eAAe;AACjB,iBAAa,aAAa,OAAO;AACjC,mBAAe,EAAE,IAAI,MAAM,MAAM,MAAM,SAAS,KAAK;AACrD,UAAM,eAAe,KAAK,MAAM,EAAE,aAAa,QAAQ,OAAO,KAAK,IAAI;AACvE,QAAI,OAAO;AACX,QAAI,SAAS,eAAe,KAAK,QAAQ,EAAE,KAAK;AAC9C,YAAM,EAAE,MAAM,OAAO,KAAK,UAAU,GAAG,kBAAkB,IAAI;AAC7D,cAAQ,KAAK,QAAQ,MAAM,MAAM,cAAc,UAAU,UAAU,EAAE,GAAG,mBAAmB,MAAM,CAAC;AAAA,IACpG,OAAO;AACL,cAAQ,KAAK,QAAQ,KAAK,OAAO,KAAK,CAAC,QAAQ,IAAI,SAAS,SAAS,IAAI;AACzE,UAAI,CAAC,OAAO;AACV,gBAAQ,KAAK,QAAQ,cAAc,KAAK,CAAC,QAAQ,IAAI,SAAS,SAAS,IAAI;AAC3E,oBAAY,CAAC,CAAC;AAAA,MAChB;AACA,UAAI,CAAC,OAAO;AACV,cAAM,WAAW,aAAa,iBAAiB,aAAa;AAC5D,cAAM,EAAE,OAAO,KAAK,GAAG,kBAAkB,IAAI;AAC7C,gBAAQ,KAAK,QAAQ,MAAM,MAAM,cAAc,UAAU,UAAU,EAAE,GAAG,mBAAmB,MAAM,CAAC;AAAA,MACpG;AAAA,IACF;AACA,UAAM,EAAE,OAAO,SAAS,OAAO,SAAS,IAAI;AAC5C,SAAK,qBAAqB,GAAG,OAAO,cAAc,QAAQ;AAC1D,QAAI,UAAW,MAAK,QAAQ,gBAAgB,CAAC,KAAK,CAAC;AACnD,UAAM,WAAW;AACjB,QAAI,SAAS,UAAU,EAAG,OAAM,QAAQ;AACxC,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,uBAAmB;AACnB,aAAS,WAAW,KAAK,QAAQ,EAAE;AACnC,UAAM,SAAS;AAAA,MACb,OAAO,KAAK,QAAQ,aAAa,KAAK;AAAA,MACtC;AAAA,MACA,SAAS,MAAM;AAAA,MACf,IAAI,SAAS,UAAU,MAAM,EAAE,UAAU,UAAU,MAAM;AAAA,MACzD,eAAe,KAAK,QAAQ,aAAa,YAAY;AAAA,MACrD,UAAU,CAAC,SAAS;AAAA;AAAA,IAEtB;AACA,SAAK,QAAQ,MAAM,cAAc,MAAM;AACvC,SAAK,QAAQ,MAAM,gBAAgB,EAAE,OAAO,OAAO,OAAO,eAAe,OAAO,cAAc,CAAC;AAC/F,eAAW,MAAM;AACf,UAAI,SAAS,KAAM,MAAK,aAAa,KAAK;AAAA,IAC5C,GAAG,GAAG;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,sBAAsB,GAAG,IAAI,YAAY;AACvC,QAAI,EAAE,cAAc,SAAS,EAAE,aAAa,EAAG;AAC/C,eAAW,qBAAqB;AAChC,iBAAa,iBAAiB;AAC9B,wBAAoB,WAAW,MAAM;AACnC,UAAI,CAAC,YAAY,MAAM,EAAE,SAAS,EAAE,GAAG;AACrC,aAAK,QAAQ,EAAE,EAAE;AACjB,sBAAc,uBAAuB;AACrC,kCAA0B,YAAY,KAAK,QAAQ,EAAE,GAAG,eAAe;AAAA,MACzE,WAAW,OAAO,SAAS;AACzB,sBAAc,uBAAuB;AACrC,YAAI;AACJ,YAAI,KAAK,QAAQ,KAAK,GAAG,SAAS,MAAM,GAAG;AACzC,mBAAS,KAAK,QAAQ,aAAa,OAAO,CAAC,SAAS,CAAC,KAAK,SAAS,MAAM,CAAC,EAAE,CAAC;AAAA,QAC/E;AACA,aAAK,QAAQ,WAAW,UAAU,KAAK,QAAQ,KAAK,IAAI,IAAI,MAAsB,oBAAI,KAAK,GAAG,SAAS,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI;AAAA,MAC3H,MAAO,MAAK,QAAQ,WAAW,IAAI,MAAM,IAAI;AAC7C,oBAAc;AAAA,IAChB,GAAG,eAAe;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,sBAAsB,GAAG,IAAI,YAAY;AACvC,QAAI,EAAE,cAAc,SAAS,EAAE,aAAa,EAAG;AAC/C,QAAI,WAAW,uBAAuB,IAAI;AACxC,iBAAW,qBAAqB;AAChC,UAAI,kBAAmB,qBAAoB,aAAa,iBAAiB;AACzE,UAAI,wBAAyB,2BAA0B,cAAc,uBAAuB;AAAA,IAC9F;AAAA,EACF;AACF;", "names": []}