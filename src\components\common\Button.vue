<script setup>
import Spinner from './Spinner.vue';

const { title, theme, loading, disabled, extraClasses } = defineProps({
  title: String,
  theme: String,
  loading: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  extraClasses: {
    type: String,
    default: '',
  },
});
defineEmits(['handleClick']);
</script>

<template>
  <button
    :class="[
      (theme ? (theme === 'primary' ? 'bg-[#1A56DB] text-white dark:text-txt-150' : 'bg-gray-200 text-txt-150' ) : 'bg-bg-50 text-txt-1000'),
      'rounded-lg flex flex-row justify-center items-center gap-2',
      'px-4 py-1 max-sm:px-2 sm:py-2',
      'h-8 sm:h-10',
      'text-xs sm:text-sm font-medium',
      'disabled:opacity-80 transition-all duration-200 ease-in-out',
      extraClasses
    ]"
    :disabled="disabled || loading"
    @click="(event) => $emit('handleClick', event)"
  >
    <slot v-if="$slots.svg" name="svg">
    </slot>
    <span class="text-center text-inherit whitespace-nowrap text-sm">{{ title }}</span>
    <Spinner v-if="loading" />
  </button>
</template>
