# StatusPopup Component Documentation

A reusable modal popup component for displaying status messages with icons, titles, messages, and action buttons.

## Features

- ✅ **Multiple Status Types**: Success, Error, Warning, Info
- ✅ **Modal Structure**: Proper backdrop, focus management, and accessibility
- ✅ **Customizable**: Title, message, button text, and behavior options
- ✅ **Responsive**: Works on mobile and desktop
- ✅ **Accessible**: ARIA labels, keyboard navigation, focus management
- ✅ **Smooth Animations**: CSS transitions for show/hide
- ✅ **Flexible Closing**: Close button, backdrop click, or programmatic

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `show` | Boolean | `false` | Controls popup visibility |
| `type` | String | Required | Status type: `'success'`, `'error'`, `'warning'`, `'info'` |
| `title` | String | `''` | Popup title (optional) |
| `message` | String | Required | Main message content |
| `buttonText` | String | `'Continue'` | Action button text |
| `isMobile` | Boolean | `false` | Mobile-specific styling |
| `showCloseButton` | Boolean | `true` | Show/hide close button |
| `closeOnBackdrop` | Boolean | `true` | Allow closing by clicking backdrop |

## Events

| Event | Description | Payload |
|-------|-------------|---------|
| `close` | Emitted when popup should be closed | None |
| `button-click` | Emitted when action button is clicked | None |

## Usage Examples

### Basic Usage

```vue
<template>
  <div>
    <button @click="showPopup = true">Show Success</button>
    
    <StatusPopup
      :show="showPopup"
      type="success"
      title="Success!"
      message="Your action was completed successfully."
      button-text="Continue"
      @close="showPopup = false"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import StatusPopup from '@/components/common/StatusPopup.vue';

const showPopup = ref(false);
</script>
```

### Different Status Types

```vue
<!-- Success Popup -->
<StatusPopup
  :show="showSuccess"
  type="success"
  title="Success!"
  message="Operation completed successfully."
  @close="showSuccess = false"
/>

<!-- Error Popup -->
<StatusPopup
  :show="showError"
  type="error"
  title="Error"
  message="Something went wrong. Please try again."
  button-text="Try Again"
  @close="showError = false"
/>

<!-- Warning Popup -->
<StatusPopup
  :show="showWarning"
  type="warning"
  title="Warning"
  message="Please review your input before proceeding."
  button-text="Review"
  @close="showWarning = false"
/>

<!-- Info Popup -->
<StatusPopup
  :show="showInfo"
  type="info"
  title="Information"
  message="Here's some important information."
  button-text="Got It"
  @close="showInfo = false"
/>
```

### Custom Button Actions

```vue
<StatusPopup
  :show="showCustom"
  type="success"
  title="Custom Action"
  message="This popup has a custom button action."
  button-text="Custom Action"
  @close="showCustom = false"
  @button-click="handleCustomAction"
/>

<script setup>
const handleCustomAction = () => {
  // Perform custom action
  console.log('Custom action executed!');
  // Popup will close automatically unless you prevent it
};
</script>
```

### Advanced Configuration

```vue
<!-- No close button, no backdrop close -->
<StatusPopup
  :show="showModal"
  type="warning"
  title="Important Notice"
  message="This cannot be dismissed easily."
  button-text="Acknowledge"
  :show-close-button="false"
  :close-on-backdrop="false"
  @close="showModal = false"
/>

<!-- Mobile optimized -->
<StatusPopup
  :show="showMobile"
  type="info"
  title="Mobile Popup"
  message="Optimized for mobile devices."
  :is-mobile="true"
  @close="showMobile = false"
/>
```

## Styling

The component uses CSS custom properties and Tailwind classes. Key styling features:

- **Background**: Uses `bg-secondary` for theme consistency
- **Text**: Uses `text-secondaryText` for theme consistency  
- **Button**: Custom blue button with hover/active states
- **Icons**: SVG icons with proper colors for each status type
- **Animations**: Smooth scale and opacity transitions

## Accessibility

- **ARIA Labels**: Proper `role="dialog"` and `aria-modal="true"`
- **Focus Management**: Traps focus within the modal
- **Keyboard Navigation**: Supports Escape key to close
- **Screen Readers**: Descriptive labels and IDs for content

## Integration with Existing Codebase

This component is designed to work with your existing:
- **Theme System**: Uses CSS custom properties (`--secondaryText`, etc.)
- **Color Scheme**: Matches your existing design tokens
- **Button Styles**: Custom button styling that matches your preferences
- **Modal Pattern**: Follows the same modal structure as your sample code

## Best Practices

1. **Always provide meaningful messages**: Make sure the message clearly explains what happened
2. **Use appropriate status types**: Choose the right type for the context
3. **Handle button actions**: Implement proper handlers for button clicks
4. **Consider mobile users**: Use the `isMobile` prop when needed
5. **Manage state properly**: Use reactive data to control popup visibility

## Common Use Cases

- **Form Submission Results**: Success/error messages after form submission
- **API Response Feedback**: Show results of API calls
- **User Confirmations**: Get user acknowledgment of important actions
- **Error Handling**: Display user-friendly error messages
- **Information Notices**: Show important information to users
