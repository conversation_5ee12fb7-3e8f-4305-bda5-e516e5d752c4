{"version": 3, "sources": ["../../node_modules/three/examples/jsm/objects/Lensflare.js"], "sourcesContent": ["import {\n\tAdditiveBlending,\n\tBox2,\n\tBufferGeometry,\n\tColor,\n\tFramebufferTexture,\n\tInterleavedBuffer,\n\tInterleavedBufferAttribute,\n\tMesh,\n\tMeshBasicMaterial,\n\tRawShaderMaterial,\n\tVector2,\n\tVector3,\n\tVector4\n} from 'three';\n\nclass Lensflare extends Mesh {\n\n\tconstructor() {\n\n\t\tsuper( Lensflare.Geometry, new MeshBasicMaterial( { opacity: 0, transparent: true } ) );\n\n\t\tthis.isLensflare = true;\n\n\t\tthis.type = 'Lensflare';\n\t\tthis.frustumCulled = false;\n\t\tthis.renderOrder = Infinity;\n\n\t\t//\n\n\t\tconst positionScreen = new Vector3();\n\t\tconst positionView = new Vector3();\n\n\t\t// textures\n\n\t\tconst tempMap = new FramebufferTexture( 16, 16 );\n\t\tconst occlusionMap = new FramebufferTexture( 16, 16 );\n\n\t\t// material\n\n\t\tconst geometry = Lensflare.Geometry;\n\n\t\tconst material1a = new RawShaderMaterial( {\n\t\t\tuniforms: {\n\t\t\t\t'scale': { value: null },\n\t\t\t\t'screenPosition': { value: null }\n\t\t\t},\n\t\t\tvertexShader: /* glsl */`\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tuniform vec3 screenPosition;\n\t\t\t\tuniform vec2 scale;\n\n\t\t\t\tattribute vec3 position;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tgl_Position = vec4( position.xy * scale + screenPosition.xy, screenPosition.z, 1.0 );\n\n\t\t\t\t}`,\n\n\t\t\tfragmentShader: /* glsl */`\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tgl_FragColor = vec4( 1.0, 0.0, 1.0, 1.0 );\n\n\t\t\t\t}`,\n\t\t\tdepthTest: true,\n\t\t\tdepthWrite: false,\n\t\t\ttransparent: false\n\t\t} );\n\n\t\tconst material1b = new RawShaderMaterial( {\n\t\t\tuniforms: {\n\t\t\t\t'map': { value: tempMap },\n\t\t\t\t'scale': { value: null },\n\t\t\t\t'screenPosition': { value: null }\n\t\t\t},\n\t\t\tvertexShader: /* glsl */`\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tuniform vec3 screenPosition;\n\t\t\t\tuniform vec2 scale;\n\n\t\t\t\tattribute vec3 position;\n\t\t\t\tattribute vec2 uv;\n\n\t\t\t\tvarying vec2 vUV;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tvUV = uv;\n\n\t\t\t\t\tgl_Position = vec4( position.xy * scale + screenPosition.xy, screenPosition.z, 1.0 );\n\n\t\t\t\t}`,\n\n\t\t\tfragmentShader: /* glsl */`\n\n\t\t\t\tprecision highp float;\n\n\t\t\t\tuniform sampler2D map;\n\n\t\t\t\tvarying vec2 vUV;\n\n\t\t\t\tvoid main() {\n\n\t\t\t\t\tgl_FragColor = texture2D( map, vUV );\n\n\t\t\t\t}`,\n\t\t\tdepthTest: false,\n\t\t\tdepthWrite: false,\n\t\t\ttransparent: false\n\t\t} );\n\n\t\t// the following object is used for occlusionMap generation\n\n\t\tconst mesh1 = new Mesh( geometry, material1a );\n\n\t\t//\n\n\t\tconst elements = [];\n\n\t\tconst shader = LensflareElement.Shader;\n\n\t\tconst material2 = new RawShaderMaterial( {\n\t\t\tuniforms: {\n\t\t\t\t'map': { value: null },\n\t\t\t\t'occlusionMap': { value: occlusionMap },\n\t\t\t\t'color': { value: new Color( 0xffffff ) },\n\t\t\t\t'scale': { value: new Vector2() },\n\t\t\t\t'screenPosition': { value: new Vector3() }\n\t\t\t},\n\t\t\tvertexShader: shader.vertexShader,\n\t\t\tfragmentShader: shader.fragmentShader,\n\t\t\tblending: AdditiveBlending,\n\t\t\ttransparent: true,\n\t\t\tdepthWrite: false\n\t\t} );\n\n\t\tconst mesh2 = new Mesh( geometry, material2 );\n\n\t\tthis.addElement = function ( element ) {\n\n\t\t\telements.push( element );\n\n\t\t};\n\n\t\t//\n\n\t\tconst scale = new Vector2();\n\t\tconst screenPositionPixels = new Vector2();\n\t\tconst validArea = new Box2();\n\t\tconst viewport = new Vector4();\n\n\t\tthis.onBeforeRender = function ( renderer, scene, camera ) {\n\n\t\t\trenderer.getCurrentViewport( viewport );\n\n\t\t\tconst invAspect = viewport.w / viewport.z;\n\t\t\tconst halfViewportWidth = viewport.z / 2.0;\n\t\t\tconst halfViewportHeight = viewport.w / 2.0;\n\n\t\t\tlet size = 16 / viewport.w;\n\t\t\tscale.set( size * invAspect, size );\n\n\t\t\tvalidArea.min.set( viewport.x, viewport.y );\n\t\t\tvalidArea.max.set( viewport.x + ( viewport.z - 16 ), viewport.y + ( viewport.w - 16 ) );\n\n\t\t\t// calculate position in screen space\n\n\t\t\tpositionView.setFromMatrixPosition( this.matrixWorld );\n\t\t\tpositionView.applyMatrix4( camera.matrixWorldInverse );\n\n\t\t\tif ( positionView.z > 0 ) return; // lensflare is behind the camera\n\n\t\t\tpositionScreen.copy( positionView ).applyMatrix4( camera.projectionMatrix );\n\n\t\t\t// horizontal and vertical coordinate of the lower left corner of the pixels to copy\n\n\t\t\tscreenPositionPixels.x = viewport.x + ( positionScreen.x * halfViewportWidth ) + halfViewportWidth - 8;\n\t\t\tscreenPositionPixels.y = viewport.y + ( positionScreen.y * halfViewportHeight ) + halfViewportHeight - 8;\n\n\t\t\t// screen cull\n\n\t\t\tif ( validArea.containsPoint( screenPositionPixels ) ) {\n\n\t\t\t\t// save current RGB to temp texture\n\n\t\t\t\trenderer.copyFramebufferToTexture( screenPositionPixels, tempMap );\n\n\t\t\t\t// render pink quad\n\n\t\t\t\tlet uniforms = material1a.uniforms;\n\t\t\t\tuniforms[ 'scale' ].value = scale;\n\t\t\t\tuniforms[ 'screenPosition' ].value = positionScreen;\n\n\t\t\t\trenderer.renderBufferDirect( camera, null, geometry, material1a, mesh1, null );\n\n\t\t\t\t// copy result to occlusionMap\n\n\t\t\t\trenderer.copyFramebufferToTexture( screenPositionPixels, occlusionMap );\n\n\t\t\t\t// restore graphics\n\n\t\t\t\tuniforms = material1b.uniforms;\n\t\t\t\tuniforms[ 'scale' ].value = scale;\n\t\t\t\tuniforms[ 'screenPosition' ].value = positionScreen;\n\n\t\t\t\trenderer.renderBufferDirect( camera, null, geometry, material1b, mesh1, null );\n\n\t\t\t\t// render elements\n\n\t\t\t\tconst vecX = - positionScreen.x * 2;\n\t\t\t\tconst vecY = - positionScreen.y * 2;\n\n\t\t\t\tfor ( let i = 0, l = elements.length; i < l; i ++ ) {\n\n\t\t\t\t\tconst element = elements[ i ];\n\n\t\t\t\t\tconst uniforms = material2.uniforms;\n\n\t\t\t\t\tuniforms[ 'color' ].value.copy( element.color );\n\t\t\t\t\tuniforms[ 'map' ].value = element.texture;\n\t\t\t\t\tuniforms[ 'screenPosition' ].value.x = positionScreen.x + vecX * element.distance;\n\t\t\t\t\tuniforms[ 'screenPosition' ].value.y = positionScreen.y + vecY * element.distance;\n\n\t\t\t\t\tsize = element.size / viewport.w;\n\t\t\t\t\tconst invAspect = viewport.w / viewport.z;\n\n\t\t\t\t\tuniforms[ 'scale' ].value.set( size * invAspect, size );\n\n\t\t\t\t\tmaterial2.uniformsNeedUpdate = true;\n\n\t\t\t\t\trenderer.renderBufferDirect( camera, null, geometry, material2, mesh2, null );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t};\n\n\t\tthis.dispose = function () {\n\n\t\t\tmaterial1a.dispose();\n\t\t\tmaterial1b.dispose();\n\t\t\tmaterial2.dispose();\n\n\t\t\ttempMap.dispose();\n\t\t\tocclusionMap.dispose();\n\n\t\t\tfor ( let i = 0, l = elements.length; i < l; i ++ ) {\n\n\t\t\t\telements[ i ].texture.dispose();\n\n\t\t\t}\n\n\t\t};\n\n\t}\n\n}\n\n//\n\nclass LensflareElement {\n\n\tconstructor( texture, size = 1, distance = 0, color = new Color( 0xffffff ) ) {\n\n\t\tthis.texture = texture;\n\t\tthis.size = size;\n\t\tthis.distance = distance;\n\t\tthis.color = color;\n\n\t}\n\n}\n\nLensflareElement.Shader = {\n\n\tuniforms: {\n\n\t\t'map': { value: null },\n\t\t'occlusionMap': { value: null },\n\t\t'color': { value: null },\n\t\t'scale': { value: null },\n\t\t'screenPosition': { value: null }\n\n\t},\n\n\tvertexShader: /* glsl */`\n\n\t\tprecision highp float;\n\n\t\tuniform vec3 screenPosition;\n\t\tuniform vec2 scale;\n\n\t\tuniform sampler2D occlusionMap;\n\n\t\tattribute vec3 position;\n\t\tattribute vec2 uv;\n\n\t\tvarying vec2 vUV;\n\t\tvarying float vVisibility;\n\n\t\tvoid main() {\n\n\t\t\tvUV = uv;\n\n\t\t\tvec2 pos = position.xy;\n\n\t\t\tvec4 visibility = texture2D( occlusionMap, vec2( 0.1, 0.1 ) );\n\t\t\tvisibility += texture2D( occlusionMap, vec2( 0.5, 0.1 ) );\n\t\t\tvisibility += texture2D( occlusionMap, vec2( 0.9, 0.1 ) );\n\t\t\tvisibility += texture2D( occlusionMap, vec2( 0.9, 0.5 ) );\n\t\t\tvisibility += texture2D( occlusionMap, vec2( 0.9, 0.9 ) );\n\t\t\tvisibility += texture2D( occlusionMap, vec2( 0.5, 0.9 ) );\n\t\t\tvisibility += texture2D( occlusionMap, vec2( 0.1, 0.9 ) );\n\t\t\tvisibility += texture2D( occlusionMap, vec2( 0.1, 0.5 ) );\n\t\t\tvisibility += texture2D( occlusionMap, vec2( 0.5, 0.5 ) );\n\n\t\t\tvVisibility =        visibility.r / 9.0;\n\t\t\tvVisibility *= 1.0 - visibility.g / 9.0;\n\t\t\tvVisibility *=       visibility.b / 9.0;\n\n\t\t\tgl_Position = vec4( ( pos * scale + screenPosition.xy ).xy, screenPosition.z, 1.0 );\n\n\t\t}`,\n\n\tfragmentShader: /* glsl */`\n\n\t\tprecision highp float;\n\n\t\tuniform sampler2D map;\n\t\tuniform vec3 color;\n\n\t\tvarying vec2 vUV;\n\t\tvarying float vVisibility;\n\n\t\tvoid main() {\n\n\t\t\tvec4 texture = texture2D( map, vUV );\n\t\t\ttexture.a *= vVisibility;\n\t\t\tgl_FragColor = texture;\n\t\t\tgl_FragColor.rgb *= color;\n\n\t\t}`\n\n};\n\nLensflare.Geometry = ( function () {\n\n\tconst geometry = new BufferGeometry();\n\n\tconst float32Array = new Float32Array( [\n\t\t- 1, - 1, 0, 0, 0,\n\t\t1, - 1, 0, 1, 0,\n\t\t1, 1, 0, 1, 1,\n\t\t- 1, 1, 0, 0, 1\n\t] );\n\n\tconst interleavedBuffer = new InterleavedBuffer( float32Array, 5 );\n\n\tgeometry.setIndex( [ 0, 1, 2,\t0, 2, 3 ] );\n\tgeometry.setAttribute( 'position', new InterleavedBufferAttribute( interleavedBuffer, 3, 0, false ) );\n\tgeometry.setAttribute( 'uv', new InterleavedBufferAttribute( interleavedBuffer, 2, 3, false ) );\n\n\treturn geometry;\n\n} )();\n\nexport { Lensflare, LensflareElement };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAgBA,IAAM,YAAN,MAAM,mBAAkB,KAAK;AAAA,EAE5B,cAAc;AAEb,UAAO,WAAU,UAAU,IAAI,kBAAmB,EAAE,SAAS,GAAG,aAAa,KAAK,CAAE,CAAE;AAEtF,SAAK,cAAc;AAEnB,SAAK,OAAO;AACZ,SAAK,gBAAgB;AACrB,SAAK,cAAc;AAInB,UAAM,iBAAiB,IAAI,QAAQ;AACnC,UAAM,eAAe,IAAI,QAAQ;AAIjC,UAAM,UAAU,IAAI,mBAAoB,IAAI,EAAG;AAC/C,UAAM,eAAe,IAAI,mBAAoB,IAAI,EAAG;AAIpD,UAAM,WAAW,WAAU;AAE3B,UAAM,aAAa,IAAI,kBAAmB;AAAA,MACzC,UAAU;AAAA,QACT,SAAS,EAAE,OAAO,KAAK;AAAA,QACvB,kBAAkB,EAAE,OAAO,KAAK;AAAA,MACjC;AAAA,MACA;AAAA;AAAA,QAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAexB;AAAA;AAAA,QAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAS1B,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,IACd,CAAE;AAEF,UAAM,aAAa,IAAI,kBAAmB;AAAA,MACzC,UAAU;AAAA,QACT,OAAO,EAAE,OAAO,QAAQ;AAAA,QACxB,SAAS,EAAE,OAAO,KAAK;AAAA,QACvB,kBAAkB,EAAE,OAAO,KAAK;AAAA,MACjC;AAAA,MACA;AAAA;AAAA,QAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAoBxB;AAAA;AAAA,QAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAa1B,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,aAAa;AAAA,IACd,CAAE;AAIF,UAAM,QAAQ,IAAI,KAAM,UAAU,UAAW;AAI7C,UAAM,WAAW,CAAC;AAElB,UAAM,SAAS,iBAAiB;AAEhC,UAAM,YAAY,IAAI,kBAAmB;AAAA,MACxC,UAAU;AAAA,QACT,OAAO,EAAE,OAAO,KAAK;AAAA,QACrB,gBAAgB,EAAE,OAAO,aAAa;AAAA,QACtC,SAAS,EAAE,OAAO,IAAI,MAAO,QAAS,EAAE;AAAA,QACxC,SAAS,EAAE,OAAO,IAAI,QAAQ,EAAE;AAAA,QAChC,kBAAkB,EAAE,OAAO,IAAI,QAAQ,EAAE;AAAA,MAC1C;AAAA,MACA,cAAc,OAAO;AAAA,MACrB,gBAAgB,OAAO;AAAA,MACvB,UAAU;AAAA,MACV,aAAa;AAAA,MACb,YAAY;AAAA,IACb,CAAE;AAEF,UAAM,QAAQ,IAAI,KAAM,UAAU,SAAU;AAE5C,SAAK,aAAa,SAAW,SAAU;AAEtC,eAAS,KAAM,OAAQ;AAAA,IAExB;AAIA,UAAM,QAAQ,IAAI,QAAQ;AAC1B,UAAM,uBAAuB,IAAI,QAAQ;AACzC,UAAM,YAAY,IAAI,KAAK;AAC3B,UAAM,WAAW,IAAI,QAAQ;AAE7B,SAAK,iBAAiB,SAAW,UAAU,OAAO,QAAS;AAE1D,eAAS,mBAAoB,QAAS;AAEtC,YAAM,YAAY,SAAS,IAAI,SAAS;AACxC,YAAM,oBAAoB,SAAS,IAAI;AACvC,YAAM,qBAAqB,SAAS,IAAI;AAExC,UAAI,OAAO,KAAK,SAAS;AACzB,YAAM,IAAK,OAAO,WAAW,IAAK;AAElC,gBAAU,IAAI,IAAK,SAAS,GAAG,SAAS,CAAE;AAC1C,gBAAU,IAAI,IAAK,SAAS,KAAM,SAAS,IAAI,KAAM,SAAS,KAAM,SAAS,IAAI,GAAK;AAItF,mBAAa,sBAAuB,KAAK,WAAY;AACrD,mBAAa,aAAc,OAAO,kBAAmB;AAErD,UAAK,aAAa,IAAI,EAAI;AAE1B,qBAAe,KAAM,YAAa,EAAE,aAAc,OAAO,gBAAiB;AAI1E,2BAAqB,IAAI,SAAS,IAAM,eAAe,IAAI,oBAAsB,oBAAoB;AACrG,2BAAqB,IAAI,SAAS,IAAM,eAAe,IAAI,qBAAuB,qBAAqB;AAIvG,UAAK,UAAU,cAAe,oBAAqB,GAAI;AAItD,iBAAS,yBAA0B,sBAAsB,OAAQ;AAIjE,YAAI,WAAW,WAAW;AAC1B,iBAAU,OAAQ,EAAE,QAAQ;AAC5B,iBAAU,gBAAiB,EAAE,QAAQ;AAErC,iBAAS,mBAAoB,QAAQ,MAAM,UAAU,YAAY,OAAO,IAAK;AAI7E,iBAAS,yBAA0B,sBAAsB,YAAa;AAItE,mBAAW,WAAW;AACtB,iBAAU,OAAQ,EAAE,QAAQ;AAC5B,iBAAU,gBAAiB,EAAE,QAAQ;AAErC,iBAAS,mBAAoB,QAAQ,MAAM,UAAU,YAAY,OAAO,IAAK;AAI7E,cAAM,OAAO,CAAE,eAAe,IAAI;AAClC,cAAM,OAAO,CAAE,eAAe,IAAI;AAElC,iBAAU,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAO;AAEnD,gBAAM,UAAU,SAAU,CAAE;AAE5B,gBAAMA,YAAW,UAAU;AAE3B,UAAAA,UAAU,OAAQ,EAAE,MAAM,KAAM,QAAQ,KAAM;AAC9C,UAAAA,UAAU,KAAM,EAAE,QAAQ,QAAQ;AAClC,UAAAA,UAAU,gBAAiB,EAAE,MAAM,IAAI,eAAe,IAAI,OAAO,QAAQ;AACzE,UAAAA,UAAU,gBAAiB,EAAE,MAAM,IAAI,eAAe,IAAI,OAAO,QAAQ;AAEzE,iBAAO,QAAQ,OAAO,SAAS;AAC/B,gBAAMC,aAAY,SAAS,IAAI,SAAS;AAExC,UAAAD,UAAU,OAAQ,EAAE,MAAM,IAAK,OAAOC,YAAW,IAAK;AAEtD,oBAAU,qBAAqB;AAE/B,mBAAS,mBAAoB,QAAQ,MAAM,UAAU,WAAW,OAAO,IAAK;AAAA,QAE7E;AAAA,MAED;AAAA,IAED;AAEA,SAAK,UAAU,WAAY;AAE1B,iBAAW,QAAQ;AACnB,iBAAW,QAAQ;AACnB,gBAAU,QAAQ;AAElB,cAAQ,QAAQ;AAChB,mBAAa,QAAQ;AAErB,eAAU,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,KAAO;AAEnD,iBAAU,CAAE,EAAE,QAAQ,QAAQ;AAAA,MAE/B;AAAA,IAED;AAAA,EAED;AAED;AAIA,IAAM,mBAAN,MAAuB;AAAA,EAEtB,YAAa,SAAS,OAAO,GAAG,WAAW,GAAG,QAAQ,IAAI,MAAO,QAAS,GAAI;AAE7E,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,QAAQ;AAAA,EAEd;AAED;AAEA,iBAAiB,SAAS;AAAA,EAEzB,UAAU;AAAA,IAET,OAAO,EAAE,OAAO,KAAK;AAAA,IACrB,gBAAgB,EAAE,OAAO,KAAK;AAAA,IAC9B,SAAS,EAAE,OAAO,KAAK;AAAA,IACvB,SAAS,EAAE,OAAO,KAAK;AAAA,IACvB,kBAAkB,EAAE,OAAO,KAAK;AAAA,EAEjC;AAAA,EAEA;AAAA;AAAA,IAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuCxB;AAAA;AAAA,IAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmB3B;AAEA,UAAU,WAAa,WAAY;AAElC,QAAM,WAAW,IAAI,eAAe;AAEpC,QAAM,eAAe,IAAI,aAAc;AAAA,IACtC;AAAA,IAAK;AAAA,IAAK;AAAA,IAAG;AAAA,IAAG;AAAA,IAChB;AAAA,IAAG;AAAA,IAAK;AAAA,IAAG;AAAA,IAAG;AAAA,IACd;AAAA,IAAG;AAAA,IAAG;AAAA,IAAG;AAAA,IAAG;AAAA,IACZ;AAAA,IAAK;AAAA,IAAG;AAAA,IAAG;AAAA,IAAG;AAAA,EACf,CAAE;AAEF,QAAM,oBAAoB,IAAI,kBAAmB,cAAc,CAAE;AAEjE,WAAS,SAAU,CAAE,GAAG,GAAG,GAAG,GAAG,GAAG,CAAE,CAAE;AACxC,WAAS,aAAc,YAAY,IAAI,2BAA4B,mBAAmB,GAAG,GAAG,KAAM,CAAE;AACpG,WAAS,aAAc,MAAM,IAAI,2BAA4B,mBAAmB,GAAG,GAAG,KAAM,CAAE;AAE9F,SAAO;AAER,EAAI;", "names": ["uniforms", "invAspect"]}