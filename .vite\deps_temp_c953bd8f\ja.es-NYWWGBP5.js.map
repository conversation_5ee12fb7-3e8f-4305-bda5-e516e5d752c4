{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/ja.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"月\",\n  \"火\",\n  \"水\",\n  \"木\",\n  \"金\",\n  \"土\",\n  \"日\"\n];\nconst months = [\n  \"1月\",\n  \"2月\",\n  \"3月\",\n  \"4月\",\n  \"5月\",\n  \"6月\",\n  \"7月\",\n  \"8月\",\n  \"9月\",\n  \"10月\",\n  \"11月\",\n  \"12月\"\n];\nconst years = \"年\";\nconst year = \"今年\";\nconst month = \"月\";\nconst week = \"週\";\nconst day = \"日\";\nconst today = \"今日\";\nconst noEvent = \"イベントなし\";\nconst allDay = \"終日\";\nconst deleteEvent = \"削除\";\nconst createEvent = \"イベント作成\";\nconst dateFormat = \"YYYY年 MMMM D日 (dddd)\";\nconst ja = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  ja as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}