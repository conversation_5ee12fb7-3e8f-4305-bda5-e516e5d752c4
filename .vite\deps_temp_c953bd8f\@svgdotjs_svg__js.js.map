{"version": 3, "sources": ["../../node_modules/@svgdotjs/svg.js/src/utils/methods.js", "../../node_modules/@svgdotjs/svg.js/src/utils/utils.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/namespaces.js", "../../node_modules/@svgdotjs/svg.js/src/utils/window.js", "../../node_modules/@svgdotjs/svg.js/src/types/Base.js", "../../node_modules/@svgdotjs/svg.js/src/utils/adopter.js", "../../node_modules/@svgdotjs/svg.js/src/modules/optional/arrange.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/regex.js", "../../node_modules/@svgdotjs/svg.js/src/modules/optional/class.js", "../../node_modules/@svgdotjs/svg.js/src/modules/optional/css.js", "../../node_modules/@svgdotjs/svg.js/src/modules/optional/data.js", "../../node_modules/@svgdotjs/svg.js/src/modules/optional/memory.js", "../../node_modules/@svgdotjs/svg.js/src/types/Color.js", "../../node_modules/@svgdotjs/svg.js/src/types/Point.js", "../../node_modules/@svgdotjs/svg.js/src/types/Matrix.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/parser.js", "../../node_modules/@svgdotjs/svg.js/src/types/Box.js", "../../node_modules/@svgdotjs/svg.js/src/types/List.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/selector.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/event.js", "../../node_modules/@svgdotjs/svg.js/src/types/EventTarget.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/defaults.js", "../../node_modules/@svgdotjs/svg.js/src/types/SVGArray.js", "../../node_modules/@svgdotjs/svg.js/src/types/SVGNumber.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/attr.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Dom.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Element.js", "../../node_modules/@svgdotjs/svg.js/src/modules/optional/sugar.js", "../../node_modules/@svgdotjs/svg.js/src/modules/optional/transform.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Container.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Defs.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Shape.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/circled.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Ellipse.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Fragment.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/gradiented.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Gradient.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Pattern.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Image.js", "../../node_modules/@svgdotjs/svg.js/src/types/PointArray.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/pointed.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Line.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Marker.js", "../../node_modules/@svgdotjs/svg.js/src/animation/Controller.js", "../../node_modules/@svgdotjs/svg.js/src/utils/pathParser.js", "../../node_modules/@svgdotjs/svg.js/src/types/PathArray.js", "../../node_modules/@svgdotjs/svg.js/src/animation/Morphable.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Path.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/poly.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Polygon.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Polyline.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Rect.js", "../../node_modules/@svgdotjs/svg.js/src/animation/Queue.js", "../../node_modules/@svgdotjs/svg.js/src/animation/Animator.js", "../../node_modules/@svgdotjs/svg.js/src/animation/Timeline.js", "../../node_modules/@svgdotjs/svg.js/src/animation/Runner.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Svg.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Symbol.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/textable.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Text.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Tspan.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Circle.js", "../../node_modules/@svgdotjs/svg.js/src/elements/ClipPath.js", "../../node_modules/@svgdotjs/svg.js/src/elements/ForeignObject.js", "../../node_modules/@svgdotjs/svg.js/src/modules/core/containerGeometry.js", "../../node_modules/@svgdotjs/svg.js/src/elements/G.js", "../../node_modules/@svgdotjs/svg.js/src/elements/A.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Mask.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Stop.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Style.js", "../../node_modules/@svgdotjs/svg.js/src/elements/TextPath.js", "../../node_modules/@svgdotjs/svg.js/src/elements/Use.js", "../../node_modules/@svgdotjs/svg.js/src/main.js"], "sourcesContent": ["const methods = {}\nconst names = []\n\nexport function registerMethods(name, m) {\n  if (Array.isArray(name)) {\n    for (const _name of name) {\n      registerMethods(_name, m)\n    }\n    return\n  }\n\n  if (typeof name === 'object') {\n    for (const _name in name) {\n      registerMethods(_name, name[_name])\n    }\n    return\n  }\n\n  addMethodNames(Object.getOwnPropertyNames(m))\n  methods[name] = Object.assign(methods[name] || {}, m)\n}\n\nexport function getMethodsFor(name) {\n  return methods[name] || {}\n}\n\nexport function getMethodNames() {\n  return [...new Set(names)]\n}\n\nexport function addMethodNames(_names) {\n  names.push(..._names)\n}\n", "// Map function\nexport function map(array, block) {\n  let i\n  const il = array.length\n  const result = []\n\n  for (i = 0; i < il; i++) {\n    result.push(block(array[i]))\n  }\n\n  return result\n}\n\n// Filter function\nexport function filter(array, block) {\n  let i\n  const il = array.length\n  const result = []\n\n  for (i = 0; i < il; i++) {\n    if (block(array[i])) {\n      result.push(array[i])\n    }\n  }\n\n  return result\n}\n\n// Degrees to radians\nexport function radians(d) {\n  return ((d % 360) * Math.PI) / 180\n}\n\n// Radians to degrees\nexport function degrees(r) {\n  return ((r * 180) / Math.PI) % 360\n}\n\n// Convert camel cased string to dash separated\nexport function unCamelCase(s) {\n  return s.replace(/([A-Z])/g, function (m, g) {\n    return '-' + g.toLowerCase()\n  })\n}\n\n// Capitalize first letter of a string\nexport function capitalize(s) {\n  return s.charAt(0).toUpperCase() + s.slice(1)\n}\n\n// Calculate proportional width and height values when necessary\nexport function proportionalSize(element, width, height, box) {\n  if (width == null || height == null) {\n    box = box || element.bbox()\n\n    if (width == null) {\n      width = (box.width / box.height) * height\n    } else if (height == null) {\n      height = (box.height / box.width) * width\n    }\n  }\n\n  return {\n    width: width,\n    height: height\n  }\n}\n\n/**\n * This function adds support for string origins.\n * It searches for an origin in o.origin o.ox and o.originX.\n * This way, origin: {x: 'center', y: 50} can be passed as well as ox: 'center', oy: 50\n **/\nexport function getOrigin(o, element) {\n  const origin = o.origin\n  // First check if origin is in ox or originX\n  let ox = o.ox != null ? o.ox : o.originX != null ? o.originX : 'center'\n  let oy = o.oy != null ? o.oy : o.originY != null ? o.originY : 'center'\n\n  // Then check if origin was used and overwrite in that case\n  if (origin != null) {\n    ;[ox, oy] = Array.isArray(origin)\n      ? origin\n      : typeof origin === 'object'\n        ? [origin.x, origin.y]\n        : [origin, origin]\n  }\n\n  // Make sure to only call bbox when actually needed\n  const condX = typeof ox === 'string'\n  const condY = typeof oy === 'string'\n  if (condX || condY) {\n    const { height, width, x, y } = element.bbox()\n\n    // And only overwrite if string was passed for this specific axis\n    if (condX) {\n      ox = ox.includes('left')\n        ? x\n        : ox.includes('right')\n          ? x + width\n          : x + width / 2\n    }\n\n    if (condY) {\n      oy = oy.includes('top')\n        ? y\n        : oy.includes('bottom')\n          ? y + height\n          : y + height / 2\n    }\n  }\n\n  // Return the origin as it is if it wasn't a string\n  return [ox, oy]\n}\n\nconst descriptiveElements = new Set(['desc', 'metadata', 'title'])\nexport const isDescriptive = (element) =>\n  descriptiveElements.has(element.nodeName)\n\nexport const writeDataToDom = (element, data, defaults = {}) => {\n  const cloned = { ...data }\n\n  for (const key in cloned) {\n    if (cloned[key].valueOf() === defaults[key]) {\n      delete cloned[key]\n    }\n  }\n\n  if (Object.keys(cloned).length) {\n    element.node.setAttribute('data-svgjs', JSON.stringify(cloned)) // see #428\n  } else {\n    element.node.removeAttribute('data-svgjs')\n    element.node.removeAttribute('svgjs:data')\n  }\n}\n", "// Default namespaces\nexport const svg = 'http://www.w3.org/2000/svg'\nexport const html = 'http://www.w3.org/1999/xhtml'\nexport const xmlns = 'http://www.w3.org/2000/xmlns/'\nexport const xlink = 'http://www.w3.org/1999/xlink'\n", "export const globals = {\n  window: typeof window === 'undefined' ? null : window,\n  document: typeof document === 'undefined' ? null : document\n}\n\nexport function registerWindow(win = null, doc = null) {\n  globals.window = win\n  globals.document = doc\n}\n\nconst save = {}\n\nexport function saveWindow() {\n  save.window = globals.window\n  save.document = globals.document\n}\n\nexport function restoreWindow() {\n  globals.window = save.window\n  globals.document = save.document\n}\n\nexport function withWindow(win, fn) {\n  saveWindow()\n  registerWindow(win, win.document)\n  fn(win, win.document)\n  restoreWindow()\n}\n\nexport function getWindow() {\n  return globals.window\n}\n", "export default class Base {\n  // constructor (node/*, {extensions = []} */) {\n  //   // this.tags = []\n  //   //\n  //   // for (let extension of extensions) {\n  //   //   extension.setup.call(this, node)\n  //   //   this.tags.push(extension.name)\n  //   // }\n  // }\n}\n", "import { addMethodNames } from './methods.js'\nimport { capitalize } from './utils.js'\nimport { svg } from '../modules/core/namespaces.js'\nimport { globals } from '../utils/window.js'\nimport Base from '../types/Base.js'\n\nconst elements = {}\nexport const root = '___SYMBOL___ROOT___'\n\n// Method for element creation\nexport function create(name, ns = svg) {\n  // create element\n  return globals.document.createElementNS(ns, name)\n}\n\nexport function makeInstance(element, isHTML = false) {\n  if (element instanceof Base) return element\n\n  if (typeof element === 'object') {\n    return adopter(element)\n  }\n\n  if (element == null) {\n    return new elements[root]()\n  }\n\n  if (typeof element === 'string' && element.charAt(0) !== '<') {\n    return adopter(globals.document.querySelector(element))\n  }\n\n  // Make sure, that HTML elements are created with the correct namespace\n  const wrapper = isHTML ? globals.document.createElement('div') : create('svg')\n  wrapper.innerHTML = element\n\n  // We can use firstChild here because we know,\n  // that the first char is < and thus an element\n  element = adopter(wrapper.firstChild)\n\n  // make sure, that element doesn't have its wrapper attached\n  wrapper.removeChild(wrapper.firstChild)\n  return element\n}\n\nexport function nodeOrNew(name, node) {\n  return node &&\n    (node instanceof globals.window.Node ||\n      (node.ownerDocument &&\n        node instanceof node.ownerDocument.defaultView.Node))\n    ? node\n    : create(name)\n}\n\n// Adopt existing svg elements\nexport function adopt(node) {\n  // check for presence of node\n  if (!node) return null\n\n  // make sure a node isn't already adopted\n  if (node.instance instanceof Base) return node.instance\n\n  if (node.nodeName === '#document-fragment') {\n    return new elements.Fragment(node)\n  }\n\n  // initialize variables\n  let className = capitalize(node.nodeName || 'Dom')\n\n  // Make sure that gradients are adopted correctly\n  if (className === 'LinearGradient' || className === 'RadialGradient') {\n    className = 'Gradient'\n\n    // Fallback to Dom if element is not known\n  } else if (!elements[className]) {\n    className = 'Dom'\n  }\n\n  return new elements[className](node)\n}\n\nlet adopter = adopt\n\nexport function mockAdopt(mock = adopt) {\n  adopter = mock\n}\n\nexport function register(element, name = element.name, asRoot = false) {\n  elements[name] = element\n  if (asRoot) elements[root] = element\n\n  addMethodNames(Object.getOwnPropertyNames(element.prototype))\n\n  return element\n}\n\nexport function getClass(name) {\n  return elements[name]\n}\n\n// Element id sequence\nlet did = 1000\n\n// Get next named element id\nexport function eid(name) {\n  return 'Svgjs' + capitalize(name) + did++\n}\n\n// Deep new id assignment\nexport function assignNewId(node) {\n  // do the same for SVG child nodes as well\n  for (let i = node.children.length - 1; i >= 0; i--) {\n    assignNewId(node.children[i])\n  }\n\n  if (node.id) {\n    node.id = eid(node.nodeName)\n    return node\n  }\n\n  return node\n}\n\n// Method for extending objects\nexport function extend(modules, methods) {\n  let key, i\n\n  modules = Array.isArray(modules) ? modules : [modules]\n\n  for (i = modules.length - 1; i >= 0; i--) {\n    for (key in methods) {\n      modules[i].prototype[key] = methods[key]\n    }\n  }\n}\n\nexport function wrapWithAttrCheck(fn) {\n  return function (...args) {\n    const o = args[args.length - 1]\n\n    if (o && o.constructor === Object && !(o instanceof Array)) {\n      return fn.apply(this, args.slice(0, -1)).attr(o)\n    } else {\n      return fn.apply(this, args)\n    }\n  }\n}\n", "import { makeInstance } from '../../utils/adopter.js'\nimport { registerMethods } from '../../utils/methods.js'\n\n// Get all siblings, including myself\nexport function siblings() {\n  return this.parent().children()\n}\n\n// Get the current position siblings\nexport function position() {\n  return this.parent().index(this)\n}\n\n// Get the next element (will return null if there is none)\nexport function next() {\n  return this.siblings()[this.position() + 1]\n}\n\n// Get the next element (will return null if there is none)\nexport function prev() {\n  return this.siblings()[this.position() - 1]\n}\n\n// Send given element one step forward\nexport function forward() {\n  const i = this.position()\n  const p = this.parent()\n\n  // move node one step forward\n  p.add(this.remove(), i + 1)\n\n  return this\n}\n\n// Send given element one step backward\nexport function backward() {\n  const i = this.position()\n  const p = this.parent()\n\n  p.add(this.remove(), i ? i - 1 : 0)\n\n  return this\n}\n\n// Send given element all the way to the front\nexport function front() {\n  const p = this.parent()\n\n  // Move node forward\n  p.add(this.remove())\n\n  return this\n}\n\n// Send given element all the way to the back\nexport function back() {\n  const p = this.parent()\n\n  // Move node back\n  p.add(this.remove(), 0)\n\n  return this\n}\n\n// Inserts a given element before the targeted element\nexport function before(element) {\n  element = makeInstance(element)\n  element.remove()\n\n  const i = this.position()\n\n  this.parent().add(element, i)\n\n  return this\n}\n\n// Inserts a given element after the targeted element\nexport function after(element) {\n  element = makeInstance(element)\n  element.remove()\n\n  const i = this.position()\n\n  this.parent().add(element, i + 1)\n\n  return this\n}\n\nexport function insertBefore(element) {\n  element = makeInstance(element)\n  element.before(this)\n  return this\n}\n\nexport function insertAfter(element) {\n  element = makeInstance(element)\n  element.after(this)\n  return this\n}\n\nregisterMethods('Dom', {\n  siblings,\n  position,\n  next,\n  prev,\n  forward,\n  backward,\n  front,\n  back,\n  before,\n  after,\n  insertBefore,\n  insertAfter\n})\n", "// Parse unit value\nexport const numberAndUnit =\n  /^([+-]?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?)([a-z%]*)$/i\n\n// Parse hex value\nexport const hex = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i\n\n// Parse rgb value\nexport const rgb = /rgb\\((\\d+),(\\d+),(\\d+)\\)/\n\n// Parse reference id\nexport const reference = /(#[a-z_][a-z0-9\\-_]*)/i\n\n// splits a transformation chain\nexport const transforms = /\\)\\s*,?\\s*/\n\n// Whitespace\nexport const whitespace = /\\s/g\n\n// Test hex value\nexport const isHex = /^#[a-f0-9]{3}$|^#[a-f0-9]{6}$/i\n\n// Test rgb value\nexport const isRgb = /^rgb\\(/\n\n// Test for blank string\nexport const isBlank = /^(\\s+)?$/\n\n// Test for numeric string\nexport const isNumber = /^[+-]?(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i\n\n// Test for image url\nexport const isImage = /\\.(jpg|jpeg|png|gif|svg)(\\?[^=]+.*)?/i\n\n// split at whitespace and comma\nexport const delimiter = /[\\s,]+/\n\n// Test for path letter\nexport const isPathLetter = /[MLHVCSQTAZ]/i\n", "import { delimiter } from '../core/regex.js'\nimport { registerMethods } from '../../utils/methods.js'\n\n// Return array of classes on the node\nexport function classes() {\n  const attr = this.attr('class')\n  return attr == null ? [] : attr.trim().split(delimiter)\n}\n\n// Return true if class exists on the node, false otherwise\nexport function hasClass(name) {\n  return this.classes().indexOf(name) !== -1\n}\n\n// Add class to the node\nexport function addClass(name) {\n  if (!this.hasClass(name)) {\n    const array = this.classes()\n    array.push(name)\n    this.attr('class', array.join(' '))\n  }\n\n  return this\n}\n\n// Remove class from the node\nexport function removeClass(name) {\n  if (this.hasClass(name)) {\n    this.attr(\n      'class',\n      this.classes()\n        .filter(function (c) {\n          return c !== name\n        })\n        .join(' ')\n    )\n  }\n\n  return this\n}\n\n// Toggle the presence of a class on the node\nexport function toggleClass(name) {\n  return this.hasClass(name) ? this.removeClass(name) : this.addClass(name)\n}\n\nregisterMethods('Dom', {\n  classes,\n  hasClass,\n  addClass,\n  removeClass,\n  toggleClass\n})\n", "import { isBlank } from '../core/regex.js'\nimport { registerMethods } from '../../utils/methods.js'\n\n// Dynamic style generator\nexport function css(style, val) {\n  const ret = {}\n  if (arguments.length === 0) {\n    // get full style as object\n    this.node.style.cssText\n      .split(/\\s*;\\s*/)\n      .filter(function (el) {\n        return !!el.length\n      })\n      .forEach(function (el) {\n        const t = el.split(/\\s*:\\s*/)\n        ret[t[0]] = t[1]\n      })\n    return ret\n  }\n\n  if (arguments.length < 2) {\n    // get style properties as array\n    if (Array.isArray(style)) {\n      for (const name of style) {\n        const cased = name\n        ret[name] = this.node.style.getPropertyValue(cased)\n      }\n      return ret\n    }\n\n    // get style for property\n    if (typeof style === 'string') {\n      return this.node.style.getPropertyValue(style)\n    }\n\n    // set styles in object\n    if (typeof style === 'object') {\n      for (const name in style) {\n        // set empty string if null/undefined/'' was given\n        this.node.style.setProperty(\n          name,\n          style[name] == null || isBlank.test(style[name]) ? '' : style[name]\n        )\n      }\n    }\n  }\n\n  // set style for property\n  if (arguments.length === 2) {\n    this.node.style.setProperty(\n      style,\n      val == null || isBlank.test(val) ? '' : val\n    )\n  }\n\n  return this\n}\n\n// Show element\nexport function show() {\n  return this.css('display', '')\n}\n\n// Hide element\nexport function hide() {\n  return this.css('display', 'none')\n}\n\n// Is element visible?\nexport function visible() {\n  return this.css('display') !== 'none'\n}\n\nregisterMethods('Dom', {\n  css,\n  show,\n  hide,\n  visible\n})\n", "import { registerMethods } from '../../utils/methods.js'\nimport { filter, map } from '../../utils/utils.js'\n\n// Store data values on svg nodes\nexport function data(a, v, r) {\n  if (a == null) {\n    // get an object of attributes\n    return this.data(\n      map(\n        filter(\n          this.node.attributes,\n          (el) => el.nodeName.indexOf('data-') === 0\n        ),\n        (el) => el.nodeName.slice(5)\n      )\n    )\n  } else if (a instanceof Array) {\n    const data = {}\n    for (const key of a) {\n      data[key] = this.data(key)\n    }\n    return data\n  } else if (typeof a === 'object') {\n    for (v in a) {\n      this.data(v, a[v])\n    }\n  } else if (arguments.length < 2) {\n    try {\n      return JSON.parse(this.attr('data-' + a))\n    } catch (e) {\n      return this.attr('data-' + a)\n    }\n  } else {\n    this.attr(\n      'data-' + a,\n      v === null\n        ? null\n        : r === true || typeof v === 'string' || typeof v === 'number'\n          ? v\n          : JSON.stringify(v)\n    )\n  }\n\n  return this\n}\n\nregisterMethods('Dom', { data })\n", "import { registerMethods } from '../../utils/methods.js'\n\n// Remember arbitrary data\nexport function remember(k, v) {\n  // remember every item in an object individually\n  if (typeof arguments[0] === 'object') {\n    for (const key in k) {\n      this.remember(key, k[key])\n    }\n  } else if (arguments.length === 1) {\n    // retrieve memory\n    return this.memory()[k]\n  } else {\n    // store memory\n    this.memory()[k] = v\n  }\n\n  return this\n}\n\n// Erase a given memory\nexport function forget() {\n  if (arguments.length === 0) {\n    this._memory = {}\n  } else {\n    for (let i = arguments.length - 1; i >= 0; i--) {\n      delete this.memory()[arguments[i]]\n    }\n  }\n  return this\n}\n\n// This triggers creation of a new hidden class which is not performant\n// However, this function is not rarely used so it will not happen frequently\n// Return local memory object\nexport function memory() {\n  return (this._memory = this._memory || {})\n}\n\nregisterMethods('Dom', { remember, forget, memory })\n", "import { hex, isHex, isRgb, rgb, whitespace } from '../modules/core/regex.js'\n\nfunction sixDigitHex(hex) {\n  return hex.length === 4\n    ? [\n        '#',\n        hex.substring(1, 2),\n        hex.substring(1, 2),\n        hex.substring(2, 3),\n        hex.substring(2, 3),\n        hex.substring(3, 4),\n        hex.substring(3, 4)\n      ].join('')\n    : hex\n}\n\nfunction componentHex(component) {\n  const integer = Math.round(component)\n  const bounded = Math.max(0, Math.min(255, integer))\n  const hex = bounded.toString(16)\n  return hex.length === 1 ? '0' + hex : hex\n}\n\nfunction is(object, space) {\n  for (let i = space.length; i--; ) {\n    if (object[space[i]] == null) {\n      return false\n    }\n  }\n  return true\n}\n\nfunction getParameters(a, b) {\n  const params = is(a, 'rgb')\n    ? { _a: a.r, _b: a.g, _c: a.b, _d: 0, space: 'rgb' }\n    : is(a, 'xyz')\n      ? { _a: a.x, _b: a.y, _c: a.z, _d: 0, space: 'xyz' }\n      : is(a, 'hsl')\n        ? { _a: a.h, _b: a.s, _c: a.l, _d: 0, space: 'hsl' }\n        : is(a, 'lab')\n          ? { _a: a.l, _b: a.a, _c: a.b, _d: 0, space: 'lab' }\n          : is(a, 'lch')\n            ? { _a: a.l, _b: a.c, _c: a.h, _d: 0, space: 'lch' }\n            : is(a, 'cmyk')\n              ? { _a: a.c, _b: a.m, _c: a.y, _d: a.k, space: 'cmyk' }\n              : { _a: 0, _b: 0, _c: 0, space: 'rgb' }\n\n  params.space = b || params.space\n  return params\n}\n\nfunction cieSpace(space) {\n  if (space === 'lab' || space === 'xyz' || space === 'lch') {\n    return true\n  } else {\n    return false\n  }\n}\n\nfunction hueToRgb(p, q, t) {\n  if (t < 0) t += 1\n  if (t > 1) t -= 1\n  if (t < 1 / 6) return p + (q - p) * 6 * t\n  if (t < 1 / 2) return q\n  if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6\n  return p\n}\n\nexport default class Color {\n  constructor(...inputs) {\n    this.init(...inputs)\n  }\n\n  // Test if given value is a color\n  static isColor(color) {\n    return (\n      color && (color instanceof Color || this.isRgb(color) || this.test(color))\n    )\n  }\n\n  // Test if given value is an rgb object\n  static isRgb(color) {\n    return (\n      color &&\n      typeof color.r === 'number' &&\n      typeof color.g === 'number' &&\n      typeof color.b === 'number'\n    )\n  }\n\n  /*\n  Generating random colors\n  */\n  static random(mode = 'vibrant', t) {\n    // Get the math modules\n    const { random, round, sin, PI: pi } = Math\n\n    // Run the correct generator\n    if (mode === 'vibrant') {\n      const l = (81 - 57) * random() + 57\n      const c = (83 - 45) * random() + 45\n      const h = 360 * random()\n      const color = new Color(l, c, h, 'lch')\n      return color\n    } else if (mode === 'sine') {\n      t = t == null ? random() : t\n      const r = round(80 * sin((2 * pi * t) / 0.5 + 0.01) + 150)\n      const g = round(50 * sin((2 * pi * t) / 0.5 + 4.6) + 200)\n      const b = round(100 * sin((2 * pi * t) / 0.5 + 2.3) + 150)\n      const color = new Color(r, g, b)\n      return color\n    } else if (mode === 'pastel') {\n      const l = (94 - 86) * random() + 86\n      const c = (26 - 9) * random() + 9\n      const h = 360 * random()\n      const color = new Color(l, c, h, 'lch')\n      return color\n    } else if (mode === 'dark') {\n      const l = 10 + 10 * random()\n      const c = (125 - 75) * random() + 86\n      const h = 360 * random()\n      const color = new Color(l, c, h, 'lch')\n      return color\n    } else if (mode === 'rgb') {\n      const r = 255 * random()\n      const g = 255 * random()\n      const b = 255 * random()\n      const color = new Color(r, g, b)\n      return color\n    } else if (mode === 'lab') {\n      const l = 100 * random()\n      const a = 256 * random() - 128\n      const b = 256 * random() - 128\n      const color = new Color(l, a, b, 'lab')\n      return color\n    } else if (mode === 'grey') {\n      const grey = 255 * random()\n      const color = new Color(grey, grey, grey)\n      return color\n    } else {\n      throw new Error('Unsupported random color mode')\n    }\n  }\n\n  // Test if given value is a color string\n  static test(color) {\n    return typeof color === 'string' && (isHex.test(color) || isRgb.test(color))\n  }\n\n  cmyk() {\n    // Get the rgb values for the current color\n    const { _a, _b, _c } = this.rgb()\n    const [r, g, b] = [_a, _b, _c].map((v) => v / 255)\n\n    // Get the cmyk values in an unbounded format\n    const k = Math.min(1 - r, 1 - g, 1 - b)\n\n    if (k === 1) {\n      // Catch the black case\n      return new Color(0, 0, 0, 1, 'cmyk')\n    }\n\n    const c = (1 - r - k) / (1 - k)\n    const m = (1 - g - k) / (1 - k)\n    const y = (1 - b - k) / (1 - k)\n\n    // Construct the new color\n    const color = new Color(c, m, y, k, 'cmyk')\n    return color\n  }\n\n  hsl() {\n    // Get the rgb values\n    const { _a, _b, _c } = this.rgb()\n    const [r, g, b] = [_a, _b, _c].map((v) => v / 255)\n\n    // Find the maximum and minimum values to get the lightness\n    const max = Math.max(r, g, b)\n    const min = Math.min(r, g, b)\n    const l = (max + min) / 2\n\n    // If the r, g, v values are identical then we are grey\n    const isGrey = max === min\n\n    // Calculate the hue and saturation\n    const delta = max - min\n    const s = isGrey\n      ? 0\n      : l > 0.5\n        ? delta / (2 - max - min)\n        : delta / (max + min)\n    const h = isGrey\n      ? 0\n      : max === r\n        ? ((g - b) / delta + (g < b ? 6 : 0)) / 6\n        : max === g\n          ? ((b - r) / delta + 2) / 6\n          : max === b\n            ? ((r - g) / delta + 4) / 6\n            : 0\n\n    // Construct and return the new color\n    const color = new Color(360 * h, 100 * s, 100 * l, 'hsl')\n    return color\n  }\n\n  init(a = 0, b = 0, c = 0, d = 0, space = 'rgb') {\n    // This catches the case when a falsy value is passed like ''\n    a = !a ? 0 : a\n\n    // Reset all values in case the init function is rerun with new color space\n    if (this.space) {\n      for (const component in this.space) {\n        delete this[this.space[component]]\n      }\n    }\n\n    if (typeof a === 'number') {\n      // Allow for the case that we don't need d...\n      space = typeof d === 'string' ? d : space\n      d = typeof d === 'string' ? 0 : d\n\n      // Assign the values straight to the color\n      Object.assign(this, { _a: a, _b: b, _c: c, _d: d, space })\n      // If the user gave us an array, make the color from it\n    } else if (a instanceof Array) {\n      this.space = b || (typeof a[3] === 'string' ? a[3] : a[4]) || 'rgb'\n      Object.assign(this, { _a: a[0], _b: a[1], _c: a[2], _d: a[3] || 0 })\n    } else if (a instanceof Object) {\n      // Set the object up and assign its values directly\n      const values = getParameters(a, b)\n      Object.assign(this, values)\n    } else if (typeof a === 'string') {\n      if (isRgb.test(a)) {\n        const noWhitespace = a.replace(whitespace, '')\n        const [_a, _b, _c] = rgb\n          .exec(noWhitespace)\n          .slice(1, 4)\n          .map((v) => parseInt(v))\n        Object.assign(this, { _a, _b, _c, _d: 0, space: 'rgb' })\n      } else if (isHex.test(a)) {\n        const hexParse = (v) => parseInt(v, 16)\n        const [, _a, _b, _c] = hex.exec(sixDigitHex(a)).map(hexParse)\n        Object.assign(this, { _a, _b, _c, _d: 0, space: 'rgb' })\n      } else throw Error(\"Unsupported string format, can't construct Color\")\n    }\n\n    // Now add the components as a convenience\n    const { _a, _b, _c, _d } = this\n    const components =\n      this.space === 'rgb'\n        ? { r: _a, g: _b, b: _c }\n        : this.space === 'xyz'\n          ? { x: _a, y: _b, z: _c }\n          : this.space === 'hsl'\n            ? { h: _a, s: _b, l: _c }\n            : this.space === 'lab'\n              ? { l: _a, a: _b, b: _c }\n              : this.space === 'lch'\n                ? { l: _a, c: _b, h: _c }\n                : this.space === 'cmyk'\n                  ? { c: _a, m: _b, y: _c, k: _d }\n                  : {}\n    Object.assign(this, components)\n  }\n\n  lab() {\n    // Get the xyz color\n    const { x, y, z } = this.xyz()\n\n    // Get the lab components\n    const l = 116 * y - 16\n    const a = 500 * (x - y)\n    const b = 200 * (y - z)\n\n    // Construct and return a new color\n    const color = new Color(l, a, b, 'lab')\n    return color\n  }\n\n  lch() {\n    // Get the lab color directly\n    const { l, a, b } = this.lab()\n\n    // Get the chromaticity and the hue using polar coordinates\n    const c = Math.sqrt(a ** 2 + b ** 2)\n    let h = (180 * Math.atan2(b, a)) / Math.PI\n    if (h < 0) {\n      h *= -1\n      h = 360 - h\n    }\n\n    // Make a new color and return it\n    const color = new Color(l, c, h, 'lch')\n    return color\n  }\n  /*\n  Conversion Methods\n  */\n\n  rgb() {\n    if (this.space === 'rgb') {\n      return this\n    } else if (cieSpace(this.space)) {\n      // Convert to the xyz color space\n      let { x, y, z } = this\n      if (this.space === 'lab' || this.space === 'lch') {\n        // Get the values in the lab space\n        let { l, a, b } = this\n        if (this.space === 'lch') {\n          const { c, h } = this\n          const dToR = Math.PI / 180\n          a = c * Math.cos(dToR * h)\n          b = c * Math.sin(dToR * h)\n        }\n\n        // Undo the nonlinear function\n        const yL = (l + 16) / 116\n        const xL = a / 500 + yL\n        const zL = yL - b / 200\n\n        // Get the xyz values\n        const ct = 16 / 116\n        const mx = 0.008856\n        const nm = 7.787\n        x = 0.95047 * (xL ** 3 > mx ? xL ** 3 : (xL - ct) / nm)\n        y = 1.0 * (yL ** 3 > mx ? yL ** 3 : (yL - ct) / nm)\n        z = 1.08883 * (zL ** 3 > mx ? zL ** 3 : (zL - ct) / nm)\n      }\n\n      // Convert xyz to unbounded rgb values\n      const rU = x * 3.2406 + y * -1.5372 + z * -0.4986\n      const gU = x * -0.9689 + y * 1.8758 + z * 0.0415\n      const bU = x * 0.0557 + y * -0.204 + z * 1.057\n\n      // Convert the values to true rgb values\n      const pow = Math.pow\n      const bd = 0.0031308\n      const r = rU > bd ? 1.055 * pow(rU, 1 / 2.4) - 0.055 : 12.92 * rU\n      const g = gU > bd ? 1.055 * pow(gU, 1 / 2.4) - 0.055 : 12.92 * gU\n      const b = bU > bd ? 1.055 * pow(bU, 1 / 2.4) - 0.055 : 12.92 * bU\n\n      // Make and return the color\n      const color = new Color(255 * r, 255 * g, 255 * b)\n      return color\n    } else if (this.space === 'hsl') {\n      // https://bgrins.github.io/TinyColor/docs/tinycolor.html\n      // Get the current hsl values\n      let { h, s, l } = this\n      h /= 360\n      s /= 100\n      l /= 100\n\n      // If we are grey, then just make the color directly\n      if (s === 0) {\n        l *= 255\n        const color = new Color(l, l, l)\n        return color\n      }\n\n      // TODO I have no idea what this does :D If you figure it out, tell me!\n      const q = l < 0.5 ? l * (1 + s) : l + s - l * s\n      const p = 2 * l - q\n\n      // Get the rgb values\n      const r = 255 * hueToRgb(p, q, h + 1 / 3)\n      const g = 255 * hueToRgb(p, q, h)\n      const b = 255 * hueToRgb(p, q, h - 1 / 3)\n\n      // Make a new color\n      const color = new Color(r, g, b)\n      return color\n    } else if (this.space === 'cmyk') {\n      // https://gist.github.com/felipesabino/5066336\n      // Get the normalised cmyk values\n      const { c, m, y, k } = this\n\n      // Get the rgb values\n      const r = 255 * (1 - Math.min(1, c * (1 - k) + k))\n      const g = 255 * (1 - Math.min(1, m * (1 - k) + k))\n      const b = 255 * (1 - Math.min(1, y * (1 - k) + k))\n\n      // Form the color and return it\n      const color = new Color(r, g, b)\n      return color\n    } else {\n      return this\n    }\n  }\n\n  toArray() {\n    const { _a, _b, _c, _d, space } = this\n    return [_a, _b, _c, _d, space]\n  }\n\n  toHex() {\n    const [r, g, b] = this._clamped().map(componentHex)\n    return `#${r}${g}${b}`\n  }\n\n  toRgb() {\n    const [rV, gV, bV] = this._clamped()\n    const string = `rgb(${rV},${gV},${bV})`\n    return string\n  }\n\n  toString() {\n    return this.toHex()\n  }\n\n  xyz() {\n    // Normalise the red, green and blue values\n    const { _a: r255, _b: g255, _c: b255 } = this.rgb()\n    const [r, g, b] = [r255, g255, b255].map((v) => v / 255)\n\n    // Convert to the lab rgb space\n    const rL = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92\n    const gL = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92\n    const bL = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92\n\n    // Convert to the xyz color space without bounding the values\n    const xU = (rL * 0.4124 + gL * 0.3576 + bL * 0.1805) / 0.95047\n    const yU = (rL * 0.2126 + gL * 0.7152 + bL * 0.0722) / 1.0\n    const zU = (rL * 0.0193 + gL * 0.1192 + bL * 0.9505) / 1.08883\n\n    // Get the proper xyz values by applying the bounding\n    const x = xU > 0.008856 ? Math.pow(xU, 1 / 3) : 7.787 * xU + 16 / 116\n    const y = yU > 0.008856 ? Math.pow(yU, 1 / 3) : 7.787 * yU + 16 / 116\n    const z = zU > 0.008856 ? Math.pow(zU, 1 / 3) : 7.787 * zU + 16 / 116\n\n    // Make and return the color\n    const color = new Color(x, y, z, 'xyz')\n    return color\n  }\n\n  /*\n  Input and Output methods\n  */\n\n  _clamped() {\n    const { _a, _b, _c } = this.rgb()\n    const { max, min, round } = Math\n    const format = (v) => max(0, min(round(v), 255))\n    return [_a, _b, _c].map(format)\n  }\n\n  /*\n  Constructing colors\n  */\n}\n", "import Matrix from './Matrix.js'\n\nexport default class Point {\n  // Initialize\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  // Clone point\n  clone() {\n    return new Point(this)\n  }\n\n  init(x, y) {\n    const base = { x: 0, y: 0 }\n\n    // ensure source as object\n    const source = Array.isArray(x)\n      ? { x: x[0], y: x[1] }\n      : typeof x === 'object'\n        ? { x: x.x, y: x.y }\n        : { x: x, y: y }\n\n    // merge source\n    this.x = source.x == null ? base.x : source.x\n    this.y = source.y == null ? base.y : source.y\n\n    return this\n  }\n\n  toArray() {\n    return [this.x, this.y]\n  }\n\n  transform(m) {\n    return this.clone().transformO(m)\n  }\n\n  // Transform point with matrix\n  transformO(m) {\n    if (!Matrix.isMatrixLike(m)) {\n      m = new Matrix(m)\n    }\n\n    const { x, y } = this\n\n    // Perform the matrix multiplication\n    this.x = m.a * x + m.c * y + m.e\n    this.y = m.b * x + m.d * y + m.f\n\n    return this\n  }\n}\n\nexport function point(x, y) {\n  return new Point(x, y).transformO(this.screenCTM().inverseO())\n}\n", "import { delimiter } from '../modules/core/regex.js'\nimport { radians } from '../utils/utils.js'\nimport { register } from '../utils/adopter.js'\nimport Element from '../elements/Element.js'\nimport Point from './Point.js'\n\nfunction closeEnough(a, b, threshold) {\n  return Math.abs(b - a) < (threshold || 1e-6)\n}\n\nexport default class Matrix {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  static formatTransforms(o) {\n    // Get all of the parameters required to form the matrix\n    const flipBoth = o.flip === 'both' || o.flip === true\n    const flipX = o.flip && (flipBoth || o.flip === 'x') ? -1 : 1\n    const flipY = o.flip && (flipBoth || o.flip === 'y') ? -1 : 1\n    const skewX =\n      o.skew && o.skew.length\n        ? o.skew[0]\n        : isFinite(o.skew)\n          ? o.skew\n          : isFinite(o.skewX)\n            ? o.skewX\n            : 0\n    const skewY =\n      o.skew && o.skew.length\n        ? o.skew[1]\n        : isFinite(o.skew)\n          ? o.skew\n          : isFinite(o.skewY)\n            ? o.skewY\n            : 0\n    const scaleX =\n      o.scale && o.scale.length\n        ? o.scale[0] * flipX\n        : isFinite(o.scale)\n          ? o.scale * flipX\n          : isFinite(o.scaleX)\n            ? o.scaleX * flipX\n            : flipX\n    const scaleY =\n      o.scale && o.scale.length\n        ? o.scale[1] * flipY\n        : isFinite(o.scale)\n          ? o.scale * flipY\n          : isFinite(o.scaleY)\n            ? o.scaleY * flipY\n            : flipY\n    const shear = o.shear || 0\n    const theta = o.rotate || o.theta || 0\n    const origin = new Point(\n      o.origin || o.around || o.ox || o.originX,\n      o.oy || o.originY\n    )\n    const ox = origin.x\n    const oy = origin.y\n    // We need Point to be invalid if nothing was passed because we cannot default to 0 here. That is why NaN\n    const position = new Point(\n      o.position || o.px || o.positionX || NaN,\n      o.py || o.positionY || NaN\n    )\n    const px = position.x\n    const py = position.y\n    const translate = new Point(\n      o.translate || o.tx || o.translateX,\n      o.ty || o.translateY\n    )\n    const tx = translate.x\n    const ty = translate.y\n    const relative = new Point(\n      o.relative || o.rx || o.relativeX,\n      o.ry || o.relativeY\n    )\n    const rx = relative.x\n    const ry = relative.y\n\n    // Populate all of the values\n    return {\n      scaleX,\n      scaleY,\n      skewX,\n      skewY,\n      shear,\n      theta,\n      rx,\n      ry,\n      tx,\n      ty,\n      ox,\n      oy,\n      px,\n      py\n    }\n  }\n\n  static fromArray(a) {\n    return { a: a[0], b: a[1], c: a[2], d: a[3], e: a[4], f: a[5] }\n  }\n\n  static isMatrixLike(o) {\n    return (\n      o.a != null ||\n      o.b != null ||\n      o.c != null ||\n      o.d != null ||\n      o.e != null ||\n      o.f != null\n    )\n  }\n\n  // left matrix, right matrix, target matrix which is overwritten\n  static matrixMultiply(l, r, o) {\n    // Work out the product directly\n    const a = l.a * r.a + l.c * r.b\n    const b = l.b * r.a + l.d * r.b\n    const c = l.a * r.c + l.c * r.d\n    const d = l.b * r.c + l.d * r.d\n    const e = l.e + l.a * r.e + l.c * r.f\n    const f = l.f + l.b * r.e + l.d * r.f\n\n    // make sure to use local variables because l/r and o could be the same\n    o.a = a\n    o.b = b\n    o.c = c\n    o.d = d\n    o.e = e\n    o.f = f\n\n    return o\n  }\n\n  around(cx, cy, matrix) {\n    return this.clone().aroundO(cx, cy, matrix)\n  }\n\n  // Transform around a center point\n  aroundO(cx, cy, matrix) {\n    const dx = cx || 0\n    const dy = cy || 0\n    return this.translateO(-dx, -dy).lmultiplyO(matrix).translateO(dx, dy)\n  }\n\n  // Clones this matrix\n  clone() {\n    return new Matrix(this)\n  }\n\n  // Decomposes this matrix into its affine parameters\n  decompose(cx = 0, cy = 0) {\n    // Get the parameters from the matrix\n    const a = this.a\n    const b = this.b\n    const c = this.c\n    const d = this.d\n    const e = this.e\n    const f = this.f\n\n    // Figure out if the winding direction is clockwise or counterclockwise\n    const determinant = a * d - b * c\n    const ccw = determinant > 0 ? 1 : -1\n\n    // Since we only shear in x, we can use the x basis to get the x scale\n    // and the rotation of the resulting matrix\n    const sx = ccw * Math.sqrt(a * a + b * b)\n    const thetaRad = Math.atan2(ccw * b, ccw * a)\n    const theta = (180 / Math.PI) * thetaRad\n    const ct = Math.cos(thetaRad)\n    const st = Math.sin(thetaRad)\n\n    // We can then solve the y basis vector simultaneously to get the other\n    // two affine parameters directly from these parameters\n    const lam = (a * c + b * d) / determinant\n    const sy = (c * sx) / (lam * a - b) || (d * sx) / (lam * b + a)\n\n    // Use the translations\n    const tx = e - cx + cx * ct * sx + cy * (lam * ct * sx - st * sy)\n    const ty = f - cy + cx * st * sx + cy * (lam * st * sx + ct * sy)\n\n    // Construct the decomposition and return it\n    return {\n      // Return the affine parameters\n      scaleX: sx,\n      scaleY: sy,\n      shear: lam,\n      rotate: theta,\n      translateX: tx,\n      translateY: ty,\n      originX: cx,\n      originY: cy,\n\n      // Return the matrix parameters\n      a: this.a,\n      b: this.b,\n      c: this.c,\n      d: this.d,\n      e: this.e,\n      f: this.f\n    }\n  }\n\n  // Check if two matrices are equal\n  equals(other) {\n    if (other === this) return true\n    const comp = new Matrix(other)\n    return (\n      closeEnough(this.a, comp.a) &&\n      closeEnough(this.b, comp.b) &&\n      closeEnough(this.c, comp.c) &&\n      closeEnough(this.d, comp.d) &&\n      closeEnough(this.e, comp.e) &&\n      closeEnough(this.f, comp.f)\n    )\n  }\n\n  // Flip matrix on x or y, at a given offset\n  flip(axis, around) {\n    return this.clone().flipO(axis, around)\n  }\n\n  flipO(axis, around) {\n    return axis === 'x'\n      ? this.scaleO(-1, 1, around, 0)\n      : axis === 'y'\n        ? this.scaleO(1, -1, 0, around)\n        : this.scaleO(-1, -1, axis, around || axis) // Define an x, y flip point\n  }\n\n  // Initialize\n  init(source) {\n    const base = Matrix.fromArray([1, 0, 0, 1, 0, 0])\n\n    // ensure source as object\n    source =\n      source instanceof Element\n        ? source.matrixify()\n        : typeof source === 'string'\n          ? Matrix.fromArray(source.split(delimiter).map(parseFloat))\n          : Array.isArray(source)\n            ? Matrix.fromArray(source)\n            : typeof source === 'object' && Matrix.isMatrixLike(source)\n              ? source\n              : typeof source === 'object'\n                ? new Matrix().transform(source)\n                : arguments.length === 6\n                  ? Matrix.fromArray([].slice.call(arguments))\n                  : base\n\n    // Merge the source matrix with the base matrix\n    this.a = source.a != null ? source.a : base.a\n    this.b = source.b != null ? source.b : base.b\n    this.c = source.c != null ? source.c : base.c\n    this.d = source.d != null ? source.d : base.d\n    this.e = source.e != null ? source.e : base.e\n    this.f = source.f != null ? source.f : base.f\n\n    return this\n  }\n\n  inverse() {\n    return this.clone().inverseO()\n  }\n\n  // Inverses matrix\n  inverseO() {\n    // Get the current parameters out of the matrix\n    const a = this.a\n    const b = this.b\n    const c = this.c\n    const d = this.d\n    const e = this.e\n    const f = this.f\n\n    // Invert the 2x2 matrix in the top left\n    const det = a * d - b * c\n    if (!det) throw new Error('Cannot invert ' + this)\n\n    // Calculate the top 2x2 matrix\n    const na = d / det\n    const nb = -b / det\n    const nc = -c / det\n    const nd = a / det\n\n    // Apply the inverted matrix to the top right\n    const ne = -(na * e + nc * f)\n    const nf = -(nb * e + nd * f)\n\n    // Construct the inverted matrix\n    this.a = na\n    this.b = nb\n    this.c = nc\n    this.d = nd\n    this.e = ne\n    this.f = nf\n\n    return this\n  }\n\n  lmultiply(matrix) {\n    return this.clone().lmultiplyO(matrix)\n  }\n\n  lmultiplyO(matrix) {\n    const r = this\n    const l = matrix instanceof Matrix ? matrix : new Matrix(matrix)\n\n    return Matrix.matrixMultiply(l, r, this)\n  }\n\n  // Left multiplies by the given matrix\n  multiply(matrix) {\n    return this.clone().multiplyO(matrix)\n  }\n\n  multiplyO(matrix) {\n    // Get the matrices\n    const l = this\n    const r = matrix instanceof Matrix ? matrix : new Matrix(matrix)\n\n    return Matrix.matrixMultiply(l, r, this)\n  }\n\n  // Rotate matrix\n  rotate(r, cx, cy) {\n    return this.clone().rotateO(r, cx, cy)\n  }\n\n  rotateO(r, cx = 0, cy = 0) {\n    // Convert degrees to radians\n    r = radians(r)\n\n    const cos = Math.cos(r)\n    const sin = Math.sin(r)\n\n    const { a, b, c, d, e, f } = this\n\n    this.a = a * cos - b * sin\n    this.b = b * cos + a * sin\n    this.c = c * cos - d * sin\n    this.d = d * cos + c * sin\n    this.e = e * cos - f * sin + cy * sin - cx * cos + cx\n    this.f = f * cos + e * sin - cx * sin - cy * cos + cy\n\n    return this\n  }\n\n  // Scale matrix\n  scale() {\n    return this.clone().scaleO(...arguments)\n  }\n\n  scaleO(x, y = x, cx = 0, cy = 0) {\n    // Support uniform scaling\n    if (arguments.length === 3) {\n      cy = cx\n      cx = y\n      y = x\n    }\n\n    const { a, b, c, d, e, f } = this\n\n    this.a = a * x\n    this.b = b * y\n    this.c = c * x\n    this.d = d * y\n    this.e = e * x - cx * x + cx\n    this.f = f * y - cy * y + cy\n\n    return this\n  }\n\n  // Shear matrix\n  shear(a, cx, cy) {\n    return this.clone().shearO(a, cx, cy)\n  }\n\n  // eslint-disable-next-line no-unused-vars\n  shearO(lx, cx = 0, cy = 0) {\n    const { a, b, c, d, e, f } = this\n\n    this.a = a + b * lx\n    this.c = c + d * lx\n    this.e = e + f * lx - cy * lx\n\n    return this\n  }\n\n  // Skew Matrix\n  skew() {\n    return this.clone().skewO(...arguments)\n  }\n\n  skewO(x, y = x, cx = 0, cy = 0) {\n    // support uniformal skew\n    if (arguments.length === 3) {\n      cy = cx\n      cx = y\n      y = x\n    }\n\n    // Convert degrees to radians\n    x = radians(x)\n    y = radians(y)\n\n    const lx = Math.tan(x)\n    const ly = Math.tan(y)\n\n    const { a, b, c, d, e, f } = this\n\n    this.a = a + b * lx\n    this.b = b + a * ly\n    this.c = c + d * lx\n    this.d = d + c * ly\n    this.e = e + f * lx - cy * lx\n    this.f = f + e * ly - cx * ly\n\n    return this\n  }\n\n  // SkewX\n  skewX(x, cx, cy) {\n    return this.skew(x, 0, cx, cy)\n  }\n\n  // SkewY\n  skewY(y, cx, cy) {\n    return this.skew(0, y, cx, cy)\n  }\n\n  toArray() {\n    return [this.a, this.b, this.c, this.d, this.e, this.f]\n  }\n\n  // Convert matrix to string\n  toString() {\n    return (\n      'matrix(' +\n      this.a +\n      ',' +\n      this.b +\n      ',' +\n      this.c +\n      ',' +\n      this.d +\n      ',' +\n      this.e +\n      ',' +\n      this.f +\n      ')'\n    )\n  }\n\n  // Transform a matrix into another matrix by manipulating the space\n  transform(o) {\n    // Check if o is a matrix and then left multiply it directly\n    if (Matrix.isMatrixLike(o)) {\n      const matrix = new Matrix(o)\n      return matrix.multiplyO(this)\n    }\n\n    // Get the proposed transformations and the current transformations\n    const t = Matrix.formatTransforms(o)\n    const current = this\n    const { x: ox, y: oy } = new Point(t.ox, t.oy).transform(current)\n\n    // Construct the resulting matrix\n    const transformer = new Matrix()\n      .translateO(t.rx, t.ry)\n      .lmultiplyO(current)\n      .translateO(-ox, -oy)\n      .scaleO(t.scaleX, t.scaleY)\n      .skewO(t.skewX, t.skewY)\n      .shearO(t.shear)\n      .rotateO(t.theta)\n      .translateO(ox, oy)\n\n    // If we want the origin at a particular place, we force it there\n    if (isFinite(t.px) || isFinite(t.py)) {\n      const origin = new Point(ox, oy).transform(transformer)\n      // TODO: Replace t.px with isFinite(t.px)\n      // Doesn't work because t.px is also 0 if it wasn't passed\n      const dx = isFinite(t.px) ? t.px - origin.x : 0\n      const dy = isFinite(t.py) ? t.py - origin.y : 0\n      transformer.translateO(dx, dy)\n    }\n\n    // Translate now after positioning\n    transformer.translateO(t.tx, t.ty)\n    return transformer\n  }\n\n  // Translate matrix\n  translate(x, y) {\n    return this.clone().translateO(x, y)\n  }\n\n  translateO(x, y) {\n    this.e += x || 0\n    this.f += y || 0\n    return this\n  }\n\n  valueOf() {\n    return {\n      a: this.a,\n      b: this.b,\n      c: this.c,\n      d: this.d,\n      e: this.e,\n      f: this.f\n    }\n  }\n}\n\nexport function ctm() {\n  return new Matrix(this.node.getCTM())\n}\n\nexport function screenCTM() {\n  try {\n    /* https://bugzilla.mozilla.org/show_bug.cgi?id=1344537\n       This is needed because FF does not return the transformation matrix\n       for the inner coordinate system when getScreenCTM() is called on nested svgs.\n       However all other Browsers do that */\n    if (typeof this.isRoot === 'function' && !this.isRoot()) {\n      const rect = this.rect(1, 1)\n      const m = rect.node.getScreenCTM()\n      rect.remove()\n      return new Matrix(m)\n    }\n    return new Matrix(this.node.getScreenCTM())\n  } catch (e) {\n    console.warn(\n      `Cannot get CTM from SVG node ${this.node.nodeName}. Is the element rendered?`\n    )\n    return new Matrix()\n  }\n}\n\nregister(Matrix, 'Matrix')\n", "import { globals } from '../../utils/window.js'\nimport { makeInstance } from '../../utils/adopter.js'\n\nexport default function parser() {\n  // Reuse cached element if possible\n  if (!parser.nodes) {\n    const svg = makeInstance().size(2, 0)\n    svg.node.style.cssText = [\n      'opacity: 0',\n      'position: absolute',\n      'left: -100%',\n      'top: -100%',\n      'overflow: hidden'\n    ].join(';')\n\n    svg.attr('focusable', 'false')\n    svg.attr('aria-hidden', 'true')\n\n    const path = svg.path().node\n\n    parser.nodes = { svg, path }\n  }\n\n  if (!parser.nodes.svg.node.parentNode) {\n    const b = globals.document.body || globals.document.documentElement\n    parser.nodes.svg.addTo(b)\n  }\n\n  return parser.nodes\n}\n", "import { delimiter } from '../modules/core/regex.js'\nimport { globals } from '../utils/window.js'\nimport { register } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Matrix from './Matrix.js'\nimport Point from './Point.js'\nimport parser from '../modules/core/parser.js'\n\nexport function isNulledBox(box) {\n  return !box.width && !box.height && !box.x && !box.y\n}\n\nexport function domContains(node) {\n  return (\n    node === globals.document ||\n    (\n      globals.document.documentElement.contains ||\n      function (node) {\n        // This is IE - it does not support contains() for top-level SVGs\n        while (node.parentNode) {\n          node = node.parentNode\n        }\n        return node === globals.document\n      }\n    ).call(globals.document.documentElement, node)\n  )\n}\n\nexport default class Box {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  addOffset() {\n    // offset by window scroll position, because getBoundingClientRect changes when window is scrolled\n    this.x += globals.window.pageXOffset\n    this.y += globals.window.pageYOffset\n    return new Box(this)\n  }\n\n  init(source) {\n    const base = [0, 0, 0, 0]\n    source =\n      typeof source === 'string'\n        ? source.split(delimiter).map(parseFloat)\n        : Array.isArray(source)\n          ? source\n          : typeof source === 'object'\n            ? [\n                source.left != null ? source.left : source.x,\n                source.top != null ? source.top : source.y,\n                source.width,\n                source.height\n              ]\n            : arguments.length === 4\n              ? [].slice.call(arguments)\n              : base\n\n    this.x = source[0] || 0\n    this.y = source[1] || 0\n    this.width = this.w = source[2] || 0\n    this.height = this.h = source[3] || 0\n\n    // Add more bounding box properties\n    this.x2 = this.x + this.w\n    this.y2 = this.y + this.h\n    this.cx = this.x + this.w / 2\n    this.cy = this.y + this.h / 2\n\n    return this\n  }\n\n  isNulled() {\n    return isNulledBox(this)\n  }\n\n  // Merge rect box with another, return a new instance\n  merge(box) {\n    const x = Math.min(this.x, box.x)\n    const y = Math.min(this.y, box.y)\n    const width = Math.max(this.x + this.width, box.x + box.width) - x\n    const height = Math.max(this.y + this.height, box.y + box.height) - y\n\n    return new Box(x, y, width, height)\n  }\n\n  toArray() {\n    return [this.x, this.y, this.width, this.height]\n  }\n\n  toString() {\n    return this.x + ' ' + this.y + ' ' + this.width + ' ' + this.height\n  }\n\n  transform(m) {\n    if (!(m instanceof Matrix)) {\n      m = new Matrix(m)\n    }\n\n    let xMin = Infinity\n    let xMax = -Infinity\n    let yMin = Infinity\n    let yMax = -Infinity\n\n    const pts = [\n      new Point(this.x, this.y),\n      new Point(this.x2, this.y),\n      new Point(this.x, this.y2),\n      new Point(this.x2, this.y2)\n    ]\n\n    pts.forEach(function (p) {\n      p = p.transform(m)\n      xMin = Math.min(xMin, p.x)\n      xMax = Math.max(xMax, p.x)\n      yMin = Math.min(yMin, p.y)\n      yMax = Math.max(yMax, p.y)\n    })\n\n    return new Box(xMin, yMin, xMax - xMin, yMax - yMin)\n  }\n}\n\nfunction getBox(el, getBBoxFn, retry) {\n  let box\n\n  try {\n    // Try to get the box with the provided function\n    box = getBBoxFn(el.node)\n\n    // If the box is worthless and not even in the dom, retry\n    // by throwing an error here...\n    if (isNulledBox(box) && !domContains(el.node)) {\n      throw new Error('Element not in the dom')\n    }\n  } catch (e) {\n    // ... and calling the retry handler here\n    box = retry(el)\n  }\n\n  return box\n}\n\nexport function bbox() {\n  // Function to get bbox is getBBox()\n  const getBBox = (node) => node.getBBox()\n\n  // Take all measures so that a stupid browser renders the element\n  // so we can get the bbox from it when we try again\n  const retry = (el) => {\n    try {\n      const clone = el.clone().addTo(parser().svg).show()\n      const box = clone.node.getBBox()\n      clone.remove()\n      return box\n    } catch (e) {\n      // We give up...\n      throw new Error(\n        `Getting bbox of element \"${\n          el.node.nodeName\n        }\" is not possible: ${e.toString()}`\n      )\n    }\n  }\n\n  const box = getBox(this, getBBox, retry)\n  const bbox = new Box(box)\n\n  return bbox\n}\n\nexport function rbox(el) {\n  const getRBox = (node) => node.getBoundingClientRect()\n  const retry = (el) => {\n    // There is no point in trying tricks here because if we insert the element into the dom ourselves\n    // it obviously will be at the wrong position\n    throw new Error(\n      `Getting rbox of element \"${el.node.nodeName}\" is not possible`\n    )\n  }\n\n  const box = getBox(this, getRBox, retry)\n  const rbox = new Box(box)\n\n  // If an element was passed, we want the bbox in the coordinate system of that element\n  if (el) {\n    return rbox.transform(el.screenCTM().inverseO())\n  }\n\n  // Else we want it in absolute screen coordinates\n  // Therefore we need to add the scrollOffset\n  return rbox.addOffset()\n}\n\n// Checks whether the given point is inside the bounding box\nexport function inside(x, y) {\n  const box = this.bbox()\n\n  return (\n    x > box.x && y > box.y && x < box.x + box.width && y < box.y + box.height\n  )\n}\n\nregisterMethods({\n  viewbox: {\n    viewbox(x, y, width, height) {\n      // act as getter\n      if (x == null) return new Box(this.attr('viewBox'))\n\n      // act as setter\n      return this.attr('viewBox', new Box(x, y, width, height))\n    },\n\n    zoom(level, point) {\n      // Its best to rely on the attributes here and here is why:\n      // clientXYZ: Doesn't work on non-root svgs because they dont have a CSSBox (silly!)\n      // getBoundingClientRect: Doesn't work because Chrome just ignores width and height of nested svgs completely\n      //                        that means, their clientRect is always as big as the content.\n      //                        Furthermore this size is incorrect if the element is further transformed by its parents\n      // computedStyle: Only returns meaningful values if css was used with px. We dont go this route here!\n      // getBBox: returns the bounding box of its content - that doesn't help!\n      let { width, height } = this.attr(['width', 'height'])\n\n      // Width and height is a string when a number with a unit is present which we can't use\n      // So we try clientXYZ\n      if (\n        (!width && !height) ||\n        typeof width === 'string' ||\n        typeof height === 'string'\n      ) {\n        width = this.node.clientWidth\n        height = this.node.clientHeight\n      }\n\n      // Giving up...\n      if (!width || !height) {\n        throw new Error(\n          'Impossible to get absolute width and height. Please provide an absolute width and height attribute on the zooming element'\n        )\n      }\n\n      const v = this.viewbox()\n\n      const zoomX = width / v.width\n      const zoomY = height / v.height\n      const zoom = Math.min(zoomX, zoomY)\n\n      if (level == null) {\n        return zoom\n      }\n\n      let zoomAmount = zoom / level\n\n      // Set the zoomAmount to the highest value which is safe to process and recover from\n      // The * 100 is a bit of wiggle room for the matrix transformation\n      if (zoomAmount === Infinity) zoomAmount = Number.MAX_SAFE_INTEGER / 100\n\n      point =\n        point || new Point(width / 2 / zoomX + v.x, height / 2 / zoomY + v.y)\n\n      const box = new Box(v).transform(\n        new Matrix({ scale: zoomAmount, origin: point })\n      )\n\n      return this.viewbox(box)\n    }\n  }\n})\n\nregister(Box, 'Box')\n", "import { extend } from '../utils/adopter.js'\n// import { subClassArray } from './ArrayPolyfill.js'\n\nclass List extends Array {\n  constructor(arr = [], ...args) {\n    super(arr, ...args)\n    if (typeof arr === 'number') return this\n    this.length = 0\n    this.push(...arr)\n  }\n}\n\n/* = subClassArray('List', Array, function (arr = []) {\n  // This catches the case, that native map tries to create an array with new Array(1)\n  if (typeof arr === 'number') return this\n  this.length = 0\n  this.push(...arr)\n}) */\n\nexport default List\n\nextend([List], {\n  each(fnOrMethodName, ...args) {\n    if (typeof fnOrMethodName === 'function') {\n      return this.map((el, i, arr) => {\n        return fnOrMethodName.call(el, el, i, arr)\n      })\n    } else {\n      return this.map((el) => {\n        return el[fnOrMethodName](...args)\n      })\n    }\n  },\n\n  toArray() {\n    return Array.prototype.concat.apply([], this)\n  }\n})\n\nconst reserved = ['toArray', 'constructor', 'each']\n\nList.extend = function (methods) {\n  methods = methods.reduce((obj, name) => {\n    // Don't overwrite own methods\n    if (reserved.includes(name)) return obj\n\n    // Don't add private methods\n    if (name[0] === '_') return obj\n\n    // Allow access to original Array methods through a prefix\n    if (name in Array.prototype) {\n      obj['$' + name] = Array.prototype[name]\n    }\n\n    // Relay every call to each()\n    obj[name] = function (...attrs) {\n      return this.each(name, ...attrs)\n    }\n    return obj\n  }, {})\n\n  extend([List], methods)\n}\n", "import { adopt } from '../../utils/adopter.js'\nimport { globals } from '../../utils/window.js'\nimport { map } from '../../utils/utils.js'\nimport List from '../../types/List.js'\n\nexport default function baseFind(query, parent) {\n  return new List(\n    map((parent || globals.document).querySelectorAll(query), function (node) {\n      return adopt(node)\n    })\n  )\n}\n\n// Scoped find method\nexport function find(query) {\n  return baseFind(query, this.node)\n}\n\nexport function findOne(query) {\n  return adopt(this.node.querySelector(query))\n}\n", "import { delimiter } from './regex.js'\nimport { makeInstance } from '../../utils/adopter.js'\nimport { globals } from '../../utils/window.js'\n\nlet listenerId = 0\nexport const windowEvents = {}\n\nexport function getEvents(instance) {\n  let n = instance.getEventHolder()\n\n  // We dont want to save events in global space\n  if (n === globals.window) n = windowEvents\n  if (!n.events) n.events = {}\n  return n.events\n}\n\nexport function getEventTarget(instance) {\n  return instance.getEventTarget()\n}\n\nexport function clearEvents(instance) {\n  let n = instance.getEventHolder()\n  if (n === globals.window) n = windowEvents\n  if (n.events) n.events = {}\n}\n\n// Add event binder in the SVG namespace\nexport function on(node, events, listener, binding, options) {\n  const l = listener.bind(binding || node)\n  const instance = makeInstance(node)\n  const bag = getEvents(instance)\n  const n = getEventTarget(instance)\n\n  // events can be an array of events or a string of events\n  events = Array.isArray(events) ? events : events.split(delimiter)\n\n  // add id to listener\n  if (!listener._svgjsListenerId) {\n    listener._svgjsListenerId = ++listenerId\n  }\n\n  events.forEach(function (event) {\n    const ev = event.split('.')[0]\n    const ns = event.split('.')[1] || '*'\n\n    // ensure valid object\n    bag[ev] = bag[ev] || {}\n    bag[ev][ns] = bag[ev][ns] || {}\n\n    // reference listener\n    bag[ev][ns][listener._svgjsListenerId] = l\n\n    // add listener\n    n.addEventListener(ev, l, options || false)\n  })\n}\n\n// Add event unbinder in the SVG namespace\nexport function off(node, events, listener, options) {\n  const instance = makeInstance(node)\n  const bag = getEvents(instance)\n  const n = getEventTarget(instance)\n\n  // listener can be a function or a number\n  if (typeof listener === 'function') {\n    listener = listener._svgjsListenerId\n    if (!listener) return\n  }\n\n  // events can be an array of events or a string or undefined\n  events = Array.isArray(events) ? events : (events || '').split(delimiter)\n\n  events.forEach(function (event) {\n    const ev = event && event.split('.')[0]\n    const ns = event && event.split('.')[1]\n    let namespace, l\n\n    if (listener) {\n      // remove listener reference\n      if (bag[ev] && bag[ev][ns || '*']) {\n        // removeListener\n        n.removeEventListener(\n          ev,\n          bag[ev][ns || '*'][listener],\n          options || false\n        )\n\n        delete bag[ev][ns || '*'][listener]\n      }\n    } else if (ev && ns) {\n      // remove all listeners for a namespaced event\n      if (bag[ev] && bag[ev][ns]) {\n        for (l in bag[ev][ns]) {\n          off(n, [ev, ns].join('.'), l)\n        }\n\n        delete bag[ev][ns]\n      }\n    } else if (ns) {\n      // remove all listeners for a specific namespace\n      for (event in bag) {\n        for (namespace in bag[event]) {\n          if (ns === namespace) {\n            off(n, [event, ns].join('.'))\n          }\n        }\n      }\n    } else if (ev) {\n      // remove all listeners for the event\n      if (bag[ev]) {\n        for (namespace in bag[ev]) {\n          off(n, [ev, namespace].join('.'))\n        }\n\n        delete bag[ev]\n      }\n    } else {\n      // remove all listeners on a given node\n      for (event in bag) {\n        off(n, event)\n      }\n\n      clearEvents(instance)\n    }\n  })\n}\n\nexport function dispatch(node, event, data, options) {\n  const n = getEventTarget(node)\n\n  // Dispatch event\n  if (event instanceof globals.window.Event) {\n    n.dispatchEvent(event)\n  } else {\n    event = new globals.window.CustomEvent(event, {\n      detail: data,\n      cancelable: true,\n      ...options\n    })\n    n.dispatchEvent(event)\n  }\n  return event\n}\n", "import { dispatch, off, on } from '../modules/core/event.js'\nimport { register } from '../utils/adopter.js'\nimport Base from './Base.js'\n\nexport default class EventTarget extends Base {\n  addEventListener() {}\n\n  dispatch(event, data, options) {\n    return dispatch(this, event, data, options)\n  }\n\n  dispatchEvent(event) {\n    const bag = this.getEventHolder().events\n    if (!bag) return true\n\n    const events = bag[event.type]\n\n    for (const i in events) {\n      for (const j in events[i]) {\n        events[i][j](event)\n      }\n    }\n\n    return !event.defaultPrevented\n  }\n\n  // Fire given event\n  fire(event, data, options) {\n    this.dispatch(event, data, options)\n    return this\n  }\n\n  getEventHolder() {\n    return this\n  }\n\n  getEventTarget() {\n    return this\n  }\n\n  // Unbind event from listener\n  off(event, listener, options) {\n    off(this, event, listener, options)\n    return this\n  }\n\n  // Bind given event to listener\n  on(event, listener, binding, options) {\n    on(this, event, listener, binding, options)\n    return this\n  }\n\n  removeEventListener() {}\n}\n\nregister(EventTarget, 'EventTarget')\n", "export function noop() {}\n\n// Default animation values\nexport const timeline = {\n  duration: 400,\n  ease: '>',\n  delay: 0\n}\n\n// Default attribute values\nexport const attrs = {\n  // fill and stroke\n  'fill-opacity': 1,\n  'stroke-opacity': 1,\n  'stroke-width': 0,\n  'stroke-linejoin': 'miter',\n  'stroke-linecap': 'butt',\n  fill: '#000000',\n  stroke: '#000000',\n  opacity: 1,\n\n  // position\n  x: 0,\n  y: 0,\n  cx: 0,\n  cy: 0,\n\n  // size\n  width: 0,\n  height: 0,\n\n  // radius\n  r: 0,\n  rx: 0,\n  ry: 0,\n\n  // gradient\n  offset: 0,\n  'stop-opacity': 1,\n  'stop-color': '#000000',\n\n  // text\n  'text-anchor': 'start'\n}\n", "import { delimiter } from '../modules/core/regex.js'\n\nexport default class SVGArray extends Array {\n  constructor(...args) {\n    super(...args)\n    this.init(...args)\n  }\n\n  clone() {\n    return new this.constructor(this)\n  }\n\n  init(arr) {\n    // This catches the case, that native map tries to create an array with new Array(1)\n    if (typeof arr === 'number') return this\n    this.length = 0\n    this.push(...this.parse(arr))\n    return this\n  }\n\n  // Parse whitespace separated string\n  parse(array = []) {\n    // If already is an array, no need to parse it\n    if (array instanceof Array) return array\n\n    return array.trim().split(delimiter).map(parseFloat)\n  }\n\n  toArray() {\n    return Array.prototype.concat.apply([], this)\n  }\n\n  toSet() {\n    return new Set(this)\n  }\n\n  toString() {\n    return this.join(' ')\n  }\n\n  // Flattens the array if needed\n  valueOf() {\n    const ret = []\n    ret.push(...this)\n    return ret\n  }\n}\n", "import { numberAndUnit } from '../modules/core/regex.js'\n\n// Module for unit conversions\nexport default class SVGNumber {\n  // Initialize\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  convert(unit) {\n    return new SVGNumber(this.value, unit)\n  }\n\n  // Divide number\n  divide(number) {\n    number = new SVGNumber(number)\n    return new SVGNumber(this / number, this.unit || number.unit)\n  }\n\n  init(value, unit) {\n    unit = Array.isArray(value) ? value[1] : unit\n    value = Array.isArray(value) ? value[0] : value\n\n    // initialize defaults\n    this.value = 0\n    this.unit = unit || ''\n\n    // parse value\n    if (typeof value === 'number') {\n      // ensure a valid numeric value\n      this.value = isNaN(value)\n        ? 0\n        : !isFinite(value)\n          ? value < 0\n            ? -3.4e38\n            : +3.4e38\n          : value\n    } else if (typeof value === 'string') {\n      unit = value.match(numberAndUnit)\n\n      if (unit) {\n        // make value numeric\n        this.value = parseFloat(unit[1])\n\n        // normalize\n        if (unit[5] === '%') {\n          this.value /= 100\n        } else if (unit[5] === 's') {\n          this.value *= 1000\n        }\n\n        // store unit\n        this.unit = unit[5]\n      }\n    } else {\n      if (value instanceof SVGNumber) {\n        this.value = value.valueOf()\n        this.unit = value.unit\n      }\n    }\n\n    return this\n  }\n\n  // Subtract number\n  minus(number) {\n    number = new SVGNumber(number)\n    return new SVGNumber(this - number, this.unit || number.unit)\n  }\n\n  // Add number\n  plus(number) {\n    number = new SVGNumber(number)\n    return new SVGNumber(this + number, this.unit || number.unit)\n  }\n\n  // Multiply number\n  times(number) {\n    number = new SVGNumber(number)\n    return new SVGNumber(this * number, this.unit || number.unit)\n  }\n\n  toArray() {\n    return [this.value, this.unit]\n  }\n\n  toJSON() {\n    return this.toString()\n  }\n\n  toString() {\n    return (\n      (this.unit === '%'\n        ? ~~(this.value * 1e8) / 1e6\n        : this.unit === 's'\n          ? this.value / 1e3\n          : this.value) + this.unit\n    )\n  }\n\n  valueOf() {\n    return this.value\n  }\n}\n", "import { attrs as defaults } from './defaults.js'\nimport { isNumber } from './regex.js'\nimport Color from '../../types/Color.js'\nimport SVGArray from '../../types/SVGArray.js'\nimport SVGNumber from '../../types/SVGNumber.js'\n\nconst colorAttributes = new Set([\n  'fill',\n  'stroke',\n  'color',\n  'bgcolor',\n  'stop-color',\n  'flood-color',\n  'lighting-color'\n])\n\nconst hooks = []\nexport function registerAttrHook(fn) {\n  hooks.push(fn)\n}\n\n// Set svg element attribute\nexport default function attr(attr, val, ns) {\n  // act as full getter\n  if (attr == null) {\n    // get an object of attributes\n    attr = {}\n    val = this.node.attributes\n\n    for (const node of val) {\n      attr[node.nodeName] = isNumber.test(node.nodeValue)\n        ? parseFloat(node.nodeValue)\n        : node.nodeValue\n    }\n\n    return attr\n  } else if (attr instanceof Array) {\n    // loop through array and get all values\n    return attr.reduce((last, curr) => {\n      last[curr] = this.attr(curr)\n      return last\n    }, {})\n  } else if (typeof attr === 'object' && attr.constructor === Object) {\n    // apply every attribute individually if an object is passed\n    for (val in attr) this.attr(val, attr[val])\n  } else if (val === null) {\n    // remove value\n    this.node.removeAttribute(attr)\n  } else if (val == null) {\n    // act as a getter if the first and only argument is not an object\n    val = this.node.getAttribute(attr)\n    return val == null\n      ? defaults[attr]\n      : isNumber.test(val)\n        ? parseFloat(val)\n        : val\n  } else {\n    // Loop through hooks and execute them to convert value\n    val = hooks.reduce((_val, hook) => {\n      return hook(attr, _val, this)\n    }, val)\n\n    // ensure correct numeric values (also accepts NaN and Infinity)\n    if (typeof val === 'number') {\n      val = new SVGNumber(val)\n    } else if (colorAttributes.has(attr) && Color.isColor(val)) {\n      // ensure full hex color\n      val = new Color(val)\n    } else if (val.constructor === Array) {\n      // Check for plain arrays and parse array values\n      val = new SVGArray(val)\n    }\n\n    // if the passed attribute is leading...\n    if (attr === 'leading') {\n      // ... call the leading method instead\n      if (this.leading) {\n        this.leading(val)\n      }\n    } else {\n      // set given attribute on node\n      typeof ns === 'string'\n        ? this.node.setAttributeNS(ns, attr, val.toString())\n        : this.node.setAttribute(attr, val.toString())\n    }\n\n    // rebuild if required\n    if (this.rebuild && (attr === 'font-size' || attr === 'x')) {\n      this.rebuild()\n    }\n  }\n\n  return this\n}\n", "import {\n  adopt,\n  assignNewId,\n  eid,\n  extend,\n  makeInstance,\n  create,\n  register\n} from '../utils/adopter.js'\nimport { find, findOne } from '../modules/core/selector.js'\nimport { globals } from '../utils/window.js'\nimport { map } from '../utils/utils.js'\nimport { svg, html } from '../modules/core/namespaces.js'\nimport EventTarget from '../types/EventTarget.js'\nimport List from '../types/List.js'\nimport attr from '../modules/core/attr.js'\n\nexport default class Dom extends EventTarget {\n  constructor(node, attrs) {\n    super()\n    this.node = node\n    this.type = node.nodeName\n\n    if (attrs && node !== attrs) {\n      this.attr(attrs)\n    }\n  }\n\n  // Add given element at a position\n  add(element, i) {\n    element = makeInstance(element)\n\n    // If non-root svg nodes are added we have to remove their namespaces\n    if (\n      element.removeNamespace &&\n      this.node instanceof globals.window.SVGElement\n    ) {\n      element.removeNamespace()\n    }\n\n    if (i == null) {\n      this.node.appendChild(element.node)\n    } else if (element.node !== this.node.childNodes[i]) {\n      this.node.insertBefore(element.node, this.node.childNodes[i])\n    }\n\n    return this\n  }\n\n  // Add element to given container and return self\n  addTo(parent, i) {\n    return makeInstance(parent).put(this, i)\n  }\n\n  // Returns all child elements\n  children() {\n    return new List(\n      map(this.node.children, function (node) {\n        return adopt(node)\n      })\n    )\n  }\n\n  // Remove all elements in this container\n  clear() {\n    // remove children\n    while (this.node.hasChildNodes()) {\n      this.node.removeChild(this.node.lastChild)\n    }\n\n    return this\n  }\n\n  // Clone element\n  clone(deep = true, assignNewIds = true) {\n    // write dom data to the dom so the clone can pickup the data\n    this.writeDataToDom()\n\n    // clone element\n    let nodeClone = this.node.cloneNode(deep)\n    if (assignNewIds) {\n      // assign new id\n      nodeClone = assignNewId(nodeClone)\n    }\n    return new this.constructor(nodeClone)\n  }\n\n  // Iterates over all children and invokes a given block\n  each(block, deep) {\n    const children = this.children()\n    let i, il\n\n    for (i = 0, il = children.length; i < il; i++) {\n      block.apply(children[i], [i, children])\n\n      if (deep) {\n        children[i].each(block, deep)\n      }\n    }\n\n    return this\n  }\n\n  element(nodeName, attrs) {\n    return this.put(new Dom(create(nodeName), attrs))\n  }\n\n  // Get first child\n  first() {\n    return adopt(this.node.firstChild)\n  }\n\n  // Get a element at the given index\n  get(i) {\n    return adopt(this.node.childNodes[i])\n  }\n\n  getEventHolder() {\n    return this.node\n  }\n\n  getEventTarget() {\n    return this.node\n  }\n\n  // Checks if the given element is a child\n  has(element) {\n    return this.index(element) >= 0\n  }\n\n  html(htmlOrFn, outerHTML) {\n    return this.xml(htmlOrFn, outerHTML, html)\n  }\n\n  // Get / set id\n  id(id) {\n    // generate new id if no id set\n    if (typeof id === 'undefined' && !this.node.id) {\n      this.node.id = eid(this.type)\n    }\n\n    // don't set directly with this.node.id to make `null` work correctly\n    return this.attr('id', id)\n  }\n\n  // Gets index of given element\n  index(element) {\n    return [].slice.call(this.node.childNodes).indexOf(element.node)\n  }\n\n  // Get the last child\n  last() {\n    return adopt(this.node.lastChild)\n  }\n\n  // matches the element vs a css selector\n  matches(selector) {\n    const el = this.node\n    const matcher =\n      el.matches ||\n      el.matchesSelector ||\n      el.msMatchesSelector ||\n      el.mozMatchesSelector ||\n      el.webkitMatchesSelector ||\n      el.oMatchesSelector ||\n      null\n    return matcher && matcher.call(el, selector)\n  }\n\n  // Returns the parent element instance\n  parent(type) {\n    let parent = this\n\n    // check for parent\n    if (!parent.node.parentNode) return null\n\n    // get parent element\n    parent = adopt(parent.node.parentNode)\n\n    if (!type) return parent\n\n    // loop through ancestors if type is given\n    do {\n      if (\n        typeof type === 'string' ? parent.matches(type) : parent instanceof type\n      )\n        return parent\n    } while ((parent = adopt(parent.node.parentNode)))\n\n    return parent\n  }\n\n  // Basically does the same as `add()` but returns the added element instead\n  put(element, i) {\n    element = makeInstance(element)\n    this.add(element, i)\n    return element\n  }\n\n  // Add element to given container and return container\n  putIn(parent, i) {\n    return makeInstance(parent).add(this, i)\n  }\n\n  // Remove element\n  remove() {\n    if (this.parent()) {\n      this.parent().removeElement(this)\n    }\n\n    return this\n  }\n\n  // Remove a given child\n  removeElement(element) {\n    this.node.removeChild(element.node)\n\n    return this\n  }\n\n  // Replace this with element\n  replace(element) {\n    element = makeInstance(element)\n\n    if (this.node.parentNode) {\n      this.node.parentNode.replaceChild(element.node, this.node)\n    }\n\n    return element\n  }\n\n  round(precision = 2, map = null) {\n    const factor = 10 ** precision\n    const attrs = this.attr(map)\n\n    for (const i in attrs) {\n      if (typeof attrs[i] === 'number') {\n        attrs[i] = Math.round(attrs[i] * factor) / factor\n      }\n    }\n\n    this.attr(attrs)\n    return this\n  }\n\n  // Import / Export raw svg\n  svg(svgOrFn, outerSVG) {\n    return this.xml(svgOrFn, outerSVG, svg)\n  }\n\n  // Return id on string conversion\n  toString() {\n    return this.id()\n  }\n\n  words(text) {\n    // This is faster than removing all children and adding a new one\n    this.node.textContent = text\n    return this\n  }\n\n  wrap(node) {\n    const parent = this.parent()\n\n    if (!parent) {\n      return this.addTo(node)\n    }\n\n    const position = parent.index(this)\n    return parent.put(node, position).put(this)\n  }\n\n  // write svgjs data to the dom\n  writeDataToDom() {\n    // dump variables recursively\n    this.each(function () {\n      this.writeDataToDom()\n    })\n\n    return this\n  }\n\n  // Import / Export raw svg\n  xml(xmlOrFn, outerXML, ns) {\n    if (typeof xmlOrFn === 'boolean') {\n      ns = outerXML\n      outerXML = xmlOrFn\n      xmlOrFn = null\n    }\n\n    // act as getter if no svg string is given\n    if (xmlOrFn == null || typeof xmlOrFn === 'function') {\n      // The default for exports is, that the outerNode is included\n      outerXML = outerXML == null ? true : outerXML\n\n      // write svgjs data to the dom\n      this.writeDataToDom()\n      let current = this\n\n      // An export modifier was passed\n      if (xmlOrFn != null) {\n        current = adopt(current.node.cloneNode(true))\n\n        // If the user wants outerHTML we need to process this node, too\n        if (outerXML) {\n          const result = xmlOrFn(current)\n          current = result || current\n\n          // The user does not want this node? Well, then he gets nothing\n          if (result === false) return ''\n        }\n\n        // Deep loop through all children and apply modifier\n        current.each(function () {\n          const result = xmlOrFn(this)\n          const _this = result || this\n\n          // If modifier returns false, discard node\n          if (result === false) {\n            this.remove()\n\n            // If modifier returns new node, use it\n          } else if (result && this !== _this) {\n            this.replace(_this)\n          }\n        }, true)\n      }\n\n      // Return outer or inner content\n      return outerXML ? current.node.outerHTML : current.node.innerHTML\n    }\n\n    // Act as setter if we got a string\n\n    // The default for import is, that the current node is not replaced\n    outerXML = outerXML == null ? false : outerXML\n\n    // Create temporary holder\n    const well = create('wrapper', ns)\n    const fragment = globals.document.createDocumentFragment()\n\n    // Dump raw svg\n    well.innerHTML = xmlOrFn\n\n    // Transplant nodes into the fragment\n    for (let len = well.children.length; len--; ) {\n      fragment.appendChild(well.firstElementChild)\n    }\n\n    const parent = this.parent()\n\n    // Add the whole fragment at once\n    return outerXML ? this.replace(fragment) && parent : this.add(fragment)\n  }\n}\n\nextend(Dom, { attr, find, findOne })\nregister(Dom, 'Dom')\n", "import { bbox, rbox, inside } from '../types/Box.js'\nimport { ctm, screenCTM } from '../types/Matrix.js'\nimport {\n  extend,\n  getClass,\n  makeInstance,\n  register,\n  root\n} from '../utils/adopter.js'\nimport { globals } from '../utils/window.js'\nimport { point } from '../types/Point.js'\nimport { proportionalSize, writeDataToDom } from '../utils/utils.js'\nimport { reference } from '../modules/core/regex.js'\nimport Dom from './Dom.js'\nimport List from '../types/List.js'\nimport SVGNumber from '../types/SVGNumber.js'\n\nexport default class Element extends Dom {\n  constructor(node, attrs) {\n    super(node, attrs)\n\n    // initialize data object\n    this.dom = {}\n\n    // create circular reference\n    this.node.instance = this\n\n    if (node.hasAttribute('data-svgjs') || node.hasAttribute('svgjs:data')) {\n      // pull svgjs data from the dom (getAttributeNS doesn't work in html5)\n      this.setData(\n        JSON.parse(node.getAttribute('data-svgjs')) ??\n          JSON.parse(node.getAttribute('svgjs:data')) ??\n          {}\n      )\n    }\n  }\n\n  // Move element by its center\n  center(x, y) {\n    return this.cx(x).cy(y)\n  }\n\n  // Move by center over x-axis\n  cx(x) {\n    return x == null\n      ? this.x() + this.width() / 2\n      : this.x(x - this.width() / 2)\n  }\n\n  // Move by center over y-axis\n  cy(y) {\n    return y == null\n      ? this.y() + this.height() / 2\n      : this.y(y - this.height() / 2)\n  }\n\n  // Get defs\n  defs() {\n    const root = this.root()\n    return root && root.defs()\n  }\n\n  // Relative move over x and y axes\n  dmove(x, y) {\n    return this.dx(x).dy(y)\n  }\n\n  // Relative move over x axis\n  dx(x = 0) {\n    return this.x(new SVGNumber(x).plus(this.x()))\n  }\n\n  // Relative move over y axis\n  dy(y = 0) {\n    return this.y(new SVGNumber(y).plus(this.y()))\n  }\n\n  getEventHolder() {\n    return this\n  }\n\n  // Set height of element\n  height(height) {\n    return this.attr('height', height)\n  }\n\n  // Move element to given x and y values\n  move(x, y) {\n    return this.x(x).y(y)\n  }\n\n  // return array of all ancestors of given type up to the root svg\n  parents(until = this.root()) {\n    const isSelector = typeof until === 'string'\n    if (!isSelector) {\n      until = makeInstance(until)\n    }\n    const parents = new List()\n    let parent = this\n\n    while (\n      (parent = parent.parent()) &&\n      parent.node !== globals.document &&\n      parent.nodeName !== '#document-fragment'\n    ) {\n      parents.push(parent)\n\n      if (!isSelector && parent.node === until.node) {\n        break\n      }\n      if (isSelector && parent.matches(until)) {\n        break\n      }\n      if (parent.node === this.root().node) {\n        // We worked our way to the root and didn't match `until`\n        return null\n      }\n    }\n\n    return parents\n  }\n\n  // Get referenced element form attribute value\n  reference(attr) {\n    attr = this.attr(attr)\n    if (!attr) return null\n\n    const m = (attr + '').match(reference)\n    return m ? makeInstance(m[1]) : null\n  }\n\n  // Get parent document\n  root() {\n    const p = this.parent(getClass(root))\n    return p && p.root()\n  }\n\n  // set given data to the elements data property\n  setData(o) {\n    this.dom = o\n    return this\n  }\n\n  // Set element size to given width and height\n  size(width, height) {\n    const p = proportionalSize(this, width, height)\n\n    return this.width(new SVGNumber(p.width)).height(new SVGNumber(p.height))\n  }\n\n  // Set width of element\n  width(width) {\n    return this.attr('width', width)\n  }\n\n  // write svgjs data to the dom\n  writeDataToDom() {\n    writeDataToDom(this, this.dom)\n    return super.writeDataToDom()\n  }\n\n  // Move over x-axis\n  x(x) {\n    return this.attr('x', x)\n  }\n\n  // Move over y-axis\n  y(y) {\n    return this.attr('y', y)\n  }\n}\n\nextend(Element, {\n  bbox,\n  rbox,\n  inside,\n  point,\n  ctm,\n  screenCTM\n})\n\nregister(Element, 'Element')\n", "import { registerMethods } from '../../utils/methods.js'\nimport Color from '../../types/Color.js'\nimport Element from '../../elements/Element.js'\nimport Matrix from '../../types/Matrix.js'\nimport Point from '../../types/Point.js'\nimport SVGNumber from '../../types/SVGNumber.js'\n\n// Define list of available attributes for stroke and fill\nconst sugar = {\n  stroke: [\n    'color',\n    'width',\n    'opacity',\n    'linecap',\n    'linejoin',\n    'miterlimit',\n    'dasharray',\n    'dashoffset'\n  ],\n  fill: ['color', 'opacity', 'rule'],\n  prefix: function (t, a) {\n    return a === 'color' ? t : t + '-' + a\n  }\n}\n\n// Add sugar for fill and stroke\n;['fill', 'stroke'].forEach(function (m) {\n  const extension = {}\n  let i\n\n  extension[m] = function (o) {\n    if (typeof o === 'undefined') {\n      return this.attr(m)\n    }\n    if (\n      typeof o === 'string' ||\n      o instanceof Color ||\n      Color.isRgb(o) ||\n      o instanceof Element\n    ) {\n      this.attr(m, o)\n    } else {\n      // set all attributes from sugar.fill and sugar.stroke list\n      for (i = sugar[m].length - 1; i >= 0; i--) {\n        if (o[sugar[m][i]] != null) {\n          this.attr(sugar.prefix(m, sugar[m][i]), o[sugar[m][i]])\n        }\n      }\n    }\n\n    return this\n  }\n\n  registerMethods(['Element', 'Runner'], extension)\n})\n\nregisterMethods(['Element', 'Runner'], {\n  // Let the user set the matrix directly\n  matrix: function (mat, b, c, d, e, f) {\n    // Act as a getter\n    if (mat == null) {\n      return new Matrix(this)\n    }\n\n    // Act as a setter, the user can pass a matrix or a set of numbers\n    return this.attr('transform', new Matrix(mat, b, c, d, e, f))\n  },\n\n  // Map rotation to transform\n  rotate: function (angle, cx, cy) {\n    return this.transform({ rotate: angle, ox: cx, oy: cy }, true)\n  },\n\n  // Map skew to transform\n  skew: function (x, y, cx, cy) {\n    return arguments.length === 1 || arguments.length === 3\n      ? this.transform({ skew: x, ox: y, oy: cx }, true)\n      : this.transform({ skew: [x, y], ox: cx, oy: cy }, true)\n  },\n\n  shear: function (lam, cx, cy) {\n    return this.transform({ shear: lam, ox: cx, oy: cy }, true)\n  },\n\n  // Map scale to transform\n  scale: function (x, y, cx, cy) {\n    return arguments.length === 1 || arguments.length === 3\n      ? this.transform({ scale: x, ox: y, oy: cx }, true)\n      : this.transform({ scale: [x, y], ox: cx, oy: cy }, true)\n  },\n\n  // Map translate to transform\n  translate: function (x, y) {\n    return this.transform({ translate: [x, y] }, true)\n  },\n\n  // Map relative translations to transform\n  relative: function (x, y) {\n    return this.transform({ relative: [x, y] }, true)\n  },\n\n  // Map flip to transform\n  flip: function (direction = 'both', origin = 'center') {\n    if ('xybothtrue'.indexOf(direction) === -1) {\n      origin = direction\n      direction = 'both'\n    }\n\n    return this.transform({ flip: direction, origin: origin }, true)\n  },\n\n  // Opacity\n  opacity: function (value) {\n    return this.attr('opacity', value)\n  }\n})\n\nregisterMethods('radius', {\n  // Add x and y radius\n  radius: function (x, y = x) {\n    const type = (this._element || this).type\n    return type === 'radialGradient'\n      ? this.attr('r', new SVGNumber(x))\n      : this.rx(x).ry(y)\n  }\n})\n\nregisterMethods('Path', {\n  // Get path length\n  length: function () {\n    return this.node.getTotalLength()\n  },\n  // Get point at length\n  pointAt: function (length) {\n    return new Point(this.node.getPointAtLength(length))\n  }\n})\n\nregisterMethods(['Element', 'Runner'], {\n  // Set font\n  font: function (a, v) {\n    if (typeof a === 'object') {\n      for (v in a) this.font(v, a[v])\n      return this\n    }\n\n    return a === 'leading'\n      ? this.leading(v)\n      : a === 'anchor'\n        ? this.attr('text-anchor', v)\n        : a === 'size' ||\n            a === 'family' ||\n            a === 'weight' ||\n            a === 'stretch' ||\n            a === 'variant' ||\n            a === 'style'\n          ? this.attr('font-' + a, v)\n          : this.attr(a, v)\n  }\n})\n\n// Add events to elements\nconst methods = [\n  'click',\n  'dblclick',\n  'mousedown',\n  'mouseup',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'mouseenter',\n  'mouseleave',\n  'touchstart',\n  'touchmove',\n  'touchleave',\n  'touchend',\n  'touchcancel',\n  'contextmenu',\n  'wheel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel'\n].reduce(function (last, event) {\n  // add event to Element\n  const fn = function (f) {\n    if (f === null) {\n      this.off(event)\n    } else {\n      this.on(event, f)\n    }\n    return this\n  }\n\n  last[event] = fn\n  return last\n}, {})\n\nregisterMethods('Element', methods)\n", "import { getOrigin, isDescriptive } from '../../utils/utils.js'\nimport { delimiter, transforms } from '../core/regex.js'\nimport { registerMethods } from '../../utils/methods.js'\nimport Matrix from '../../types/Matrix.js'\n\n// Reset all transformations\nexport function untransform() {\n  return this.attr('transform', null)\n}\n\n// merge the whole transformation chain into one matrix and returns it\nexport function matrixify() {\n  const matrix = (this.attr('transform') || '')\n    // split transformations\n    .split(transforms)\n    .slice(0, -1)\n    .map(function (str) {\n      // generate key => value pairs\n      const kv = str.trim().split('(')\n      return [\n        kv[0],\n        kv[1].split(delimiter).map(function (str) {\n          return parseFloat(str)\n        })\n      ]\n    })\n    .reverse()\n    // merge every transformation into one matrix\n    .reduce(function (matrix, transform) {\n      if (transform[0] === 'matrix') {\n        return matrix.lmultiply(Matrix.fromArray(transform[1]))\n      }\n      return matrix[transform[0]].apply(matrix, transform[1])\n    }, new Matrix())\n\n  return matrix\n}\n\n// add an element to another parent without changing the visual representation on the screen\nexport function toParent(parent, i) {\n  if (this === parent) return this\n\n  if (isDescriptive(this.node)) return this.addTo(parent, i)\n\n  const ctm = this.screenCTM()\n  const pCtm = parent.screenCTM().inverse()\n\n  this.addTo(parent, i).untransform().transform(pCtm.multiply(ctm))\n\n  return this\n}\n\n// same as above with parent equals root-svg\nexport function toRoot(i) {\n  return this.toParent(this.root(), i)\n}\n\n// Add transformations\nexport function transform(o, relative) {\n  // Act as a getter if no object was passed\n  if (o == null || typeof o === 'string') {\n    const decomposed = new Matrix(this).decompose()\n    return o == null ? decomposed : decomposed[o]\n  }\n\n  if (!Matrix.isMatrixLike(o)) {\n    // Set the origin according to the defined transform\n    o = { ...o, origin: getOrigin(o, this) }\n  }\n\n  // The user can pass a boolean, an Element or an Matrix or nothing\n  const cleanRelative = relative === true ? this : relative || false\n  const result = new Matrix(cleanRelative).transform(o)\n  return this.attr('transform', result)\n}\n\nregisterMethods('Element', {\n  untransform,\n  matrixify,\n  toParent,\n  toRoot,\n  transform\n})\n", "import { register } from '../utils/adopter.js'\nimport Element from './Element.js'\n\nexport default class Container extends Element {\n  flatten() {\n    this.each(function () {\n      if (this instanceof Container) {\n        return this.flatten().ungroup()\n      }\n    })\n\n    return this\n  }\n\n  ungroup(parent = this.parent(), index = parent.index(this)) {\n    // when parent != this, we want append all elements to the end\n    index = index === -1 ? parent.children().length : index\n\n    this.each(function (i, children) {\n      // reverse each\n      return children[children.length - i - 1].toParent(parent, index)\n    })\n\n    return this.remove()\n  }\n}\n\nregister(Container, 'Container')\n", "import { nodeOrNew, register } from '../utils/adopter.js'\nimport Container from './Container.js'\n\nexport default class Defs extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('defs', node), attrs)\n  }\n\n  flatten() {\n    return this\n  }\n\n  ungroup() {\n    return this\n  }\n}\n\nregister(Defs, 'Defs')\n", "import { register } from '../utils/adopter.js'\nimport Element from './Element.js'\n\nexport default class Shape extends Element {}\n\nregister(Shape, 'Shape')\n", "import SVGNumber from '../../types/SVGNumber.js'\n\n// Radius x value\nexport function rx(rx) {\n  return this.attr('rx', rx)\n}\n\n// Radius y value\nexport function ry(ry) {\n  return this.attr('ry', ry)\n}\n\n// Move over x-axis\nexport function x(x) {\n  return x == null ? this.cx() - this.rx() : this.cx(x + this.rx())\n}\n\n// Move over y-axis\nexport function y(y) {\n  return y == null ? this.cy() - this.ry() : this.cy(y + this.ry())\n}\n\n// Move by center over x-axis\nexport function cx(x) {\n  return this.attr('cx', x)\n}\n\n// Move by center over y-axis\nexport function cy(y) {\n  return this.attr('cy', y)\n}\n\n// Set width of element\nexport function width(width) {\n  return width == null ? this.rx() * 2 : this.rx(new SVGNumber(width).divide(2))\n}\n\n// Set height of element\nexport function height(height) {\n  return height == null\n    ? this.ry() * 2\n    : this.ry(new SVGNumber(height).divide(2))\n}\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { proportionalSize } from '../utils/utils.js'\nimport { registerMethods } from '../utils/methods.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Shape from './Shape.js'\nimport * as circled from '../modules/core/circled.js'\n\nexport default class Ellipse extends Shape {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('ellipse', node), attrs)\n  }\n\n  size(width, height) {\n    const p = proportionalSize(this, width, height)\n\n    return this.rx(new SVGNumber(p.width).divide(2)).ry(\n      new SVGNumber(p.height).divide(2)\n    )\n  }\n}\n\nextend(Ellipse, circled)\n\nregisterMethods('Container', {\n  // Create an ellipse\n  ellipse: wrapWithAttrCheck(function (width = 0, height = width) {\n    return this.put(new Ellipse()).size(width, height).move(0, 0)\n  })\n})\n\nregister(Ellipse, 'Ellipse')\n", "import Dom from './Dom.js'\nimport { globals } from '../utils/window.js'\nimport { register, create } from '../utils/adopter.js'\n\nclass Fragment extends Dom {\n  constructor(node = globals.document.createDocumentFragment()) {\n    super(node)\n  }\n\n  // Import / Export raw xml\n  xml(xmlOrFn, outerXML, ns) {\n    if (typeof xmlOrFn === 'boolean') {\n      ns = outerXML\n      outerXML = xmlOrFn\n      xmlOrFn = null\n    }\n\n    // because this is a fragment we have to put all elements into a wrapper first\n    // before we can get the innerXML from it\n    if (xmlOrFn == null || typeof xmlOrFn === 'function') {\n      const wrapper = new Dom(create('wrapper', ns))\n      wrapper.add(this.node.cloneNode(true))\n\n      return wrapper.xml(false, ns)\n    }\n\n    // Act as setter if we got a string\n    return super.xml(xmlOrFn, false, ns)\n  }\n}\n\nregister(Fragment, 'Fragment')\n\nexport default Fragment\n", "import SVGNumber from '../../types/SVGNumber.js'\n\nexport function from(x, y) {\n  return (this._element || this).type === 'radialGradient'\n    ? this.attr({ fx: new SVGNumber(x), fy: new SVGNumber(y) })\n    : this.attr({ x1: new SVGNumber(x), y1: new SVGNumber(y) })\n}\n\nexport function to(x, y) {\n  return (this._element || this).type === 'radialGradient'\n    ? this.attr({ cx: new SVGNumber(x), cy: new SVGNumber(y) })\n    : this.attr({ x2: new SVGNumber(x), y2: new SVGNumber(y) })\n}\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Box from '../types/Box.js'\nimport Container from './Container.js'\nimport baseFind from '../modules/core/selector.js'\nimport * as gradiented from '../modules/core/gradiented.js'\n\nexport default class Gradient extends Container {\n  constructor(type, attrs) {\n    super(\n      nodeOrNew(type + 'Gradient', typeof type === 'string' ? null : type),\n      attrs\n    )\n  }\n\n  // custom attr to handle transform\n  attr(a, b, c) {\n    if (a === 'transform') a = 'gradientTransform'\n    return super.attr(a, b, c)\n  }\n\n  bbox() {\n    return new Box()\n  }\n\n  targets() {\n    return baseFind('svg [fill*=' + this.id() + ']')\n  }\n\n  // Alias string conversion to fill\n  toString() {\n    return this.url()\n  }\n\n  // Update gradient\n  update(block) {\n    // remove all stops\n    this.clear()\n\n    // invoke passed block\n    if (typeof block === 'function') {\n      block.call(this, this)\n    }\n\n    return this\n  }\n\n  // Return the fill id\n  url() {\n    return 'url(#' + this.id() + ')'\n  }\n}\n\nextend(Gradient, gradiented)\n\nregisterMethods({\n  Container: {\n    // Create gradient element in defs\n    gradient(...args) {\n      return this.defs().gradient(...args)\n    }\n  },\n  // define gradient\n  Defs: {\n    gradient: wrapWithAttrCheck(function (type, block) {\n      return this.put(new Gradient(type)).update(block)\n    })\n  }\n})\n\nregister(Gradient, 'Gradient')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Box from '../types/Box.js'\nimport Container from './Container.js'\nimport baseFind from '../modules/core/selector.js'\n\nexport default class Pattern extends Container {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('pattern', node), attrs)\n  }\n\n  // custom attr to handle transform\n  attr(a, b, c) {\n    if (a === 'transform') a = 'patternTransform'\n    return super.attr(a, b, c)\n  }\n\n  bbox() {\n    return new Box()\n  }\n\n  targets() {\n    return baseFind('svg [fill*=' + this.id() + ']')\n  }\n\n  // Alias string conversion to fill\n  toString() {\n    return this.url()\n  }\n\n  // Update pattern by rebuilding\n  update(block) {\n    // remove content\n    this.clear()\n\n    // invoke passed block\n    if (typeof block === 'function') {\n      block.call(this, this)\n    }\n\n    return this\n  }\n\n  // Return the fill id\n  url() {\n    return 'url(#' + this.id() + ')'\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create pattern element in defs\n    pattern(...args) {\n      return this.defs().pattern(...args)\n    }\n  },\n  Defs: {\n    pattern: wrapWithAttrCheck(function (width, height, block) {\n      return this.put(new Pattern()).update(block).attr({\n        x: 0,\n        y: 0,\n        width: width,\n        height: height,\n        patternUnits: 'userSpaceOnUse'\n      })\n    })\n  }\n})\n\nregister(Pattern, 'Pattern')\n", "import { isImage } from '../modules/core/regex.js'\nimport { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { off, on } from '../modules/core/event.js'\nimport { registerAttrHook } from '../modules/core/attr.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { xlink } from '../modules/core/namespaces.js'\nimport Pattern from './Pattern.js'\nimport Shape from './Shape.js'\nimport { globals } from '../utils/window.js'\n\nexport default class Image extends Shape {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('image', node), attrs)\n  }\n\n  // (re)load image\n  load(url, callback) {\n    if (!url) return this\n\n    const img = new globals.window.Image()\n\n    on(\n      img,\n      'load',\n      function (e) {\n        const p = this.parent(Pattern)\n\n        // ensure image size\n        if (this.width() === 0 && this.height() === 0) {\n          this.size(img.width, img.height)\n        }\n\n        if (p instanceof Pattern) {\n          // ensure pattern size if not set\n          if (p.width() === 0 && p.height() === 0) {\n            p.size(this.width(), this.height())\n          }\n        }\n\n        if (typeof callback === 'function') {\n          callback.call(this, e)\n        }\n      },\n      this\n    )\n\n    on(img, 'load error', function () {\n      // dont forget to unbind memory leaking events\n      off(img)\n    })\n\n    return this.attr('href', (img.src = url), xlink)\n  }\n}\n\nregisterAttrHook(function (attr, val, _this) {\n  // convert image fill and stroke to patterns\n  if (attr === 'fill' || attr === 'stroke') {\n    if (isImage.test(val)) {\n      val = _this.root().defs().image(val)\n    }\n  }\n\n  if (val instanceof Image) {\n    val = _this\n      .root()\n      .defs()\n      .pattern(0, 0, (pattern) => {\n        pattern.add(val)\n      })\n  }\n\n  return val\n})\n\nregisterMethods({\n  Container: {\n    // create image element, load image and set its size\n    image: wrapWithAttrCheck(function (source, callback) {\n      return this.put(new Image()).size(0, 0).load(source, callback)\n    })\n  }\n})\n\nregister(Image, 'Image')\n", "import { delimiter } from '../modules/core/regex.js'\nimport SVGArray from './SVGArray.js'\nimport Box from './Box.js'\nimport Matrix from './Matrix.js'\n\nexport default class PointArray extends SVGArray {\n  // Get bounding box of points\n  bbox() {\n    let maxX = -Infinity\n    let maxY = -Infinity\n    let minX = Infinity\n    let minY = Infinity\n    this.forEach(function (el) {\n      maxX = Math.max(el[0], maxX)\n      maxY = Math.max(el[1], maxY)\n      minX = Math.min(el[0], minX)\n      minY = Math.min(el[1], minY)\n    })\n    return new Box(minX, minY, maxX - minX, maxY - minY)\n  }\n\n  // Move point string\n  move(x, y) {\n    const box = this.bbox()\n\n    // get relative offset\n    x -= box.x\n    y -= box.y\n\n    // move every point\n    if (!isNaN(x) && !isNaN(y)) {\n      for (let i = this.length - 1; i >= 0; i--) {\n        this[i] = [this[i][0] + x, this[i][1] + y]\n      }\n    }\n\n    return this\n  }\n\n  // Parse point string and flat array\n  parse(array = [0, 0]) {\n    const points = []\n\n    // if it is an array, we flatten it and therefore clone it to 1 depths\n    if (array instanceof Array) {\n      array = Array.prototype.concat.apply([], array)\n    } else {\n      // Else, it is considered as a string\n      // parse points\n      array = array.trim().split(delimiter).map(parseFloat)\n    }\n\n    // validate points - https://svgwg.org/svg2-draft/shapes.html#DataTypePoints\n    // Odd number of coordinates is an error. In such cases, drop the last odd coordinate.\n    if (array.length % 2 !== 0) array.pop()\n\n    // wrap points in two-tuples\n    for (let i = 0, len = array.length; i < len; i = i + 2) {\n      points.push([array[i], array[i + 1]])\n    }\n\n    return points\n  }\n\n  // Resize poly string\n  size(width, height) {\n    let i\n    const box = this.bbox()\n\n    // recalculate position of all points according to new size\n    for (i = this.length - 1; i >= 0; i--) {\n      if (box.width)\n        this[i][0] = ((this[i][0] - box.x) * width) / box.width + box.x\n      if (box.height)\n        this[i][1] = ((this[i][1] - box.y) * height) / box.height + box.y\n    }\n\n    return this\n  }\n\n  // Convert array to line object\n  toLine() {\n    return {\n      x1: this[0][0],\n      y1: this[0][1],\n      x2: this[1][0],\n      y2: this[1][1]\n    }\n  }\n\n  // Convert array to string\n  toString() {\n    const array = []\n    // convert to a poly point string\n    for (let i = 0, il = this.length; i < il; i++) {\n      array.push(this[i].join(','))\n    }\n\n    return array.join(' ')\n  }\n\n  transform(m) {\n    return this.clone().transformO(m)\n  }\n\n  // transform points with matrix (similar to Point.transform)\n  transformO(m) {\n    if (!Matrix.isMatrixLike(m)) {\n      m = new Matrix(m)\n    }\n\n    for (let i = this.length; i--; ) {\n      // Perform the matrix multiplication\n      const [x, y] = this[i]\n      this[i][0] = m.a * x + m.c * y + m.e\n      this[i][1] = m.b * x + m.d * y + m.f\n    }\n\n    return this\n  }\n}\n", "import PointArray from '../../types/PointArray.js'\n\nexport const MorphArray = PointArray\n\n// Move by left top corner over x-axis\nexport function x(x) {\n  return x == null ? this.bbox().x : this.move(x, this.bbox().y)\n}\n\n// Move by left top corner over y-axis\nexport function y(y) {\n  return y == null ? this.bbox().y : this.move(this.bbox().x, y)\n}\n\n// Set width of element\nexport function width(width) {\n  const b = this.bbox()\n  return width == null ? b.width : this.size(width, b.height)\n}\n\n// Set height of element\nexport function height(height) {\n  const b = this.bbox()\n  return height == null ? b.height : this.size(b.width, height)\n}\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { proportionalSize } from '../utils/utils.js'\nimport { registerMethods } from '../utils/methods.js'\nimport PointArray from '../types/PointArray.js'\nimport Shape from './Shape.js'\nimport * as pointed from '../modules/core/pointed.js'\n\nexport default class Line extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('line', node), attrs)\n  }\n\n  // Get array\n  array() {\n    return new PointArray([\n      [this.attr('x1'), this.attr('y1')],\n      [this.attr('x2'), this.attr('y2')]\n    ])\n  }\n\n  // Move by left top corner\n  move(x, y) {\n    return this.attr(this.array().move(x, y).toLine())\n  }\n\n  // Overwrite native plot() method\n  plot(x1, y1, x2, y2) {\n    if (x1 == null) {\n      return this.array()\n    } else if (typeof y1 !== 'undefined') {\n      x1 = { x1, y1, x2, y2 }\n    } else {\n      x1 = new PointArray(x1).toLine()\n    }\n\n    return this.attr(x1)\n  }\n\n  // Set element size to given width and height\n  size(width, height) {\n    const p = proportionalSize(this, width, height)\n    return this.attr(this.array().size(p.width, p.height).toLine())\n  }\n}\n\nextend(Line, pointed)\n\nregisterMethods({\n  Container: {\n    // Create a line element\n    line: wrapWithAttrCheck(function (...args) {\n      // make sure plot is called as a setter\n      // x1 is not necessarily a number, it can also be an array, a string and a PointArray\n      return Line.prototype.plot.apply(\n        this.put(new Line()),\n        args[0] != null ? args : [0, 0, 0, 0]\n      )\n    })\n  }\n})\n\nregister(Line, 'Line')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\n\nexport default class Marker extends Container {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('marker', node), attrs)\n  }\n\n  // Set height of element\n  height(height) {\n    return this.attr('markerHeight', height)\n  }\n\n  orient(orient) {\n    return this.attr('orient', orient)\n  }\n\n  // Set marker refX and refY\n  ref(x, y) {\n    return this.attr('refX', x).attr('refY', y)\n  }\n\n  // Return the fill id\n  toString() {\n    return 'url(#' + this.id() + ')'\n  }\n\n  // Update marker\n  update(block) {\n    // remove all content\n    this.clear()\n\n    // invoke passed block\n    if (typeof block === 'function') {\n      block.call(this, this)\n    }\n\n    return this\n  }\n\n  // Set width of element\n  width(width) {\n    return this.attr('markerWidth', width)\n  }\n}\n\nregisterMethods({\n  Container: {\n    marker(...args) {\n      // Create marker element in defs\n      return this.defs().marker(...args)\n    }\n  },\n  Defs: {\n    // Create marker\n    marker: wrapWithAttrCheck(function (width, height, block) {\n      // Set default viewbox to match the width and height, set ref to cx and cy and set orient to auto\n      return this.put(new Marker())\n        .size(width, height)\n        .ref(width / 2, height / 2)\n        .viewbox(0, 0, width, height)\n        .attr('orient', 'auto')\n        .update(block)\n    })\n  },\n  marker: {\n    // Create and attach markers\n    marker(marker, width, height, block) {\n      let attr = ['marker']\n\n      // Build attribute name\n      if (marker !== 'all') attr.push(marker)\n      attr = attr.join('-')\n\n      // Set marker attribute\n      marker =\n        arguments[1] instanceof Marker\n          ? arguments[1]\n          : this.defs().marker(width, height, block)\n\n      return this.attr(attr, marker)\n    }\n  }\n})\n\nregister(Marker, 'Marker')\n", "import { timeline } from '../modules/core/defaults.js'\nimport { extend } from '../utils/adopter.js'\n\n/***\nBase Class\n==========\nThe base stepper class that will be\n***/\n\nfunction makeSetterGetter(k, f) {\n  return function (v) {\n    if (v == null) return this[k]\n    this[k] = v\n    if (f) f.call(this)\n    return this\n  }\n}\n\nexport const easing = {\n  '-': function (pos) {\n    return pos\n  },\n  '<>': function (pos) {\n    return -Math.cos(pos * Math.PI) / 2 + 0.5\n  },\n  '>': function (pos) {\n    return Math.sin((pos * Math.PI) / 2)\n  },\n  '<': function (pos) {\n    return -Math.cos((pos * Math.PI) / 2) + 1\n  },\n  bezier: function (x1, y1, x2, y2) {\n    // see https://www.w3.org/TR/css-easing-1/#cubic-bezier-algo\n    return function (t) {\n      if (t < 0) {\n        if (x1 > 0) {\n          return (y1 / x1) * t\n        } else if (x2 > 0) {\n          return (y2 / x2) * t\n        } else {\n          return 0\n        }\n      } else if (t > 1) {\n        if (x2 < 1) {\n          return ((1 - y2) / (1 - x2)) * t + (y2 - x2) / (1 - x2)\n        } else if (x1 < 1) {\n          return ((1 - y1) / (1 - x1)) * t + (y1 - x1) / (1 - x1)\n        } else {\n          return 1\n        }\n      } else {\n        return 3 * t * (1 - t) ** 2 * y1 + 3 * t ** 2 * (1 - t) * y2 + t ** 3\n      }\n    }\n  },\n  // see https://www.w3.org/TR/css-easing-1/#step-timing-function-algo\n  steps: function (steps, stepPosition = 'end') {\n    // deal with \"jump-\" prefix\n    stepPosition = stepPosition.split('-').reverse()[0]\n\n    let jumps = steps\n    if (stepPosition === 'none') {\n      --jumps\n    } else if (stepPosition === 'both') {\n      ++jumps\n    }\n\n    // The beforeFlag is essentially useless\n    return (t, beforeFlag = false) => {\n      // Step is called currentStep in referenced url\n      let step = Math.floor(t * steps)\n      const jumping = (t * step) % 1 === 0\n\n      if (stepPosition === 'start' || stepPosition === 'both') {\n        ++step\n      }\n\n      if (beforeFlag && jumping) {\n        --step\n      }\n\n      if (t >= 0 && step < 0) {\n        step = 0\n      }\n\n      if (t <= 1 && step > jumps) {\n        step = jumps\n      }\n\n      return step / jumps\n    }\n  }\n}\n\nexport class Stepper {\n  done() {\n    return false\n  }\n}\n\n/***\nEasing Functions\n================\n***/\n\nexport class Ease extends Stepper {\n  constructor(fn = timeline.ease) {\n    super()\n    this.ease = easing[fn] || fn\n  }\n\n  step(from, to, pos) {\n    if (typeof from !== 'number') {\n      return pos < 1 ? from : to\n    }\n    return from + (to - from) * this.ease(pos)\n  }\n}\n\n/***\nController Types\n================\n***/\n\nexport class Controller extends Stepper {\n  constructor(fn) {\n    super()\n    this.stepper = fn\n  }\n\n  done(c) {\n    return c.done\n  }\n\n  step(current, target, dt, c) {\n    return this.stepper(current, target, dt, c)\n  }\n}\n\nfunction recalculate() {\n  // Apply the default parameters\n  const duration = (this._duration || 500) / 1000\n  const overshoot = this._overshoot || 0\n\n  // Calculate the PID natural response\n  const eps = 1e-10\n  const pi = Math.PI\n  const os = Math.log(overshoot / 100 + eps)\n  const zeta = -os / Math.sqrt(pi * pi + os * os)\n  const wn = 3.9 / (zeta * duration)\n\n  // Calculate the Spring values\n  this.d = 2 * zeta * wn\n  this.k = wn * wn\n}\n\nexport class Spring extends Controller {\n  constructor(duration = 500, overshoot = 0) {\n    super()\n    this.duration(duration).overshoot(overshoot)\n  }\n\n  step(current, target, dt, c) {\n    if (typeof current === 'string') return current\n    c.done = dt === Infinity\n    if (dt === Infinity) return target\n    if (dt === 0) return current\n\n    if (dt > 100) dt = 16\n\n    dt /= 1000\n\n    // Get the previous velocity\n    const velocity = c.velocity || 0\n\n    // Apply the control to get the new position and store it\n    const acceleration = -this.d * velocity - this.k * (current - target)\n    const newPosition = current + velocity * dt + (acceleration * dt * dt) / 2\n\n    // Store the velocity\n    c.velocity = velocity + acceleration * dt\n\n    // Figure out if we have converged, and if so, pass the value\n    c.done = Math.abs(target - newPosition) + Math.abs(velocity) < 0.002\n    return c.done ? target : newPosition\n  }\n}\n\nextend(Spring, {\n  duration: makeSetterGetter('_duration', recalculate),\n  overshoot: makeSetterGetter('_overshoot', recalculate)\n})\n\nexport class PID extends Controller {\n  constructor(p = 0.1, i = 0.01, d = 0, windup = 1000) {\n    super()\n    this.p(p).i(i).d(d).windup(windup)\n  }\n\n  step(current, target, dt, c) {\n    if (typeof current === 'string') return current\n    c.done = dt === Infinity\n\n    if (dt === Infinity) return target\n    if (dt === 0) return current\n\n    const p = target - current\n    let i = (c.integral || 0) + p * dt\n    const d = (p - (c.error || 0)) / dt\n    const windup = this._windup\n\n    // antiwindup\n    if (windup !== false) {\n      i = Math.max(-windup, Math.min(i, windup))\n    }\n\n    c.error = p\n    c.integral = i\n\n    c.done = Math.abs(p) < 0.001\n\n    return c.done ? target : current + (this.P * p + this.I * i + this.D * d)\n  }\n}\n\nextend(PID, {\n  windup: makeSetterGetter('_windup'),\n  p: makeSetterGetter('P'),\n  i: makeSetterGetter('I'),\n  d: makeSetterGetter('D')\n})\n", "import { isPathLetter } from '../modules/core/regex.js'\nimport Point from '../types/Point.js'\n\nconst segmentParameters = {\n  M: 2,\n  L: 2,\n  H: 1,\n  V: 1,\n  C: 6,\n  S: 4,\n  Q: 4,\n  T: 2,\n  A: 7,\n  Z: 0\n}\n\nconst pathHandlers = {\n  M: function (c, p, p0) {\n    p.x = p0.x = c[0]\n    p.y = p0.y = c[1]\n\n    return ['M', p.x, p.y]\n  },\n  L: function (c, p) {\n    p.x = c[0]\n    p.y = c[1]\n    return ['L', c[0], c[1]]\n  },\n  H: function (c, p) {\n    p.x = c[0]\n    return ['H', c[0]]\n  },\n  V: function (c, p) {\n    p.y = c[0]\n    return ['V', c[0]]\n  },\n  C: function (c, p) {\n    p.x = c[4]\n    p.y = c[5]\n    return ['C', c[0], c[1], c[2], c[3], c[4], c[5]]\n  },\n  S: function (c, p) {\n    p.x = c[2]\n    p.y = c[3]\n    return ['S', c[0], c[1], c[2], c[3]]\n  },\n  Q: function (c, p) {\n    p.x = c[2]\n    p.y = c[3]\n    return ['Q', c[0], c[1], c[2], c[3]]\n  },\n  T: function (c, p) {\n    p.x = c[0]\n    p.y = c[1]\n    return ['T', c[0], c[1]]\n  },\n  Z: function (c, p, p0) {\n    p.x = p0.x\n    p.y = p0.y\n    return ['Z']\n  },\n  A: function (c, p) {\n    p.x = c[5]\n    p.y = c[6]\n    return ['A', c[0], c[1], c[2], c[3], c[4], c[5], c[6]]\n  }\n}\n\nconst mlhvqtcsaz = 'mlhvqtcsaz'.split('')\n\nfor (let i = 0, il = mlhvqtcsaz.length; i < il; ++i) {\n  pathHandlers[mlhvqtcsaz[i]] = (function (i) {\n    return function (c, p, p0) {\n      if (i === 'H') c[0] = c[0] + p.x\n      else if (i === 'V') c[0] = c[0] + p.y\n      else if (i === 'A') {\n        c[5] = c[5] + p.x\n        c[6] = c[6] + p.y\n      } else {\n        for (let j = 0, jl = c.length; j < jl; ++j) {\n          c[j] = c[j] + (j % 2 ? p.y : p.x)\n        }\n      }\n\n      return pathHandlers[i](c, p, p0)\n    }\n  })(mlhvqtcsaz[i].toUpperCase())\n}\n\nfunction makeAbsolut(parser) {\n  const command = parser.segment[0]\n  return pathHandlers[command](parser.segment.slice(1), parser.p, parser.p0)\n}\n\nfunction segmentComplete(parser) {\n  return (\n    parser.segment.length &&\n    parser.segment.length - 1 ===\n      segmentParameters[parser.segment[0].toUpperCase()]\n  )\n}\n\nfunction startNewSegment(parser, token) {\n  parser.inNumber && finalizeNumber(parser, false)\n  const pathLetter = isPathLetter.test(token)\n\n  if (pathLetter) {\n    parser.segment = [token]\n  } else {\n    const lastCommand = parser.lastCommand\n    const small = lastCommand.toLowerCase()\n    const isSmall = lastCommand === small\n    parser.segment = [small === 'm' ? (isSmall ? 'l' : 'L') : lastCommand]\n  }\n\n  parser.inSegment = true\n  parser.lastCommand = parser.segment[0]\n\n  return pathLetter\n}\n\nfunction finalizeNumber(parser, inNumber) {\n  if (!parser.inNumber) throw new Error('Parser Error')\n  parser.number && parser.segment.push(parseFloat(parser.number))\n  parser.inNumber = inNumber\n  parser.number = ''\n  parser.pointSeen = false\n  parser.hasExponent = false\n\n  if (segmentComplete(parser)) {\n    finalizeSegment(parser)\n  }\n}\n\nfunction finalizeSegment(parser) {\n  parser.inSegment = false\n  if (parser.absolute) {\n    parser.segment = makeAbsolut(parser)\n  }\n  parser.segments.push(parser.segment)\n}\n\nfunction isArcFlag(parser) {\n  if (!parser.segment.length) return false\n  const isArc = parser.segment[0].toUpperCase() === 'A'\n  const length = parser.segment.length\n\n  return isArc && (length === 4 || length === 5)\n}\n\nfunction isExponential(parser) {\n  return parser.lastToken.toUpperCase() === 'E'\n}\n\nconst pathDelimiters = new Set([' ', ',', '\\t', '\\n', '\\r', '\\f'])\nexport function pathParser(d, toAbsolute = true) {\n  let index = 0\n  let token = ''\n  const parser = {\n    segment: [],\n    inNumber: false,\n    number: '',\n    lastToken: '',\n    inSegment: false,\n    segments: [],\n    pointSeen: false,\n    hasExponent: false,\n    absolute: toAbsolute,\n    p0: new Point(),\n    p: new Point()\n  }\n\n  while (((parser.lastToken = token), (token = d.charAt(index++)))) {\n    if (!parser.inSegment) {\n      if (startNewSegment(parser, token)) {\n        continue\n      }\n    }\n\n    if (token === '.') {\n      if (parser.pointSeen || parser.hasExponent) {\n        finalizeNumber(parser, false)\n        --index\n        continue\n      }\n      parser.inNumber = true\n      parser.pointSeen = true\n      parser.number += token\n      continue\n    }\n\n    if (!isNaN(parseInt(token))) {\n      if (parser.number === '0' || isArcFlag(parser)) {\n        parser.inNumber = true\n        parser.number = token\n        finalizeNumber(parser, true)\n        continue\n      }\n\n      parser.inNumber = true\n      parser.number += token\n      continue\n    }\n\n    if (pathDelimiters.has(token)) {\n      if (parser.inNumber) {\n        finalizeNumber(parser, false)\n      }\n      continue\n    }\n\n    if (token === '-' || token === '+') {\n      if (parser.inNumber && !isExponential(parser)) {\n        finalizeNumber(parser, false)\n        --index\n        continue\n      }\n      parser.number += token\n      parser.inNumber = true\n      continue\n    }\n\n    if (token.toUpperCase() === 'E') {\n      parser.number += token\n      parser.hasExponent = true\n      continue\n    }\n\n    if (isPathLetter.test(token)) {\n      if (parser.inNumber) {\n        finalizeNumber(parser, false)\n      } else if (!segmentComplete(parser)) {\n        throw new Error('parser Error')\n      } else {\n        finalizeSegment(parser)\n      }\n      --index\n    }\n  }\n\n  if (parser.inNumber) {\n    finalizeNumber(parser, false)\n  }\n\n  if (parser.inSegment && segmentComplete(parser)) {\n    finalizeSegment(parser)\n  }\n\n  return parser.segments\n}\n", "import SVGArray from './SVGArray.js'\nimport parser from '../modules/core/parser.js'\nimport Box from './Box.js'\nimport { pathParser } from '../utils/pathParser.js'\n\nfunction arrayToString(a) {\n  let s = ''\n  for (let i = 0, il = a.length; i < il; i++) {\n    s += a[i][0]\n\n    if (a[i][1] != null) {\n      s += a[i][1]\n\n      if (a[i][2] != null) {\n        s += ' '\n        s += a[i][2]\n\n        if (a[i][3] != null) {\n          s += ' '\n          s += a[i][3]\n          s += ' '\n          s += a[i][4]\n\n          if (a[i][5] != null) {\n            s += ' '\n            s += a[i][5]\n            s += ' '\n            s += a[i][6]\n\n            if (a[i][7] != null) {\n              s += ' '\n              s += a[i][7]\n            }\n          }\n        }\n      }\n    }\n  }\n\n  return s + ' '\n}\n\nexport default class PathArray extends SVGArray {\n  // Get bounding box of path\n  bbox() {\n    parser().path.setAttribute('d', this.toString())\n    return new Box(parser.nodes.path.getBBox())\n  }\n\n  // Move path string\n  move(x, y) {\n    // get bounding box of current situation\n    const box = this.bbox()\n\n    // get relative offset\n    x -= box.x\n    y -= box.y\n\n    if (!isNaN(x) && !isNaN(y)) {\n      // move every point\n      for (let l, i = this.length - 1; i >= 0; i--) {\n        l = this[i][0]\n\n        if (l === 'M' || l === 'L' || l === 'T') {\n          this[i][1] += x\n          this[i][2] += y\n        } else if (l === 'H') {\n          this[i][1] += x\n        } else if (l === 'V') {\n          this[i][1] += y\n        } else if (l === 'C' || l === 'S' || l === 'Q') {\n          this[i][1] += x\n          this[i][2] += y\n          this[i][3] += x\n          this[i][4] += y\n\n          if (l === 'C') {\n            this[i][5] += x\n            this[i][6] += y\n          }\n        } else if (l === 'A') {\n          this[i][6] += x\n          this[i][7] += y\n        }\n      }\n    }\n\n    return this\n  }\n\n  // Absolutize and parse path to array\n  parse(d = 'M0 0') {\n    if (Array.isArray(d)) {\n      d = Array.prototype.concat.apply([], d).toString()\n    }\n\n    return pathParser(d)\n  }\n\n  // Resize path string\n  size(width, height) {\n    // get bounding box of current situation\n    const box = this.bbox()\n    let i, l\n\n    // If the box width or height is 0 then we ignore\n    // transformations on the respective axis\n    box.width = box.width === 0 ? 1 : box.width\n    box.height = box.height === 0 ? 1 : box.height\n\n    // recalculate position of all points according to new size\n    for (i = this.length - 1; i >= 0; i--) {\n      l = this[i][0]\n\n      if (l === 'M' || l === 'L' || l === 'T') {\n        this[i][1] = ((this[i][1] - box.x) * width) / box.width + box.x\n        this[i][2] = ((this[i][2] - box.y) * height) / box.height + box.y\n      } else if (l === 'H') {\n        this[i][1] = ((this[i][1] - box.x) * width) / box.width + box.x\n      } else if (l === 'V') {\n        this[i][1] = ((this[i][1] - box.y) * height) / box.height + box.y\n      } else if (l === 'C' || l === 'S' || l === 'Q') {\n        this[i][1] = ((this[i][1] - box.x) * width) / box.width + box.x\n        this[i][2] = ((this[i][2] - box.y) * height) / box.height + box.y\n        this[i][3] = ((this[i][3] - box.x) * width) / box.width + box.x\n        this[i][4] = ((this[i][4] - box.y) * height) / box.height + box.y\n\n        if (l === 'C') {\n          this[i][5] = ((this[i][5] - box.x) * width) / box.width + box.x\n          this[i][6] = ((this[i][6] - box.y) * height) / box.height + box.y\n        }\n      } else if (l === 'A') {\n        // resize radii\n        this[i][1] = (this[i][1] * width) / box.width\n        this[i][2] = (this[i][2] * height) / box.height\n\n        // move position values\n        this[i][6] = ((this[i][6] - box.x) * width) / box.width + box.x\n        this[i][7] = ((this[i][7] - box.y) * height) / box.height + box.y\n      }\n    }\n\n    return this\n  }\n\n  // Convert array to string\n  toString() {\n    return arrayToString(this)\n  }\n}\n", "import { Ease } from './Controller.js'\nimport {\n  delimiter,\n  numberAndUnit,\n  isPathLetter\n} from '../modules/core/regex.js'\nimport { extend } from '../utils/adopter.js'\nimport Color from '../types/Color.js'\nimport PathArray from '../types/PathArray.js'\nimport SVGArray from '../types/SVGArray.js'\nimport SVGNumber from '../types/SVGNumber.js'\n\nconst getClassForType = (value) => {\n  const type = typeof value\n\n  if (type === 'number') {\n    return SVGNumber\n  } else if (type === 'string') {\n    if (Color.isColor(value)) {\n      return Color\n    } else if (delimiter.test(value)) {\n      return isPathLetter.test(value) ? PathArray : SVGArray\n    } else if (numberAndUnit.test(value)) {\n      return SVGNumber\n    } else {\n      return NonMorphable\n    }\n  } else if (morphableTypes.indexOf(value.constructor) > -1) {\n    return value.constructor\n  } else if (Array.isArray(value)) {\n    return SVGArray\n  } else if (type === 'object') {\n    return ObjectBag\n  } else {\n    return NonMorphable\n  }\n}\n\nexport default class Morphable {\n  constructor(stepper) {\n    this._stepper = stepper || new Ease('-')\n\n    this._from = null\n    this._to = null\n    this._type = null\n    this._context = null\n    this._morphObj = null\n  }\n\n  at(pos) {\n    return this._morphObj.morph(\n      this._from,\n      this._to,\n      pos,\n      this._stepper,\n      this._context\n    )\n  }\n\n  done() {\n    const complete = this._context.map(this._stepper.done).reduce(function (\n      last,\n      curr\n    ) {\n      return last && curr\n    }, true)\n    return complete\n  }\n\n  from(val) {\n    if (val == null) {\n      return this._from\n    }\n\n    this._from = this._set(val)\n    return this\n  }\n\n  stepper(stepper) {\n    if (stepper == null) return this._stepper\n    this._stepper = stepper\n    return this\n  }\n\n  to(val) {\n    if (val == null) {\n      return this._to\n    }\n\n    this._to = this._set(val)\n    return this\n  }\n\n  type(type) {\n    // getter\n    if (type == null) {\n      return this._type\n    }\n\n    // setter\n    this._type = type\n    return this\n  }\n\n  _set(value) {\n    if (!this._type) {\n      this.type(getClassForType(value))\n    }\n\n    let result = new this._type(value)\n    if (this._type === Color) {\n      result = this._to\n        ? result[this._to[4]]()\n        : this._from\n          ? result[this._from[4]]()\n          : result\n    }\n\n    if (this._type === ObjectBag) {\n      result = this._to\n        ? result.align(this._to)\n        : this._from\n          ? result.align(this._from)\n          : result\n    }\n\n    result = result.toConsumable()\n\n    this._morphObj = this._morphObj || new this._type()\n    this._context =\n      this._context ||\n      Array.apply(null, Array(result.length))\n        .map(Object)\n        .map(function (o) {\n          o.done = true\n          return o\n        })\n    return result\n  }\n}\n\nexport class NonMorphable {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  init(val) {\n    val = Array.isArray(val) ? val[0] : val\n    this.value = val\n    return this\n  }\n\n  toArray() {\n    return [this.value]\n  }\n\n  valueOf() {\n    return this.value\n  }\n}\n\nexport class TransformBag {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  init(obj) {\n    if (Array.isArray(obj)) {\n      obj = {\n        scaleX: obj[0],\n        scaleY: obj[1],\n        shear: obj[2],\n        rotate: obj[3],\n        translateX: obj[4],\n        translateY: obj[5],\n        originX: obj[6],\n        originY: obj[7]\n      }\n    }\n\n    Object.assign(this, TransformBag.defaults, obj)\n    return this\n  }\n\n  toArray() {\n    const v = this\n\n    return [\n      v.scaleX,\n      v.scaleY,\n      v.shear,\n      v.rotate,\n      v.translateX,\n      v.translateY,\n      v.originX,\n      v.originY\n    ]\n  }\n}\n\nTransformBag.defaults = {\n  scaleX: 1,\n  scaleY: 1,\n  shear: 0,\n  rotate: 0,\n  translateX: 0,\n  translateY: 0,\n  originX: 0,\n  originY: 0\n}\n\nconst sortByKey = (a, b) => {\n  return a[0] < b[0] ? -1 : a[0] > b[0] ? 1 : 0\n}\n\nexport class ObjectBag {\n  constructor(...args) {\n    this.init(...args)\n  }\n\n  align(other) {\n    const values = this.values\n    for (let i = 0, il = values.length; i < il; ++i) {\n      // If the type is the same we only need to check if the color is in the correct format\n      if (values[i + 1] === other[i + 1]) {\n        if (values[i + 1] === Color && other[i + 7] !== values[i + 7]) {\n          const space = other[i + 7]\n          const color = new Color(this.values.splice(i + 3, 5))\n            [space]()\n            .toArray()\n          this.values.splice(i + 3, 0, ...color)\n        }\n\n        i += values[i + 2] + 2\n        continue\n      }\n\n      if (!other[i + 1]) {\n        return this\n      }\n\n      // The types differ, so we overwrite the new type with the old one\n      // And initialize it with the types default (e.g. black for color or 0 for number)\n      const defaultObject = new other[i + 1]().toArray()\n\n      // Than we fix the values array\n      const toDelete = values[i + 2] + 3\n\n      values.splice(\n        i,\n        toDelete,\n        other[i],\n        other[i + 1],\n        other[i + 2],\n        ...defaultObject\n      )\n\n      i += values[i + 2] + 2\n    }\n    return this\n  }\n\n  init(objOrArr) {\n    this.values = []\n\n    if (Array.isArray(objOrArr)) {\n      this.values = objOrArr.slice()\n      return\n    }\n\n    objOrArr = objOrArr || {}\n    const entries = []\n\n    for (const i in objOrArr) {\n      const Type = getClassForType(objOrArr[i])\n      const val = new Type(objOrArr[i]).toArray()\n      entries.push([i, Type, val.length, ...val])\n    }\n\n    entries.sort(sortByKey)\n\n    this.values = entries.reduce((last, curr) => last.concat(curr), [])\n    return this\n  }\n\n  toArray() {\n    return this.values\n  }\n\n  valueOf() {\n    const obj = {}\n    const arr = this.values\n\n    // for (var i = 0, len = arr.length; i < len; i += 2) {\n    while (arr.length) {\n      const key = arr.shift()\n      const Type = arr.shift()\n      const num = arr.shift()\n      const values = arr.splice(0, num)\n      obj[key] = new Type(values) // .valueOf()\n    }\n\n    return obj\n  }\n}\n\nconst morphableTypes = [NonMorphable, TransformBag, ObjectBag]\n\nexport function registerMorphableType(type = []) {\n  morphableTypes.push(...[].concat(type))\n}\n\nexport function makeMorphable() {\n  extend(morphableTypes, {\n    to(val) {\n      return new Morphable()\n        .type(this.constructor)\n        .from(this.toArray()) // this.valueOf())\n        .to(val)\n    },\n    fromArray(arr) {\n      this.init(arr)\n      return this\n    },\n    toConsumable() {\n      return this.toArray()\n    },\n    morph(from, to, pos, stepper, context) {\n      const mapper = function (i, index) {\n        return stepper.step(i, to[index], pos, context[index], context)\n      }\n\n      return this.fromArray(from.map(mapper))\n    }\n  })\n}\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { proportionalSize } from '../utils/utils.js'\nimport { registerMethods } from '../utils/methods.js'\nimport PathArray from '../types/PathArray.js'\nimport Shape from './Shape.js'\n\nexport default class Path extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('path', node), attrs)\n  }\n\n  // Get array\n  array() {\n    return this._array || (this._array = new PathArray(this.attr('d')))\n  }\n\n  // Clear array cache\n  clear() {\n    delete this._array\n    return this\n  }\n\n  // Set height of element\n  height(height) {\n    return height == null\n      ? this.bbox().height\n      : this.size(this.bbox().width, height)\n  }\n\n  // Move by left top corner\n  move(x, y) {\n    return this.attr('d', this.array().move(x, y))\n  }\n\n  // Plot new path\n  plot(d) {\n    return d == null\n      ? this.array()\n      : this.clear().attr(\n          'd',\n          typeof d === 'string' ? d : (this._array = new PathArray(d))\n        )\n  }\n\n  // Set element size to given width and height\n  size(width, height) {\n    const p = proportionalSize(this, width, height)\n    return this.attr('d', this.array().size(p.width, p.height))\n  }\n\n  // Set width of element\n  width(width) {\n    return width == null\n      ? this.bbox().width\n      : this.size(width, this.bbox().height)\n  }\n\n  // Move by left top corner over x-axis\n  x(x) {\n    return x == null ? this.bbox().x : this.move(x, this.bbox().y)\n  }\n\n  // Move by left top corner over y-axis\n  y(y) {\n    return y == null ? this.bbox().y : this.move(this.bbox().x, y)\n  }\n}\n\n// Define morphable array\nPath.prototype.MorphArray = PathArray\n\n// Add parent method\nregisterMethods({\n  Container: {\n    // Create a wrapped path element\n    path: wrapWithAttrCheck(function (d) {\n      // make sure plot is called as a setter\n      return this.put(new Path()).plot(d || new PathArray())\n    })\n  }\n})\n\nregister(Path, 'Path')\n", "import { proportionalSize } from '../../utils/utils.js'\nimport PointArray from '../../types/PointArray.js'\n\n// Get array\nexport function array() {\n  return this._array || (this._array = new PointArray(this.attr('points')))\n}\n\n// Clear array cache\nexport function clear() {\n  delete this._array\n  return this\n}\n\n// Move by left top corner\nexport function move(x, y) {\n  return this.attr('points', this.array().move(x, y))\n}\n\n// Plot new path\nexport function plot(p) {\n  return p == null\n    ? this.array()\n    : this.clear().attr(\n        'points',\n        typeof p === 'string' ? p : (this._array = new PointArray(p))\n      )\n}\n\n// Set element size to given width and height\nexport function size(width, height) {\n  const p = proportionalSize(this, width, height)\n  return this.attr('points', this.array().size(p.width, p.height))\n}\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport PointArray from '../types/PointArray.js'\nimport Shape from './Shape.js'\nimport * as pointed from '../modules/core/pointed.js'\nimport * as poly from '../modules/core/poly.js'\n\nexport default class Polygon extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('polygon', node), attrs)\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create a wrapped polygon element\n    polygon: wrapWithAttrCheck(function (p) {\n      // make sure plot is called as a setter\n      return this.put(new Polygon()).plot(p || new PointArray())\n    })\n  }\n})\n\nextend(Polygon, pointed)\nextend(Polygon, poly)\nregister(Polygon, 'Polygon')\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport PointArray from '../types/PointArray.js'\nimport Shape from './Shape.js'\nimport * as pointed from '../modules/core/pointed.js'\nimport * as poly from '../modules/core/poly.js'\n\nexport default class Polyline extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('polyline', node), attrs)\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create a wrapped polygon element\n    polyline: wrapWithAttrCheck(function (p) {\n      // make sure plot is called as a setter\n      return this.put(new Polyline()).plot(p || new PointArray())\n    })\n  }\n})\n\nextend(Polyline, pointed)\nextend(Polyline, poly)\nregister(Polyline, 'Polyline')\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { rx, ry } from '../modules/core/circled.js'\nimport Shape from './Shape.js'\n\nexport default class Rect extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('rect', node), attrs)\n  }\n}\n\nextend(Rect, { rx, ry })\n\nregisterMethods({\n  Container: {\n    // Create a rect element\n    rect: wrapWithAttrCheck(function (width, height) {\n      return this.put(new Rect()).size(width, height)\n    })\n  }\n})\n\nregister(Rect, 'Rect')\n", "export default class Queue {\n  constructor() {\n    this._first = null\n    this._last = null\n  }\n\n  // Shows us the first item in the list\n  first() {\n    return this._first && this._first.value\n  }\n\n  // Shows us the last item in the list\n  last() {\n    return this._last && this._last.value\n  }\n\n  push(value) {\n    // An item stores an id and the provided value\n    const item =\n      typeof value.next !== 'undefined'\n        ? value\n        : { value: value, next: null, prev: null }\n\n    // Deal with the queue being empty or populated\n    if (this._last) {\n      item.prev = this._last\n      this._last.next = item\n      this._last = item\n    } else {\n      this._last = item\n      this._first = item\n    }\n\n    // Return the current item\n    return item\n  }\n\n  // Removes the item that was returned from the push\n  remove(item) {\n    // Relink the previous item\n    if (item.prev) item.prev.next = item.next\n    if (item.next) item.next.prev = item.prev\n    if (item === this._last) this._last = item.prev\n    if (item === this._first) this._first = item.next\n\n    // Invalidate item\n    item.prev = null\n    item.next = null\n  }\n\n  shift() {\n    // Check if we have a value\n    const remove = this._first\n    if (!remove) return null\n\n    // If we do, remove it and relink things\n    this._first = remove.next\n    if (this._first) this._first.prev = null\n    this._last = this._first ? this._last : null\n    return remove.value\n  }\n}\n", "import { globals } from '../utils/window.js'\nimport Queue from './Queue.js'\n\nconst Animator = {\n  nextDraw: null,\n  frames: new Queue(),\n  timeouts: new Queue(),\n  immediates: new Queue(),\n  timer: () => globals.window.performance || globals.window.Date,\n  transforms: [],\n\n  frame(fn) {\n    // Store the node\n    const node = Animator.frames.push({ run: fn })\n\n    // Request an animation frame if we don't have one\n    if (Animator.nextDraw === null) {\n      Animator.nextDraw = globals.window.requestAnimationFrame(Animator._draw)\n    }\n\n    // Return the node so we can remove it easily\n    return node\n  },\n\n  timeout(fn, delay) {\n    delay = delay || 0\n\n    // Work out when the event should fire\n    const time = Animator.timer().now() + delay\n\n    // Add the timeout to the end of the queue\n    const node = Animator.timeouts.push({ run: fn, time: time })\n\n    // Request another animation frame if we need one\n    if (Animator.nextDraw === null) {\n      Animator.nextDraw = globals.window.requestAnimationFrame(Animator._draw)\n    }\n\n    return node\n  },\n\n  immediate(fn) {\n    // Add the immediate fn to the end of the queue\n    const node = Animator.immediates.push(fn)\n    // Request another animation frame if we need one\n    if (Animator.nextDraw === null) {\n      Animator.nextDraw = globals.window.requestAnimationFrame(Animator._draw)\n    }\n\n    return node\n  },\n\n  cancelFrame(node) {\n    node != null && Animator.frames.remove(node)\n  },\n\n  clearTimeout(node) {\n    node != null && Animator.timeouts.remove(node)\n  },\n\n  cancelImmediate(node) {\n    node != null && Animator.immediates.remove(node)\n  },\n\n  _draw(now) {\n    // Run all the timeouts we can run, if they are not ready yet, add them\n    // to the end of the queue immediately! (bad timeouts!!! [sarcasm])\n    let nextTimeout = null\n    const lastTimeout = Animator.timeouts.last()\n    while ((nextTimeout = Animator.timeouts.shift())) {\n      // Run the timeout if its time, or push it to the end\n      if (now >= nextTimeout.time) {\n        nextTimeout.run()\n      } else {\n        Animator.timeouts.push(nextTimeout)\n      }\n\n      // If we hit the last item, we should stop shifting out more items\n      if (nextTimeout === lastTimeout) break\n    }\n\n    // Run all of the animation frames\n    let nextFrame = null\n    const lastFrame = Animator.frames.last()\n    while (nextFrame !== lastFrame && (nextFrame = Animator.frames.shift())) {\n      nextFrame.run(now)\n    }\n\n    let nextImmediate = null\n    while ((nextImmediate = Animator.immediates.shift())) {\n      nextImmediate()\n    }\n\n    // If we have remaining timeouts or frames, draw until we don't anymore\n    Animator.nextDraw =\n      Animator.timeouts.first() || Animator.frames.first()\n        ? globals.window.requestAnimationFrame(Animator._draw)\n        : null\n  }\n}\n\nexport default Animator\n", "import { globals } from '../utils/window.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Animator from './Animator.js'\nimport EventTarget from '../types/EventTarget.js'\n\nconst makeSchedule = function (runnerInfo) {\n  const start = runnerInfo.start\n  const duration = runnerInfo.runner.duration()\n  const end = start + duration\n  return {\n    start: start,\n    duration: duration,\n    end: end,\n    runner: runnerInfo.runner\n  }\n}\n\nconst defaultSource = function () {\n  const w = globals.window\n  return (w.performance || w.Date).now()\n}\n\nexport default class Timeline extends EventTarget {\n  // Construct a new timeline on the given element\n  constructor(timeSource = defaultSource) {\n    super()\n\n    this._timeSource = timeSource\n\n    // terminate resets all variables to their initial state\n    this.terminate()\n  }\n\n  active() {\n    return !!this._nextFrame\n  }\n\n  finish() {\n    // Go to end and pause\n    this.time(this.getEndTimeOfTimeline() + 1)\n    return this.pause()\n  }\n\n  // Calculates the end of the timeline\n  getEndTime() {\n    const lastRunnerInfo = this.getLastRunnerInfo()\n    const lastDuration = lastRunnerInfo ? lastRunnerInfo.runner.duration() : 0\n    const lastStartTime = lastRunnerInfo ? lastRunnerInfo.start : this._time\n    return lastStartTime + lastDuration\n  }\n\n  getEndTimeOfTimeline() {\n    const endTimes = this._runners.map((i) => i.start + i.runner.duration())\n    return Math.max(0, ...endTimes)\n  }\n\n  getLastRunnerInfo() {\n    return this.getRunnerInfoById(this._lastRunnerId)\n  }\n\n  getRunnerInfoById(id) {\n    return this._runners[this._runnerIds.indexOf(id)] || null\n  }\n\n  pause() {\n    this._paused = true\n    return this._continue()\n  }\n\n  persist(dtOrForever) {\n    if (dtOrForever == null) return this._persist\n    this._persist = dtOrForever\n    return this\n  }\n\n  play() {\n    // Now make sure we are not paused and continue the animation\n    this._paused = false\n    return this.updateTime()._continue()\n  }\n\n  reverse(yes) {\n    const currentSpeed = this.speed()\n    if (yes == null) return this.speed(-currentSpeed)\n\n    const positive = Math.abs(currentSpeed)\n    return this.speed(yes ? -positive : positive)\n  }\n\n  // schedules a runner on the timeline\n  schedule(runner, delay, when) {\n    if (runner == null) {\n      return this._runners.map(makeSchedule)\n    }\n\n    // The start time for the next animation can either be given explicitly,\n    // derived from the current timeline time or it can be relative to the\n    // last start time to chain animations directly\n\n    let absoluteStartTime = 0\n    const endTime = this.getEndTime()\n    delay = delay || 0\n\n    // Work out when to start the animation\n    if (when == null || when === 'last' || when === 'after') {\n      // Take the last time and increment\n      absoluteStartTime = endTime\n    } else if (when === 'absolute' || when === 'start') {\n      absoluteStartTime = delay\n      delay = 0\n    } else if (when === 'now') {\n      absoluteStartTime = this._time\n    } else if (when === 'relative') {\n      const runnerInfo = this.getRunnerInfoById(runner.id)\n      if (runnerInfo) {\n        absoluteStartTime = runnerInfo.start + delay\n        delay = 0\n      }\n    } else if (when === 'with-last') {\n      const lastRunnerInfo = this.getLastRunnerInfo()\n      const lastStartTime = lastRunnerInfo ? lastRunnerInfo.start : this._time\n      absoluteStartTime = lastStartTime\n    } else {\n      throw new Error('Invalid value for the \"when\" parameter')\n    }\n\n    // Manage runner\n    runner.unschedule()\n    runner.timeline(this)\n\n    const persist = runner.persist()\n    const runnerInfo = {\n      persist: persist === null ? this._persist : persist,\n      start: absoluteStartTime + delay,\n      runner\n    }\n\n    this._lastRunnerId = runner.id\n\n    this._runners.push(runnerInfo)\n    this._runners.sort((a, b) => a.start - b.start)\n    this._runnerIds = this._runners.map((info) => info.runner.id)\n\n    this.updateTime()._continue()\n    return this\n  }\n\n  seek(dt) {\n    return this.time(this._time + dt)\n  }\n\n  source(fn) {\n    if (fn == null) return this._timeSource\n    this._timeSource = fn\n    return this\n  }\n\n  speed(speed) {\n    if (speed == null) return this._speed\n    this._speed = speed\n    return this\n  }\n\n  stop() {\n    // Go to start and pause\n    this.time(0)\n    return this.pause()\n  }\n\n  time(time) {\n    if (time == null) return this._time\n    this._time = time\n    return this._continue(true)\n  }\n\n  // Remove the runner from this timeline\n  unschedule(runner) {\n    const index = this._runnerIds.indexOf(runner.id)\n    if (index < 0) return this\n\n    this._runners.splice(index, 1)\n    this._runnerIds.splice(index, 1)\n\n    runner.timeline(null)\n    return this\n  }\n\n  // Makes sure, that after pausing the time doesn't jump\n  updateTime() {\n    if (!this.active()) {\n      this._lastSourceTime = this._timeSource()\n    }\n    return this\n  }\n\n  // Checks if we are running and continues the animation\n  _continue(immediateStep = false) {\n    Animator.cancelFrame(this._nextFrame)\n    this._nextFrame = null\n\n    if (immediateStep) return this._stepImmediate()\n    if (this._paused) return this\n\n    this._nextFrame = Animator.frame(this._step)\n    return this\n  }\n\n  _stepFn(immediateStep = false) {\n    // Get the time delta from the last time and update the time\n    const time = this._timeSource()\n    let dtSource = time - this._lastSourceTime\n\n    if (immediateStep) dtSource = 0\n\n    const dtTime = this._speed * dtSource + (this._time - this._lastStepTime)\n    this._lastSourceTime = time\n\n    // Only update the time if we use the timeSource.\n    // Otherwise use the current time\n    if (!immediateStep) {\n      // Update the time\n      this._time += dtTime\n      this._time = this._time < 0 ? 0 : this._time\n    }\n    this._lastStepTime = this._time\n    this.fire('time', this._time)\n\n    // This is for the case that the timeline was seeked so that the time\n    // is now before the startTime of the runner. That is why we need to set\n    // the runner to position 0\n\n    // FIXME:\n    // However, resetting in insertion order leads to bugs. Considering the case,\n    // where 2 runners change the same attribute but in different times,\n    // resetting both of them will lead to the case where the later defined\n    // runner always wins the reset even if the other runner started earlier\n    // and therefore should win the attribute battle\n    // this can be solved by resetting them backwards\n    for (let k = this._runners.length; k--; ) {\n      // Get and run the current runner and ignore it if its inactive\n      const runnerInfo = this._runners[k]\n      const runner = runnerInfo.runner\n\n      // Make sure that we give the actual difference\n      // between runner start time and now\n      const dtToStart = this._time - runnerInfo.start\n\n      // Dont run runner if not started yet\n      // and try to reset it\n      if (dtToStart <= 0) {\n        runner.reset()\n      }\n    }\n\n    // Run all of the runners directly\n    let runnersLeft = false\n    for (let i = 0, len = this._runners.length; i < len; i++) {\n      // Get and run the current runner and ignore it if its inactive\n      const runnerInfo = this._runners[i]\n      const runner = runnerInfo.runner\n      let dt = dtTime\n\n      // Make sure that we give the actual difference\n      // between runner start time and now\n      const dtToStart = this._time - runnerInfo.start\n\n      // Dont run runner if not started yet\n      if (dtToStart <= 0) {\n        runnersLeft = true\n        continue\n      } else if (dtToStart < dt) {\n        // Adjust dt to make sure that animation is on point\n        dt = dtToStart\n      }\n\n      if (!runner.active()) continue\n\n      // If this runner is still going, signal that we need another animation\n      // frame, otherwise, remove the completed runner\n      const finished = runner.step(dt).done\n      if (!finished) {\n        runnersLeft = true\n        // continue\n      } else if (runnerInfo.persist !== true) {\n        // runner is finished. And runner might get removed\n        const endTime = runner.duration() - runner.time() + this._time\n\n        if (endTime + runnerInfo.persist < this._time) {\n          // Delete runner and correct index\n          runner.unschedule()\n          --i\n          --len\n        }\n      }\n    }\n\n    // Basically: we continue when there are runners right from us in time\n    // when -->, and when runners are left from us when <--\n    if (\n      (runnersLeft && !(this._speed < 0 && this._time === 0)) ||\n      (this._runnerIds.length && this._speed < 0 && this._time > 0)\n    ) {\n      this._continue()\n    } else {\n      this.pause()\n      this.fire('finished')\n    }\n\n    return this\n  }\n\n  terminate() {\n    // cleanup memory\n\n    // Store the timing variables\n    this._startTime = 0\n    this._speed = 1.0\n\n    // Determines how long a runner is hold in memory. Can be a dt or true/false\n    this._persist = 0\n\n    // Keep track of the running animations and their starting parameters\n    this._nextFrame = null\n    this._paused = true\n    this._runners = []\n    this._runnerIds = []\n    this._lastRunnerId = -1\n    this._time = 0\n    this._lastSourceTime = 0\n    this._lastStepTime = 0\n\n    // Make sure that step is always called in class context\n    this._step = this._stepFn.bind(this, false)\n    this._stepImmediate = this._stepFn.bind(this, true)\n  }\n}\n\nregisterMethods({\n  Element: {\n    timeline: function (timeline) {\n      if (timeline == null) {\n        this._timeline = this._timeline || new Timeline()\n        return this._timeline\n      } else {\n        this._timeline = timeline\n        return this\n      }\n    }\n  }\n})\n", "import { <PERSON>, Ease, Stepper } from './Controller.js'\nimport { extend, register } from '../utils/adopter.js'\nimport { from, to } from '../modules/core/gradiented.js'\nimport { getOrigin } from '../utils/utils.js'\nimport { noop, timeline } from '../modules/core/defaults.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { rx, ry } from '../modules/core/circled.js'\nimport Animator from './Animator.js'\nimport Box from '../types/Box.js'\nimport EventTarget from '../types/EventTarget.js'\nimport Matrix from '../types/Matrix.js'\nimport Morphable, { TransformBag, ObjectBag } from './Morphable.js'\nimport Point from '../types/Point.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Timeline from './Timeline.js'\n\nexport default class Runner extends EventTarget {\n  constructor(options) {\n    super()\n\n    // Store a unique id on the runner, so that we can identify it later\n    this.id = Runner.id++\n\n    // Ensure a default value\n    options = options == null ? timeline.duration : options\n\n    // Ensure that we get a controller\n    options = typeof options === 'function' ? new Controller(options) : options\n\n    // Declare all of the variables\n    this._element = null\n    this._timeline = null\n    this.done = false\n    this._queue = []\n\n    // Work out the stepper and the duration\n    this._duration = typeof options === 'number' && options\n    this._isDeclarative = options instanceof Controller\n    this._stepper = this._isDeclarative ? options : new Ease()\n\n    // We copy the current values from the timeline because they can change\n    this._history = {}\n\n    // Store the state of the runner\n    this.enabled = true\n    this._time = 0\n    this._lastTime = 0\n\n    // At creation, the runner is in reset state\n    this._reseted = true\n\n    // Save transforms applied to this runner\n    this.transforms = new Matrix()\n    this.transformId = 1\n\n    // Looping variables\n    this._haveReversed = false\n    this._reverse = false\n    this._loopsDone = 0\n    this._swing = false\n    this._wait = 0\n    this._times = 1\n\n    this._frameId = null\n\n    // Stores how long a runner is stored after being done\n    this._persist = this._isDeclarative ? true : null\n  }\n\n  static sanitise(duration, delay, when) {\n    // Initialise the default parameters\n    let times = 1\n    let swing = false\n    let wait = 0\n    duration = duration ?? timeline.duration\n    delay = delay ?? timeline.delay\n    when = when || 'last'\n\n    // If we have an object, unpack the values\n    if (typeof duration === 'object' && !(duration instanceof Stepper)) {\n      delay = duration.delay ?? delay\n      when = duration.when ?? when\n      swing = duration.swing || swing\n      times = duration.times ?? times\n      wait = duration.wait ?? wait\n      duration = duration.duration ?? timeline.duration\n    }\n\n    return {\n      duration: duration,\n      delay: delay,\n      swing: swing,\n      times: times,\n      wait: wait,\n      when: when\n    }\n  }\n\n  active(enabled) {\n    if (enabled == null) return this.enabled\n    this.enabled = enabled\n    return this\n  }\n\n  /*\n  Private Methods\n  ===============\n  Methods that shouldn't be used externally\n  */\n  addTransform(transform) {\n    this.transforms.lmultiplyO(transform)\n    return this\n  }\n\n  after(fn) {\n    return this.on('finished', fn)\n  }\n\n  animate(duration, delay, when) {\n    const o = Runner.sanitise(duration, delay, when)\n    const runner = new Runner(o.duration)\n    if (this._timeline) runner.timeline(this._timeline)\n    if (this._element) runner.element(this._element)\n    return runner.loop(o).schedule(o.delay, o.when)\n  }\n\n  clearTransform() {\n    this.transforms = new Matrix()\n    return this\n  }\n\n  // TODO: Keep track of all transformations so that deletion is faster\n  clearTransformsFromQueue() {\n    if (\n      !this.done ||\n      !this._timeline ||\n      !this._timeline._runnerIds.includes(this.id)\n    ) {\n      this._queue = this._queue.filter((item) => {\n        return !item.isTransform\n      })\n    }\n  }\n\n  delay(delay) {\n    return this.animate(0, delay)\n  }\n\n  duration() {\n    return this._times * (this._wait + this._duration) - this._wait\n  }\n\n  during(fn) {\n    return this.queue(null, fn)\n  }\n\n  ease(fn) {\n    this._stepper = new Ease(fn)\n    return this\n  }\n  /*\n  Runner Definitions\n  ==================\n  These methods help us define the runtime behaviour of the Runner or they\n  help us make new runners from the current runner\n  */\n\n  element(element) {\n    if (element == null) return this._element\n    this._element = element\n    element._prepareRunner()\n    return this\n  }\n\n  finish() {\n    return this.step(Infinity)\n  }\n\n  loop(times, swing, wait) {\n    // Deal with the user passing in an object\n    if (typeof times === 'object') {\n      swing = times.swing\n      wait = times.wait\n      times = times.times\n    }\n\n    // Sanitise the values and store them\n    this._times = times || Infinity\n    this._swing = swing || false\n    this._wait = wait || 0\n\n    // Allow true to be passed\n    if (this._times === true) {\n      this._times = Infinity\n    }\n\n    return this\n  }\n\n  loops(p) {\n    const loopDuration = this._duration + this._wait\n    if (p == null) {\n      const loopsDone = Math.floor(this._time / loopDuration)\n      const relativeTime = this._time - loopsDone * loopDuration\n      const position = relativeTime / this._duration\n      return Math.min(loopsDone + position, this._times)\n    }\n    const whole = Math.floor(p)\n    const partial = p % 1\n    const time = loopDuration * whole + this._duration * partial\n    return this.time(time)\n  }\n\n  persist(dtOrForever) {\n    if (dtOrForever == null) return this._persist\n    this._persist = dtOrForever\n    return this\n  }\n\n  position(p) {\n    // Get all of the variables we need\n    const x = this._time\n    const d = this._duration\n    const w = this._wait\n    const t = this._times\n    const s = this._swing\n    const r = this._reverse\n    let position\n\n    if (p == null) {\n      /*\n      This function converts a time to a position in the range [0, 1]\n      The full explanation can be found in this desmos demonstration\n        https://www.desmos.com/calculator/u4fbavgche\n      The logic is slightly simplified here because we can use booleans\n      */\n\n      // Figure out the value without thinking about the start or end time\n      const f = function (x) {\n        const swinging = s * Math.floor((x % (2 * (w + d))) / (w + d))\n        const backwards = (swinging && !r) || (!swinging && r)\n        const uncliped =\n          (Math.pow(-1, backwards) * (x % (w + d))) / d + backwards\n        const clipped = Math.max(Math.min(uncliped, 1), 0)\n        return clipped\n      }\n\n      // Figure out the value by incorporating the start time\n      const endTime = t * (w + d) - w\n      position =\n        x <= 0\n          ? Math.round(f(1e-5))\n          : x < endTime\n            ? f(x)\n            : Math.round(f(endTime - 1e-5))\n      return position\n    }\n\n    // Work out the loops done and add the position to the loops done\n    const loopsDone = Math.floor(this.loops())\n    const swingForward = s && loopsDone % 2 === 0\n    const forwards = (swingForward && !r) || (r && swingForward)\n    position = loopsDone + (forwards ? p : 1 - p)\n    return this.loops(position)\n  }\n\n  progress(p) {\n    if (p == null) {\n      return Math.min(1, this._time / this.duration())\n    }\n    return this.time(p * this.duration())\n  }\n\n  /*\n  Basic Functionality\n  ===================\n  These methods allow us to attach basic functions to the runner directly\n  */\n  queue(initFn, runFn, retargetFn, isTransform) {\n    this._queue.push({\n      initialiser: initFn || noop,\n      runner: runFn || noop,\n      retarget: retargetFn,\n      isTransform: isTransform,\n      initialised: false,\n      finished: false\n    })\n    const timeline = this.timeline()\n    timeline && this.timeline()._continue()\n    return this\n  }\n\n  reset() {\n    if (this._reseted) return this\n    this.time(0)\n    this._reseted = true\n    return this\n  }\n\n  reverse(reverse) {\n    this._reverse = reverse == null ? !this._reverse : reverse\n    return this\n  }\n\n  schedule(timeline, delay, when) {\n    // The user doesn't need to pass a timeline if we already have one\n    if (!(timeline instanceof Timeline)) {\n      when = delay\n      delay = timeline\n      timeline = this.timeline()\n    }\n\n    // If there is no timeline, yell at the user...\n    if (!timeline) {\n      throw Error('Runner cannot be scheduled without timeline')\n    }\n\n    // Schedule the runner on the timeline provided\n    timeline.schedule(this, delay, when)\n    return this\n  }\n\n  step(dt) {\n    // If we are inactive, this stepper just gets skipped\n    if (!this.enabled) return this\n\n    // Update the time and get the new position\n    dt = dt == null ? 16 : dt\n    this._time += dt\n    const position = this.position()\n\n    // Figure out if we need to run the stepper in this frame\n    const running = this._lastPosition !== position && this._time >= 0\n    this._lastPosition = position\n\n    // Figure out if we just started\n    const duration = this.duration()\n    const justStarted = this._lastTime <= 0 && this._time > 0\n    const justFinished = this._lastTime < duration && this._time >= duration\n\n    this._lastTime = this._time\n    if (justStarted) {\n      this.fire('start', this)\n    }\n\n    // Work out if the runner is finished set the done flag here so animations\n    // know, that they are running in the last step (this is good for\n    // transformations which can be merged)\n    const declarative = this._isDeclarative\n    this.done = !declarative && !justFinished && this._time >= duration\n\n    // Runner is running. So its not in reset state anymore\n    this._reseted = false\n\n    let converged = false\n    // Call initialise and the run function\n    if (running || declarative) {\n      this._initialise(running)\n\n      // clear the transforms on this runner so they dont get added again and again\n      this.transforms = new Matrix()\n      converged = this._run(declarative ? dt : position)\n\n      this.fire('step', this)\n    }\n    // correct the done flag here\n    // declarative animations itself know when they converged\n    this.done = this.done || (converged && declarative)\n    if (justFinished) {\n      this.fire('finished', this)\n    }\n    return this\n  }\n\n  /*\n  Runner animation methods\n  ========================\n  Control how the animation plays\n  */\n  time(time) {\n    if (time == null) {\n      return this._time\n    }\n    const dt = time - this._time\n    this.step(dt)\n    return this\n  }\n\n  timeline(timeline) {\n    // check explicitly for undefined so we can set the timeline to null\n    if (typeof timeline === 'undefined') return this._timeline\n    this._timeline = timeline\n    return this\n  }\n\n  unschedule() {\n    const timeline = this.timeline()\n    timeline && timeline.unschedule(this)\n    return this\n  }\n\n  // Run each initialise function in the runner if required\n  _initialise(running) {\n    // If we aren't running, we shouldn't initialise when not declarative\n    if (!running && !this._isDeclarative) return\n\n    // Loop through all of the initialisers\n    for (let i = 0, len = this._queue.length; i < len; ++i) {\n      // Get the current initialiser\n      const current = this._queue[i]\n\n      // Determine whether we need to initialise\n      const needsIt = this._isDeclarative || (!current.initialised && running)\n      running = !current.finished\n\n      // Call the initialiser if we need to\n      if (needsIt && running) {\n        current.initialiser.call(this)\n        current.initialised = true\n      }\n    }\n  }\n\n  // Save a morpher to the morpher list so that we can retarget it later\n  _rememberMorpher(method, morpher) {\n    this._history[method] = {\n      morpher: morpher,\n      caller: this._queue[this._queue.length - 1]\n    }\n\n    // We have to resume the timeline in case a controller\n    // is already done without being ever run\n    // This can happen when e.g. this is done:\n    //    anim = el.animate(new SVG.Spring)\n    // and later\n    //    anim.move(...)\n    if (this._isDeclarative) {\n      const timeline = this.timeline()\n      timeline && timeline.play()\n    }\n  }\n\n  // Try to set the target for a morpher if the morpher exists, otherwise\n  // Run each run function for the position or dt given\n  _run(positionOrDt) {\n    // Run all of the _queue directly\n    let allfinished = true\n    for (let i = 0, len = this._queue.length; i < len; ++i) {\n      // Get the current function to run\n      const current = this._queue[i]\n\n      // Run the function if its not finished, we keep track of the finished\n      // flag for the sake of declarative _queue\n      const converged = current.runner.call(this, positionOrDt)\n      current.finished = current.finished || converged === true\n      allfinished = allfinished && current.finished\n    }\n\n    // We report when all of the constructors are finished\n    return allfinished\n  }\n\n  // do nothing and return false\n  _tryRetarget(method, target, extra) {\n    if (this._history[method]) {\n      // if the last method wasn't even initialised, throw it away\n      if (!this._history[method].caller.initialised) {\n        const index = this._queue.indexOf(this._history[method].caller)\n        this._queue.splice(index, 1)\n        return false\n      }\n\n      // for the case of transformations, we use the special retarget function\n      // which has access to the outer scope\n      if (this._history[method].caller.retarget) {\n        this._history[method].caller.retarget.call(this, target, extra)\n        // for everything else a simple morpher change is sufficient\n      } else {\n        this._history[method].morpher.to(target)\n      }\n\n      this._history[method].caller.finished = false\n      const timeline = this.timeline()\n      timeline && timeline.play()\n      return true\n    }\n    return false\n  }\n}\n\nRunner.id = 0\n\nexport class FakeRunner {\n  constructor(transforms = new Matrix(), id = -1, done = true) {\n    this.transforms = transforms\n    this.id = id\n    this.done = done\n  }\n\n  clearTransformsFromQueue() {}\n}\n\nextend([Runner, FakeRunner], {\n  mergeWith(runner) {\n    return new FakeRunner(\n      runner.transforms.lmultiply(this.transforms),\n      runner.id\n    )\n  }\n})\n\n// FakeRunner.emptyRunner = new FakeRunner()\n\nconst lmultiply = (last, curr) => last.lmultiplyO(curr)\nconst getRunnerTransform = (runner) => runner.transforms\n\nfunction mergeTransforms() {\n  // Find the matrix to apply to the element and apply it\n  const runners = this._transformationRunners.runners\n  const netTransform = runners\n    .map(getRunnerTransform)\n    .reduce(lmultiply, new Matrix())\n\n  this.transform(netTransform)\n\n  this._transformationRunners.merge()\n\n  if (this._transformationRunners.length() === 1) {\n    this._frameId = null\n  }\n}\n\nexport class RunnerArray {\n  constructor() {\n    this.runners = []\n    this.ids = []\n  }\n\n  add(runner) {\n    if (this.runners.includes(runner)) return\n    const id = runner.id + 1\n\n    this.runners.push(runner)\n    this.ids.push(id)\n\n    return this\n  }\n\n  clearBefore(id) {\n    const deleteCnt = this.ids.indexOf(id + 1) || 1\n    this.ids.splice(0, deleteCnt, 0)\n    this.runners\n      .splice(0, deleteCnt, new FakeRunner())\n      .forEach((r) => r.clearTransformsFromQueue())\n    return this\n  }\n\n  edit(id, newRunner) {\n    const index = this.ids.indexOf(id + 1)\n    this.ids.splice(index, 1, id + 1)\n    this.runners.splice(index, 1, newRunner)\n    return this\n  }\n\n  getByID(id) {\n    return this.runners[this.ids.indexOf(id + 1)]\n  }\n\n  length() {\n    return this.ids.length\n  }\n\n  merge() {\n    let lastRunner = null\n    for (let i = 0; i < this.runners.length; ++i) {\n      const runner = this.runners[i]\n\n      const condition =\n        lastRunner &&\n        runner.done &&\n        lastRunner.done &&\n        // don't merge runner when persisted on timeline\n        (!runner._timeline ||\n          !runner._timeline._runnerIds.includes(runner.id)) &&\n        (!lastRunner._timeline ||\n          !lastRunner._timeline._runnerIds.includes(lastRunner.id))\n\n      if (condition) {\n        // the +1 happens in the function\n        this.remove(runner.id)\n        const newRunner = runner.mergeWith(lastRunner)\n        this.edit(lastRunner.id, newRunner)\n        lastRunner = newRunner\n        --i\n      } else {\n        lastRunner = runner\n      }\n    }\n\n    return this\n  }\n\n  remove(id) {\n    const index = this.ids.indexOf(id + 1)\n    this.ids.splice(index, 1)\n    this.runners.splice(index, 1)\n    return this\n  }\n}\n\nregisterMethods({\n  Element: {\n    animate(duration, delay, when) {\n      const o = Runner.sanitise(duration, delay, when)\n      const timeline = this.timeline()\n      return new Runner(o.duration)\n        .loop(o)\n        .element(this)\n        .timeline(timeline.play())\n        .schedule(o.delay, o.when)\n    },\n\n    delay(by, when) {\n      return this.animate(0, by, when)\n    },\n\n    // this function searches for all runners on the element and deletes the ones\n    // which run before the current one. This is because absolute transformations\n    // overwrite anything anyway so there is no need to waste time computing\n    // other runners\n    _clearTransformRunnersBefore(currentRunner) {\n      this._transformationRunners.clearBefore(currentRunner.id)\n    },\n\n    _currentTransform(current) {\n      return (\n        this._transformationRunners.runners\n          // we need the equal sign here to make sure, that also transformations\n          // on the same runner which execute before the current transformation are\n          // taken into account\n          .filter((runner) => runner.id <= current.id)\n          .map(getRunnerTransform)\n          .reduce(lmultiply, new Matrix())\n      )\n    },\n\n    _addRunner(runner) {\n      this._transformationRunners.add(runner)\n\n      // Make sure that the runner merge is executed at the very end of\n      // all Animator functions. That is why we use immediate here to execute\n      // the merge right after all frames are run\n      Animator.cancelImmediate(this._frameId)\n      this._frameId = Animator.immediate(mergeTransforms.bind(this))\n    },\n\n    _prepareRunner() {\n      if (this._frameId == null) {\n        this._transformationRunners = new RunnerArray().add(\n          new FakeRunner(new Matrix(this))\n        )\n      }\n    }\n  }\n})\n\n// Will output the elements from array A that are not in the array B\nconst difference = (a, b) => a.filter((x) => !b.includes(x))\n\nextend(Runner, {\n  attr(a, v) {\n    return this.styleAttr('attr', a, v)\n  },\n\n  // Add animatable styles\n  css(s, v) {\n    return this.styleAttr('css', s, v)\n  },\n\n  styleAttr(type, nameOrAttrs, val) {\n    if (typeof nameOrAttrs === 'string') {\n      return this.styleAttr(type, { [nameOrAttrs]: val })\n    }\n\n    let attrs = nameOrAttrs\n    if (this._tryRetarget(type, attrs)) return this\n\n    let morpher = new Morphable(this._stepper).to(attrs)\n    let keys = Object.keys(attrs)\n\n    this.queue(\n      function () {\n        morpher = morpher.from(this.element()[type](keys))\n      },\n      function (pos) {\n        this.element()[type](morpher.at(pos).valueOf())\n        return morpher.done()\n      },\n      function (newToAttrs) {\n        // Check if any new keys were added\n        const newKeys = Object.keys(newToAttrs)\n        const differences = difference(newKeys, keys)\n\n        // If their are new keys, initialize them and add them to morpher\n        if (differences.length) {\n          // Get the values\n          const addedFromAttrs = this.element()[type](differences)\n\n          // Get the already initialized values\n          const oldFromAttrs = new ObjectBag(morpher.from()).valueOf()\n\n          // Merge old and new\n          Object.assign(oldFromAttrs, addedFromAttrs)\n          morpher.from(oldFromAttrs)\n        }\n\n        // Get the object from the morpher\n        const oldToAttrs = new ObjectBag(morpher.to()).valueOf()\n\n        // Merge in new attributes\n        Object.assign(oldToAttrs, newToAttrs)\n\n        // Change morpher target\n        morpher.to(oldToAttrs)\n\n        // Make sure that we save the work we did so we don't need it to do again\n        keys = newKeys\n        attrs = newToAttrs\n      }\n    )\n\n    this._rememberMorpher(type, morpher)\n    return this\n  },\n\n  zoom(level, point) {\n    if (this._tryRetarget('zoom', level, point)) return this\n\n    let morpher = new Morphable(this._stepper).to(new SVGNumber(level))\n\n    this.queue(\n      function () {\n        morpher = morpher.from(this.element().zoom())\n      },\n      function (pos) {\n        this.element().zoom(morpher.at(pos), point)\n        return morpher.done()\n      },\n      function (newLevel, newPoint) {\n        point = newPoint\n        morpher.to(newLevel)\n      }\n    )\n\n    this._rememberMorpher('zoom', morpher)\n    return this\n  },\n\n  /**\n   ** absolute transformations\n   **/\n\n  //\n  // M v -----|-----(D M v = F v)------|----->  T v\n  //\n  // 1. define the final state (T) and decompose it (once)\n  //    t = [tx, ty, the, lam, sy, sx]\n  // 2. on every frame: pull the current state of all previous transforms\n  //    (M - m can change)\n  //   and then write this as m = [tx0, ty0, the0, lam0, sy0, sx0]\n  // 3. Find the interpolated matrix F(pos) = m + pos * (t - m)\n  //   - Note F(0) = M\n  //   - Note F(1) = T\n  // 4. Now you get the delta matrix as a result: D = F * inv(M)\n\n  transform(transforms, relative, affine) {\n    // If we have a declarative function, we should retarget it if possible\n    relative = transforms.relative || relative\n    if (\n      this._isDeclarative &&\n      !relative &&\n      this._tryRetarget('transform', transforms)\n    ) {\n      return this\n    }\n\n    // Parse the parameters\n    const isMatrix = Matrix.isMatrixLike(transforms)\n    affine =\n      transforms.affine != null\n        ? transforms.affine\n        : affine != null\n          ? affine\n          : !isMatrix\n\n    // Create a morpher and set its type\n    const morpher = new Morphable(this._stepper).type(\n      affine ? TransformBag : Matrix\n    )\n\n    let origin\n    let element\n    let current\n    let currentAngle\n    let startTransform\n\n    function setup() {\n      // make sure element and origin is defined\n      element = element || this.element()\n      origin = origin || getOrigin(transforms, element)\n\n      startTransform = new Matrix(relative ? undefined : element)\n\n      // add the runner to the element so it can merge transformations\n      element._addRunner(this)\n\n      // Deactivate all transforms that have run so far if we are absolute\n      if (!relative) {\n        element._clearTransformRunnersBefore(this)\n      }\n    }\n\n    function run(pos) {\n      // clear all other transforms before this in case something is saved\n      // on this runner. We are absolute. We dont need these!\n      if (!relative) this.clearTransform()\n\n      const { x, y } = new Point(origin).transform(\n        element._currentTransform(this)\n      )\n\n      let target = new Matrix({ ...transforms, origin: [x, y] })\n      let start = this._isDeclarative && current ? current : startTransform\n\n      if (affine) {\n        target = target.decompose(x, y)\n        start = start.decompose(x, y)\n\n        // Get the current and target angle as it was set\n        const rTarget = target.rotate\n        const rCurrent = start.rotate\n\n        // Figure out the shortest path to rotate directly\n        const possibilities = [rTarget - 360, rTarget, rTarget + 360]\n        const distances = possibilities.map((a) => Math.abs(a - rCurrent))\n        const shortest = Math.min(...distances)\n        const index = distances.indexOf(shortest)\n        target.rotate = possibilities[index]\n      }\n\n      if (relative) {\n        // we have to be careful here not to overwrite the rotation\n        // with the rotate method of Matrix\n        if (!isMatrix) {\n          target.rotate = transforms.rotate || 0\n        }\n        if (this._isDeclarative && currentAngle) {\n          start.rotate = currentAngle\n        }\n      }\n\n      morpher.from(start)\n      morpher.to(target)\n\n      const affineParameters = morpher.at(pos)\n      currentAngle = affineParameters.rotate\n      current = new Matrix(affineParameters)\n\n      this.addTransform(current)\n      element._addRunner(this)\n      return morpher.done()\n    }\n\n    function retarget(newTransforms) {\n      // only get a new origin if it changed since the last call\n      if (\n        (newTransforms.origin || 'center').toString() !==\n        (transforms.origin || 'center').toString()\n      ) {\n        origin = getOrigin(newTransforms, element)\n      }\n\n      // overwrite the old transformations with the new ones\n      transforms = { ...newTransforms, origin }\n    }\n\n    this.queue(setup, run, retarget, true)\n    this._isDeclarative && this._rememberMorpher('transform', morpher)\n    return this\n  },\n\n  // Animatable x-axis\n  x(x) {\n    return this._queueNumber('x', x)\n  },\n\n  // Animatable y-axis\n  y(y) {\n    return this._queueNumber('y', y)\n  },\n\n  ax(x) {\n    return this._queueNumber('ax', x)\n  },\n\n  ay(y) {\n    return this._queueNumber('ay', y)\n  },\n\n  dx(x = 0) {\n    return this._queueNumberDelta('x', x)\n  },\n\n  dy(y = 0) {\n    return this._queueNumberDelta('y', y)\n  },\n\n  dmove(x, y) {\n    return this.dx(x).dy(y)\n  },\n\n  _queueNumberDelta(method, to) {\n    to = new SVGNumber(to)\n\n    // Try to change the target if we have this method already registered\n    if (this._tryRetarget(method, to)) return this\n\n    // Make a morpher and queue the animation\n    const morpher = new Morphable(this._stepper).to(to)\n    let from = null\n    this.queue(\n      function () {\n        from = this.element()[method]()\n        morpher.from(from)\n        morpher.to(from + to)\n      },\n      function (pos) {\n        this.element()[method](morpher.at(pos))\n        return morpher.done()\n      },\n      function (newTo) {\n        morpher.to(from + new SVGNumber(newTo))\n      }\n    )\n\n    // Register the morpher so that if it is changed again, we can retarget it\n    this._rememberMorpher(method, morpher)\n    return this\n  },\n\n  _queueObject(method, to) {\n    // Try to change the target if we have this method already registered\n    if (this._tryRetarget(method, to)) return this\n\n    // Make a morpher and queue the animation\n    const morpher = new Morphable(this._stepper).to(to)\n    this.queue(\n      function () {\n        morpher.from(this.element()[method]())\n      },\n      function (pos) {\n        this.element()[method](morpher.at(pos))\n        return morpher.done()\n      }\n    )\n\n    // Register the morpher so that if it is changed again, we can retarget it\n    this._rememberMorpher(method, morpher)\n    return this\n  },\n\n  _queueNumber(method, value) {\n    return this._queueObject(method, new SVGNumber(value))\n  },\n\n  // Animatable center x-axis\n  cx(x) {\n    return this._queueNumber('cx', x)\n  },\n\n  // Animatable center y-axis\n  cy(y) {\n    return this._queueNumber('cy', y)\n  },\n\n  // Add animatable move\n  move(x, y) {\n    return this.x(x).y(y)\n  },\n\n  amove(x, y) {\n    return this.ax(x).ay(y)\n  },\n\n  // Add animatable center\n  center(x, y) {\n    return this.cx(x).cy(y)\n  },\n\n  // Add animatable size\n  size(width, height) {\n    // animate bbox based size for all other elements\n    let box\n\n    if (!width || !height) {\n      box = this._element.bbox()\n    }\n\n    if (!width) {\n      width = (box.width / box.height) * height\n    }\n\n    if (!height) {\n      height = (box.height / box.width) * width\n    }\n\n    return this.width(width).height(height)\n  },\n\n  // Add animatable width\n  width(width) {\n    return this._queueNumber('width', width)\n  },\n\n  // Add animatable height\n  height(height) {\n    return this._queueNumber('height', height)\n  },\n\n  // Add animatable plot\n  plot(a, b, c, d) {\n    // Lines can be plotted with 4 arguments\n    if (arguments.length === 4) {\n      return this.plot([a, b, c, d])\n    }\n\n    if (this._tryRetarget('plot', a)) return this\n\n    const morpher = new Morphable(this._stepper)\n      .type(this._element.MorphArray)\n      .to(a)\n\n    this.queue(\n      function () {\n        morpher.from(this._element.array())\n      },\n      function (pos) {\n        this._element.plot(morpher.at(pos))\n        return morpher.done()\n      }\n    )\n\n    this._rememberMorpher('plot', morpher)\n    return this\n  },\n\n  // Add leading method\n  leading(value) {\n    return this._queueNumber('leading', value)\n  },\n\n  // Add animatable viewbox\n  viewbox(x, y, width, height) {\n    return this._queueObject('viewbox', new Box(x, y, width, height))\n  },\n\n  update(o) {\n    if (typeof o !== 'object') {\n      return this.update({\n        offset: arguments[0],\n        color: arguments[1],\n        opacity: arguments[2]\n      })\n    }\n\n    if (o.opacity != null) this.attr('stop-opacity', o.opacity)\n    if (o.color != null) this.attr('stop-color', o.color)\n    if (o.offset != null) this.attr('offset', o.offset)\n\n    return this\n  }\n})\n\nextend(Runner, { rx, ry, from, to })\nregister(Runner, 'Runner')\n", "import {\n  adopt,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { svg, xlink, xmlns } from '../modules/core/namespaces.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\nimport Defs from './Defs.js'\nimport { globals } from '../utils/window.js'\n\nexport default class Svg extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('svg', node), attrs)\n    this.namespace()\n  }\n\n  // Creates and returns defs element\n  defs() {\n    if (!this.isRoot()) return this.root().defs()\n\n    return adopt(this.node.querySelector('defs')) || this.put(new Defs())\n  }\n\n  isRoot() {\n    return (\n      !this.node.parentNode ||\n      (!(this.node.parentNode instanceof globals.window.SVGElement) &&\n        this.node.parentNode.nodeName !== '#document-fragment')\n    )\n  }\n\n  // Add namespaces\n  namespace() {\n    if (!this.isRoot()) return this.root().namespace()\n    return this.attr({ xmlns: svg, version: '1.1' }).attr(\n      'xmlns:xlink',\n      xlink,\n      xmlns\n    )\n  }\n\n  removeNamespace() {\n    return this.attr({ xmlns: null, version: null })\n      .attr('xmlns:xlink', null, xmlns)\n      .attr('xmlns:svgjs', null, xmlns)\n  }\n\n  // Check if this is a root svg\n  // If not, call root() from this element\n  root() {\n    if (this.isRoot()) return this\n    return super.root()\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create nested svg document\n    nested: wrapWithAttrCheck(function () {\n      return this.put(new Svg())\n    })\n  }\n})\n\nregister(Svg, 'Svg', true)\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\n\nexport default class Symbol extends Container {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('symbol', node), attrs)\n  }\n}\n\nregisterMethods({\n  Container: {\n    symbol: wrapWithAttrCheck(function () {\n      return this.put(new Symbol())\n    })\n  }\n})\n\nregister(Symbol, 'Symbol')\n", "import { globals } from '../../utils/window.js'\n\n// Create plain text node\nexport function plain(text) {\n  // clear if build mode is disabled\n  if (this._build === false) {\n    this.clear()\n  }\n\n  // create text node\n  this.node.appendChild(globals.document.createTextNode(text))\n\n  return this\n}\n\n// Get length of text element\nexport function length() {\n  return this.node.getComputedTextLength()\n}\n\n// Move over x-axis\n// Text is moved by its bounding box\n// text-anchor does NOT matter\nexport function x(x, box = this.bbox()) {\n  if (x == null) {\n    return box.x\n  }\n\n  return this.attr('x', this.attr('x') + x - box.x)\n}\n\n// Move over y-axis\nexport function y(y, box = this.bbox()) {\n  if (y == null) {\n    return box.y\n  }\n\n  return this.attr('y', this.attr('y') + y - box.y)\n}\n\nexport function move(x, y, box = this.bbox()) {\n  return this.x(x, box).y(y, box)\n}\n\n// Move center over x-axis\nexport function cx(x, box = this.bbox()) {\n  if (x == null) {\n    return box.cx\n  }\n\n  return this.attr('x', this.attr('x') + x - box.cx)\n}\n\n// Move center over y-axis\nexport function cy(y, box = this.bbox()) {\n  if (y == null) {\n    return box.cy\n  }\n\n  return this.attr('y', this.attr('y') + y - box.cy)\n}\n\nexport function center(x, y, box = this.bbox()) {\n  return this.cx(x, box).cy(y, box)\n}\n\nexport function ax(x) {\n  return this.attr('x', x)\n}\n\nexport function ay(y) {\n  return this.attr('y', y)\n}\n\nexport function amove(x, y) {\n  return this.ax(x).ay(y)\n}\n\n// Enable / disable build mode\nexport function build(build) {\n  this._build = !!build\n  return this\n}\n", "import {\n  adopt,\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Shape from './Shape.js'\nimport { globals } from '../utils/window.js'\nimport * as textable from '../modules/core/textable.js'\nimport { isDescriptive, writeDataToDom } from '../utils/utils.js'\n\nexport default class Text extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('text', node), attrs)\n\n    this.dom.leading = this.dom.leading ?? new SVGNumber(1.3) // store leading value for rebuilding\n    this._rebuild = true // enable automatic updating of dy values\n    this._build = false // disable build mode for adding multiple lines\n  }\n\n  // Set / get leading\n  leading(value) {\n    // act as getter\n    if (value == null) {\n      return this.dom.leading\n    }\n\n    // act as setter\n    this.dom.leading = new SVGNumber(value)\n\n    return this.rebuild()\n  }\n\n  // Rebuild appearance type\n  rebuild(rebuild) {\n    // store new rebuild flag if given\n    if (typeof rebuild === 'boolean') {\n      this._rebuild = rebuild\n    }\n\n    // define position of all lines\n    if (this._rebuild) {\n      const self = this\n      let blankLineOffset = 0\n      const leading = this.dom.leading\n\n      this.each(function (i) {\n        if (isDescriptive(this.node)) return\n\n        const fontSize = globals.window\n          .getComputedStyle(this.node)\n          .getPropertyValue('font-size')\n\n        const dy = leading * new SVGNumber(fontSize)\n\n        if (this.dom.newLined) {\n          this.attr('x', self.attr('x'))\n\n          if (this.text() === '\\n') {\n            blankLineOffset += dy\n          } else {\n            this.attr('dy', i ? dy + blankLineOffset : 0)\n            blankLineOffset = 0\n          }\n        }\n      })\n\n      this.fire('rebuild')\n    }\n\n    return this\n  }\n\n  // overwrite method from parent to set data properly\n  setData(o) {\n    this.dom = o\n    this.dom.leading = new SVGNumber(o.leading || 1.3)\n    return this\n  }\n\n  writeDataToDom() {\n    writeDataToDom(this, this.dom, { leading: 1.3 })\n    return this\n  }\n\n  // Set the text content\n  text(text) {\n    // act as getter\n    if (text === undefined) {\n      const children = this.node.childNodes\n      let firstLine = 0\n      text = ''\n\n      for (let i = 0, len = children.length; i < len; ++i) {\n        // skip textPaths - they are no lines\n        if (children[i].nodeName === 'textPath' || isDescriptive(children[i])) {\n          if (i === 0) firstLine = i + 1\n          continue\n        }\n\n        // add newline if its not the first child and newLined is set to true\n        if (\n          i !== firstLine &&\n          children[i].nodeType !== 3 &&\n          adopt(children[i]).dom.newLined === true\n        ) {\n          text += '\\n'\n        }\n\n        // add content of this node\n        text += children[i].textContent\n      }\n\n      return text\n    }\n\n    // remove existing content\n    this.clear().build(true)\n\n    if (typeof text === 'function') {\n      // call block\n      text.call(this, this)\n    } else {\n      // store text and make sure text is not blank\n      text = (text + '').split('\\n')\n\n      // build new lines\n      for (let j = 0, jl = text.length; j < jl; j++) {\n        this.newLine(text[j])\n      }\n    }\n\n    // disable build mode and rebuild lines\n    return this.build(false).rebuild()\n  }\n}\n\nextend(Text, textable)\n\nregisterMethods({\n  Container: {\n    // Create text element\n    text: wrapWithAttrCheck(function (text = '') {\n      return this.put(new Text()).text(text)\n    }),\n\n    // Create plain text element\n    plain: wrapWithAttrCheck(function (text = '') {\n      return this.put(new Text()).plain(text)\n    })\n  }\n})\n\nregister(Text, 'Text')\n", "import {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { globals } from '../utils/window.js'\nimport { registerMethods } from '../utils/methods.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Shape from './Shape.js'\nimport Text from './Text.js'\nimport * as textable from '../modules/core/textable.js'\n\nexport default class Tspan extends Shape {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('tspan', node), attrs)\n    this._build = false // disable build mode for adding multiple lines\n  }\n\n  // Shortcut dx\n  dx(dx) {\n    return this.attr('dx', dx)\n  }\n\n  // Shortcut dy\n  dy(dy) {\n    return this.attr('dy', dy)\n  }\n\n  // Create new line\n  newLine() {\n    // mark new line\n    this.dom.newLined = true\n\n    // fetch parent\n    const text = this.parent()\n\n    // early return in case we are not in a text element\n    if (!(text instanceof Text)) {\n      return this\n    }\n\n    const i = text.index(this)\n\n    const fontSize = globals.window\n      .getComputedStyle(this.node)\n      .getPropertyValue('font-size')\n    const dy = text.dom.leading * new SVGNumber(fontSize)\n\n    // apply new position\n    return this.dy(i ? dy : 0).attr('x', text.x())\n  }\n\n  // Set text content\n  text(text) {\n    if (text == null)\n      return this.node.textContent + (this.dom.newLined ? '\\n' : '')\n\n    if (typeof text === 'function') {\n      this.clear().build(true)\n      text.call(this, this)\n      this.build(false)\n    } else {\n      this.plain(text)\n    }\n\n    return this\n  }\n}\n\nextend(Tspan, textable)\n\nregisterMethods({\n  Tspan: {\n    tspan: wrapWithAttrCheck(function (text = '') {\n      const tspan = new Tspan()\n\n      // clear if build mode is disabled\n      if (!this._build) {\n        this.clear()\n      }\n\n      // add new tspan\n      return this.put(tspan).text(text)\n    })\n  },\n  Text: {\n    newLine: function (text = '') {\n      return this.tspan(text).newLine()\n    }\n  }\n})\n\nregister(Tspan, 'Tspan')\n", "import { cx, cy, height, width, x, y } from '../modules/core/circled.js'\nimport {\n  extend,\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport Shape from './Shape.js'\n\nexport default class Circle extends Shape {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('circle', node), attrs)\n  }\n\n  radius(r) {\n    return this.attr('r', r)\n  }\n\n  // Radius x value\n  rx(rx) {\n    return this.attr('r', rx)\n  }\n\n  // Alias radius x value\n  ry(ry) {\n    return this.rx(ry)\n  }\n\n  size(size) {\n    return this.radius(new SVGNumber(size).divide(2))\n  }\n}\n\nextend(Circle, { x, y, cx, cy, width, height })\n\nregisterMethods({\n  Container: {\n    // Create circle element\n    circle: wrapWithAttrCheck(function (size = 0) {\n      return this.put(new Circle()).size(size).move(0, 0)\n    })\n  }\n})\n\nregister(Circle, 'Circle')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\nimport baseFind from '../modules/core/selector.js'\n\nexport default class ClipPath extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('clipPath', node), attrs)\n  }\n\n  // Unclip all clipped elements and remove itself\n  remove() {\n    // unclip all targets\n    this.targets().forEach(function (el) {\n      el.unclip()\n    })\n\n    // remove clipPath from parent\n    return super.remove()\n  }\n\n  targets() {\n    return baseFind('svg [clip-path*=' + this.id() + ']')\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create clipping element\n    clip: wrapWithAttrCheck(function () {\n      return this.defs().put(new ClipPath())\n    })\n  },\n  Element: {\n    // Distribute clipPath to svg element\n    clipper() {\n      return this.reference('clip-path')\n    },\n\n    clipWith(element) {\n      // use given clip or create a new one\n      const clipper =\n        element instanceof ClipPath\n          ? element\n          : this.parent().clip().add(element)\n\n      // apply mask\n      return this.attr('clip-path', 'url(#' + clipper.id() + ')')\n    },\n\n    // Unclip element\n    unclip() {\n      return this.attr('clip-path', null)\n    }\n  }\n})\n\nregister(ClipPath, 'ClipPath')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Element from './Element.js'\n\nexport default class ForeignObject extends Element {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('foreignObject', node), attrs)\n  }\n}\n\nregisterMethods({\n  Container: {\n    foreignObject: wrapWithAttrCheck(function (width, height) {\n      return this.put(new ForeignObject()).size(width, height)\n    })\n  }\n})\n\nregister(ForeignObject, 'ForeignObject')\n", "import Matrix from '../../types/Matrix.js'\nimport Point from '../../types/Point.js'\nimport Box from '../../types/Box.js'\nimport { proportionalSize } from '../../utils/utils.js'\nimport { getWindow } from '../../utils/window.js'\n\nexport function dmove(dx, dy) {\n  this.children().forEach((child) => {\n    let bbox\n\n    // We have to wrap this for elements that dont have a bbox\n    // e.g. title and other descriptive elements\n    try {\n      // Get the childs bbox\n      // Bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1905039\n      // Because bbox for nested svgs returns the contents bbox in the coordinate space of the svg itself (weird!), we cant use bbox for svgs\n      // Therefore we have to use getBoundingClientRect. But THAT is broken (as explained in the bug).\n      // Funnily enough the broken behavior would work for us but that breaks it in chrome\n      // So we have to replicate the broken behavior of FF by just reading the attributes of the svg itself\n      bbox =\n        child.node instanceof getWindow().SVGSVGElement\n          ? new Box(child.attr(['x', 'y', 'width', 'height']))\n          : child.bbox()\n    } catch (e) {\n      return\n    }\n\n    // Get childs matrix\n    const m = new Matrix(child)\n    // Translate childs matrix by amount and\n    // transform it back into parents space\n    const matrix = m.translate(dx, dy).transform(m.inverse())\n    // Calculate new x and y from old box\n    const p = new Point(bbox.x, bbox.y).transform(matrix)\n    // Move element\n    child.move(p.x, p.y)\n  })\n\n  return this\n}\n\nexport function dx(dx) {\n  return this.dmove(dx, 0)\n}\n\nexport function dy(dy) {\n  return this.dmove(0, dy)\n}\n\nexport function height(height, box = this.bbox()) {\n  if (height == null) return box.height\n  return this.size(box.width, height, box)\n}\n\nexport function move(x = 0, y = 0, box = this.bbox()) {\n  const dx = x - box.x\n  const dy = y - box.y\n\n  return this.dmove(dx, dy)\n}\n\nexport function size(width, height, box = this.bbox()) {\n  const p = proportionalSize(this, width, height, box)\n  const scaleX = p.width / box.width\n  const scaleY = p.height / box.height\n\n  this.children().forEach((child) => {\n    const o = new Point(box).transform(new Matrix(child).inverse())\n    child.scale(scaleX, scaleY, o.x, o.y)\n  })\n\n  return this\n}\n\nexport function width(width, box = this.bbox()) {\n  if (width == null) return box.width\n  return this.size(width, box.height, box)\n}\n\nexport function x(x, box = this.bbox()) {\n  if (x == null) return box.x\n  return this.move(x, box.y, box)\n}\n\nexport function y(y, box = this.bbox()) {\n  if (y == null) return box.y\n  return this.move(box.x, y, box)\n}\n", "import {\n  nodeOrNew,\n  register,\n  wrapWithAttrCheck,\n  extend\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\nimport * as containerGeometry from '../modules/core/containerGeometry.js'\n\nexport default class G extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('g', node), attrs)\n  }\n}\n\nextend(G, containerGeometry)\n\nregisterMethods({\n  Container: {\n    // Create a group element\n    group: wrapWithAttrCheck(function () {\n      return this.put(new G())\n    })\n  }\n})\n\nregister(G, 'G')\n", "import {\n  nodeOr<PERSON><PERSON>,\n  register,\n  wrapWithAttrCheck,\n  extend\n} from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { xlink } from '../modules/core/namespaces.js'\nimport Container from './Container.js'\nimport * as containerGeometry from '../modules/core/containerGeometry.js'\n\nexport default class A extends Container {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('a', node), attrs)\n  }\n\n  // Link target attribute\n  target(target) {\n    return this.attr('target', target)\n  }\n\n  // Link url\n  to(url) {\n    return this.attr('href', url, xlink)\n  }\n}\n\nextend(A, containerGeometry)\n\nregisterMethods({\n  Container: {\n    // Create a hyperlink element\n    link: wrapWithAttrCheck(function (url) {\n      return this.put(new A()).to(url)\n    })\n  },\n  Element: {\n    unlink() {\n      const link = this.linker()\n\n      if (!link) return this\n\n      const parent = link.parent()\n\n      if (!parent) {\n        return this.remove()\n      }\n\n      const index = parent.index(link)\n      parent.add(this, index)\n\n      link.remove()\n      return this\n    },\n    linkTo(url) {\n      // reuse old link if possible\n      let link = this.linker()\n\n      if (!link) {\n        link = new A()\n        this.wrap(link)\n      }\n\n      if (typeof url === 'function') {\n        url.call(link, link)\n      } else {\n        link.to(url)\n      }\n\n      return this\n    },\n    linker() {\n      const link = this.parent()\n      if (link && link.node.nodeName.toLowerCase() === 'a') {\n        return link\n      }\n\n      return null\n    }\n  }\n})\n\nregister(A, 'A')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport Container from './Container.js'\nimport baseFind from '../modules/core/selector.js'\n\nexport default class Mask extends Container {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('mask', node), attrs)\n  }\n\n  // Unmask all masked elements and remove itself\n  remove() {\n    // unmask all targets\n    this.targets().forEach(function (el) {\n      el.unmask()\n    })\n\n    // remove mask from parent\n    return super.remove()\n  }\n\n  targets() {\n    return baseFind('svg [mask*=' + this.id() + ']')\n  }\n}\n\nregisterMethods({\n  Container: {\n    mask: wrapWithAttrCheck(function () {\n      return this.defs().put(new Mask())\n    })\n  },\n  Element: {\n    // Distribute mask to svg element\n    masker() {\n      return this.reference('mask')\n    },\n\n    maskWith(element) {\n      // use given mask or create a new one\n      const masker =\n        element instanceof Mask ? element : this.parent().mask().add(element)\n\n      // apply mask\n      return this.attr('mask', 'url(#' + masker.id() + ')')\n    },\n\n    // Unmask element\n    unmask() {\n      return this.attr('mask', null)\n    }\n  }\n})\n\nregister(Mask, 'Mask')\n", "import { nodeOrNew, register } from '../utils/adopter.js'\nimport Element from './Element.js'\nimport SVGNumber from '../types/SVGNumber.js'\nimport { registerMethods } from '../utils/methods.js'\n\nexport default class Stop extends Element {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('stop', node), attrs)\n  }\n\n  // add color stops\n  update(o) {\n    if (typeof o === 'number' || o instanceof SVGNumber) {\n      o = {\n        offset: arguments[0],\n        color: arguments[1],\n        opacity: arguments[2]\n      }\n    }\n\n    // set attributes\n    if (o.opacity != null) this.attr('stop-opacity', o.opacity)\n    if (o.color != null) this.attr('stop-color', o.color)\n    if (o.offset != null) this.attr('offset', new SVGNumber(o.offset))\n\n    return this\n  }\n}\n\nregisterMethods({\n  Gradient: {\n    // Add a color stop\n    stop: function (offset, color, opacity) {\n      return this.put(new Stop()).update(offset, color, opacity)\n    }\n  }\n})\n\nregister(Stop, 'Stop')\n", "import { nodeOrNew, register } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { unCamelCase } from '../utils/utils.js'\nimport Element from './Element.js'\n\nfunction cssRule(selector, rule) {\n  if (!selector) return ''\n  if (!rule) return selector\n\n  let ret = selector + '{'\n\n  for (const i in rule) {\n    ret += unCamelCase(i) + ':' + rule[i] + ';'\n  }\n\n  ret += '}'\n\n  return ret\n}\n\nexport default class Style extends Element {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('style', node), attrs)\n  }\n\n  addText(w = '') {\n    this.node.textContent += w\n    return this\n  }\n\n  font(name, src, params = {}) {\n    return this.rule('@font-face', {\n      fontFamily: name,\n      src: src,\n      ...params\n    })\n  }\n\n  rule(selector, obj) {\n    return this.addText(cssRule(selector, obj))\n  }\n}\n\nregisterMethods('Dom', {\n  style(selector, obj) {\n    return this.put(new Style()).rule(selector, obj)\n  },\n  fontface(name, src, params) {\n    return this.put(new Style()).font(name, src, params)\n  }\n})\n\nregister(Style, 'Style')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { xlink } from '../modules/core/namespaces.js'\nimport Path from './Path.js'\nimport PathArray from '../types/PathArray.js'\nimport Text from './Text.js'\nimport baseFind from '../modules/core/selector.js'\n\nexport default class TextPath extends Text {\n  // Initialize node\n  constructor(node, attrs = node) {\n    super(nodeOrNew('textPath', node), attrs)\n  }\n\n  // return the array of the path track element\n  array() {\n    const track = this.track()\n\n    return track ? track.array() : null\n  }\n\n  // Plot path if any\n  plot(d) {\n    const track = this.track()\n    let pathArray = null\n\n    if (track) {\n      pathArray = track.plot(d)\n    }\n\n    return d == null ? pathArray : this\n  }\n\n  // Get the path element\n  track() {\n    return this.reference('href')\n  }\n}\n\nregisterMethods({\n  Container: {\n    textPath: wrapWithAttrCheck(function (text, path) {\n      // Convert text to instance if needed\n      if (!(text instanceof Text)) {\n        text = this.text(text)\n      }\n\n      return text.path(path)\n    })\n  },\n  Text: {\n    // Create path for text to run on\n    path: wrapWithAttrCheck(function (track, importNodes = true) {\n      const textPath = new TextPath()\n\n      // if track is a path, reuse it\n      if (!(track instanceof Path)) {\n        // create path element\n        track = this.defs().path(track)\n      }\n\n      // link textPath to path and add content\n      textPath.attr('href', '#' + track, xlink)\n\n      // Transplant all nodes from text to textPath\n      let node\n      if (importNodes) {\n        while ((node = this.node.firstChild)) {\n          textPath.node.appendChild(node)\n        }\n      }\n\n      // add textPath element as child node and return textPath\n      return this.put(textPath)\n    }),\n\n    // Get the textPath children\n    textPath() {\n      return this.findOne('textPath')\n    }\n  },\n  Path: {\n    // creates a textPath from this path\n    text: wrapWithAttrCheck(function (text) {\n      // Convert text to instance if needed\n      if (!(text instanceof Text)) {\n        text = new Text().addTo(this.parent()).text(text)\n      }\n\n      // Create textPath from text and path and return\n      return text.path(this)\n    }),\n\n    targets() {\n      return baseFind('svg textPath').filter((node) => {\n        return (node.attr('href') || '').includes(this.id())\n      })\n\n      // Does not work in IE11. Use when IE support is dropped\n      // return baseFind('svg textPath[*|href*=' + this.id() + ']')\n    }\n  }\n})\n\nTextPath.prototype.MorphArray = PathArray\nregister(TextPath, 'TextPath')\n", "import { nodeOrNew, register, wrapWithAttrCheck } from '../utils/adopter.js'\nimport { registerMethods } from '../utils/methods.js'\nimport { xlink } from '../modules/core/namespaces.js'\nimport Shape from './Shape.js'\n\nexport default class Use extends Shape {\n  constructor(node, attrs = node) {\n    super(nodeOrNew('use', node), attrs)\n  }\n\n  // Use element as a reference\n  use(element, file) {\n    // Set lined element\n    return this.attr('href', (file || '') + '#' + element, xlink)\n  }\n}\n\nregisterMethods({\n  Container: {\n    // Create a use element\n    use: wrapWithAttrCheck(function (element, file) {\n      return this.put(new Use()).use(element, file)\n    })\n  }\n})\n\nregister(Use, 'Use')\n", "/* Optional Modules */\nimport './modules/optional/arrange.js'\nimport './modules/optional/class.js'\nimport './modules/optional/css.js'\nimport './modules/optional/data.js'\nimport './modules/optional/memory.js'\nimport './modules/optional/sugar.js'\nimport './modules/optional/transform.js'\n\nimport { extend, makeInstance } from './utils/adopter.js'\nimport { getMethodNames, getMethodsFor } from './utils/methods.js'\nimport Box from './types/Box.js'\nimport Color from './types/Color.js'\nimport Container from './elements/Container.js'\nimport Defs from './elements/Defs.js'\nimport Dom from './elements/Dom.js'\nimport Element from './elements/Element.js'\nimport Ellipse from './elements/Ellipse.js'\nimport EventTarget from './types/EventTarget.js'\nimport Fragment from './elements/Fragment.js'\nimport Gradient from './elements/Gradient.js'\nimport Image from './elements/Image.js'\nimport Line from './elements/Line.js'\nimport List from './types/List.js'\nimport Marker from './elements/Marker.js'\nimport Matrix from './types/Matrix.js'\nimport Morphable, {\n  NonMorphable,\n  ObjectBag,\n  TransformBag,\n  makeMorphable,\n  registerMorphableType\n} from './animation/Morphable.js'\nimport Path from './elements/Path.js'\nimport PathArray from './types/PathArray.js'\nimport Pattern from './elements/Pattern.js'\nimport PointArray from './types/PointArray.js'\nimport Point from './types/Point.js'\nimport Polygon from './elements/Polygon.js'\nimport Polyline from './elements/Polyline.js'\nimport Rect from './elements/Rect.js'\nimport Runner from './animation/Runner.js'\nimport SVGArray from './types/SVGArray.js'\nimport SVGNumber from './types/SVGNumber.js'\nimport Shape from './elements/Shape.js'\nimport Svg from './elements/Svg.js'\nimport Symbol from './elements/Symbol.js'\nimport Text from './elements/Text.js'\nimport Tspan from './elements/Tspan.js'\nimport * as defaults from './modules/core/defaults.js'\nimport * as utils from './utils/utils.js'\nimport * as namespaces from './modules/core/namespaces.js'\nimport * as regex from './modules/core/regex.js'\n\nexport {\n  Morphable,\n  registerMorphableType,\n  makeMorphable,\n  TransformBag,\n  ObjectBag,\n  NonMorphable\n}\n\nexport { defaults, utils, namespaces, regex }\nexport const SVG = makeInstance\nexport { default as parser } from './modules/core/parser.js'\nexport { default as find } from './modules/core/selector.js'\nexport * from './modules/core/event.js'\nexport * from './utils/adopter.js'\nexport {\n  getWindow,\n  registerWindow,\n  restoreWindow,\n  saveWindow,\n  withWindow\n} from './utils/window.js'\n\n/* Animation Modules */\nexport { default as Animator } from './animation/Animator.js'\nexport {\n  Controller,\n  Ease,\n  PID,\n  Spring,\n  easing\n} from './animation/Controller.js'\nexport { default as Queue } from './animation/Queue.js'\nexport { default as Runner } from './animation/Runner.js'\nexport { default as Timeline } from './animation/Timeline.js'\n\n/* Types */\nexport { default as Array } from './types/SVGArray.js'\nexport { default as Box } from './types/Box.js'\nexport { default as Color } from './types/Color.js'\nexport { default as EventTarget } from './types/EventTarget.js'\nexport { default as Matrix } from './types/Matrix.js'\nexport { default as Number } from './types/SVGNumber.js'\nexport { default as PathArray } from './types/PathArray.js'\nexport { default as Point } from './types/Point.js'\nexport { default as PointArray } from './types/PointArray.js'\nexport { default as List } from './types/List.js'\n\n/* Elements */\nexport { default as Circle } from './elements/Circle.js'\nexport { default as ClipPath } from './elements/ClipPath.js'\nexport { default as Container } from './elements/Container.js'\nexport { default as Defs } from './elements/Defs.js'\nexport { default as Dom } from './elements/Dom.js'\nexport { default as Element } from './elements/Element.js'\nexport { default as Ellipse } from './elements/Ellipse.js'\nexport { default as ForeignObject } from './elements/ForeignObject.js'\nexport { default as Fragment } from './elements/Fragment.js'\nexport { default as Gradient } from './elements/Gradient.js'\nexport { default as G } from './elements/G.js'\nexport { default as A } from './elements/A.js'\nexport { default as Image } from './elements/Image.js'\nexport { default as Line } from './elements/Line.js'\nexport { default as Marker } from './elements/Marker.js'\nexport { default as Mask } from './elements/Mask.js'\nexport { default as Path } from './elements/Path.js'\nexport { default as Pattern } from './elements/Pattern.js'\nexport { default as Polygon } from './elements/Polygon.js'\nexport { default as Polyline } from './elements/Polyline.js'\nexport { default as Rect } from './elements/Rect.js'\nexport { default as Shape } from './elements/Shape.js'\nexport { default as Stop } from './elements/Stop.js'\nexport { default as Style } from './elements/Style.js'\nexport { default as Svg } from './elements/Svg.js'\nexport { default as Symbol } from './elements/Symbol.js'\nexport { default as Text } from './elements/Text.js'\nexport { default as TextPath } from './elements/TextPath.js'\nexport { default as Tspan } from './elements/Tspan.js'\nexport { default as Use } from './elements/Use.js'\n\nextend([Svg, Symbol, Image, Pattern, Marker], getMethodsFor('viewbox'))\n\nextend([Line, Polyline, Polygon, Path], getMethodsFor('marker'))\n\nextend(Text, getMethodsFor('Text'))\nextend(Path, getMethodsFor('Path'))\n\nextend(Defs, getMethodsFor('Defs'))\n\nextend([Text, Tspan], getMethodsFor('Tspan'))\n\nextend([Rect, Ellipse, Gradient, Runner], getMethodsFor('radius'))\n\nextend(EventTarget, getMethodsFor('EventTarget'))\nextend(Dom, getMethodsFor('Dom'))\nextend(Element, getMethodsFor('Element'))\nextend(Shape, getMethodsFor('Shape'))\nextend([Container, Fragment], getMethodsFor('Container'))\nextend(Gradient, getMethodsFor('Gradient'))\n\nextend(Runner, getMethodsFor('Runner'))\n\nList.extend(getMethodNames())\n\nregisterMorphableType([\n  SVGNumber,\n  Color,\n  Box,\n  Matrix,\n  SVGArray,\n  PointArray,\n  PathArray,\n  Point\n])\n\nmakeMorphable()\n"], "mappings": ";;;;;AAAA,IAAM,UAAU,CAAC;AACjB,IAAM,QAAQ,CAAC;AAER,SAAS,gBAAgB,MAAM,GAAG;AACvC,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,eAAW,SAAS,MAAM;AACxB,sBAAgB,OAAO,CAAC;AAAA,IAC1B;AACA;AAAA,EACF;AAEA,MAAI,OAAO,SAAS,UAAU;AAC5B,eAAW,SAAS,MAAM;AACxB,sBAAgB,OAAO,KAAK,KAAK,CAAC;AAAA,IACpC;AACA;AAAA,EACF;AAEA,iBAAe,OAAO,oBAAoB,CAAC,CAAC;AAC5C,UAAQ,IAAI,IAAI,OAAO,OAAO,QAAQ,IAAI,KAAK,CAAC,GAAG,CAAC;AACtD;AAEO,SAAS,cAAc,MAAM;AAClC,SAAO,QAAQ,IAAI,KAAK,CAAC;AAC3B;AAEO,SAAS,iBAAiB;AAC/B,SAAO,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC;AAC3B;AAEO,SAAS,eAAe,QAAQ;AACrC,QAAM,KAAK,GAAG,MAAM;AACtB;;;AChCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACO,SAAS,IAAIA,QAAO,OAAO;AAChC,MAAI;AACJ,QAAM,KAAKA,OAAM;AACjB,QAAM,SAAS,CAAC;AAEhB,OAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,WAAO,KAAK,MAAMA,OAAM,CAAC,CAAC,CAAC;AAAA,EAC7B;AAEA,SAAO;AACT;AAGO,SAAS,OAAOA,QAAO,OAAO;AACnC,MAAI;AACJ,QAAM,KAAKA,OAAM;AACjB,QAAM,SAAS,CAAC;AAEhB,OAAK,IAAI,GAAG,IAAI,IAAI,KAAK;AACvB,QAAI,MAAMA,OAAM,CAAC,CAAC,GAAG;AACnB,aAAO,KAAKA,OAAM,CAAC,CAAC;AAAA,IACtB;AAAA,EACF;AAEA,SAAO;AACT;AAGO,SAAS,QAAQ,GAAG;AACzB,SAAS,IAAI,MAAO,KAAK,KAAM;AACjC;AAGO,SAAS,QAAQ,GAAG;AACzB,SAAS,IAAI,MAAO,KAAK,KAAM;AACjC;AAGO,SAAS,YAAY,GAAG;AAC7B,SAAO,EAAE,QAAQ,YAAY,SAAU,GAAG,GAAG;AAC3C,WAAO,MAAM,EAAE,YAAY;AAAA,EAC7B,CAAC;AACH;AAGO,SAAS,WAAW,GAAG;AAC5B,SAAO,EAAE,OAAO,CAAC,EAAE,YAAY,IAAI,EAAE,MAAM,CAAC;AAC9C;AAGO,SAAS,iBAAiB,SAASC,QAAOC,SAAQ,KAAK;AAC5D,MAAID,UAAS,QAAQC,WAAU,MAAM;AACnC,UAAM,OAAO,QAAQ,KAAK;AAE1B,QAAID,UAAS,MAAM;AACjB,MAAAA,SAAS,IAAI,QAAQ,IAAI,SAAUC;AAAA,IACrC,WAAWA,WAAU,MAAM;AACzB,MAAAA,UAAU,IAAI,SAAS,IAAI,QAASD;AAAA,IACtC;AAAA,EACF;AAEA,SAAO;AAAA,IACL,OAAOA;AAAA,IACP,QAAQC;AAAA,EACV;AACF;AAOO,SAAS,UAAU,GAAG,SAAS;AACpC,QAAM,SAAS,EAAE;AAEjB,MAAI,KAAK,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,WAAW,OAAO,EAAE,UAAU;AAC/D,MAAI,KAAK,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,WAAW,OAAO,EAAE,UAAU;AAG/D,MAAI,UAAU,MAAM;AAClB;AAAC,KAAC,IAAI,EAAE,IAAI,MAAM,QAAQ,MAAM,IAC5B,SACA,OAAO,WAAW,WAChB,CAAC,OAAO,GAAG,OAAO,CAAC,IACnB,CAAC,QAAQ,MAAM;AAAA,EACvB;AAGA,QAAM,QAAQ,OAAO,OAAO;AAC5B,QAAM,QAAQ,OAAO,OAAO;AAC5B,MAAI,SAAS,OAAO;AAClB,UAAM,EAAE,QAAAA,SAAQ,OAAAD,QAAO,GAAAE,IAAG,GAAAC,GAAE,IAAI,QAAQ,KAAK;AAG7C,QAAI,OAAO;AACT,WAAK,GAAG,SAAS,MAAM,IACnBD,KACA,GAAG,SAAS,OAAO,IACjBA,KAAIF,SACJE,KAAIF,SAAQ;AAAA,IACpB;AAEA,QAAI,OAAO;AACT,WAAK,GAAG,SAAS,KAAK,IAClBG,KACA,GAAG,SAAS,QAAQ,IAClBA,KAAIF,UACJE,KAAIF,UAAS;AAAA,IACrB;AAAA,EACF;AAGA,SAAO,CAAC,IAAI,EAAE;AAChB;AAEA,IAAM,sBAAsB,oBAAI,IAAI,CAAC,QAAQ,YAAY,OAAO,CAAC;AAC1D,IAAM,gBAAgB,CAAC,YAC5B,oBAAoB,IAAI,QAAQ,QAAQ;AAEnC,IAAM,iBAAiB,CAAC,SAASG,OAAM,WAAW,CAAC,MAAM;AAC9D,QAAM,SAAS,EAAE,GAAGA,MAAK;AAEzB,aAAW,OAAO,QAAQ;AACxB,QAAI,OAAO,GAAG,EAAE,QAAQ,MAAM,SAAS,GAAG,GAAG;AAC3C,aAAO,OAAO,GAAG;AAAA,IACnB;AAAA,EACF;AAEA,MAAI,OAAO,KAAK,MAAM,EAAE,QAAQ;AAC9B,YAAQ,KAAK,aAAa,cAAc,KAAK,UAAU,MAAM,CAAC;AAAA,EAChE,OAAO;AACL,YAAQ,KAAK,gBAAgB,YAAY;AACzC,YAAQ,KAAK,gBAAgB,YAAY;AAAA,EAC3C;AACF;;;ACvIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACO,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,QAAQ;;;ACJd,IAAM,UAAU;AAAA,EACrB,QAAQ,OAAO,WAAW,cAAc,OAAO;AAAA,EAC/C,UAAU,OAAO,aAAa,cAAc,OAAO;AACrD;AAEO,SAAS,eAAe,MAAM,MAAM,MAAM,MAAM;AACrD,UAAQ,SAAS;AACjB,UAAQ,WAAW;AACrB;AAEA,IAAM,OAAO,CAAC;AAEP,SAAS,aAAa;AAC3B,OAAK,SAAS,QAAQ;AACtB,OAAK,WAAW,QAAQ;AAC1B;AAEO,SAAS,gBAAgB;AAC9B,UAAQ,SAAS,KAAK;AACtB,UAAQ,WAAW,KAAK;AAC1B;AAEO,SAAS,WAAW,KAAK,IAAI;AAClC,aAAW;AACX,iBAAe,KAAK,IAAI,QAAQ;AAChC,KAAG,KAAK,IAAI,QAAQ;AACpB,gBAAc;AAChB;AAEO,SAAS,YAAY;AAC1B,SAAO,QAAQ;AACjB;;;AC/BA,IAAqB,OAArB,MAA0B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAS1B;;;ACHA,IAAM,WAAW,CAAC;AACX,IAAM,OAAO;AAGb,SAAS,OAAO,MAAM,KAAK,KAAK;AAErC,SAAO,QAAQ,SAAS,gBAAgB,IAAI,IAAI;AAClD;AAEO,SAAS,aAAa,SAAS,SAAS,OAAO;AACpD,MAAI,mBAAmB,KAAM,QAAO;AAEpC,MAAI,OAAO,YAAY,UAAU;AAC/B,WAAO,QAAQ,OAAO;AAAA,EACxB;AAEA,MAAI,WAAW,MAAM;AACnB,WAAO,IAAI,SAAS,IAAI,EAAE;AAAA,EAC5B;AAEA,MAAI,OAAO,YAAY,YAAY,QAAQ,OAAO,CAAC,MAAM,KAAK;AAC5D,WAAO,QAAQ,QAAQ,SAAS,cAAc,OAAO,CAAC;AAAA,EACxD;AAGA,QAAM,UAAU,SAAS,QAAQ,SAAS,cAAc,KAAK,IAAI,OAAO,KAAK;AAC7E,UAAQ,YAAY;AAIpB,YAAU,QAAQ,QAAQ,UAAU;AAGpC,UAAQ,YAAY,QAAQ,UAAU;AACtC,SAAO;AACT;AAEO,SAAS,UAAU,MAAM,MAAM;AACpC,SAAO,SACJ,gBAAgB,QAAQ,OAAO,QAC7B,KAAK,iBACJ,gBAAgB,KAAK,cAAc,YAAY,QACjD,OACA,OAAO,IAAI;AACjB;AAGO,SAAS,MAAM,MAAM;AAE1B,MAAI,CAAC,KAAM,QAAO;AAGlB,MAAI,KAAK,oBAAoB,KAAM,QAAO,KAAK;AAE/C,MAAI,KAAK,aAAa,sBAAsB;AAC1C,WAAO,IAAI,SAAS,SAAS,IAAI;AAAA,EACnC;AAGA,MAAI,YAAY,WAAW,KAAK,YAAY,KAAK;AAGjD,MAAI,cAAc,oBAAoB,cAAc,kBAAkB;AACpE,gBAAY;AAAA,EAGd,WAAW,CAAC,SAAS,SAAS,GAAG;AAC/B,gBAAY;AAAA,EACd;AAEA,SAAO,IAAI,SAAS,SAAS,EAAE,IAAI;AACrC;AAEA,IAAI,UAAU;AAEP,SAAS,UAAU,OAAO,OAAO;AACtC,YAAU;AACZ;AAEO,SAAS,SAAS,SAAS,OAAO,QAAQ,MAAM,SAAS,OAAO;AACrE,WAAS,IAAI,IAAI;AACjB,MAAI,OAAQ,UAAS,IAAI,IAAI;AAE7B,iBAAe,OAAO,oBAAoB,QAAQ,SAAS,CAAC;AAE5D,SAAO;AACT;AAEO,SAAS,SAAS,MAAM;AAC7B,SAAO,SAAS,IAAI;AACtB;AAGA,IAAI,MAAM;AAGH,SAAS,IAAI,MAAM;AACxB,SAAO,UAAU,WAAW,IAAI,IAAI;AACtC;AAGO,SAAS,YAAY,MAAM;AAEhC,WAAS,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAClD,gBAAY,KAAK,SAAS,CAAC,CAAC;AAAA,EAC9B;AAEA,MAAI,KAAK,IAAI;AACX,SAAK,KAAK,IAAI,KAAK,QAAQ;AAC3B,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAGO,SAAS,OAAO,SAASC,UAAS;AACvC,MAAI,KAAK;AAET,YAAU,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAErD,OAAK,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,SAAK,OAAOA,UAAS;AACnB,cAAQ,CAAC,EAAE,UAAU,GAAG,IAAIA,SAAQ,GAAG;AAAA,IACzC;AAAA,EACF;AACF;AAEO,SAAS,kBAAkB,IAAI;AACpC,SAAO,YAAa,MAAM;AACxB,UAAM,IAAI,KAAK,KAAK,SAAS,CAAC;AAE9B,QAAI,KAAK,EAAE,gBAAgB,UAAU,EAAE,aAAa,QAAQ;AAC1D,aAAO,GAAG,MAAM,MAAM,KAAK,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC;AAAA,IACjD,OAAO;AACL,aAAO,GAAG,MAAM,MAAM,IAAI;AAAA,IAC5B;AAAA,EACF;AACF;;;AC5IO,SAAS,WAAW;AACzB,SAAO,KAAK,OAAO,EAAE,SAAS;AAChC;AAGO,SAAS,WAAW;AACzB,SAAO,KAAK,OAAO,EAAE,MAAM,IAAI;AACjC;AAGO,SAAS,OAAO;AACrB,SAAO,KAAK,SAAS,EAAE,KAAK,SAAS,IAAI,CAAC;AAC5C;AAGO,SAAS,OAAO;AACrB,SAAO,KAAK,SAAS,EAAE,KAAK,SAAS,IAAI,CAAC;AAC5C;AAGO,SAAS,UAAU;AACxB,QAAM,IAAI,KAAK,SAAS;AACxB,QAAM,IAAI,KAAK,OAAO;AAGtB,IAAE,IAAI,KAAK,OAAO,GAAG,IAAI,CAAC;AAE1B,SAAO;AACT;AAGO,SAAS,WAAW;AACzB,QAAM,IAAI,KAAK,SAAS;AACxB,QAAM,IAAI,KAAK,OAAO;AAEtB,IAAE,IAAI,KAAK,OAAO,GAAG,IAAI,IAAI,IAAI,CAAC;AAElC,SAAO;AACT;AAGO,SAAS,QAAQ;AACtB,QAAM,IAAI,KAAK,OAAO;AAGtB,IAAE,IAAI,KAAK,OAAO,CAAC;AAEnB,SAAO;AACT;AAGO,SAAS,OAAO;AACrB,QAAM,IAAI,KAAK,OAAO;AAGtB,IAAE,IAAI,KAAK,OAAO,GAAG,CAAC;AAEtB,SAAO;AACT;AAGO,SAAS,OAAO,SAAS;AAC9B,YAAU,aAAa,OAAO;AAC9B,UAAQ,OAAO;AAEf,QAAM,IAAI,KAAK,SAAS;AAExB,OAAK,OAAO,EAAE,IAAI,SAAS,CAAC;AAE5B,SAAO;AACT;AAGO,SAAS,MAAM,SAAS;AAC7B,YAAU,aAAa,OAAO;AAC9B,UAAQ,OAAO;AAEf,QAAM,IAAI,KAAK,SAAS;AAExB,OAAK,OAAO,EAAE,IAAI,SAAS,IAAI,CAAC;AAEhC,SAAO;AACT;AAEO,SAAS,aAAa,SAAS;AACpC,YAAU,aAAa,OAAO;AAC9B,UAAQ,OAAO,IAAI;AACnB,SAAO;AACT;AAEO,SAAS,YAAY,SAAS;AACnC,YAAU,aAAa,OAAO;AAC9B,UAAQ,MAAM,IAAI;AAClB,SAAO;AACT;AAEA,gBAAgB,OAAO;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;ACjHD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACO,IAAM,gBACX;AAGK,IAAM,MAAM;AAGZ,IAAM,MAAM;AAGZ,IAAM,YAAY;AAGlB,IAAM,aAAa;AAGnB,IAAM,aAAa;AAGnB,IAAM,QAAQ;AAGd,IAAM,QAAQ;AAGd,IAAM,UAAU;AAGhB,IAAM,WAAW;AAGjB,IAAM,UAAU;AAGhB,IAAM,YAAY;AAGlB,IAAM,eAAe;;;AClCrB,SAAS,UAAU;AACxB,QAAMC,QAAO,KAAK,KAAK,OAAO;AAC9B,SAAOA,SAAQ,OAAO,CAAC,IAAIA,MAAK,KAAK,EAAE,MAAM,SAAS;AACxD;AAGO,SAAS,SAAS,MAAM;AAC7B,SAAO,KAAK,QAAQ,EAAE,QAAQ,IAAI,MAAM;AAC1C;AAGO,SAAS,SAAS,MAAM;AAC7B,MAAI,CAAC,KAAK,SAAS,IAAI,GAAG;AACxB,UAAMC,SAAQ,KAAK,QAAQ;AAC3B,IAAAA,OAAM,KAAK,IAAI;AACf,SAAK,KAAK,SAASA,OAAM,KAAK,GAAG,CAAC;AAAA,EACpC;AAEA,SAAO;AACT;AAGO,SAAS,YAAY,MAAM;AAChC,MAAI,KAAK,SAAS,IAAI,GAAG;AACvB,SAAK;AAAA,MACH;AAAA,MACA,KAAK,QAAQ,EACV,OAAO,SAAU,GAAG;AACnB,eAAO,MAAM;AAAA,MACf,CAAC,EACA,KAAK,GAAG;AAAA,IACb;AAAA,EACF;AAEA,SAAO;AACT;AAGO,SAAS,YAAY,MAAM;AAChC,SAAO,KAAK,SAAS,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,SAAS,IAAI;AAC1E;AAEA,gBAAgB,OAAO;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;AChDM,SAAS,IAAI,OAAO,KAAK;AAC9B,QAAM,MAAM,CAAC;AACb,MAAI,UAAU,WAAW,GAAG;AAE1B,SAAK,KAAK,MAAM,QACb,MAAM,SAAS,EACf,OAAO,SAAU,IAAI;AACpB,aAAO,CAAC,CAAC,GAAG;AAAA,IACd,CAAC,EACA,QAAQ,SAAU,IAAI;AACrB,YAAM,IAAI,GAAG,MAAM,SAAS;AAC5B,UAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,IACjB,CAAC;AACH,WAAO;AAAA,EACT;AAEA,MAAI,UAAU,SAAS,GAAG;AAExB,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,iBAAW,QAAQ,OAAO;AACxB,cAAM,QAAQ;AACd,YAAI,IAAI,IAAI,KAAK,KAAK,MAAM,iBAAiB,KAAK;AAAA,MACpD;AACA,aAAO;AAAA,IACT;AAGA,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,KAAK,KAAK,MAAM,iBAAiB,KAAK;AAAA,IAC/C;AAGA,QAAI,OAAO,UAAU,UAAU;AAC7B,iBAAW,QAAQ,OAAO;AAExB,aAAK,KAAK,MAAM;AAAA,UACd;AAAA,UACA,MAAM,IAAI,KAAK,QAAQ,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI;AAAA,QACpE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAGA,MAAI,UAAU,WAAW,GAAG;AAC1B,SAAK,KAAK,MAAM;AAAA,MACd;AAAA,MACA,OAAO,QAAQ,QAAQ,KAAK,GAAG,IAAI,KAAK;AAAA,IAC1C;AAAA,EACF;AAEA,SAAO;AACT;AAGO,SAAS,OAAO;AACrB,SAAO,KAAK,IAAI,WAAW,EAAE;AAC/B;AAGO,SAAS,OAAO;AACrB,SAAO,KAAK,IAAI,WAAW,MAAM;AACnC;AAGO,SAAS,UAAU;AACxB,SAAO,KAAK,IAAI,SAAS,MAAM;AACjC;AAEA,gBAAgB,OAAO;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;AC1EM,SAAS,KAAK,GAAG,GAAG,GAAG;AAC5B,MAAI,KAAK,MAAM;AAEb,WAAO,KAAK;AAAA,MACV;AAAA,QACE;AAAA,UACE,KAAK,KAAK;AAAA,UACV,CAAC,OAAO,GAAG,SAAS,QAAQ,OAAO,MAAM;AAAA,QAC3C;AAAA,QACA,CAAC,OAAO,GAAG,SAAS,MAAM,CAAC;AAAA,MAC7B;AAAA,IACF;AAAA,EACF,WAAW,aAAa,OAAO;AAC7B,UAAMC,QAAO,CAAC;AACd,eAAW,OAAO,GAAG;AACnB,MAAAA,MAAK,GAAG,IAAI,KAAK,KAAK,GAAG;AAAA,IAC3B;AACA,WAAOA;AAAA,EACT,WAAW,OAAO,MAAM,UAAU;AAChC,SAAK,KAAK,GAAG;AACX,WAAK,KAAK,GAAG,EAAE,CAAC,CAAC;AAAA,IACnB;AAAA,EACF,WAAW,UAAU,SAAS,GAAG;AAC/B,QAAI;AACF,aAAO,KAAK,MAAM,KAAK,KAAK,UAAU,CAAC,CAAC;AAAA,IAC1C,SAAS,GAAG;AACV,aAAO,KAAK,KAAK,UAAU,CAAC;AAAA,IAC9B;AAAA,EACF,OAAO;AACL,SAAK;AAAA,MACH,UAAU;AAAA,MACV,MAAM,OACF,OACA,MAAM,QAAQ,OAAO,MAAM,YAAY,OAAO,MAAM,WAClD,IACA,KAAK,UAAU,CAAC;AAAA,IACxB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,gBAAgB,OAAO,EAAE,KAAK,CAAC;;;AC3CxB,SAAS,SAAS,GAAG,GAAG;AAE7B,MAAI,OAAO,UAAU,CAAC,MAAM,UAAU;AACpC,eAAW,OAAO,GAAG;AACnB,WAAK,SAAS,KAAK,EAAE,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF,WAAW,UAAU,WAAW,GAAG;AAEjC,WAAO,KAAK,OAAO,EAAE,CAAC;AAAA,EACxB,OAAO;AAEL,SAAK,OAAO,EAAE,CAAC,IAAI;AAAA,EACrB;AAEA,SAAO;AACT;AAGO,SAAS,SAAS;AACvB,MAAI,UAAU,WAAW,GAAG;AAC1B,SAAK,UAAU,CAAC;AAAA,EAClB,OAAO;AACL,aAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,KAAK;AAC9C,aAAO,KAAK,OAAO,EAAE,UAAU,CAAC,CAAC;AAAA,IACnC;AAAA,EACF;AACA,SAAO;AACT;AAKO,SAAS,SAAS;AACvB,SAAQ,KAAK,UAAU,KAAK,WAAW,CAAC;AAC1C;AAEA,gBAAgB,OAAO,EAAE,UAAU,QAAQ,OAAO,CAAC;;;ACrCnD,SAAS,YAAYC,MAAK;AACxB,SAAOA,KAAI,WAAW,IAClB;AAAA,IACE;AAAA,IACAA,KAAI,UAAU,GAAG,CAAC;AAAA,IAClBA,KAAI,UAAU,GAAG,CAAC;AAAA,IAClBA,KAAI,UAAU,GAAG,CAAC;AAAA,IAClBA,KAAI,UAAU,GAAG,CAAC;AAAA,IAClBA,KAAI,UAAU,GAAG,CAAC;AAAA,IAClBA,KAAI,UAAU,GAAG,CAAC;AAAA,EACpB,EAAE,KAAK,EAAE,IACTA;AACN;AAEA,SAAS,aAAa,WAAW;AAC/B,QAAM,UAAU,KAAK,MAAM,SAAS;AACpC,QAAM,UAAU,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,OAAO,CAAC;AAClD,QAAMA,OAAM,QAAQ,SAAS,EAAE;AAC/B,SAAOA,KAAI,WAAW,IAAI,MAAMA,OAAMA;AACxC;AAEA,SAAS,GAAG,QAAQ,OAAO;AACzB,WAAS,IAAI,MAAM,QAAQ,OAAO;AAChC,QAAI,OAAO,MAAM,CAAC,CAAC,KAAK,MAAM;AAC5B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,QAAM,SAAS,GAAG,GAAG,KAAK,IACtB,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,GAAG,OAAO,MAAM,IACjD,GAAG,GAAG,KAAK,IACT,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,GAAG,OAAO,MAAM,IACjD,GAAG,GAAG,KAAK,IACT,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,GAAG,OAAO,MAAM,IACjD,GAAG,GAAG,KAAK,IACT,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,GAAG,OAAO,MAAM,IACjD,GAAG,GAAG,KAAK,IACT,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,GAAG,OAAO,MAAM,IACjD,GAAG,GAAG,MAAM,IACV,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,OAAO,IACpD,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,OAAO,MAAM;AAElD,SAAO,QAAQ,KAAK,OAAO;AAC3B,SAAO;AACT;AAEA,SAAS,SAAS,OAAO;AACvB,MAAI,UAAU,SAAS,UAAU,SAAS,UAAU,OAAO;AACzD,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,MAAI,IAAI,EAAG,MAAK;AAChB,MAAI,IAAI,EAAG,MAAK;AAChB,MAAI,IAAI,IAAI,EAAG,QAAO,KAAK,IAAI,KAAK,IAAI;AACxC,MAAI,IAAI,IAAI,EAAG,QAAO;AACtB,MAAI,IAAI,IAAI,EAAG,QAAO,KAAK,IAAI,MAAM,IAAI,IAAI,KAAK;AAClD,SAAO;AACT;AAEA,IAAqB,QAArB,MAAqB,OAAM;AAAA,EACzB,eAAe,QAAQ;AACrB,SAAK,KAAK,GAAG,MAAM;AAAA,EACrB;AAAA;AAAA,EAGA,OAAO,QAAQ,OAAO;AACpB,WACE,UAAU,iBAAiB,UAAS,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK;AAAA,EAE5E;AAAA;AAAA,EAGA,OAAO,MAAM,OAAO;AAClB,WACE,SACA,OAAO,MAAM,MAAM,YACnB,OAAO,MAAM,MAAM,YACnB,OAAO,MAAM,MAAM;AAAA,EAEvB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO,OAAO,WAAW,GAAG;AAEjC,UAAM,EAAE,QAAQ,OAAO,KAAK,IAAI,GAAG,IAAI;AAGvC,QAAI,SAAS,WAAW;AACtB,YAAM,KAAK,KAAK,MAAM,OAAO,IAAI;AACjC,YAAM,KAAK,KAAK,MAAM,OAAO,IAAI;AACjC,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,GAAG,KAAK;AACtC,aAAO;AAAA,IACT,WAAW,SAAS,QAAQ;AAC1B,UAAI,KAAK,OAAO,OAAO,IAAI;AAC3B,YAAM,IAAI,MAAM,KAAK,IAAK,IAAI,KAAK,IAAK,MAAM,IAAI,IAAI,GAAG;AACzD,YAAM,IAAI,MAAM,KAAK,IAAK,IAAI,KAAK,IAAK,MAAM,GAAG,IAAI,GAAG;AACxD,YAAM,IAAI,MAAM,MAAM,IAAK,IAAI,KAAK,IAAK,MAAM,GAAG,IAAI,GAAG;AACzD,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,CAAC;AAC/B,aAAO;AAAA,IACT,WAAW,SAAS,UAAU;AAC5B,YAAM,KAAK,KAAK,MAAM,OAAO,IAAI;AACjC,YAAM,KAAK,KAAK,KAAK,OAAO,IAAI;AAChC,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,GAAG,KAAK;AACtC,aAAO;AAAA,IACT,WAAW,SAAS,QAAQ;AAC1B,YAAM,IAAI,KAAK,KAAK,OAAO;AAC3B,YAAM,KAAK,MAAM,MAAM,OAAO,IAAI;AAClC,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,GAAG,KAAK;AACtC,aAAO;AAAA,IACT,WAAW,SAAS,OAAO;AACzB,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,CAAC;AAC/B,aAAO;AAAA,IACT,WAAW,SAAS,OAAO;AACzB,YAAM,IAAI,MAAM,OAAO;AACvB,YAAM,IAAI,MAAM,OAAO,IAAI;AAC3B,YAAM,IAAI,MAAM,OAAO,IAAI;AAC3B,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,GAAG,KAAK;AACtC,aAAO;AAAA,IACT,WAAW,SAAS,QAAQ;AAC1B,YAAM,OAAO,MAAM,OAAO;AAC1B,YAAM,QAAQ,IAAI,OAAM,MAAM,MAAM,IAAI;AACxC,aAAO;AAAA,IACT,OAAO;AACL,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACjD;AAAA,EACF;AAAA;AAAA,EAGA,OAAO,KAAK,OAAO;AACjB,WAAO,OAAO,UAAU,aAAa,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK;AAAA,EAC5E;AAAA,EAEA,OAAO;AAEL,UAAM,EAAE,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI;AAChC,UAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC,MAAM,IAAI,GAAG;AAGjD,UAAM,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;AAEtC,QAAI,MAAM,GAAG;AAEX,aAAO,IAAI,OAAM,GAAG,GAAG,GAAG,GAAG,MAAM;AAAA,IACrC;AAEA,UAAM,KAAK,IAAI,IAAI,MAAM,IAAI;AAC7B,UAAM,KAAK,IAAI,IAAI,MAAM,IAAI;AAC7B,UAAMC,MAAK,IAAI,IAAI,MAAM,IAAI;AAG7B,UAAM,QAAQ,IAAI,OAAM,GAAG,GAAGA,IAAG,GAAG,MAAM;AAC1C,WAAO;AAAA,EACT;AAAA,EAEA,MAAM;AAEJ,UAAM,EAAE,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI;AAChC,UAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC,MAAM,IAAI,GAAG;AAGjD,UAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,UAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,UAAM,KAAK,MAAM,OAAO;AAGxB,UAAM,SAAS,QAAQ;AAGvB,UAAM,QAAQ,MAAM;AACpB,UAAM,IAAI,SACN,IACA,IAAI,MACF,SAAS,IAAI,MAAM,OACnB,SAAS,MAAM;AACrB,UAAM,IAAI,SACN,IACA,QAAQ,MACJ,IAAI,KAAK,SAAS,IAAI,IAAI,IAAI,MAAM,IACtC,QAAQ,MACJ,IAAI,KAAK,QAAQ,KAAK,IACxB,QAAQ,MACJ,IAAI,KAAK,QAAQ,KAAK,IACxB;AAGV,UAAM,QAAQ,IAAI,OAAM,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK;AACxD,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,QAAQ,OAAO;AAE9C,QAAI,CAAC,IAAI,IAAI;AAGb,QAAI,KAAK,OAAO;AACd,iBAAW,aAAa,KAAK,OAAO;AAClC,eAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAAA,MACnC;AAAA,IACF;AAEA,QAAI,OAAO,MAAM,UAAU;AAEzB,cAAQ,OAAO,MAAM,WAAW,IAAI;AACpC,UAAI,OAAO,MAAM,WAAW,IAAI;AAGhC,aAAO,OAAO,MAAM,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,MAAM,CAAC;AAAA,IAE3D,WAAW,aAAa,OAAO;AAC7B,WAAK,QAAQ,MAAM,OAAO,EAAE,CAAC,MAAM,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM;AAC9D,aAAO,OAAO,MAAM,EAAE,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;AAAA,IACrE,WAAW,aAAa,QAAQ;AAE9B,YAAM,SAAS,cAAc,GAAG,CAAC;AACjC,aAAO,OAAO,MAAM,MAAM;AAAA,IAC5B,WAAW,OAAO,MAAM,UAAU;AAChC,UAAI,MAAM,KAAK,CAAC,GAAG;AACjB,cAAM,eAAe,EAAE,QAAQ,YAAY,EAAE;AAC7C,cAAM,CAACC,KAAIC,KAAIC,GAAE,IAAI,IAClB,KAAK,YAAY,EACjB,MAAM,GAAG,CAAC,EACV,IAAI,CAAC,MAAM,SAAS,CAAC,CAAC;AACzB,eAAO,OAAO,MAAM,EAAE,IAAAF,KAAI,IAAAC,KAAI,IAAAC,KAAI,IAAI,GAAG,OAAO,MAAM,CAAC;AAAA,MACzD,WAAW,MAAM,KAAK,CAAC,GAAG;AACxB,cAAM,WAAW,CAAC,MAAM,SAAS,GAAG,EAAE;AACtC,cAAM,CAAC,EAAEF,KAAIC,KAAIC,GAAE,IAAI,IAAI,KAAK,YAAY,CAAC,CAAC,EAAE,IAAI,QAAQ;AAC5D,eAAO,OAAO,MAAM,EAAE,IAAAF,KAAI,IAAAC,KAAI,IAAAC,KAAI,IAAI,GAAG,OAAO,MAAM,CAAC;AAAA,MACzD,MAAO,OAAM,MAAM,kDAAkD;AAAA,IACvE;AAGA,UAAM,EAAE,IAAI,IAAI,IAAI,GAAG,IAAI;AAC3B,UAAM,aACJ,KAAK,UAAU,QACX,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IACtB,KAAK,UAAU,QACb,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IACtB,KAAK,UAAU,QACb,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IACtB,KAAK,UAAU,QACb,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IACtB,KAAK,UAAU,QACb,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IACtB,KAAK,UAAU,SACb,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,IAC7B,CAAC;AACjB,WAAO,OAAO,MAAM,UAAU;AAAA,EAChC;AAAA,EAEA,MAAM;AAEJ,UAAM,EAAE,GAAAC,IAAG,GAAAJ,IAAG,EAAE,IAAI,KAAK,IAAI;AAG7B,UAAM,IAAI,MAAMA,KAAI;AACpB,UAAM,IAAI,OAAOI,KAAIJ;AACrB,UAAM,IAAI,OAAOA,KAAI;AAGrB,UAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,GAAG,KAAK;AACtC,WAAO;AAAA,EACT;AAAA,EAEA,MAAM;AAEJ,UAAM,EAAE,GAAG,GAAG,EAAE,IAAI,KAAK,IAAI;AAG7B,UAAM,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,CAAC;AACnC,QAAI,IAAK,MAAM,KAAK,MAAM,GAAG,CAAC,IAAK,KAAK;AACxC,QAAI,IAAI,GAAG;AACT,WAAK;AACL,UAAI,MAAM;AAAA,IACZ;AAGA,UAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,GAAG,KAAK;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM;AACJ,QAAI,KAAK,UAAU,OAAO;AACxB,aAAO;AAAA,IACT,WAAW,SAAS,KAAK,KAAK,GAAG;AAE/B,UAAI,EAAE,GAAAI,IAAG,GAAAJ,IAAG,EAAE,IAAI;AAClB,UAAI,KAAK,UAAU,SAAS,KAAK,UAAU,OAAO;AAEhD,YAAI,EAAE,GAAG,GAAG,GAAAK,GAAE,IAAI;AAClB,YAAI,KAAK,UAAU,OAAO;AACxB,gBAAM,EAAE,GAAG,EAAE,IAAI;AACjB,gBAAM,OAAO,KAAK,KAAK;AACvB,cAAI,IAAI,KAAK,IAAI,OAAO,CAAC;AACzB,UAAAA,KAAI,IAAI,KAAK,IAAI,OAAO,CAAC;AAAA,QAC3B;AAGA,cAAM,MAAM,IAAI,MAAM;AACtB,cAAM,KAAK,IAAI,MAAM;AACrB,cAAM,KAAK,KAAKA,KAAI;AAGpB,cAAM,KAAK,KAAK;AAChB,cAAM,KAAK;AACX,cAAM,KAAK;AACX,QAAAD,KAAI,WAAW,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM;AACpD,QAAAJ,KAAI,KAAO,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM;AAChD,YAAI,WAAW,MAAM,IAAI,KAAK,MAAM,KAAK,KAAK,MAAM;AAAA,MACtD;AAGA,YAAM,KAAKI,KAAI,SAASJ,KAAI,UAAU,IAAI;AAC1C,YAAM,KAAKI,KAAI,UAAUJ,KAAI,SAAS,IAAI;AAC1C,YAAM,KAAKI,KAAI,SAASJ,KAAI,SAAS,IAAI;AAGzC,YAAM,MAAM,KAAK;AACjB,YAAM,KAAK;AACX,YAAM,IAAI,KAAK,KAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAC/D,YAAM,IAAI,KAAK,KAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAC/D,YAAM,IAAI,KAAK,KAAK,QAAQ,IAAI,IAAI,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAG/D,YAAM,QAAQ,IAAI,OAAM,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACjD,aAAO;AAAA,IACT,WAAW,KAAK,UAAU,OAAO;AAG/B,UAAI,EAAE,GAAG,GAAG,EAAE,IAAI;AAClB,WAAK;AACL,WAAK;AACL,WAAK;AAGL,UAAI,MAAM,GAAG;AACX,aAAK;AACL,cAAMM,SAAQ,IAAI,OAAM,GAAG,GAAG,CAAC;AAC/B,eAAOA;AAAA,MACT;AAGA,YAAM,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;AAC9C,YAAM,IAAI,IAAI,IAAI;AAGlB,YAAM,IAAI,MAAM,SAAS,GAAG,GAAG,IAAI,IAAI,CAAC;AACxC,YAAM,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAChC,YAAM,IAAI,MAAM,SAAS,GAAG,GAAG,IAAI,IAAI,CAAC;AAGxC,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,CAAC;AAC/B,aAAO;AAAA,IACT,WAAW,KAAK,UAAU,QAAQ;AAGhC,YAAM,EAAE,GAAG,GAAG,GAAAN,IAAG,EAAE,IAAI;AAGvB,YAAM,IAAI,OAAO,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AAChD,YAAM,IAAI,OAAO,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,CAAC;AAChD,YAAM,IAAI,OAAO,IAAI,KAAK,IAAI,GAAGA,MAAK,IAAI,KAAK,CAAC;AAGhD,YAAM,QAAQ,IAAI,OAAM,GAAG,GAAG,CAAC;AAC/B,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEA,UAAU;AACR,UAAM,EAAE,IAAI,IAAI,IAAI,IAAI,MAAM,IAAI;AAClC,WAAO,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK;AAAA,EAC/B;AAAA,EAEA,QAAQ;AACN,UAAM,CAAC,GAAG,GAAG,CAAC,IAAI,KAAK,SAAS,EAAE,IAAI,YAAY;AAClD,WAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AAAA,EACtB;AAAA,EAEA,QAAQ;AACN,UAAM,CAAC,IAAI,IAAI,EAAE,IAAI,KAAK,SAAS;AACnC,UAAM,SAAS,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;AACpC,WAAO;AAAA,EACT;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EAEA,MAAM;AAEJ,UAAM,EAAE,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,IAAI;AAClD,UAAM,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,MAAM,IAAI,EAAE,IAAI,CAAC,MAAM,IAAI,GAAG;AAGvD,UAAM,KAAK,IAAI,UAAU,KAAK,KAAK,IAAI,SAAS,OAAO,GAAG,IAAI,IAAI;AAClE,UAAM,KAAK,IAAI,UAAU,KAAK,KAAK,IAAI,SAAS,OAAO,GAAG,IAAI,IAAI;AAClE,UAAM,KAAK,IAAI,UAAU,KAAK,KAAK,IAAI,SAAS,OAAO,GAAG,IAAI,IAAI;AAGlE,UAAM,MAAM,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU;AACvD,UAAM,MAAM,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU;AACvD,UAAM,MAAM,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU;AAGvD,UAAMI,KAAI,KAAK,UAAW,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,QAAQ,KAAK,KAAK;AAClE,UAAMJ,KAAI,KAAK,UAAW,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,QAAQ,KAAK,KAAK;AAClE,UAAM,IAAI,KAAK,UAAW,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,QAAQ,KAAK,KAAK;AAGlE,UAAM,QAAQ,IAAI,OAAMI,IAAGJ,IAAG,GAAG,KAAK;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,UAAM,EAAE,IAAI,IAAI,GAAG,IAAI,KAAK,IAAI;AAChC,UAAM,EAAE,KAAK,KAAK,MAAM,IAAI;AAC5B,UAAM,SAAS,CAAC,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC;AAC/C,WAAO,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,MAAM;AAAA,EAChC;AAAA;AAAA;AAAA;AAKF;;;AC/bA,IAAqB,QAArB,MAAqB,OAAM;AAAA;AAAA,EAEzB,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA;AAAA,EAGA,QAAQ;AACN,WAAO,IAAI,OAAM,IAAI;AAAA,EACvB;AAAA,EAEA,KAAKO,IAAGC,IAAG;AACT,UAAM,OAAO,EAAE,GAAG,GAAG,GAAG,EAAE;AAG1B,UAAM,SAAS,MAAM,QAAQD,EAAC,IAC1B,EAAE,GAAGA,GAAE,CAAC,GAAG,GAAGA,GAAE,CAAC,EAAE,IACnB,OAAOA,OAAM,WACX,EAAE,GAAGA,GAAE,GAAG,GAAGA,GAAE,EAAE,IACjB,EAAE,GAAGA,IAAG,GAAGC,GAAE;AAGnB,SAAK,IAAI,OAAO,KAAK,OAAO,KAAK,IAAI,OAAO;AAC5C,SAAK,IAAI,OAAO,KAAK,OAAO,KAAK,IAAI,OAAO;AAE5C,WAAO;AAAA,EACT;AAAA,EAEA,UAAU;AACR,WAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAAA,EACxB;AAAA,EAEA,UAAU,GAAG;AACX,WAAO,KAAK,MAAM,EAAE,WAAW,CAAC;AAAA,EAClC;AAAA;AAAA,EAGA,WAAW,GAAG;AACZ,QAAI,CAAC,OAAO,aAAa,CAAC,GAAG;AAC3B,UAAI,IAAI,OAAO,CAAC;AAAA,IAClB;AAEA,UAAM,EAAE,GAAAD,IAAG,GAAAC,GAAE,IAAI;AAGjB,SAAK,IAAI,EAAE,IAAID,KAAI,EAAE,IAAIC,KAAI,EAAE;AAC/B,SAAK,IAAI,EAAE,IAAID,KAAI,EAAE,IAAIC,KAAI,EAAE;AAE/B,WAAO;AAAA,EACT;AACF;AAEO,SAAS,MAAMD,IAAGC,IAAG;AAC1B,SAAO,IAAI,MAAMD,IAAGC,EAAC,EAAE,WAAW,KAAK,UAAU,EAAE,SAAS,CAAC;AAC/D;;;AClDA,SAAS,YAAY,GAAG,GAAG,WAAW;AACpC,SAAO,KAAK,IAAI,IAAI,CAAC,KAAK,aAAa;AACzC;AAEA,IAAqB,SAArB,MAAqB,QAAO;AAAA,EAC1B,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EAEA,OAAO,iBAAiB,GAAG;AAEzB,UAAM,WAAW,EAAE,SAAS,UAAU,EAAE,SAAS;AACjD,UAAM,QAAQ,EAAE,SAAS,YAAY,EAAE,SAAS,OAAO,KAAK;AAC5D,UAAM,QAAQ,EAAE,SAAS,YAAY,EAAE,SAAS,OAAO,KAAK;AAC5D,UAAM,QACJ,EAAE,QAAQ,EAAE,KAAK,SACb,EAAE,KAAK,CAAC,IACR,SAAS,EAAE,IAAI,IACb,EAAE,OACF,SAAS,EAAE,KAAK,IACd,EAAE,QACF;AACV,UAAM,QACJ,EAAE,QAAQ,EAAE,KAAK,SACb,EAAE,KAAK,CAAC,IACR,SAAS,EAAE,IAAI,IACb,EAAE,OACF,SAAS,EAAE,KAAK,IACd,EAAE,QACF;AACV,UAAM,SACJ,EAAE,SAAS,EAAE,MAAM,SACf,EAAE,MAAM,CAAC,IAAI,QACb,SAAS,EAAE,KAAK,IACd,EAAE,QAAQ,QACV,SAAS,EAAE,MAAM,IACf,EAAE,SAAS,QACX;AACV,UAAM,SACJ,EAAE,SAAS,EAAE,MAAM,SACf,EAAE,MAAM,CAAC,IAAI,QACb,SAAS,EAAE,KAAK,IACd,EAAE,QAAQ,QACV,SAAS,EAAE,MAAM,IACf,EAAE,SAAS,QACX;AACV,UAAM,QAAQ,EAAE,SAAS;AACzB,UAAM,QAAQ,EAAE,UAAU,EAAE,SAAS;AACrC,UAAM,SAAS,IAAI;AAAA,MACjB,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE;AAAA,MAClC,EAAE,MAAM,EAAE;AAAA,IACZ;AACA,UAAM,KAAK,OAAO;AAClB,UAAM,KAAK,OAAO;AAElB,UAAMC,YAAW,IAAI;AAAA,MACnB,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa;AAAA,MACrC,EAAE,MAAM,EAAE,aAAa;AAAA,IACzB;AACA,UAAM,KAAKA,UAAS;AACpB,UAAM,KAAKA,UAAS;AACpB,UAAM,YAAY,IAAI;AAAA,MACpB,EAAE,aAAa,EAAE,MAAM,EAAE;AAAA,MACzB,EAAE,MAAM,EAAE;AAAA,IACZ;AACA,UAAM,KAAK,UAAU;AACrB,UAAM,KAAK,UAAU;AACrB,UAAM,WAAW,IAAI;AAAA,MACnB,EAAE,YAAY,EAAE,MAAM,EAAE;AAAA,MACxB,EAAE,MAAM,EAAE;AAAA,IACZ;AACA,UAAMC,MAAK,SAAS;AACpB,UAAMC,MAAK,SAAS;AAGpB,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAAD;AAAA,MACA,IAAAC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO,UAAU,GAAG;AAClB,WAAO,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;AAAA,EAChE;AAAA,EAEA,OAAO,aAAa,GAAG;AACrB,WACE,EAAE,KAAK,QACP,EAAE,KAAK,QACP,EAAE,KAAK,QACP,EAAE,KAAK,QACP,EAAE,KAAK,QACP,EAAE,KAAK;AAAA,EAEX;AAAA;AAAA,EAGA,OAAO,eAAe,GAAG,GAAG,GAAG;AAE7B,UAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAC9B,UAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAC9B,UAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAC9B,UAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAC9B,UAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AACpC,UAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAGpC,MAAE,IAAI;AACN,MAAE,IAAI;AACN,MAAE,IAAI;AACN,MAAE,IAAI;AACN,MAAE,IAAI;AACN,MAAE,IAAI;AAEN,WAAO;AAAA,EACT;AAAA,EAEA,OAAOC,KAAIC,KAAI,QAAQ;AACrB,WAAO,KAAK,MAAM,EAAE,QAAQD,KAAIC,KAAI,MAAM;AAAA,EAC5C;AAAA;AAAA,EAGA,QAAQD,KAAIC,KAAI,QAAQ;AACtB,UAAMC,MAAKF,OAAM;AACjB,UAAMG,MAAKF,OAAM;AACjB,WAAO,KAAK,WAAW,CAACC,KAAI,CAACC,GAAE,EAAE,WAAW,MAAM,EAAE,WAAWD,KAAIC,GAAE;AAAA,EACvE;AAAA;AAAA,EAGA,QAAQ;AACN,WAAO,IAAI,QAAO,IAAI;AAAA,EACxB;AAAA;AAAA,EAGA,UAAUH,MAAK,GAAGC,MAAK,GAAG;AAExB,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AAGf,UAAM,cAAc,IAAI,IAAI,IAAI;AAChC,UAAM,MAAM,cAAc,IAAI,IAAI;AAIlC,UAAM,KAAK,MAAM,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC;AACxC,UAAM,WAAW,KAAK,MAAM,MAAM,GAAG,MAAM,CAAC;AAC5C,UAAM,QAAS,MAAM,KAAK,KAAM;AAChC,UAAM,KAAK,KAAK,IAAI,QAAQ;AAC5B,UAAM,KAAK,KAAK,IAAI,QAAQ;AAI5B,UAAM,OAAO,IAAI,IAAI,IAAI,KAAK;AAC9B,UAAM,KAAM,IAAI,MAAO,MAAM,IAAI,MAAO,IAAI,MAAO,MAAM,IAAI;AAG7D,UAAM,KAAK,IAAID,MAAKA,MAAK,KAAK,KAAKC,OAAM,MAAM,KAAK,KAAK,KAAK;AAC9D,UAAM,KAAK,IAAIA,MAAKD,MAAK,KAAK,KAAKC,OAAM,MAAM,KAAK,KAAK,KAAK;AAG9D,WAAO;AAAA;AAAA,MAEL,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,SAASD;AAAA,MACT,SAASC;AAAA;AAAA,MAGT,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAGA,OAAO,OAAO;AACZ,QAAI,UAAU,KAAM,QAAO;AAC3B,UAAM,OAAO,IAAI,QAAO,KAAK;AAC7B,WACE,YAAY,KAAK,GAAG,KAAK,CAAC,KAC1B,YAAY,KAAK,GAAG,KAAK,CAAC,KAC1B,YAAY,KAAK,GAAG,KAAK,CAAC,KAC1B,YAAY,KAAK,GAAG,KAAK,CAAC,KAC1B,YAAY,KAAK,GAAG,KAAK,CAAC,KAC1B,YAAY,KAAK,GAAG,KAAK,CAAC;AAAA,EAE9B;AAAA;AAAA,EAGA,KAAK,MAAM,QAAQ;AACjB,WAAO,KAAK,MAAM,EAAE,MAAM,MAAM,MAAM;AAAA,EACxC;AAAA,EAEA,MAAM,MAAM,QAAQ;AAClB,WAAO,SAAS,MACZ,KAAK,OAAO,IAAI,GAAG,QAAQ,CAAC,IAC5B,SAAS,MACP,KAAK,OAAO,GAAG,IAAI,GAAG,MAAM,IAC5B,KAAK,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AAAA,EAChD;AAAA;AAAA,EAGA,KAAK,QAAQ;AACX,UAAM,OAAO,QAAO,UAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAGhD,aACE,kBAAkB,UACd,OAAO,UAAU,IACjB,OAAO,WAAW,WAChB,QAAO,UAAU,OAAO,MAAM,SAAS,EAAE,IAAI,UAAU,CAAC,IACxD,MAAM,QAAQ,MAAM,IAClB,QAAO,UAAU,MAAM,IACvB,OAAO,WAAW,YAAY,QAAO,aAAa,MAAM,IACtD,SACA,OAAO,WAAW,WAChB,IAAI,QAAO,EAAE,UAAU,MAAM,IAC7B,UAAU,WAAW,IACnB,QAAO,UAAU,CAAC,EAAE,MAAM,KAAK,SAAS,CAAC,IACzC;AAGhB,SAAK,IAAI,OAAO,KAAK,OAAO,OAAO,IAAI,KAAK;AAC5C,SAAK,IAAI,OAAO,KAAK,OAAO,OAAO,IAAI,KAAK;AAC5C,SAAK,IAAI,OAAO,KAAK,OAAO,OAAO,IAAI,KAAK;AAC5C,SAAK,IAAI,OAAO,KAAK,OAAO,OAAO,IAAI,KAAK;AAC5C,SAAK,IAAI,OAAO,KAAK,OAAO,OAAO,IAAI,KAAK;AAC5C,SAAK,IAAI,OAAO,KAAK,OAAO,OAAO,IAAI,KAAK;AAE5C,WAAO;AAAA,EACT;AAAA,EAEA,UAAU;AACR,WAAO,KAAK,MAAM,EAAE,SAAS;AAAA,EAC/B;AAAA;AAAA,EAGA,WAAW;AAET,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AAGf,UAAM,MAAM,IAAI,IAAI,IAAI;AACxB,QAAI,CAAC,IAAK,OAAM,IAAI,MAAM,mBAAmB,IAAI;AAGjD,UAAM,KAAK,IAAI;AACf,UAAM,KAAK,CAAC,IAAI;AAChB,UAAM,KAAK,CAAC,IAAI;AAChB,UAAM,KAAK,IAAI;AAGf,UAAM,KAAK,EAAE,KAAK,IAAI,KAAK;AAC3B,UAAM,KAAK,EAAE,KAAK,IAAI,KAAK;AAG3B,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AAET,WAAO;AAAA,EACT;AAAA,EAEA,UAAU,QAAQ;AAChB,WAAO,KAAK,MAAM,EAAE,WAAW,MAAM;AAAA,EACvC;AAAA,EAEA,WAAW,QAAQ;AACjB,UAAM,IAAI;AACV,UAAM,IAAI,kBAAkB,UAAS,SAAS,IAAI,QAAO,MAAM;AAE/D,WAAO,QAAO,eAAe,GAAG,GAAG,IAAI;AAAA,EACzC;AAAA;AAAA,EAGA,SAAS,QAAQ;AACf,WAAO,KAAK,MAAM,EAAE,UAAU,MAAM;AAAA,EACtC;AAAA,EAEA,UAAU,QAAQ;AAEhB,UAAM,IAAI;AACV,UAAM,IAAI,kBAAkB,UAAS,SAAS,IAAI,QAAO,MAAM;AAE/D,WAAO,QAAO,eAAe,GAAG,GAAG,IAAI;AAAA,EACzC;AAAA;AAAA,EAGA,OAAO,GAAGD,KAAIC,KAAI;AAChB,WAAO,KAAK,MAAM,EAAE,QAAQ,GAAGD,KAAIC,GAAE;AAAA,EACvC;AAAA,EAEA,QAAQ,GAAGD,MAAK,GAAGC,MAAK,GAAG;AAEzB,QAAI,QAAQ,CAAC;AAEb,UAAM,MAAM,KAAK,IAAI,CAAC;AACtB,UAAM,MAAM,KAAK,IAAI,CAAC;AAEtB,UAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI;AAE7B,SAAK,IAAI,IAAI,MAAM,IAAI;AACvB,SAAK,IAAI,IAAI,MAAM,IAAI;AACvB,SAAK,IAAI,IAAI,MAAM,IAAI;AACvB,SAAK,IAAI,IAAI,MAAM,IAAI;AACvB,SAAK,IAAI,IAAI,MAAM,IAAI,MAAMA,MAAK,MAAMD,MAAK,MAAMA;AACnD,SAAK,IAAI,IAAI,MAAM,IAAI,MAAMA,MAAK,MAAMC,MAAK,MAAMA;AAEnD,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,QAAQ;AACN,WAAO,KAAK,MAAM,EAAE,OAAO,GAAG,SAAS;AAAA,EACzC;AAAA,EAEA,OAAOG,IAAGC,KAAID,IAAGJ,MAAK,GAAGC,MAAK,GAAG;AAE/B,QAAI,UAAU,WAAW,GAAG;AAC1B,MAAAA,MAAKD;AACL,MAAAA,MAAKK;AACL,MAAAA,KAAID;AAAA,IACN;AAEA,UAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI;AAE7B,SAAK,IAAI,IAAIA;AACb,SAAK,IAAI,IAAIC;AACb,SAAK,IAAI,IAAID;AACb,SAAK,IAAI,IAAIC;AACb,SAAK,IAAI,IAAID,KAAIJ,MAAKI,KAAIJ;AAC1B,SAAK,IAAI,IAAIK,KAAIJ,MAAKI,KAAIJ;AAE1B,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,GAAGD,KAAIC,KAAI;AACf,WAAO,KAAK,MAAM,EAAE,OAAO,GAAGD,KAAIC,GAAE;AAAA,EACtC;AAAA;AAAA,EAGA,OAAO,IAAID,MAAK,GAAGC,MAAK,GAAG;AACzB,UAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI;AAE7B,SAAK,IAAI,IAAI,IAAI;AACjB,SAAK,IAAI,IAAI,IAAI;AACjB,SAAK,IAAI,IAAI,IAAI,KAAKA,MAAK;AAE3B,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,OAAO;AACL,WAAO,KAAK,MAAM,EAAE,MAAM,GAAG,SAAS;AAAA,EACxC;AAAA,EAEA,MAAMG,IAAGC,KAAID,IAAGJ,MAAK,GAAGC,MAAK,GAAG;AAE9B,QAAI,UAAU,WAAW,GAAG;AAC1B,MAAAA,MAAKD;AACL,MAAAA,MAAKK;AACL,MAAAA,KAAID;AAAA,IACN;AAGA,IAAAA,KAAI,QAAQA,EAAC;AACb,IAAAC,KAAI,QAAQA,EAAC;AAEb,UAAM,KAAK,KAAK,IAAID,EAAC;AACrB,UAAM,KAAK,KAAK,IAAIC,EAAC;AAErB,UAAM,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,IAAI;AAE7B,SAAK,IAAI,IAAI,IAAI;AACjB,SAAK,IAAI,IAAI,IAAI;AACjB,SAAK,IAAI,IAAI,IAAI;AACjB,SAAK,IAAI,IAAI,IAAI;AACjB,SAAK,IAAI,IAAI,IAAI,KAAKJ,MAAK;AAC3B,SAAK,IAAI,IAAI,IAAI,KAAKD,MAAK;AAE3B,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAMI,IAAGJ,KAAIC,KAAI;AACf,WAAO,KAAK,KAAKG,IAAG,GAAGJ,KAAIC,GAAE;AAAA,EAC/B;AAAA;AAAA,EAGA,MAAMI,IAAGL,KAAIC,KAAI;AACf,WAAO,KAAK,KAAK,GAAGI,IAAGL,KAAIC,GAAE;AAAA,EAC/B;AAAA,EAEA,UAAU;AACR,WAAO,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,EACxD;AAAA;AAAA,EAGA,WAAW;AACT,WACE,YACA,KAAK,IACL,MACA,KAAK,IACL,MACA,KAAK,IACL,MACA,KAAK,IACL,MACA,KAAK,IACL,MACA,KAAK,IACL;AAAA,EAEJ;AAAA;AAAA,EAGA,UAAU,GAAG;AAEX,QAAI,QAAO,aAAa,CAAC,GAAG;AAC1B,YAAM,SAAS,IAAI,QAAO,CAAC;AAC3B,aAAO,OAAO,UAAU,IAAI;AAAA,IAC9B;AAGA,UAAM,IAAI,QAAO,iBAAiB,CAAC;AACnC,UAAM,UAAU;AAChB,UAAM,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,IAAI,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,OAAO;AAGhE,UAAM,cAAc,IAAI,QAAO,EAC5B,WAAW,EAAE,IAAI,EAAE,EAAE,EACrB,WAAW,OAAO,EAClB,WAAW,CAAC,IAAI,CAAC,EAAE,EACnB,OAAO,EAAE,QAAQ,EAAE,MAAM,EACzB,MAAM,EAAE,OAAO,EAAE,KAAK,EACtB,OAAO,EAAE,KAAK,EACd,QAAQ,EAAE,KAAK,EACf,WAAW,IAAI,EAAE;AAGpB,QAAI,SAAS,EAAE,EAAE,KAAK,SAAS,EAAE,EAAE,GAAG;AACpC,YAAM,SAAS,IAAI,MAAM,IAAI,EAAE,EAAE,UAAU,WAAW;AAGtD,YAAMC,MAAK,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,OAAO,IAAI;AAC9C,YAAMC,MAAK,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,OAAO,IAAI;AAC9C,kBAAY,WAAWD,KAAIC,GAAE;AAAA,IAC/B;AAGA,gBAAY,WAAW,EAAE,IAAI,EAAE,EAAE;AACjC,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,UAAUC,IAAGC,IAAG;AACd,WAAO,KAAK,MAAM,EAAE,WAAWD,IAAGC,EAAC;AAAA,EACrC;AAAA,EAEA,WAAWD,IAAGC,IAAG;AACf,SAAK,KAAKD,MAAK;AACf,SAAK,KAAKC,MAAK;AACf,WAAO;AAAA,EACT;AAAA,EAEA,UAAU;AACR,WAAO;AAAA,MACL,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AACF;AAEO,SAAS,MAAM;AACpB,SAAO,IAAI,OAAO,KAAK,KAAK,OAAO,CAAC;AACtC;AAEO,SAAS,YAAY;AAC1B,MAAI;AAKF,QAAI,OAAO,KAAK,WAAW,cAAc,CAAC,KAAK,OAAO,GAAG;AACvD,YAAM,OAAO,KAAK,KAAK,GAAG,CAAC;AAC3B,YAAM,IAAI,KAAK,KAAK,aAAa;AACjC,WAAK,OAAO;AACZ,aAAO,IAAI,OAAO,CAAC;AAAA,IACrB;AACA,WAAO,IAAI,OAAO,KAAK,KAAK,aAAa,CAAC;AAAA,EAC5C,SAAS,GAAG;AACV,YAAQ;AAAA,MACN,gCAAgC,KAAK,KAAK,QAAQ;AAAA,IACpD;AACA,WAAO,IAAI,OAAO;AAAA,EACpB;AACF;AAEA,SAAS,QAAQ,QAAQ;;;AC3hBV,SAAR,SAA0B;AAE/B,MAAI,CAAC,OAAO,OAAO;AACjB,UAAMC,OAAM,aAAa,EAAE,KAAK,GAAG,CAAC;AACpC,IAAAA,KAAI,KAAK,MAAM,UAAU;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAE,KAAK,GAAG;AAEV,IAAAA,KAAI,KAAK,aAAa,OAAO;AAC7B,IAAAA,KAAI,KAAK,eAAe,MAAM;AAE9B,UAAM,OAAOA,KAAI,KAAK,EAAE;AAExB,WAAO,QAAQ,EAAE,KAAAA,MAAK,KAAK;AAAA,EAC7B;AAEA,MAAI,CAAC,OAAO,MAAM,IAAI,KAAK,YAAY;AACrC,UAAM,IAAI,QAAQ,SAAS,QAAQ,QAAQ,SAAS;AACpD,WAAO,MAAM,IAAI,MAAM,CAAC;AAAA,EAC1B;AAEA,SAAO,OAAO;AAChB;;;ACrBO,SAAS,YAAY,KAAK;AAC/B,SAAO,CAAC,IAAI,SAAS,CAAC,IAAI,UAAU,CAAC,IAAI,KAAK,CAAC,IAAI;AACrD;AAEO,SAAS,YAAY,MAAM;AAChC,SACE,SAAS,QAAQ,aAEf,QAAQ,SAAS,gBAAgB,YACjC,SAAUC,OAAM;AAEd,WAAOA,MAAK,YAAY;AACtB,MAAAA,QAAOA,MAAK;AAAA,IACd;AACA,WAAOA,UAAS,QAAQ;AAAA,EAC1B,GACA,KAAK,QAAQ,SAAS,iBAAiB,IAAI;AAEjD;AAEA,IAAqB,MAArB,MAAqB,KAAI;AAAA,EACvB,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EAEA,YAAY;AAEV,SAAK,KAAK,QAAQ,OAAO;AACzB,SAAK,KAAK,QAAQ,OAAO;AACzB,WAAO,IAAI,KAAI,IAAI;AAAA,EACrB;AAAA,EAEA,KAAK,QAAQ;AACX,UAAM,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AACxB,aACE,OAAO,WAAW,WACd,OAAO,MAAM,SAAS,EAAE,IAAI,UAAU,IACtC,MAAM,QAAQ,MAAM,IAClB,SACA,OAAO,WAAW,WAChB;AAAA,MACE,OAAO,QAAQ,OAAO,OAAO,OAAO,OAAO;AAAA,MAC3C,OAAO,OAAO,OAAO,OAAO,MAAM,OAAO;AAAA,MACzC,OAAO;AAAA,MACP,OAAO;AAAA,IACT,IACA,UAAU,WAAW,IACnB,CAAC,EAAE,MAAM,KAAK,SAAS,IACvB;AAEZ,SAAK,IAAI,OAAO,CAAC,KAAK;AACtB,SAAK,IAAI,OAAO,CAAC,KAAK;AACtB,SAAK,QAAQ,KAAK,IAAI,OAAO,CAAC,KAAK;AACnC,SAAK,SAAS,KAAK,IAAI,OAAO,CAAC,KAAK;AAGpC,SAAK,KAAK,KAAK,IAAI,KAAK;AACxB,SAAK,KAAK,KAAK,IAAI,KAAK;AACxB,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI;AAC5B,SAAK,KAAK,KAAK,IAAI,KAAK,IAAI;AAE5B,WAAO;AAAA,EACT;AAAA,EAEA,WAAW;AACT,WAAO,YAAY,IAAI;AAAA,EACzB;AAAA;AAAA,EAGA,MAAM,KAAK;AACT,UAAMC,KAAI,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC;AAChC,UAAMC,KAAI,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC;AAChC,UAAMC,SAAQ,KAAK,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,IAAI,IAAI,KAAK,IAAIF;AACjE,UAAMG,UAAS,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ,IAAI,IAAI,IAAI,MAAM,IAAIF;AAEpE,WAAO,IAAI,KAAID,IAAGC,IAAGC,QAAOC,OAAM;AAAA,EACpC;AAAA,EAEA,UAAU;AACR,WAAO,CAAC,KAAK,GAAG,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,QAAQ,MAAM,KAAK;AAAA,EAC/D;AAAA,EAEA,UAAU,GAAG;AACX,QAAI,EAAE,aAAa,SAAS;AAC1B,UAAI,IAAI,OAAO,CAAC;AAAA,IAClB;AAEA,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AAEX,UAAM,MAAM;AAAA,MACV,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC;AAAA,MACxB,IAAI,MAAM,KAAK,IAAI,KAAK,CAAC;AAAA,MACzB,IAAI,MAAM,KAAK,GAAG,KAAK,EAAE;AAAA,MACzB,IAAI,MAAM,KAAK,IAAI,KAAK,EAAE;AAAA,IAC5B;AAEA,QAAI,QAAQ,SAAU,GAAG;AACvB,UAAI,EAAE,UAAU,CAAC;AACjB,aAAO,KAAK,IAAI,MAAM,EAAE,CAAC;AACzB,aAAO,KAAK,IAAI,MAAM,EAAE,CAAC;AACzB,aAAO,KAAK,IAAI,MAAM,EAAE,CAAC;AACzB,aAAO,KAAK,IAAI,MAAM,EAAE,CAAC;AAAA,IAC3B,CAAC;AAED,WAAO,IAAI,KAAI,MAAM,MAAM,OAAO,MAAM,OAAO,IAAI;AAAA,EACrD;AACF;AAEA,SAAS,OAAO,IAAI,WAAW,OAAO;AACpC,MAAI;AAEJ,MAAI;AAEF,UAAM,UAAU,GAAG,IAAI;AAIvB,QAAI,YAAY,GAAG,KAAK,CAAC,YAAY,GAAG,IAAI,GAAG;AAC7C,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AAAA,EACF,SAAS,GAAG;AAEV,UAAM,MAAM,EAAE;AAAA,EAChB;AAEA,SAAO;AACT;AAEO,SAAS,OAAO;AAErB,QAAM,UAAU,CAAC,SAAS,KAAK,QAAQ;AAIvC,QAAM,QAAQ,CAAC,OAAO;AACpB,QAAI;AACF,YAAM,QAAQ,GAAG,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,EAAE,KAAK;AAClD,YAAMC,OAAM,MAAM,KAAK,QAAQ;AAC/B,YAAM,OAAO;AACb,aAAOA;AAAA,IACT,SAAS,GAAG;AAEV,YAAM,IAAI;AAAA,QACR,4BACE,GAAG,KAAK,QACV,sBAAsB,EAAE,SAAS,CAAC;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AAEA,QAAM,MAAM,OAAO,MAAM,SAAS,KAAK;AACvC,QAAMC,QAAO,IAAI,IAAI,GAAG;AAExB,SAAOA;AACT;AAEO,SAAS,KAAK,IAAI;AACvB,QAAM,UAAU,CAAC,SAAS,KAAK,sBAAsB;AACrD,QAAM,QAAQ,CAACC,QAAO;AAGpB,UAAM,IAAI;AAAA,MACR,4BAA4BA,IAAG,KAAK,QAAQ;AAAA,IAC9C;AAAA,EACF;AAEA,QAAM,MAAM,OAAO,MAAM,SAAS,KAAK;AACvC,QAAMC,QAAO,IAAI,IAAI,GAAG;AAGxB,MAAI,IAAI;AACN,WAAOA,MAAK,UAAU,GAAG,UAAU,EAAE,SAAS,CAAC;AAAA,EACjD;AAIA,SAAOA,MAAK,UAAU;AACxB;AAGO,SAAS,OAAOP,IAAGC,IAAG;AAC3B,QAAM,MAAM,KAAK,KAAK;AAEtB,SACED,KAAI,IAAI,KAAKC,KAAI,IAAI,KAAKD,KAAI,IAAI,IAAI,IAAI,SAASC,KAAI,IAAI,IAAI,IAAI;AAEvE;AAEA,gBAAgB;AAAA,EACd,SAAS;AAAA,IACP,QAAQD,IAAGC,IAAGC,QAAOC,SAAQ;AAE3B,UAAIH,MAAK,KAAM,QAAO,IAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAGlD,aAAO,KAAK,KAAK,WAAW,IAAI,IAAIA,IAAGC,IAAGC,QAAOC,OAAM,CAAC;AAAA,IAC1D;AAAA,IAEA,KAAK,OAAOK,QAAO;AAQjB,UAAI,EAAE,OAAAN,QAAO,QAAAC,QAAO,IAAI,KAAK,KAAK,CAAC,SAAS,QAAQ,CAAC;AAIrD,UACG,CAACD,UAAS,CAACC,WACZ,OAAOD,WAAU,YACjB,OAAOC,YAAW,UAClB;AACA,QAAAD,SAAQ,KAAK,KAAK;AAClB,QAAAC,UAAS,KAAK,KAAK;AAAA,MACrB;AAGA,UAAI,CAACD,UAAS,CAACC,SAAQ;AACrB,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,YAAM,IAAI,KAAK,QAAQ;AAEvB,YAAM,QAAQD,SAAQ,EAAE;AACxB,YAAM,QAAQC,UAAS,EAAE;AACzB,YAAM,OAAO,KAAK,IAAI,OAAO,KAAK;AAElC,UAAI,SAAS,MAAM;AACjB,eAAO;AAAA,MACT;AAEA,UAAI,aAAa,OAAO;AAIxB,UAAI,eAAe,SAAU,cAAa,OAAO,mBAAmB;AAEpE,MAAAK,SACEA,UAAS,IAAI,MAAMN,SAAQ,IAAI,QAAQ,EAAE,GAAGC,UAAS,IAAI,QAAQ,EAAE,CAAC;AAEtE,YAAM,MAAM,IAAI,IAAI,CAAC,EAAE;AAAA,QACrB,IAAI,OAAO,EAAE,OAAO,YAAY,QAAQK,OAAM,CAAC;AAAA,MACjD;AAEA,aAAO,KAAK,QAAQ,GAAG;AAAA,IACzB;AAAA,EACF;AACF,CAAC;AAED,SAAS,KAAK,KAAK;;;AC1QnB,IAAM,OAAN,cAAmB,MAAM;AAAA,EACvB,YAAY,MAAM,CAAC,MAAM,MAAM;AAC7B,UAAM,KAAK,GAAG,IAAI;AAClB,QAAI,OAAO,QAAQ,SAAU,QAAO;AACpC,SAAK,SAAS;AACd,SAAK,KAAK,GAAG,GAAG;AAAA,EAClB;AACF;AASA,IAAO,eAAQ;AAEf,OAAO,CAAC,IAAI,GAAG;AAAA,EACb,KAAK,mBAAmB,MAAM;AAC5B,QAAI,OAAO,mBAAmB,YAAY;AACxC,aAAO,KAAK,IAAI,CAAC,IAAI,GAAG,QAAQ;AAC9B,eAAO,eAAe,KAAK,IAAI,IAAI,GAAG,GAAG;AAAA,MAC3C,CAAC;AAAA,IACH,OAAO;AACL,aAAO,KAAK,IAAI,CAAC,OAAO;AACtB,eAAO,GAAG,cAAc,EAAE,GAAG,IAAI;AAAA,MACnC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,UAAU;AACR,WAAO,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,IAAI;AAAA,EAC9C;AACF,CAAC;AAED,IAAM,WAAW,CAAC,WAAW,eAAe,MAAM;AAElD,KAAK,SAAS,SAAUC,UAAS;AAC/B,EAAAA,WAAUA,SAAQ,OAAO,CAAC,KAAK,SAAS;AAEtC,QAAI,SAAS,SAAS,IAAI,EAAG,QAAO;AAGpC,QAAI,KAAK,CAAC,MAAM,IAAK,QAAO;AAG5B,QAAI,QAAQ,MAAM,WAAW;AAC3B,UAAI,MAAM,IAAI,IAAI,MAAM,UAAU,IAAI;AAAA,IACxC;AAGA,QAAI,IAAI,IAAI,YAAaC,QAAO;AAC9B,aAAO,KAAK,KAAK,MAAM,GAAGA,MAAK;AAAA,IACjC;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAEL,SAAO,CAAC,IAAI,GAAGD,QAAO;AACxB;;;ACzDe,SAAR,SAA0B,OAAO,QAAQ;AAC9C,SAAO,IAAI;AAAA,IACT,KAAK,UAAU,QAAQ,UAAU,iBAAiB,KAAK,GAAG,SAAU,MAAM;AACxE,aAAO,MAAM,IAAI;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAGO,SAAS,KAAK,OAAO;AAC1B,SAAO,SAAS,OAAO,KAAK,IAAI;AAClC;AAEO,SAAS,QAAQ,OAAO;AAC7B,SAAO,MAAM,KAAK,KAAK,cAAc,KAAK,CAAC;AAC7C;;;AChBA,IAAI,aAAa;AACV,IAAM,eAAe,CAAC;AAEtB,SAAS,UAAU,UAAU;AAClC,MAAI,IAAI,SAAS,eAAe;AAGhC,MAAI,MAAM,QAAQ,OAAQ,KAAI;AAC9B,MAAI,CAAC,EAAE,OAAQ,GAAE,SAAS,CAAC;AAC3B,SAAO,EAAE;AACX;AAEO,SAAS,eAAe,UAAU;AACvC,SAAO,SAAS,eAAe;AACjC;AAEO,SAAS,YAAY,UAAU;AACpC,MAAI,IAAI,SAAS,eAAe;AAChC,MAAI,MAAM,QAAQ,OAAQ,KAAI;AAC9B,MAAI,EAAE,OAAQ,GAAE,SAAS,CAAC;AAC5B;AAGO,SAAS,GAAG,MAAM,QAAQ,UAAU,SAAS,SAAS;AAC3D,QAAM,IAAI,SAAS,KAAK,WAAW,IAAI;AACvC,QAAM,WAAW,aAAa,IAAI;AAClC,QAAM,MAAM,UAAU,QAAQ;AAC9B,QAAM,IAAI,eAAe,QAAQ;AAGjC,WAAS,MAAM,QAAQ,MAAM,IAAI,SAAS,OAAO,MAAM,SAAS;AAGhE,MAAI,CAAC,SAAS,kBAAkB;AAC9B,aAAS,mBAAmB,EAAE;AAAA,EAChC;AAEA,SAAO,QAAQ,SAAU,OAAO;AAC9B,UAAM,KAAK,MAAM,MAAM,GAAG,EAAE,CAAC;AAC7B,UAAM,KAAK,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK;AAGlC,QAAI,EAAE,IAAI,IAAI,EAAE,KAAK,CAAC;AACtB,QAAI,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC;AAG9B,QAAI,EAAE,EAAE,EAAE,EAAE,SAAS,gBAAgB,IAAI;AAGzC,MAAE,iBAAiB,IAAI,GAAG,WAAW,KAAK;AAAA,EAC5C,CAAC;AACH;AAGO,SAAS,IAAI,MAAM,QAAQ,UAAU,SAAS;AACnD,QAAM,WAAW,aAAa,IAAI;AAClC,QAAM,MAAM,UAAU,QAAQ;AAC9B,QAAM,IAAI,eAAe,QAAQ;AAGjC,MAAI,OAAO,aAAa,YAAY;AAClC,eAAW,SAAS;AACpB,QAAI,CAAC,SAAU;AAAA,EACjB;AAGA,WAAS,MAAM,QAAQ,MAAM,IAAI,UAAU,UAAU,IAAI,MAAM,SAAS;AAExE,SAAO,QAAQ,SAAU,OAAO;AAC9B,UAAM,KAAK,SAAS,MAAM,MAAM,GAAG,EAAE,CAAC;AACtC,UAAM,KAAK,SAAS,MAAM,MAAM,GAAG,EAAE,CAAC;AACtC,QAAI,WAAW;AAEf,QAAI,UAAU;AAEZ,UAAI,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE,MAAM,GAAG,GAAG;AAEjC,UAAE;AAAA,UACA;AAAA,UACA,IAAI,EAAE,EAAE,MAAM,GAAG,EAAE,QAAQ;AAAA,UAC3B,WAAW;AAAA,QACb;AAEA,eAAO,IAAI,EAAE,EAAE,MAAM,GAAG,EAAE,QAAQ;AAAA,MACpC;AAAA,IACF,WAAW,MAAM,IAAI;AAEnB,UAAI,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE,EAAE,GAAG;AAC1B,aAAK,KAAK,IAAI,EAAE,EAAE,EAAE,GAAG;AACrB,cAAI,GAAG,CAAC,IAAI,EAAE,EAAE,KAAK,GAAG,GAAG,CAAC;AAAA,QAC9B;AAEA,eAAO,IAAI,EAAE,EAAE,EAAE;AAAA,MACnB;AAAA,IACF,WAAW,IAAI;AAEb,WAAK,SAAS,KAAK;AACjB,aAAK,aAAa,IAAI,KAAK,GAAG;AAC5B,cAAI,OAAO,WAAW;AACpB,gBAAI,GAAG,CAAC,OAAO,EAAE,EAAE,KAAK,GAAG,CAAC;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,IAAI;AAEb,UAAI,IAAI,EAAE,GAAG;AACX,aAAK,aAAa,IAAI,EAAE,GAAG;AACzB,cAAI,GAAG,CAAC,IAAI,SAAS,EAAE,KAAK,GAAG,CAAC;AAAA,QAClC;AAEA,eAAO,IAAI,EAAE;AAAA,MACf;AAAA,IACF,OAAO;AAEL,WAAK,SAAS,KAAK;AACjB,YAAI,GAAG,KAAK;AAAA,MACd;AAEA,kBAAY,QAAQ;AAAA,IACtB;AAAA,EACF,CAAC;AACH;AAEO,SAAS,SAAS,MAAM,OAAOE,OAAM,SAAS;AACnD,QAAM,IAAI,eAAe,IAAI;AAG7B,MAAI,iBAAiB,QAAQ,OAAO,OAAO;AACzC,MAAE,cAAc,KAAK;AAAA,EACvB,OAAO;AACL,YAAQ,IAAI,QAAQ,OAAO,YAAY,OAAO;AAAA,MAC5C,QAAQA;AAAA,MACR,YAAY;AAAA,MACZ,GAAG;AAAA,IACL,CAAC;AACD,MAAE,cAAc,KAAK;AAAA,EACvB;AACA,SAAO;AACT;;;AC1IA,IAAqB,cAArB,cAAyC,KAAK;AAAA,EAC5C,mBAAmB;AAAA,EAAC;AAAA,EAEpB,SAAS,OAAOC,OAAM,SAAS;AAC7B,WAAO,SAAS,MAAM,OAAOA,OAAM,OAAO;AAAA,EAC5C;AAAA,EAEA,cAAc,OAAO;AACnB,UAAM,MAAM,KAAK,eAAe,EAAE;AAClC,QAAI,CAAC,IAAK,QAAO;AAEjB,UAAM,SAAS,IAAI,MAAM,IAAI;AAE7B,eAAW,KAAK,QAAQ;AACtB,iBAAW,KAAK,OAAO,CAAC,GAAG;AACzB,eAAO,CAAC,EAAE,CAAC,EAAE,KAAK;AAAA,MACpB;AAAA,IACF;AAEA,WAAO,CAAC,MAAM;AAAA,EAChB;AAAA;AAAA,EAGA,KAAK,OAAOA,OAAM,SAAS;AACzB,SAAK,SAAS,OAAOA,OAAM,OAAO;AAClC,WAAO;AAAA,EACT;AAAA,EAEA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA,EAEA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,IAAI,OAAO,UAAU,SAAS;AAC5B,QAAI,MAAM,OAAO,UAAU,OAAO;AAClC,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,GAAG,OAAO,UAAU,SAAS,SAAS;AACpC,OAAG,MAAM,OAAO,UAAU,SAAS,OAAO;AAC1C,WAAO;AAAA,EACT;AAAA,EAEA,sBAAsB;AAAA,EAAC;AACzB;AAEA,SAAS,aAAa,aAAa;;;ACvDnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,SAAS,OAAO;AAAC;AAGjB,IAAM,WAAW;AAAA,EACtB,UAAU;AAAA,EACV,MAAM;AAAA,EACN,OAAO;AACT;AAGO,IAAM,QAAQ;AAAA;AAAA,EAEnB,gBAAgB;AAAA,EAChB,kBAAkB;AAAA,EAClB,gBAAgB;AAAA,EAChB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA;AAAA,EAGT,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA;AAAA,EAGJ,OAAO;AAAA,EACP,QAAQ;AAAA;AAAA,EAGR,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA;AAAA,EAGJ,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,cAAc;AAAA;AAAA,EAGd,eAAe;AACjB;;;ACzCA,IAAqB,WAArB,cAAsC,MAAM;AAAA,EAC1C,eAAe,MAAM;AACnB,UAAM,GAAG,IAAI;AACb,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EAEA,QAAQ;AACN,WAAO,IAAI,KAAK,YAAY,IAAI;AAAA,EAClC;AAAA,EAEA,KAAK,KAAK;AAER,QAAI,OAAO,QAAQ,SAAU,QAAO;AACpC,SAAK,SAAS;AACd,SAAK,KAAK,GAAG,KAAK,MAAM,GAAG,CAAC;AAC5B,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAMC,SAAQ,CAAC,GAAG;AAEhB,QAAIA,kBAAiB,MAAO,QAAOA;AAEnC,WAAOA,OAAM,KAAK,EAAE,MAAM,SAAS,EAAE,IAAI,UAAU;AAAA,EACrD;AAAA,EAEA,UAAU;AACR,WAAO,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,IAAI;AAAA,EAC9C;AAAA,EAEA,QAAQ;AACN,WAAO,IAAI,IAAI,IAAI;AAAA,EACrB;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,KAAK,GAAG;AAAA,EACtB;AAAA;AAAA,EAGA,UAAU;AACR,UAAM,MAAM,CAAC;AACb,QAAI,KAAK,GAAG,IAAI;AAChB,WAAO;AAAA,EACT;AACF;;;AC3CA,IAAqB,YAArB,MAAqB,WAAU;AAAA;AAAA,EAE7B,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EAEA,QAAQ,MAAM;AACZ,WAAO,IAAI,WAAU,KAAK,OAAO,IAAI;AAAA,EACvC;AAAA;AAAA,EAGA,OAAO,QAAQ;AACb,aAAS,IAAI,WAAU,MAAM;AAC7B,WAAO,IAAI,WAAU,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAAA,EAC9D;AAAA,EAEA,KAAK,OAAO,MAAM;AAChB,WAAO,MAAM,QAAQ,KAAK,IAAI,MAAM,CAAC,IAAI;AACzC,YAAQ,MAAM,QAAQ,KAAK,IAAI,MAAM,CAAC,IAAI;AAG1C,SAAK,QAAQ;AACb,SAAK,OAAO,QAAQ;AAGpB,QAAI,OAAO,UAAU,UAAU;AAE7B,WAAK,QAAQ,MAAM,KAAK,IACpB,IACA,CAAC,SAAS,KAAK,IACb,QAAQ,IACN,SACA,QACF;AAAA,IACR,WAAW,OAAO,UAAU,UAAU;AACpC,aAAO,MAAM,MAAM,aAAa;AAEhC,UAAI,MAAM;AAER,aAAK,QAAQ,WAAW,KAAK,CAAC,CAAC;AAG/B,YAAI,KAAK,CAAC,MAAM,KAAK;AACnB,eAAK,SAAS;AAAA,QAChB,WAAW,KAAK,CAAC,MAAM,KAAK;AAC1B,eAAK,SAAS;AAAA,QAChB;AAGA,aAAK,OAAO,KAAK,CAAC;AAAA,MACpB;AAAA,IACF,OAAO;AACL,UAAI,iBAAiB,YAAW;AAC9B,aAAK,QAAQ,MAAM,QAAQ;AAC3B,aAAK,OAAO,MAAM;AAAA,MACpB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,QAAQ;AACZ,aAAS,IAAI,WAAU,MAAM;AAC7B,WAAO,IAAI,WAAU,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAAA,EAC9D;AAAA;AAAA,EAGA,KAAK,QAAQ;AACX,aAAS,IAAI,WAAU,MAAM;AAC7B,WAAO,IAAI,WAAU,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAAA,EAC9D;AAAA;AAAA,EAGA,MAAM,QAAQ;AACZ,aAAS,IAAI,WAAU,MAAM;AAC7B,WAAO,IAAI,WAAU,OAAO,QAAQ,KAAK,QAAQ,OAAO,IAAI;AAAA,EAC9D;AAAA,EAEA,UAAU;AACR,WAAO,CAAC,KAAK,OAAO,KAAK,IAAI;AAAA,EAC/B;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EAEA,WAAW;AACT,YACG,KAAK,SAAS,MACX,CAAC,EAAE,KAAK,QAAQ,OAAO,MACvB,KAAK,SAAS,MACZ,KAAK,QAAQ,MACb,KAAK,SAAS,KAAK;AAAA,EAE7B;AAAA,EAEA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AACF;;;ACjGA,IAAM,kBAAkB,oBAAI,IAAI;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,IAAM,QAAQ,CAAC;AACR,SAAS,iBAAiB,IAAI;AACnC,QAAM,KAAK,EAAE;AACf;AAGe,SAAR,KAAsBC,OAAM,KAAK,IAAI;AAE1C,MAAIA,SAAQ,MAAM;AAEhB,IAAAA,QAAO,CAAC;AACR,UAAM,KAAK,KAAK;AAEhB,eAAW,QAAQ,KAAK;AACtB,MAAAA,MAAK,KAAK,QAAQ,IAAI,SAAS,KAAK,KAAK,SAAS,IAC9C,WAAW,KAAK,SAAS,IACzB,KAAK;AAAA,IACX;AAEA,WAAOA;AAAA,EACT,WAAWA,iBAAgB,OAAO;AAEhC,WAAOA,MAAK,OAAO,CAAC,MAAM,SAAS;AACjC,WAAK,IAAI,IAAI,KAAK,KAAK,IAAI;AAC3B,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP,WAAW,OAAOA,UAAS,YAAYA,MAAK,gBAAgB,QAAQ;AAElE,SAAK,OAAOA,MAAM,MAAK,KAAK,KAAKA,MAAK,GAAG,CAAC;AAAA,EAC5C,WAAW,QAAQ,MAAM;AAEvB,SAAK,KAAK,gBAAgBA,KAAI;AAAA,EAChC,WAAW,OAAO,MAAM;AAEtB,UAAM,KAAK,KAAK,aAAaA,KAAI;AACjC,WAAO,OAAO,OACV,MAASA,KAAI,IACb,SAAS,KAAK,GAAG,IACf,WAAW,GAAG,IACd;AAAA,EACR,OAAO;AAEL,UAAM,MAAM,OAAO,CAAC,MAAM,SAAS;AACjC,aAAO,KAAKA,OAAM,MAAM,IAAI;AAAA,IAC9B,GAAG,GAAG;AAGN,QAAI,OAAO,QAAQ,UAAU;AAC3B,YAAM,IAAI,UAAU,GAAG;AAAA,IACzB,WAAW,gBAAgB,IAAIA,KAAI,KAAK,MAAM,QAAQ,GAAG,GAAG;AAE1D,YAAM,IAAI,MAAM,GAAG;AAAA,IACrB,WAAW,IAAI,gBAAgB,OAAO;AAEpC,YAAM,IAAI,SAAS,GAAG;AAAA,IACxB;AAGA,QAAIA,UAAS,WAAW;AAEtB,UAAI,KAAK,SAAS;AAChB,aAAK,QAAQ,GAAG;AAAA,MAClB;AAAA,IACF,OAAO;AAEL,aAAO,OAAO,WACV,KAAK,KAAK,eAAe,IAAIA,OAAM,IAAI,SAAS,CAAC,IACjD,KAAK,KAAK,aAAaA,OAAM,IAAI,SAAS,CAAC;AAAA,IACjD;AAGA,QAAI,KAAK,YAAYA,UAAS,eAAeA,UAAS,MAAM;AAC1D,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAEA,SAAO;AACT;;;AC5EA,IAAqB,MAArB,MAAqB,aAAY,YAAY;AAAA,EAC3C,YAAY,MAAMC,QAAO;AACvB,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,OAAO,KAAK;AAEjB,QAAIA,UAAS,SAASA,QAAO;AAC3B,WAAK,KAAKA,MAAK;AAAA,IACjB;AAAA,EACF;AAAA;AAAA,EAGA,IAAI,SAAS,GAAG;AACd,cAAU,aAAa,OAAO;AAG9B,QACE,QAAQ,mBACR,KAAK,gBAAgB,QAAQ,OAAO,YACpC;AACA,cAAQ,gBAAgB;AAAA,IAC1B;AAEA,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,YAAY,QAAQ,IAAI;AAAA,IACpC,WAAW,QAAQ,SAAS,KAAK,KAAK,WAAW,CAAC,GAAG;AACnD,WAAK,KAAK,aAAa,QAAQ,MAAM,KAAK,KAAK,WAAW,CAAC,CAAC;AAAA,IAC9D;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,QAAQ,GAAG;AACf,WAAO,aAAa,MAAM,EAAE,IAAI,MAAM,CAAC;AAAA,EACzC;AAAA;AAAA,EAGA,WAAW;AACT,WAAO,IAAI;AAAA,MACT,IAAI,KAAK,KAAK,UAAU,SAAU,MAAM;AACtC,eAAO,MAAM,IAAI;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAGA,QAAQ;AAEN,WAAO,KAAK,KAAK,cAAc,GAAG;AAChC,WAAK,KAAK,YAAY,KAAK,KAAK,SAAS;AAAA,IAC3C;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,OAAO,MAAM,eAAe,MAAM;AAEtC,SAAK,eAAe;AAGpB,QAAI,YAAY,KAAK,KAAK,UAAU,IAAI;AACxC,QAAI,cAAc;AAEhB,kBAAY,YAAY,SAAS;AAAA,IACnC;AACA,WAAO,IAAI,KAAK,YAAY,SAAS;AAAA,EACvC;AAAA;AAAA,EAGA,KAAK,OAAO,MAAM;AAChB,UAAM,WAAW,KAAK,SAAS;AAC/B,QAAI,GAAG;AAEP,SAAK,IAAI,GAAG,KAAK,SAAS,QAAQ,IAAI,IAAI,KAAK;AAC7C,YAAM,MAAM,SAAS,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;AAEtC,UAAI,MAAM;AACR,iBAAS,CAAC,EAAE,KAAK,OAAO,IAAI;AAAA,MAC9B;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,UAAUA,QAAO;AACvB,WAAO,KAAK,IAAI,IAAI,KAAI,OAAO,QAAQ,GAAGA,MAAK,CAAC;AAAA,EAClD;AAAA;AAAA,EAGA,QAAQ;AACN,WAAO,MAAM,KAAK,KAAK,UAAU;AAAA,EACnC;AAAA;AAAA,EAGA,IAAI,GAAG;AACL,WAAO,MAAM,KAAK,KAAK,WAAW,CAAC,CAAC;AAAA,EACtC;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAGA,IAAI,SAAS;AACX,WAAO,KAAK,MAAM,OAAO,KAAK;AAAA,EAChC;AAAA,EAEA,KAAK,UAAU,WAAW;AACxB,WAAO,KAAK,IAAI,UAAU,WAAW,IAAI;AAAA,EAC3C;AAAA;AAAA,EAGA,GAAG,IAAI;AAEL,QAAI,OAAO,OAAO,eAAe,CAAC,KAAK,KAAK,IAAI;AAC9C,WAAK,KAAK,KAAK,IAAI,KAAK,IAAI;AAAA,IAC9B;AAGA,WAAO,KAAK,KAAK,MAAM,EAAE;AAAA,EAC3B;AAAA;AAAA,EAGA,MAAM,SAAS;AACb,WAAO,CAAC,EAAE,MAAM,KAAK,KAAK,KAAK,UAAU,EAAE,QAAQ,QAAQ,IAAI;AAAA,EACjE;AAAA;AAAA,EAGA,OAAO;AACL,WAAO,MAAM,KAAK,KAAK,SAAS;AAAA,EAClC;AAAA;AAAA,EAGA,QAAQ,UAAU;AAChB,UAAM,KAAK,KAAK;AAChB,UAAM,UACJ,GAAG,WACH,GAAG,mBACH,GAAG,qBACH,GAAG,sBACH,GAAG,yBACH,GAAG,oBACH;AACF,WAAO,WAAW,QAAQ,KAAK,IAAI,QAAQ;AAAA,EAC7C;AAAA;AAAA,EAGA,OAAO,MAAM;AACX,QAAI,SAAS;AAGb,QAAI,CAAC,OAAO,KAAK,WAAY,QAAO;AAGpC,aAAS,MAAM,OAAO,KAAK,UAAU;AAErC,QAAI,CAAC,KAAM,QAAO;AAGlB,OAAG;AACD,UACE,OAAO,SAAS,WAAW,OAAO,QAAQ,IAAI,IAAI,kBAAkB;AAEpE,eAAO;AAAA,IACX,SAAU,SAAS,MAAM,OAAO,KAAK,UAAU;AAE/C,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,IAAI,SAAS,GAAG;AACd,cAAU,aAAa,OAAO;AAC9B,SAAK,IAAI,SAAS,CAAC;AACnB,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,QAAQ,GAAG;AACf,WAAO,aAAa,MAAM,EAAE,IAAI,MAAM,CAAC;AAAA,EACzC;AAAA;AAAA,EAGA,SAAS;AACP,QAAI,KAAK,OAAO,GAAG;AACjB,WAAK,OAAO,EAAE,cAAc,IAAI;AAAA,IAClC;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,cAAc,SAAS;AACrB,SAAK,KAAK,YAAY,QAAQ,IAAI;AAElC,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,QAAQ,SAAS;AACf,cAAU,aAAa,OAAO;AAE9B,QAAI,KAAK,KAAK,YAAY;AACxB,WAAK,KAAK,WAAW,aAAa,QAAQ,MAAM,KAAK,IAAI;AAAA,IAC3D;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,YAAY,GAAGC,OAAM,MAAM;AAC/B,UAAM,SAAS,MAAM;AACrB,UAAMD,SAAQ,KAAK,KAAKC,IAAG;AAE3B,eAAW,KAAKD,QAAO;AACrB,UAAI,OAAOA,OAAM,CAAC,MAAM,UAAU;AAChC,QAAAA,OAAM,CAAC,IAAI,KAAK,MAAMA,OAAM,CAAC,IAAI,MAAM,IAAI;AAAA,MAC7C;AAAA,IACF;AAEA,SAAK,KAAKA,MAAK;AACf,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,IAAI,SAAS,UAAU;AACrB,WAAO,KAAK,IAAI,SAAS,UAAU,GAAG;AAAA,EACxC;AAAA;AAAA,EAGA,WAAW;AACT,WAAO,KAAK,GAAG;AAAA,EACjB;AAAA,EAEA,MAAM,MAAM;AAEV,SAAK,KAAK,cAAc;AACxB,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,MAAM;AACT,UAAM,SAAS,KAAK,OAAO;AAE3B,QAAI,CAAC,QAAQ;AACX,aAAO,KAAK,MAAM,IAAI;AAAA,IACxB;AAEA,UAAME,YAAW,OAAO,MAAM,IAAI;AAClC,WAAO,OAAO,IAAI,MAAMA,SAAQ,EAAE,IAAI,IAAI;AAAA,EAC5C;AAAA;AAAA,EAGA,iBAAiB;AAEf,SAAK,KAAK,WAAY;AACpB,WAAK,eAAe;AAAA,IACtB,CAAC;AAED,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,IAAI,SAAS,UAAU,IAAI;AACzB,QAAI,OAAO,YAAY,WAAW;AAChC,WAAK;AACL,iBAAW;AACX,gBAAU;AAAA,IACZ;AAGA,QAAI,WAAW,QAAQ,OAAO,YAAY,YAAY;AAEpD,iBAAW,YAAY,OAAO,OAAO;AAGrC,WAAK,eAAe;AACpB,UAAI,UAAU;AAGd,UAAI,WAAW,MAAM;AACnB,kBAAU,MAAM,QAAQ,KAAK,UAAU,IAAI,CAAC;AAG5C,YAAI,UAAU;AACZ,gBAAM,SAAS,QAAQ,OAAO;AAC9B,oBAAU,UAAU;AAGpB,cAAI,WAAW,MAAO,QAAO;AAAA,QAC/B;AAGA,gBAAQ,KAAK,WAAY;AACvB,gBAAM,SAAS,QAAQ,IAAI;AAC3B,gBAAM,QAAQ,UAAU;AAGxB,cAAI,WAAW,OAAO;AACpB,iBAAK,OAAO;AAAA,UAGd,WAAW,UAAU,SAAS,OAAO;AACnC,iBAAK,QAAQ,KAAK;AAAA,UACpB;AAAA,QACF,GAAG,IAAI;AAAA,MACT;AAGA,aAAO,WAAW,QAAQ,KAAK,YAAY,QAAQ,KAAK;AAAA,IAC1D;AAKA,eAAW,YAAY,OAAO,QAAQ;AAGtC,UAAM,OAAO,OAAO,WAAW,EAAE;AACjC,UAAM,WAAW,QAAQ,SAAS,uBAAuB;AAGzD,SAAK,YAAY;AAGjB,aAAS,MAAM,KAAK,SAAS,QAAQ,SAAS;AAC5C,eAAS,YAAY,KAAK,iBAAiB;AAAA,IAC7C;AAEA,UAAM,SAAS,KAAK,OAAO;AAG3B,WAAO,WAAW,KAAK,QAAQ,QAAQ,KAAK,SAAS,KAAK,IAAI,QAAQ;AAAA,EACxE;AACF;AAEA,OAAO,KAAK,EAAE,MAAM,MAAM,QAAQ,CAAC;AACnC,SAAS,KAAK,KAAK;;;ACpVnB,IAAqB,UAArB,cAAqC,IAAI;AAAA,EACvC,YAAY,MAAMC,QAAO;AACvB,UAAM,MAAMA,MAAK;AAGjB,SAAK,MAAM,CAAC;AAGZ,SAAK,KAAK,WAAW;AAErB,QAAI,KAAK,aAAa,YAAY,KAAK,KAAK,aAAa,YAAY,GAAG;AAEtE,WAAK;AAAA,QACH,KAAK,MAAM,KAAK,aAAa,YAAY,CAAC,KACxC,KAAK,MAAM,KAAK,aAAa,YAAY,CAAC,KAC1C,CAAC;AAAA,MACL;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,OAAOC,IAAGC,IAAG;AACX,WAAO,KAAK,GAAGD,EAAC,EAAE,GAAGC,EAAC;AAAA,EACxB;AAAA;AAAA,EAGA,GAAGD,IAAG;AACJ,WAAOA,MAAK,OACR,KAAK,EAAE,IAAI,KAAK,MAAM,IAAI,IAC1B,KAAK,EAAEA,KAAI,KAAK,MAAM,IAAI,CAAC;AAAA,EACjC;AAAA;AAAA,EAGA,GAAGC,IAAG;AACJ,WAAOA,MAAK,OACR,KAAK,EAAE,IAAI,KAAK,OAAO,IAAI,IAC3B,KAAK,EAAEA,KAAI,KAAK,OAAO,IAAI,CAAC;AAAA,EAClC;AAAA;AAAA,EAGA,OAAO;AACL,UAAMC,QAAO,KAAK,KAAK;AACvB,WAAOA,SAAQA,MAAK,KAAK;AAAA,EAC3B;AAAA;AAAA,EAGA,MAAMF,IAAGC,IAAG;AACV,WAAO,KAAK,GAAGD,EAAC,EAAE,GAAGC,EAAC;AAAA,EACxB;AAAA;AAAA,EAGA,GAAGD,KAAI,GAAG;AACR,WAAO,KAAK,EAAE,IAAI,UAAUA,EAAC,EAAE,KAAK,KAAK,EAAE,CAAC,CAAC;AAAA,EAC/C;AAAA;AAAA,EAGA,GAAGC,KAAI,GAAG;AACR,WAAO,KAAK,EAAE,IAAI,UAAUA,EAAC,EAAE,KAAK,KAAK,EAAE,CAAC,CAAC;AAAA,EAC/C;AAAA,EAEA,iBAAiB;AACf,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,OAAOE,SAAQ;AACb,WAAO,KAAK,KAAK,UAAUA,OAAM;AAAA,EACnC;AAAA;AAAA,EAGA,KAAKH,IAAGC,IAAG;AACT,WAAO,KAAK,EAAED,EAAC,EAAE,EAAEC,EAAC;AAAA,EACtB;AAAA;AAAA,EAGA,QAAQ,QAAQ,KAAK,KAAK,GAAG;AAC3B,UAAM,aAAa,OAAO,UAAU;AACpC,QAAI,CAAC,YAAY;AACf,cAAQ,aAAa,KAAK;AAAA,IAC5B;AACA,UAAM,UAAU,IAAI,aAAK;AACzB,QAAI,SAAS;AAEb,YACG,SAAS,OAAO,OAAO,MACxB,OAAO,SAAS,QAAQ,YACxB,OAAO,aAAa,sBACpB;AACA,cAAQ,KAAK,MAAM;AAEnB,UAAI,CAAC,cAAc,OAAO,SAAS,MAAM,MAAM;AAC7C;AAAA,MACF;AACA,UAAI,cAAc,OAAO,QAAQ,KAAK,GAAG;AACvC;AAAA,MACF;AACA,UAAI,OAAO,SAAS,KAAK,KAAK,EAAE,MAAM;AAEpC,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,UAAUG,OAAM;AACd,IAAAA,QAAO,KAAK,KAAKA,KAAI;AACrB,QAAI,CAACA,MAAM,QAAO;AAElB,UAAM,KAAKA,QAAO,IAAI,MAAM,SAAS;AACrC,WAAO,IAAI,aAAa,EAAE,CAAC,CAAC,IAAI;AAAA,EAClC;AAAA;AAAA,EAGA,OAAO;AACL,UAAM,IAAI,KAAK,OAAO,SAAS,IAAI,CAAC;AACpC,WAAO,KAAK,EAAE,KAAK;AAAA,EACrB;AAAA;AAAA,EAGA,QAAQ,GAAG;AACT,SAAK,MAAM;AACX,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,KAAKC,QAAOF,SAAQ;AAClB,UAAM,IAAI,iBAAiB,MAAME,QAAOF,OAAM;AAE9C,WAAO,KAAK,MAAM,IAAI,UAAU,EAAE,KAAK,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,MAAM,CAAC;AAAA,EAC1E;AAAA;AAAA,EAGA,MAAME,QAAO;AACX,WAAO,KAAK,KAAK,SAASA,MAAK;AAAA,EACjC;AAAA;AAAA,EAGA,iBAAiB;AACf,mBAAe,MAAM,KAAK,GAAG;AAC7B,WAAO,MAAM,eAAe;AAAA,EAC9B;AAAA;AAAA,EAGA,EAAEL,IAAG;AACH,WAAO,KAAK,KAAK,KAAKA,EAAC;AAAA,EACzB;AAAA;AAAA,EAGA,EAAEC,IAAG;AACH,WAAO,KAAK,KAAK,KAAKA,EAAC;AAAA,EACzB;AACF;AAEA,OAAO,SAAS;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,SAAS,SAAS,SAAS;;;AC7K3B,IAAM,QAAQ;AAAA,EACZ,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,CAAC,SAAS,WAAW,MAAM;AAAA,EACjC,QAAQ,SAAU,GAAG,GAAG;AACtB,WAAO,MAAM,UAAU,IAAI,IAAI,MAAM;AAAA,EACvC;AACF;AAGC,CAAC,QAAQ,QAAQ,EAAE,QAAQ,SAAU,GAAG;AACvC,QAAM,YAAY,CAAC;AACnB,MAAI;AAEJ,YAAU,CAAC,IAAI,SAAU,GAAG;AAC1B,QAAI,OAAO,MAAM,aAAa;AAC5B,aAAO,KAAK,KAAK,CAAC;AAAA,IACpB;AACA,QACE,OAAO,MAAM,YACb,aAAa,SACb,MAAM,MAAM,CAAC,KACb,aAAa,SACb;AACA,WAAK,KAAK,GAAG,CAAC;AAAA,IAChB,OAAO;AAEL,WAAK,IAAI,MAAM,CAAC,EAAE,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,YAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM;AAC1B,eAAK,KAAK,MAAM,OAAO,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,kBAAgB,CAAC,WAAW,QAAQ,GAAG,SAAS;AAClD,CAAC;AAED,gBAAgB,CAAC,WAAW,QAAQ,GAAG;AAAA;AAAA,EAErC,QAAQ,SAAU,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AAEpC,QAAI,OAAO,MAAM;AACf,aAAO,IAAI,OAAO,IAAI;AAAA,IACxB;AAGA,WAAO,KAAK,KAAK,aAAa,IAAI,OAAO,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,EAC9D;AAAA;AAAA,EAGA,QAAQ,SAAU,OAAOK,KAAIC,KAAI;AAC/B,WAAO,KAAK,UAAU,EAAE,QAAQ,OAAO,IAAID,KAAI,IAAIC,IAAG,GAAG,IAAI;AAAA,EAC/D;AAAA;AAAA,EAGA,MAAM,SAAUC,IAAGC,IAAGH,KAAIC,KAAI;AAC5B,WAAO,UAAU,WAAW,KAAK,UAAU,WAAW,IAClD,KAAK,UAAU,EAAE,MAAMC,IAAG,IAAIC,IAAG,IAAIH,IAAG,GAAG,IAAI,IAC/C,KAAK,UAAU,EAAE,MAAM,CAACE,IAAGC,EAAC,GAAG,IAAIH,KAAI,IAAIC,IAAG,GAAG,IAAI;AAAA,EAC3D;AAAA,EAEA,OAAO,SAAU,KAAKD,KAAIC,KAAI;AAC5B,WAAO,KAAK,UAAU,EAAE,OAAO,KAAK,IAAID,KAAI,IAAIC,IAAG,GAAG,IAAI;AAAA,EAC5D;AAAA;AAAA,EAGA,OAAO,SAAUC,IAAGC,IAAGH,KAAIC,KAAI;AAC7B,WAAO,UAAU,WAAW,KAAK,UAAU,WAAW,IAClD,KAAK,UAAU,EAAE,OAAOC,IAAG,IAAIC,IAAG,IAAIH,IAAG,GAAG,IAAI,IAChD,KAAK,UAAU,EAAE,OAAO,CAACE,IAAGC,EAAC,GAAG,IAAIH,KAAI,IAAIC,IAAG,GAAG,IAAI;AAAA,EAC5D;AAAA;AAAA,EAGA,WAAW,SAAUC,IAAGC,IAAG;AACzB,WAAO,KAAK,UAAU,EAAE,WAAW,CAACD,IAAGC,EAAC,EAAE,GAAG,IAAI;AAAA,EACnD;AAAA;AAAA,EAGA,UAAU,SAAUD,IAAGC,IAAG;AACxB,WAAO,KAAK,UAAU,EAAE,UAAU,CAACD,IAAGC,EAAC,EAAE,GAAG,IAAI;AAAA,EAClD;AAAA;AAAA,EAGA,MAAM,SAAU,YAAY,QAAQ,SAAS,UAAU;AACrD,QAAI,aAAa,QAAQ,SAAS,MAAM,IAAI;AAC1C,eAAS;AACT,kBAAY;AAAA,IACd;AAEA,WAAO,KAAK,UAAU,EAAE,MAAM,WAAW,OAAe,GAAG,IAAI;AAAA,EACjE;AAAA;AAAA,EAGA,SAAS,SAAU,OAAO;AACxB,WAAO,KAAK,KAAK,WAAW,KAAK;AAAA,EACnC;AACF,CAAC;AAED,gBAAgB,UAAU;AAAA;AAAA,EAExB,QAAQ,SAAUD,IAAGC,KAAID,IAAG;AAC1B,UAAM,QAAQ,KAAK,YAAY,MAAM;AACrC,WAAO,SAAS,mBACZ,KAAK,KAAK,KAAK,IAAI,UAAUA,EAAC,CAAC,IAC/B,KAAK,GAAGA,EAAC,EAAE,GAAGC,EAAC;AAAA,EACrB;AACF,CAAC;AAED,gBAAgB,QAAQ;AAAA;AAAA,EAEtB,QAAQ,WAAY;AAClB,WAAO,KAAK,KAAK,eAAe;AAAA,EAClC;AAAA;AAAA,EAEA,SAAS,SAAUC,SAAQ;AACzB,WAAO,IAAI,MAAM,KAAK,KAAK,iBAAiBA,OAAM,CAAC;AAAA,EACrD;AACF,CAAC;AAED,gBAAgB,CAAC,WAAW,QAAQ,GAAG;AAAA;AAAA,EAErC,MAAM,SAAU,GAAG,GAAG;AACpB,QAAI,OAAO,MAAM,UAAU;AACzB,WAAK,KAAK,EAAG,MAAK,KAAK,GAAG,EAAE,CAAC,CAAC;AAC9B,aAAO;AAAA,IACT;AAEA,WAAO,MAAM,YACT,KAAK,QAAQ,CAAC,IACd,MAAM,WACJ,KAAK,KAAK,eAAe,CAAC,IAC1B,MAAM,UACJ,MAAM,YACN,MAAM,YACN,MAAM,aACN,MAAM,aACN,MAAM,UACN,KAAK,KAAK,UAAU,GAAG,CAAC,IACxB,KAAK,KAAK,GAAG,CAAC;AAAA,EACxB;AACF,CAAC;AAGD,IAAMC,WAAU;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,EAAE,OAAO,SAAU,MAAM,OAAO;AAE9B,QAAM,KAAK,SAAU,GAAG;AACtB,QAAI,MAAM,MAAM;AACd,WAAK,IAAI,KAAK;AAAA,IAChB,OAAO;AACL,WAAK,GAAG,OAAO,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAEA,OAAK,KAAK,IAAI;AACd,SAAO;AACT,GAAG,CAAC,CAAC;AAEL,gBAAgB,WAAWA,QAAO;;;ACjM3B,SAAS,cAAc;AAC5B,SAAO,KAAK,KAAK,aAAa,IAAI;AACpC;AAGO,SAAS,YAAY;AAC1B,QAAM,UAAU,KAAK,KAAK,WAAW,KAAK,IAEvC,MAAM,UAAU,EAChB,MAAM,GAAG,EAAE,EACX,IAAI,SAAU,KAAK;AAElB,UAAM,KAAK,IAAI,KAAK,EAAE,MAAM,GAAG;AAC/B,WAAO;AAAA,MACL,GAAG,CAAC;AAAA,MACJ,GAAG,CAAC,EAAE,MAAM,SAAS,EAAE,IAAI,SAAUC,MAAK;AACxC,eAAO,WAAWA,IAAG;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,EACF,CAAC,EACA,QAAQ,EAER,OAAO,SAAUC,SAAQC,YAAW;AACnC,QAAIA,WAAU,CAAC,MAAM,UAAU;AAC7B,aAAOD,QAAO,UAAU,OAAO,UAAUC,WAAU,CAAC,CAAC,CAAC;AAAA,IACxD;AACA,WAAOD,QAAOC,WAAU,CAAC,CAAC,EAAE,MAAMD,SAAQC,WAAU,CAAC,CAAC;AAAA,EACxD,GAAG,IAAI,OAAO,CAAC;AAEjB,SAAO;AACT;AAGO,SAAS,SAAS,QAAQ,GAAG;AAClC,MAAI,SAAS,OAAQ,QAAO;AAE5B,MAAI,cAAc,KAAK,IAAI,EAAG,QAAO,KAAK,MAAM,QAAQ,CAAC;AAEzD,QAAMC,OAAM,KAAK,UAAU;AAC3B,QAAM,OAAO,OAAO,UAAU,EAAE,QAAQ;AAExC,OAAK,MAAM,QAAQ,CAAC,EAAE,YAAY,EAAE,UAAU,KAAK,SAASA,IAAG,CAAC;AAEhE,SAAO;AACT;AAGO,SAAS,OAAO,GAAG;AACxB,SAAO,KAAK,SAAS,KAAK,KAAK,GAAG,CAAC;AACrC;AAGO,SAAS,UAAU,GAAG,UAAU;AAErC,MAAI,KAAK,QAAQ,OAAO,MAAM,UAAU;AACtC,UAAM,aAAa,IAAI,OAAO,IAAI,EAAE,UAAU;AAC9C,WAAO,KAAK,OAAO,aAAa,WAAW,CAAC;AAAA,EAC9C;AAEA,MAAI,CAAC,OAAO,aAAa,CAAC,GAAG;AAE3B,QAAI,EAAE,GAAG,GAAG,QAAQ,UAAU,GAAG,IAAI,EAAE;AAAA,EACzC;AAGA,QAAM,gBAAgB,aAAa,OAAO,OAAO,YAAY;AAC7D,QAAM,SAAS,IAAI,OAAO,aAAa,EAAE,UAAU,CAAC;AACpD,SAAO,KAAK,KAAK,aAAa,MAAM;AACtC;AAEA,gBAAgB,WAAW;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;AC/ED,IAAqB,YAArB,MAAqB,mBAAkB,QAAQ;AAAA,EAC7C,UAAU;AACR,SAAK,KAAK,WAAY;AACpB,UAAI,gBAAgB,YAAW;AAC7B,eAAO,KAAK,QAAQ,EAAE,QAAQ;AAAA,MAChC;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS,KAAK,OAAO,GAAG,QAAQ,OAAO,MAAM,IAAI,GAAG;AAE1D,YAAQ,UAAU,KAAK,OAAO,SAAS,EAAE,SAAS;AAElD,SAAK,KAAK,SAAU,GAAG,UAAU;AAE/B,aAAO,SAAS,SAAS,SAAS,IAAI,CAAC,EAAE,SAAS,QAAQ,KAAK;AAAA,IACjE,CAAC;AAED,WAAO,KAAK,OAAO;AAAA,EACrB;AACF;AAEA,SAAS,WAAW,WAAW;;;ACxB/B,IAAqB,OAArB,cAAkC,UAAU;AAAA,EAC1C,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,QAAQ,IAAI,GAAGA,MAAK;AAAA,EACtC;AAAA,EAEA,UAAU;AACR,WAAO;AAAA,EACT;AAAA,EAEA,UAAU;AACR,WAAO;AAAA,EACT;AACF;AAEA,SAAS,MAAM,MAAM;;;ACdrB,IAAqB,QAArB,cAAmC,QAAQ;AAAC;AAE5C,SAAS,OAAO,OAAO;;;ACLvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGO,SAAS,GAAGC,KAAI;AACrB,SAAO,KAAK,KAAK,MAAMA,GAAE;AAC3B;AAGO,SAAS,GAAGC,KAAI;AACrB,SAAO,KAAK,KAAK,MAAMA,GAAE;AAC3B;AAGO,SAAS,EAAEC,IAAG;AACnB,SAAOA,MAAK,OAAO,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAGA,KAAI,KAAK,GAAG,CAAC;AAClE;AAGO,SAAS,EAAEC,IAAG;AACnB,SAAOA,MAAK,OAAO,KAAK,GAAG,IAAI,KAAK,GAAG,IAAI,KAAK,GAAGA,KAAI,KAAK,GAAG,CAAC;AAClE;AAGO,SAAS,GAAGD,IAAG;AACpB,SAAO,KAAK,KAAK,MAAMA,EAAC;AAC1B;AAGO,SAAS,GAAGC,IAAG;AACpB,SAAO,KAAK,KAAK,MAAMA,EAAC;AAC1B;AAGO,SAAS,MAAMC,QAAO;AAC3B,SAAOA,UAAS,OAAO,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,UAAUA,MAAK,EAAE,OAAO,CAAC,CAAC;AAC/E;AAGO,SAAS,OAAOC,SAAQ;AAC7B,SAAOA,WAAU,OACb,KAAK,GAAG,IAAI,IACZ,KAAK,GAAG,IAAI,UAAUA,OAAM,EAAE,OAAO,CAAC,CAAC;AAC7C;;;AC9BA,IAAqB,UAArB,cAAqC,MAAM;AAAA,EACzC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,WAAW,IAAI,GAAGA,MAAK;AAAA,EACzC;AAAA,EAEA,KAAKC,QAAOC,SAAQ;AAClB,UAAM,IAAI,iBAAiB,MAAMD,QAAOC,OAAM;AAE9C,WAAO,KAAK,GAAG,IAAI,UAAU,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE;AAAA,MAC/C,IAAI,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;AAAA,IAClC;AAAA,EACF;AACF;AAEA,OAAO,SAAS,eAAO;AAEvB,gBAAgB,aAAa;AAAA;AAAA,EAE3B,SAAS,kBAAkB,SAAUD,SAAQ,GAAGC,UAASD,QAAO;AAC9D,WAAO,KAAK,IAAI,IAAI,QAAQ,CAAC,EAAE,KAAKA,QAAOC,OAAM,EAAE,KAAK,GAAG,CAAC;AAAA,EAC9D,CAAC;AACH,CAAC;AAED,SAAS,SAAS,SAAS;;;AC/B3B,IAAM,WAAN,cAAuB,IAAI;AAAA,EACzB,YAAY,OAAO,QAAQ,SAAS,uBAAuB,GAAG;AAC5D,UAAM,IAAI;AAAA,EACZ;AAAA;AAAA,EAGA,IAAI,SAAS,UAAU,IAAI;AACzB,QAAI,OAAO,YAAY,WAAW;AAChC,WAAK;AACL,iBAAW;AACX,gBAAU;AAAA,IACZ;AAIA,QAAI,WAAW,QAAQ,OAAO,YAAY,YAAY;AACpD,YAAM,UAAU,IAAI,IAAI,OAAO,WAAW,EAAE,CAAC;AAC7C,cAAQ,IAAI,KAAK,KAAK,UAAU,IAAI,CAAC;AAErC,aAAO,QAAQ,IAAI,OAAO,EAAE;AAAA,IAC9B;AAGA,WAAO,MAAM,IAAI,SAAS,OAAO,EAAE;AAAA,EACrC;AACF;AAEA,SAAS,UAAU,UAAU;AAE7B,IAAO,mBAAQ;;;ACjCf;AAAA;AAAA;AAAA;AAAA;AAEO,SAAS,KAAKC,IAAGC,IAAG;AACzB,UAAQ,KAAK,YAAY,MAAM,SAAS,mBACpC,KAAK,KAAK,EAAE,IAAI,IAAI,UAAUD,EAAC,GAAG,IAAI,IAAI,UAAUC,EAAC,EAAE,CAAC,IACxD,KAAK,KAAK,EAAE,IAAI,IAAI,UAAUD,EAAC,GAAG,IAAI,IAAI,UAAUC,EAAC,EAAE,CAAC;AAC9D;AAEO,SAAS,GAAGD,IAAGC,IAAG;AACvB,UAAQ,KAAK,YAAY,MAAM,SAAS,mBACpC,KAAK,KAAK,EAAE,IAAI,IAAI,UAAUD,EAAC,GAAG,IAAI,IAAI,UAAUC,EAAC,EAAE,CAAC,IACxD,KAAK,KAAK,EAAE,IAAI,IAAI,UAAUD,EAAC,GAAG,IAAI,IAAI,UAAUC,EAAC,EAAE,CAAC;AAC9D;;;ACAA,IAAqB,WAArB,cAAsC,UAAU;AAAA,EAC9C,YAAY,MAAMC,QAAO;AACvB;AAAA,MACE,UAAU,OAAO,YAAY,OAAO,SAAS,WAAW,OAAO,IAAI;AAAA,MACnEA;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,KAAK,GAAG,GAAG,GAAG;AACZ,QAAI,MAAM,YAAa,KAAI;AAC3B,WAAO,MAAM,KAAK,GAAG,GAAG,CAAC;AAAA,EAC3B;AAAA,EAEA,OAAO;AACL,WAAO,IAAI,IAAI;AAAA,EACjB;AAAA,EAEA,UAAU;AACR,WAAO,SAAS,gBAAgB,KAAK,GAAG,IAAI,GAAG;AAAA,EACjD;AAAA;AAAA,EAGA,WAAW;AACT,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA;AAAA,EAGA,OAAO,OAAO;AAEZ,SAAK,MAAM;AAGX,QAAI,OAAO,UAAU,YAAY;AAC/B,YAAM,KAAK,MAAM,IAAI;AAAA,IACvB;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM;AACJ,WAAO,UAAU,KAAK,GAAG,IAAI;AAAA,EAC/B;AACF;AAEA,OAAO,UAAU,kBAAU;AAE3B,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,YAAY,MAAM;AAChB,aAAO,KAAK,KAAK,EAAE,SAAS,GAAG,IAAI;AAAA,IACrC;AAAA,EACF;AAAA;AAAA,EAEA,MAAM;AAAA,IACJ,UAAU,kBAAkB,SAAU,MAAM,OAAO;AACjD,aAAO,KAAK,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,OAAO,KAAK;AAAA,IAClD,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,UAAU,UAAU;;;ACrE7B,IAAqB,UAArB,cAAqC,UAAU;AAAA;AAAA,EAE7C,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,WAAW,IAAI,GAAGA,MAAK;AAAA,EACzC;AAAA;AAAA,EAGA,KAAK,GAAG,GAAG,GAAG;AACZ,QAAI,MAAM,YAAa,KAAI;AAC3B,WAAO,MAAM,KAAK,GAAG,GAAG,CAAC;AAAA,EAC3B;AAAA,EAEA,OAAO;AACL,WAAO,IAAI,IAAI;AAAA,EACjB;AAAA,EAEA,UAAU;AACR,WAAO,SAAS,gBAAgB,KAAK,GAAG,IAAI,GAAG;AAAA,EACjD;AAAA;AAAA,EAGA,WAAW;AACT,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA;AAAA,EAGA,OAAO,OAAO;AAEZ,SAAK,MAAM;AAGX,QAAI,OAAO,UAAU,YAAY;AAC/B,YAAM,KAAK,MAAM,IAAI;AAAA,IACvB;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM;AACJ,WAAO,UAAU,KAAK,GAAG,IAAI;AAAA,EAC/B;AACF;AAEA,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,WAAW,MAAM;AACf,aAAO,KAAK,KAAK,EAAE,QAAQ,GAAG,IAAI;AAAA,IACpC;AAAA,EACF;AAAA,EACA,MAAM;AAAA,IACJ,SAAS,kBAAkB,SAAUC,QAAOC,SAAQ,OAAO;AACzD,aAAO,KAAK,IAAI,IAAI,QAAQ,CAAC,EAAE,OAAO,KAAK,EAAE,KAAK;AAAA,QAChD,GAAG;AAAA,QACH,GAAG;AAAA,QACH,OAAOD;AAAA,QACP,QAAQC;AAAA,QACR,cAAc;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,SAAS,SAAS;;;AC5D3B,IAAqB,QAArB,cAAmC,MAAM;AAAA,EACvC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,SAAS,IAAI,GAAGA,MAAK;AAAA,EACvC;AAAA;AAAA,EAGA,KAAK,KAAK,UAAU;AAClB,QAAI,CAAC,IAAK,QAAO;AAEjB,UAAM,MAAM,IAAI,QAAQ,OAAO,MAAM;AAErC;AAAA,MACE;AAAA,MACA;AAAA,MACA,SAAU,GAAG;AACX,cAAM,IAAI,KAAK,OAAO,OAAO;AAG7B,YAAI,KAAK,MAAM,MAAM,KAAK,KAAK,OAAO,MAAM,GAAG;AAC7C,eAAK,KAAK,IAAI,OAAO,IAAI,MAAM;AAAA,QACjC;AAEA,YAAI,aAAa,SAAS;AAExB,cAAI,EAAE,MAAM,MAAM,KAAK,EAAE,OAAO,MAAM,GAAG;AACvC,cAAE,KAAK,KAAK,MAAM,GAAG,KAAK,OAAO,CAAC;AAAA,UACpC;AAAA,QACF;AAEA,YAAI,OAAO,aAAa,YAAY;AAClC,mBAAS,KAAK,MAAM,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAEA,OAAG,KAAK,cAAc,WAAY;AAEhC,UAAI,GAAG;AAAA,IACT,CAAC;AAED,WAAO,KAAK,KAAK,QAAS,IAAI,MAAM,KAAM,KAAK;AAAA,EACjD;AACF;AAEA,iBAAiB,SAAUC,OAAM,KAAK,OAAO;AAE3C,MAAIA,UAAS,UAAUA,UAAS,UAAU;AACxC,QAAI,QAAQ,KAAK,GAAG,GAAG;AACrB,YAAM,MAAM,KAAK,EAAE,KAAK,EAAE,MAAM,GAAG;AAAA,IACrC;AAAA,EACF;AAEA,MAAI,eAAe,OAAO;AACxB,UAAM,MACH,KAAK,EACL,KAAK,EACL,QAAQ,GAAG,GAAG,CAAC,YAAY;AAC1B,cAAQ,IAAI,GAAG;AAAA,IACjB,CAAC;AAAA,EACL;AAEA,SAAO;AACT,CAAC;AAED,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,OAAO,kBAAkB,SAAU,QAAQ,UAAU;AACnD,aAAO,KAAK,IAAI,IAAI,MAAM,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,QAAQ,QAAQ;AAAA,IAC/D,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,OAAO,OAAO;;;AC/EvB,IAAqB,aAArB,cAAwC,SAAS;AAAA;AAAA,EAE/C,OAAO;AACL,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,QAAI,OAAO;AACX,SAAK,QAAQ,SAAU,IAAI;AACzB,aAAO,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI;AAC3B,aAAO,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI;AAC3B,aAAO,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI;AAC3B,aAAO,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI;AAAA,IAC7B,CAAC;AACD,WAAO,IAAI,IAAI,MAAM,MAAM,OAAO,MAAM,OAAO,IAAI;AAAA,EACrD;AAAA;AAAA,EAGA,KAAKC,IAAGC,IAAG;AACT,UAAM,MAAM,KAAK,KAAK;AAGtB,IAAAD,MAAK,IAAI;AACT,IAAAC,MAAK,IAAI;AAGT,QAAI,CAAC,MAAMD,EAAC,KAAK,CAAC,MAAMC,EAAC,GAAG;AAC1B,eAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,aAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAID,IAAG,KAAK,CAAC,EAAE,CAAC,IAAIC,EAAC;AAAA,MAC3C;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAMC,SAAQ,CAAC,GAAG,CAAC,GAAG;AACpB,UAAM,SAAS,CAAC;AAGhB,QAAIA,kBAAiB,OAAO;AAC1B,MAAAA,SAAQ,MAAM,UAAU,OAAO,MAAM,CAAC,GAAGA,MAAK;AAAA,IAChD,OAAO;AAGL,MAAAA,SAAQA,OAAM,KAAK,EAAE,MAAM,SAAS,EAAE,IAAI,UAAU;AAAA,IACtD;AAIA,QAAIA,OAAM,SAAS,MAAM,EAAG,CAAAA,OAAM,IAAI;AAGtC,aAAS,IAAI,GAAG,MAAMA,OAAM,QAAQ,IAAI,KAAK,IAAI,IAAI,GAAG;AACtD,aAAO,KAAK,CAACA,OAAM,CAAC,GAAGA,OAAM,IAAI,CAAC,CAAC,CAAC;AAAA,IACtC;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,KAAKC,QAAOC,SAAQ;AAClB,QAAI;AACJ,UAAM,MAAM,KAAK,KAAK;AAGtB,SAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,UAAI,IAAI;AACN,aAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKD,SAAS,IAAI,QAAQ,IAAI;AAChE,UAAI,IAAI;AACN,aAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKC,UAAU,IAAI,SAAS,IAAI;AAAA,IACpE;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,SAAS;AACP,WAAO;AAAA,MACL,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,MACb,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,MACb,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,MACb,IAAI,KAAK,CAAC,EAAE,CAAC;AAAA,IACf;AAAA,EACF;AAAA;AAAA,EAGA,WAAW;AACT,UAAMF,SAAQ,CAAC;AAEf,aAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC7C,MAAAA,OAAM,KAAK,KAAK,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,IAC9B;AAEA,WAAOA,OAAM,KAAK,GAAG;AAAA,EACvB;AAAA,EAEA,UAAU,GAAG;AACX,WAAO,KAAK,MAAM,EAAE,WAAW,CAAC;AAAA,EAClC;AAAA;AAAA,EAGA,WAAW,GAAG;AACZ,QAAI,CAAC,OAAO,aAAa,CAAC,GAAG;AAC3B,UAAI,IAAI,OAAO,CAAC;AAAA,IAClB;AAEA,aAAS,IAAI,KAAK,QAAQ,OAAO;AAE/B,YAAM,CAACF,IAAGC,EAAC,IAAI,KAAK,CAAC;AACrB,WAAK,CAAC,EAAE,CAAC,IAAI,EAAE,IAAID,KAAI,EAAE,IAAIC,KAAI,EAAE;AACnC,WAAK,CAAC,EAAE,CAAC,IAAI,EAAE,IAAID,KAAI,EAAE,IAAIC,KAAI,EAAE;AAAA,IACrC;AAEA,WAAO;AAAA,EACT;AACF;;;ACxHA;AAAA;AAAA;AAAA,gBAAAI;AAAA,EAAA,aAAAC;AAAA,EAAA,SAAAC;AAAA,EAAA,SAAAC;AAAA;AAEO,IAAM,aAAa;AAGnB,SAASC,GAAEA,IAAG;AACnB,SAAOA,MAAK,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAKA,IAAG,KAAK,KAAK,EAAE,CAAC;AAC/D;AAGO,SAASC,GAAEA,IAAG;AACnB,SAAOA,MAAK,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE,GAAGA,EAAC;AAC/D;AAGO,SAASC,OAAMA,QAAO;AAC3B,QAAM,IAAI,KAAK,KAAK;AACpB,SAAOA,UAAS,OAAO,EAAE,QAAQ,KAAK,KAAKA,QAAO,EAAE,MAAM;AAC5D;AAGO,SAASC,QAAOA,SAAQ;AAC7B,QAAM,IAAI,KAAK,KAAK;AACpB,SAAOA,WAAU,OAAO,EAAE,SAAS,KAAK,KAAK,EAAE,OAAOA,OAAM;AAC9D;;;ACZA,IAAqB,OAArB,cAAkC,MAAM;AAAA;AAAA,EAEtC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,QAAQ,IAAI,GAAGA,MAAK;AAAA,EACtC;AAAA;AAAA,EAGA,QAAQ;AACN,WAAO,IAAI,WAAW;AAAA,MACpB,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,CAAC;AAAA,MACjC,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,CAAC;AAAA,IACnC,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,KAAKC,IAAGC,IAAG;AACT,WAAO,KAAK,KAAK,KAAK,MAAM,EAAE,KAAKD,IAAGC,EAAC,EAAE,OAAO,CAAC;AAAA,EACnD;AAAA;AAAA,EAGA,KAAK,IAAI,IAAIC,KAAIC,KAAI;AACnB,QAAI,MAAM,MAAM;AACd,aAAO,KAAK,MAAM;AAAA,IACpB,WAAW,OAAO,OAAO,aAAa;AACpC,WAAK,EAAE,IAAI,IAAI,IAAAD,KAAI,IAAAC,IAAG;AAAA,IACxB,OAAO;AACL,WAAK,IAAI,WAAW,EAAE,EAAE,OAAO;AAAA,IACjC;AAEA,WAAO,KAAK,KAAK,EAAE;AAAA,EACrB;AAAA;AAAA,EAGA,KAAKC,QAAOC,SAAQ;AAClB,UAAM,IAAI,iBAAiB,MAAMD,QAAOC,OAAM;AAC9C,WAAO,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AAAA,EAChE;AACF;AAEA,OAAO,MAAM,eAAO;AAEpB,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,MAAM,kBAAkB,YAAa,MAAM;AAGzC,aAAO,KAAK,UAAU,KAAK;AAAA,QACzB,KAAK,IAAI,IAAI,KAAK,CAAC;AAAA,QACnB,KAAK,CAAC,KAAK,OAAO,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,MAAM,MAAM;;;AC/DrB,IAAqB,SAArB,cAAoC,UAAU;AAAA;AAAA,EAE5C,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,UAAU,IAAI,GAAGA,MAAK;AAAA,EACxC;AAAA;AAAA,EAGA,OAAOC,SAAQ;AACb,WAAO,KAAK,KAAK,gBAAgBA,OAAM;AAAA,EACzC;AAAA,EAEA,OAAO,QAAQ;AACb,WAAO,KAAK,KAAK,UAAU,MAAM;AAAA,EACnC;AAAA;AAAA,EAGA,IAAIC,IAAGC,IAAG;AACR,WAAO,KAAK,KAAK,QAAQD,EAAC,EAAE,KAAK,QAAQC,EAAC;AAAA,EAC5C;AAAA;AAAA,EAGA,WAAW;AACT,WAAO,UAAU,KAAK,GAAG,IAAI;AAAA,EAC/B;AAAA;AAAA,EAGA,OAAO,OAAO;AAEZ,SAAK,MAAM;AAGX,QAAI,OAAO,UAAU,YAAY;AAC/B,YAAM,KAAK,MAAM,IAAI;AAAA,IACvB;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAMC,QAAO;AACX,WAAO,KAAK,KAAK,eAAeA,MAAK;AAAA,EACvC;AACF;AAEA,gBAAgB;AAAA,EACd,WAAW;AAAA,IACT,UAAU,MAAM;AAEd,aAAO,KAAK,KAAK,EAAE,OAAO,GAAG,IAAI;AAAA,IACnC;AAAA,EACF;AAAA,EACA,MAAM;AAAA;AAAA,IAEJ,QAAQ,kBAAkB,SAAUA,QAAOH,SAAQ,OAAO;AAExD,aAAO,KAAK,IAAI,IAAI,OAAO,CAAC,EACzB,KAAKG,QAAOH,OAAM,EAClB,IAAIG,SAAQ,GAAGH,UAAS,CAAC,EACzB,QAAQ,GAAG,GAAGG,QAAOH,OAAM,EAC3B,KAAK,UAAU,MAAM,EACrB,OAAO,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA,EACA,QAAQ;AAAA;AAAA,IAEN,OAAO,QAAQG,QAAOH,SAAQ,OAAO;AACnC,UAAII,QAAO,CAAC,QAAQ;AAGpB,UAAI,WAAW,MAAO,CAAAA,MAAK,KAAK,MAAM;AACtC,MAAAA,QAAOA,MAAK,KAAK,GAAG;AAGpB,eACE,UAAU,CAAC,aAAa,SACpB,UAAU,CAAC,IACX,KAAK,KAAK,EAAE,OAAOD,QAAOH,SAAQ,KAAK;AAE7C,aAAO,KAAK,KAAKI,OAAM,MAAM;AAAA,IAC/B;AAAA,EACF;AACF,CAAC;AAED,SAAS,QAAQ,QAAQ;;;AC9EzB,SAAS,iBAAiB,GAAG,GAAG;AAC9B,SAAO,SAAU,GAAG;AAClB,QAAI,KAAK,KAAM,QAAO,KAAK,CAAC;AAC5B,SAAK,CAAC,IAAI;AACV,QAAI,EAAG,GAAE,KAAK,IAAI;AAClB,WAAO;AAAA,EACT;AACF;AAEO,IAAM,SAAS;AAAA,EACpB,KAAK,SAAU,KAAK;AAClB,WAAO;AAAA,EACT;AAAA,EACA,MAAM,SAAU,KAAK;AACnB,WAAO,CAAC,KAAK,IAAI,MAAM,KAAK,EAAE,IAAI,IAAI;AAAA,EACxC;AAAA,EACA,KAAK,SAAU,KAAK;AAClB,WAAO,KAAK,IAAK,MAAM,KAAK,KAAM,CAAC;AAAA,EACrC;AAAA,EACA,KAAK,SAAU,KAAK;AAClB,WAAO,CAAC,KAAK,IAAK,MAAM,KAAK,KAAM,CAAC,IAAI;AAAA,EAC1C;AAAA,EACA,QAAQ,SAAU,IAAI,IAAIC,KAAIC,KAAI;AAEhC,WAAO,SAAU,GAAG;AAClB,UAAI,IAAI,GAAG;AACT,YAAI,KAAK,GAAG;AACV,iBAAQ,KAAK,KAAM;AAAA,QACrB,WAAWD,MAAK,GAAG;AACjB,iBAAQC,MAAKD,MAAM;AAAA,QACrB,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,WAAW,IAAI,GAAG;AAChB,YAAIA,MAAK,GAAG;AACV,kBAAS,IAAIC,QAAO,IAAID,OAAO,KAAKC,MAAKD,QAAO,IAAIA;AAAA,QACtD,WAAW,KAAK,GAAG;AACjB,kBAAS,IAAI,OAAO,IAAI,MAAO,KAAK,KAAK,OAAO,IAAI;AAAA,QACtD,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF,OAAO;AACL,eAAO,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAKC,MAAK,KAAK;AAAA,MACtE;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,OAAO,SAAU,OAAO,eAAe,OAAO;AAE5C,mBAAe,aAAa,MAAM,GAAG,EAAE,QAAQ,EAAE,CAAC;AAElD,QAAI,QAAQ;AACZ,QAAI,iBAAiB,QAAQ;AAC3B,QAAE;AAAA,IACJ,WAAW,iBAAiB,QAAQ;AAClC,QAAE;AAAA,IACJ;AAGA,WAAO,CAAC,GAAG,aAAa,UAAU;AAEhC,UAAI,OAAO,KAAK,MAAM,IAAI,KAAK;AAC/B,YAAM,UAAW,IAAI,OAAQ,MAAM;AAEnC,UAAI,iBAAiB,WAAW,iBAAiB,QAAQ;AACvD,UAAE;AAAA,MACJ;AAEA,UAAI,cAAc,SAAS;AACzB,UAAE;AAAA,MACJ;AAEA,UAAI,KAAK,KAAK,OAAO,GAAG;AACtB,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,KAAK,OAAO,OAAO;AAC1B,eAAO;AAAA,MACT;AAEA,aAAO,OAAO;AAAA,IAChB;AAAA,EACF;AACF;AAEO,IAAM,UAAN,MAAc;AAAA,EACnB,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAOO,IAAM,OAAN,cAAmB,QAAQ;AAAA,EAChC,YAAY,KAAK,SAAS,MAAM;AAC9B,UAAM;AACN,SAAK,OAAO,OAAO,EAAE,KAAK;AAAA,EAC5B;AAAA,EAEA,KAAKC,OAAMC,KAAI,KAAK;AAClB,QAAI,OAAOD,UAAS,UAAU;AAC5B,aAAO,MAAM,IAAIA,QAAOC;AAAA,IAC1B;AACA,WAAOD,SAAQC,MAAKD,SAAQ,KAAK,KAAK,GAAG;AAAA,EAC3C;AACF;AAOO,IAAM,aAAN,cAAyB,QAAQ;AAAA,EACtC,YAAY,IAAI;AACd,UAAM;AACN,SAAK,UAAU;AAAA,EACjB;AAAA,EAEA,KAAK,GAAG;AACN,WAAO,EAAE;AAAA,EACX;AAAA,EAEA,KAAK,SAAS,QAAQ,IAAI,GAAG;AAC3B,WAAO,KAAK,QAAQ,SAAS,QAAQ,IAAI,CAAC;AAAA,EAC5C;AACF;AAEA,SAAS,cAAc;AAErB,QAAM,YAAY,KAAK,aAAa,OAAO;AAC3C,QAAM,YAAY,KAAK,cAAc;AAGrC,QAAM,MAAM;AACZ,QAAM,KAAK,KAAK;AAChB,QAAM,KAAK,KAAK,IAAI,YAAY,MAAM,GAAG;AACzC,QAAM,OAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;AAC9C,QAAM,KAAK,OAAO,OAAO;AAGzB,OAAK,IAAI,IAAI,OAAO;AACpB,OAAK,IAAI,KAAK;AAChB;AAEO,IAAM,SAAN,cAAqB,WAAW;AAAA,EACrC,YAAY,WAAW,KAAK,YAAY,GAAG;AACzC,UAAM;AACN,SAAK,SAAS,QAAQ,EAAE,UAAU,SAAS;AAAA,EAC7C;AAAA,EAEA,KAAK,SAAS,QAAQ,IAAI,GAAG;AAC3B,QAAI,OAAO,YAAY,SAAU,QAAO;AACxC,MAAE,OAAO,OAAO;AAChB,QAAI,OAAO,SAAU,QAAO;AAC5B,QAAI,OAAO,EAAG,QAAO;AAErB,QAAI,KAAK,IAAK,MAAK;AAEnB,UAAM;AAGN,UAAM,WAAW,EAAE,YAAY;AAG/B,UAAM,eAAe,CAAC,KAAK,IAAI,WAAW,KAAK,KAAK,UAAU;AAC9D,UAAM,cAAc,UAAU,WAAW,KAAM,eAAe,KAAK,KAAM;AAGzE,MAAE,WAAW,WAAW,eAAe;AAGvC,MAAE,OAAO,KAAK,IAAI,SAAS,WAAW,IAAI,KAAK,IAAI,QAAQ,IAAI;AAC/D,WAAO,EAAE,OAAO,SAAS;AAAA,EAC3B;AACF;AAEA,OAAO,QAAQ;AAAA,EACb,UAAU,iBAAiB,aAAa,WAAW;AAAA,EACnD,WAAW,iBAAiB,cAAc,WAAW;AACvD,CAAC;AAEM,IAAM,MAAN,cAAkB,WAAW;AAAA,EAClC,YAAY,IAAI,KAAK,IAAI,MAAM,IAAI,GAAG,SAAS,KAAM;AACnD,UAAM;AACN,SAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,MAAM;AAAA,EACnC;AAAA,EAEA,KAAK,SAAS,QAAQ,IAAI,GAAG;AAC3B,QAAI,OAAO,YAAY,SAAU,QAAO;AACxC,MAAE,OAAO,OAAO;AAEhB,QAAI,OAAO,SAAU,QAAO;AAC5B,QAAI,OAAO,EAAG,QAAO;AAErB,UAAM,IAAI,SAAS;AACnB,QAAI,KAAK,EAAE,YAAY,KAAK,IAAI;AAChC,UAAM,KAAK,KAAK,EAAE,SAAS,MAAM;AACjC,UAAM,SAAS,KAAK;AAGpB,QAAI,WAAW,OAAO;AACpB,UAAI,KAAK,IAAI,CAAC,QAAQ,KAAK,IAAI,GAAG,MAAM,CAAC;AAAA,IAC3C;AAEA,MAAE,QAAQ;AACV,MAAE,WAAW;AAEb,MAAE,OAAO,KAAK,IAAI,CAAC,IAAI;AAEvB,WAAO,EAAE,OAAO,SAAS,WAAW,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI;AAAA,EACzE;AACF;AAEA,OAAO,KAAK;AAAA,EACV,QAAQ,iBAAiB,SAAS;AAAA,EAClC,GAAG,iBAAiB,GAAG;AAAA,EACvB,GAAG,iBAAiB,GAAG;AAAA,EACvB,GAAG,iBAAiB,GAAG;AACzB,CAAC;;;ACnOD,IAAM,oBAAoB;AAAA,EACxB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACL;AAEA,IAAM,eAAe;AAAA,EACnB,GAAG,SAAU,GAAG,GAAG,IAAI;AACrB,MAAE,IAAI,GAAG,IAAI,EAAE,CAAC;AAChB,MAAE,IAAI,GAAG,IAAI,EAAE,CAAC;AAEhB,WAAO,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;AAAA,EACvB;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACzB;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,CAAC;AAAA,EACnB;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,CAAC;AAAA,EACnB;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACjD;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACrC;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACrC;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACzB;AAAA,EACA,GAAG,SAAU,GAAG,GAAG,IAAI;AACrB,MAAE,IAAI,GAAG;AACT,MAAE,IAAI,GAAG;AACT,WAAO,CAAC,GAAG;AAAA,EACb;AAAA,EACA,GAAG,SAAU,GAAG,GAAG;AACjB,MAAE,IAAI,EAAE,CAAC;AACT,MAAE,IAAI,EAAE,CAAC;AACT,WAAO,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACvD;AACF;AAEA,IAAM,aAAa,aAAa,MAAM,EAAE;AAExC,SAAS,IAAI,GAAG,KAAK,WAAW,QAAQ,IAAI,IAAI,EAAE,GAAG;AACnD,eAAa,WAAW,CAAC,CAAC,IAAK,yBAAUE,IAAG;AAC1C,WAAO,SAAU,GAAG,GAAG,IAAI;AACzB,UAAIA,OAAM,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;AAAA,eACtBA,OAAM,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;AAAA,eAC3BA,OAAM,KAAK;AAClB,UAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;AAChB,UAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;AAAA,MAClB,OAAO;AACL,iBAAS,IAAI,GAAG,KAAK,EAAE,QAAQ,IAAI,IAAI,EAAE,GAAG;AAC1C,YAAE,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI,IAAI,EAAE,IAAI,EAAE;AAAA,QACjC;AAAA,MACF;AAEA,aAAO,aAAaA,EAAC,EAAE,GAAG,GAAG,EAAE;AAAA,IACjC;AAAA,EACF,EAAG,WAAW,CAAC,EAAE,YAAY,CAAC;AAChC;AAEA,SAAS,YAAYC,SAAQ;AAC3B,QAAM,UAAUA,QAAO,QAAQ,CAAC;AAChC,SAAO,aAAa,OAAO,EAAEA,QAAO,QAAQ,MAAM,CAAC,GAAGA,QAAO,GAAGA,QAAO,EAAE;AAC3E;AAEA,SAAS,gBAAgBA,SAAQ;AAC/B,SACEA,QAAO,QAAQ,UACfA,QAAO,QAAQ,SAAS,MACtB,kBAAkBA,QAAO,QAAQ,CAAC,EAAE,YAAY,CAAC;AAEvD;AAEA,SAAS,gBAAgBA,SAAQ,OAAO;AACtC,EAAAA,QAAO,YAAY,eAAeA,SAAQ,KAAK;AAC/C,QAAM,aAAa,aAAa,KAAK,KAAK;AAE1C,MAAI,YAAY;AACd,IAAAA,QAAO,UAAU,CAAC,KAAK;AAAA,EACzB,OAAO;AACL,UAAM,cAAcA,QAAO;AAC3B,UAAM,QAAQ,YAAY,YAAY;AACtC,UAAM,UAAU,gBAAgB;AAChC,IAAAA,QAAO,UAAU,CAAC,UAAU,MAAO,UAAU,MAAM,MAAO,WAAW;AAAA,EACvE;AAEA,EAAAA,QAAO,YAAY;AACnB,EAAAA,QAAO,cAAcA,QAAO,QAAQ,CAAC;AAErC,SAAO;AACT;AAEA,SAAS,eAAeA,SAAQ,UAAU;AACxC,MAAI,CAACA,QAAO,SAAU,OAAM,IAAI,MAAM,cAAc;AACpD,EAAAA,QAAO,UAAUA,QAAO,QAAQ,KAAK,WAAWA,QAAO,MAAM,CAAC;AAC9D,EAAAA,QAAO,WAAW;AAClB,EAAAA,QAAO,SAAS;AAChB,EAAAA,QAAO,YAAY;AACnB,EAAAA,QAAO,cAAc;AAErB,MAAI,gBAAgBA,OAAM,GAAG;AAC3B,oBAAgBA,OAAM;AAAA,EACxB;AACF;AAEA,SAAS,gBAAgBA,SAAQ;AAC/B,EAAAA,QAAO,YAAY;AACnB,MAAIA,QAAO,UAAU;AACnB,IAAAA,QAAO,UAAU,YAAYA,OAAM;AAAA,EACrC;AACA,EAAAA,QAAO,SAAS,KAAKA,QAAO,OAAO;AACrC;AAEA,SAAS,UAAUA,SAAQ;AACzB,MAAI,CAACA,QAAO,QAAQ,OAAQ,QAAO;AACnC,QAAM,QAAQA,QAAO,QAAQ,CAAC,EAAE,YAAY,MAAM;AAClD,QAAMC,UAASD,QAAO,QAAQ;AAE9B,SAAO,UAAUC,YAAW,KAAKA,YAAW;AAC9C;AAEA,SAAS,cAAcD,SAAQ;AAC7B,SAAOA,QAAO,UAAU,YAAY,MAAM;AAC5C;AAEA,IAAM,iBAAiB,oBAAI,IAAI,CAAC,KAAK,KAAK,KAAM,MAAM,MAAM,IAAI,CAAC;AAC1D,SAAS,WAAW,GAAG,aAAa,MAAM;AAC/C,MAAI,QAAQ;AACZ,MAAI,QAAQ;AACZ,QAAMA,UAAS;AAAA,IACb,SAAS,CAAC;AAAA,IACV,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU,CAAC;AAAA,IACX,WAAW;AAAA,IACX,aAAa;AAAA,IACb,UAAU;AAAA,IACV,IAAI,IAAI,MAAM;AAAA,IACd,GAAG,IAAI,MAAM;AAAA,EACf;AAEA,SAASA,QAAO,YAAY,OAAS,QAAQ,EAAE,OAAO,OAAO,GAAK;AAChE,QAAI,CAACA,QAAO,WAAW;AACrB,UAAI,gBAAgBA,SAAQ,KAAK,GAAG;AAClC;AAAA,MACF;AAAA,IACF;AAEA,QAAI,UAAU,KAAK;AACjB,UAAIA,QAAO,aAAaA,QAAO,aAAa;AAC1C,uBAAeA,SAAQ,KAAK;AAC5B,UAAE;AACF;AAAA,MACF;AACA,MAAAA,QAAO,WAAW;AAClB,MAAAA,QAAO,YAAY;AACnB,MAAAA,QAAO,UAAU;AACjB;AAAA,IACF;AAEA,QAAI,CAAC,MAAM,SAAS,KAAK,CAAC,GAAG;AAC3B,UAAIA,QAAO,WAAW,OAAO,UAAUA,OAAM,GAAG;AAC9C,QAAAA,QAAO,WAAW;AAClB,QAAAA,QAAO,SAAS;AAChB,uBAAeA,SAAQ,IAAI;AAC3B;AAAA,MACF;AAEA,MAAAA,QAAO,WAAW;AAClB,MAAAA,QAAO,UAAU;AACjB;AAAA,IACF;AAEA,QAAI,eAAe,IAAI,KAAK,GAAG;AAC7B,UAAIA,QAAO,UAAU;AACnB,uBAAeA,SAAQ,KAAK;AAAA,MAC9B;AACA;AAAA,IACF;AAEA,QAAI,UAAU,OAAO,UAAU,KAAK;AAClC,UAAIA,QAAO,YAAY,CAAC,cAAcA,OAAM,GAAG;AAC7C,uBAAeA,SAAQ,KAAK;AAC5B,UAAE;AACF;AAAA,MACF;AACA,MAAAA,QAAO,UAAU;AACjB,MAAAA,QAAO,WAAW;AAClB;AAAA,IACF;AAEA,QAAI,MAAM,YAAY,MAAM,KAAK;AAC/B,MAAAA,QAAO,UAAU;AACjB,MAAAA,QAAO,cAAc;AACrB;AAAA,IACF;AAEA,QAAI,aAAa,KAAK,KAAK,GAAG;AAC5B,UAAIA,QAAO,UAAU;AACnB,uBAAeA,SAAQ,KAAK;AAAA,MAC9B,WAAW,CAAC,gBAAgBA,OAAM,GAAG;AACnC,cAAM,IAAI,MAAM,cAAc;AAAA,MAChC,OAAO;AACL,wBAAgBA,OAAM;AAAA,MACxB;AACA,QAAE;AAAA,IACJ;AAAA,EACF;AAEA,MAAIA,QAAO,UAAU;AACnB,mBAAeA,SAAQ,KAAK;AAAA,EAC9B;AAEA,MAAIA,QAAO,aAAa,gBAAgBA,OAAM,GAAG;AAC/C,oBAAgBA,OAAM;AAAA,EACxB;AAEA,SAAOA,QAAO;AAChB;;;ACpPA,SAAS,cAAc,GAAG;AACxB,MAAI,IAAI;AACR,WAAS,IAAI,GAAG,KAAK,EAAE,QAAQ,IAAI,IAAI,KAAK;AAC1C,SAAK,EAAE,CAAC,EAAE,CAAC;AAEX,QAAI,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM;AACnB,WAAK,EAAE,CAAC,EAAE,CAAC;AAEX,UAAI,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM;AACnB,aAAK;AACL,aAAK,EAAE,CAAC,EAAE,CAAC;AAEX,YAAI,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM;AACnB,eAAK;AACL,eAAK,EAAE,CAAC,EAAE,CAAC;AACX,eAAK;AACL,eAAK,EAAE,CAAC,EAAE,CAAC;AAEX,cAAI,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM;AACnB,iBAAK;AACL,iBAAK,EAAE,CAAC,EAAE,CAAC;AACX,iBAAK;AACL,iBAAK,EAAE,CAAC,EAAE,CAAC;AAEX,gBAAI,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM;AACnB,mBAAK;AACL,mBAAK,EAAE,CAAC,EAAE,CAAC;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI;AACb;AAEA,IAAqB,YAArB,cAAuC,SAAS;AAAA;AAAA,EAE9C,OAAO;AACL,WAAO,EAAE,KAAK,aAAa,KAAK,KAAK,SAAS,CAAC;AAC/C,WAAO,IAAI,IAAI,OAAO,MAAM,KAAK,QAAQ,CAAC;AAAA,EAC5C;AAAA;AAAA,EAGA,KAAKE,IAAGC,IAAG;AAET,UAAM,MAAM,KAAK,KAAK;AAGtB,IAAAD,MAAK,IAAI;AACT,IAAAC,MAAK,IAAI;AAET,QAAI,CAAC,MAAMD,EAAC,KAAK,CAAC,MAAMC,EAAC,GAAG;AAE1B,eAAS,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,YAAI,KAAK,CAAC,EAAE,CAAC;AAEb,YAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AACvC,eAAK,CAAC,EAAE,CAAC,KAAKD;AACd,eAAK,CAAC,EAAE,CAAC,KAAKC;AAAA,QAChB,WAAW,MAAM,KAAK;AACpB,eAAK,CAAC,EAAE,CAAC,KAAKD;AAAA,QAChB,WAAW,MAAM,KAAK;AACpB,eAAK,CAAC,EAAE,CAAC,KAAKC;AAAA,QAChB,WAAW,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AAC9C,eAAK,CAAC,EAAE,CAAC,KAAKD;AACd,eAAK,CAAC,EAAE,CAAC,KAAKC;AACd,eAAK,CAAC,EAAE,CAAC,KAAKD;AACd,eAAK,CAAC,EAAE,CAAC,KAAKC;AAEd,cAAI,MAAM,KAAK;AACb,iBAAK,CAAC,EAAE,CAAC,KAAKD;AACd,iBAAK,CAAC,EAAE,CAAC,KAAKC;AAAA,UAChB;AAAA,QACF,WAAW,MAAM,KAAK;AACpB,eAAK,CAAC,EAAE,CAAC,KAAKD;AACd,eAAK,CAAC,EAAE,CAAC,KAAKC;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,MAAM,IAAI,QAAQ;AAChB,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,UAAI,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,CAAC,EAAE,SAAS;AAAA,IACnD;AAEA,WAAO,WAAW,CAAC;AAAA,EACrB;AAAA;AAAA,EAGA,KAAKC,QAAOC,SAAQ;AAElB,UAAM,MAAM,KAAK,KAAK;AACtB,QAAI,GAAG;AAIP,QAAI,QAAQ,IAAI,UAAU,IAAI,IAAI,IAAI;AACtC,QAAI,SAAS,IAAI,WAAW,IAAI,IAAI,IAAI;AAGxC,SAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,UAAI,KAAK,CAAC,EAAE,CAAC;AAEb,UAAI,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AACvC,aAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKD,SAAS,IAAI,QAAQ,IAAI;AAC9D,aAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKC,UAAU,IAAI,SAAS,IAAI;AAAA,MAClE,WAAW,MAAM,KAAK;AACpB,aAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKD,SAAS,IAAI,QAAQ,IAAI;AAAA,MAChE,WAAW,MAAM,KAAK;AACpB,aAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKC,UAAU,IAAI,SAAS,IAAI;AAAA,MAClE,WAAW,MAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AAC9C,aAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKD,SAAS,IAAI,QAAQ,IAAI;AAC9D,aAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKC,UAAU,IAAI,SAAS,IAAI;AAChE,aAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKD,SAAS,IAAI,QAAQ,IAAI;AAC9D,aAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKC,UAAU,IAAI,SAAS,IAAI;AAEhE,YAAI,MAAM,KAAK;AACb,eAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKD,SAAS,IAAI,QAAQ,IAAI;AAC9D,eAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKC,UAAU,IAAI,SAAS,IAAI;AAAA,QAClE;AAAA,MACF,WAAW,MAAM,KAAK;AAEpB,aAAK,CAAC,EAAE,CAAC,IAAK,KAAK,CAAC,EAAE,CAAC,IAAID,SAAS,IAAI;AACxC,aAAK,CAAC,EAAE,CAAC,IAAK,KAAK,CAAC,EAAE,CAAC,IAAIC,UAAU,IAAI;AAGzC,aAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKD,SAAS,IAAI,QAAQ,IAAI;AAC9D,aAAK,CAAC,EAAE,CAAC,KAAM,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,KAAKC,UAAU,IAAI,SAAS,IAAI;AAAA,MAClE;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,WAAW;AACT,WAAO,cAAc,IAAI;AAAA,EAC3B;AACF;;;ACzIA,IAAM,kBAAkB,CAAC,UAAU;AACjC,QAAM,OAAO,OAAO;AAEpB,MAAI,SAAS,UAAU;AACrB,WAAO;AAAA,EACT,WAAW,SAAS,UAAU;AAC5B,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAO;AAAA,IACT,WAAW,UAAU,KAAK,KAAK,GAAG;AAChC,aAAO,aAAa,KAAK,KAAK,IAAI,YAAY;AAAA,IAChD,WAAW,cAAc,KAAK,KAAK,GAAG;AACpC,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF,WAAW,eAAe,QAAQ,MAAM,WAAW,IAAI,IAAI;AACzD,WAAO,MAAM;AAAA,EACf,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,WAAO;AAAA,EACT,WAAW,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AAEA,IAAqB,YAArB,MAA+B;AAAA,EAC7B,YAAY,SAAS;AACnB,SAAK,WAAW,WAAW,IAAI,KAAK,GAAG;AAEvC,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AAAA,EAEA,GAAG,KAAK;AACN,WAAO,KAAK,UAAU;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA,EAEA,OAAO;AACL,UAAM,WAAW,KAAK,SAAS,IAAI,KAAK,SAAS,IAAI,EAAE,OAAO,SAC5D,MACA,MACA;AACA,aAAO,QAAQ;AAAA,IACjB,GAAG,IAAI;AACP,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,KAAK;AACR,QAAI,OAAO,MAAM;AACf,aAAO,KAAK;AAAA,IACd;AAEA,SAAK,QAAQ,KAAK,KAAK,GAAG;AAC1B,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS;AACf,QAAI,WAAW,KAAM,QAAO,KAAK;AACjC,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA,EAEA,GAAG,KAAK;AACN,QAAI,OAAO,MAAM;AACf,aAAO,KAAK;AAAA,IACd;AAEA,SAAK,MAAM,KAAK,KAAK,GAAG;AACxB,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,MAAM;AAET,QAAI,QAAQ,MAAM;AAChB,aAAO,KAAK;AAAA,IACd;AAGA,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,OAAO;AACV,QAAI,CAAC,KAAK,OAAO;AACf,WAAK,KAAK,gBAAgB,KAAK,CAAC;AAAA,IAClC;AAEA,QAAI,SAAS,IAAI,KAAK,MAAM,KAAK;AACjC,QAAI,KAAK,UAAU,OAAO;AACxB,eAAS,KAAK,MACV,OAAO,KAAK,IAAI,CAAC,CAAC,EAAE,IACpB,KAAK,QACH,OAAO,KAAK,MAAM,CAAC,CAAC,EAAE,IACtB;AAAA,IACR;AAEA,QAAI,KAAK,UAAU,WAAW;AAC5B,eAAS,KAAK,MACV,OAAO,MAAM,KAAK,GAAG,IACrB,KAAK,QACH,OAAO,MAAM,KAAK,KAAK,IACvB;AAAA,IACR;AAEA,aAAS,OAAO,aAAa;AAE7B,SAAK,YAAY,KAAK,aAAa,IAAI,KAAK,MAAM;AAClD,SAAK,WACH,KAAK,YACL,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,CAAC,EACnC,IAAI,MAAM,EACV,IAAI,SAAU,GAAG;AAChB,QAAE,OAAO;AACT,aAAO;AAAA,IACT,CAAC;AACL,WAAO;AAAA,EACT;AACF;AAEO,IAAM,eAAN,MAAmB;AAAA,EACxB,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EAEA,KAAK,KAAK;AACR,UAAM,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,IAAI;AACpC,SAAK,QAAQ;AACb,WAAO;AAAA,EACT;AAAA,EAEA,UAAU;AACR,WAAO,CAAC,KAAK,KAAK;AAAA,EACpB;AAAA,EAEA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AACF;AAEO,IAAM,eAAN,MAAM,cAAa;AAAA,EACxB,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EAEA,KAAK,KAAK;AACR,QAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,YAAM;AAAA,QACJ,QAAQ,IAAI,CAAC;AAAA,QACb,QAAQ,IAAI,CAAC;AAAA,QACb,OAAO,IAAI,CAAC;AAAA,QACZ,QAAQ,IAAI,CAAC;AAAA,QACb,YAAY,IAAI,CAAC;AAAA,QACjB,YAAY,IAAI,CAAC;AAAA,QACjB,SAAS,IAAI,CAAC;AAAA,QACd,SAAS,IAAI,CAAC;AAAA,MAChB;AAAA,IACF;AAEA,WAAO,OAAO,MAAM,cAAa,UAAU,GAAG;AAC9C,WAAO;AAAA,EACT;AAAA,EAEA,UAAU;AACR,UAAM,IAAI;AAEV,WAAO;AAAA,MACL,EAAE;AAAA,MACF,EAAE;AAAA,MACF,EAAE;AAAA,MACF,EAAE;AAAA,MACF,EAAE;AAAA,MACF,EAAE;AAAA,MACF,EAAE;AAAA,MACF,EAAE;AAAA,IACJ;AAAA,EACF;AACF;AAEA,aAAa,WAAW;AAAA,EACtB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,SAAS;AACX;AAEA,IAAM,YAAY,CAAC,GAAG,MAAM;AAC1B,SAAO,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI;AAC9C;AAEO,IAAM,YAAN,MAAgB;AAAA,EACrB,eAAe,MAAM;AACnB,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB;AAAA,EAEA,MAAM,OAAO;AACX,UAAM,SAAS,KAAK;AACpB,aAAS,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,IAAI,EAAE,GAAG;AAE/C,UAAI,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,GAAG;AAClC,YAAI,OAAO,IAAI,CAAC,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,GAAG;AAC7D,gBAAM,QAAQ,MAAM,IAAI,CAAC;AACzB,gBAAM,QAAQ,IAAI,MAAM,KAAK,OAAO,OAAO,IAAI,GAAG,CAAC,CAAC,EACjD,KAAK,EAAE,EACP,QAAQ;AACX,eAAK,OAAO,OAAO,IAAI,GAAG,GAAG,GAAG,KAAK;AAAA,QACvC;AAEA,aAAK,OAAO,IAAI,CAAC,IAAI;AACrB;AAAA,MACF;AAEA,UAAI,CAAC,MAAM,IAAI,CAAC,GAAG;AACjB,eAAO;AAAA,MACT;AAIA,YAAM,gBAAgB,IAAI,MAAM,IAAI,CAAC,EAAE,EAAE,QAAQ;AAGjD,YAAM,WAAW,OAAO,IAAI,CAAC,IAAI;AAEjC,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,MAAM,CAAC;AAAA,QACP,MAAM,IAAI,CAAC;AAAA,QACX,MAAM,IAAI,CAAC;AAAA,QACX,GAAG;AAAA,MACL;AAEA,WAAK,OAAO,IAAI,CAAC,IAAI;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,UAAU;AACb,SAAK,SAAS,CAAC;AAEf,QAAI,MAAM,QAAQ,QAAQ,GAAG;AAC3B,WAAK,SAAS,SAAS,MAAM;AAC7B;AAAA,IACF;AAEA,eAAW,YAAY,CAAC;AACxB,UAAM,UAAU,CAAC;AAEjB,eAAW,KAAK,UAAU;AACxB,YAAM,OAAO,gBAAgB,SAAS,CAAC,CAAC;AACxC,YAAM,MAAM,IAAI,KAAK,SAAS,CAAC,CAAC,EAAE,QAAQ;AAC1C,cAAQ,KAAK,CAAC,GAAG,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC;AAAA,IAC5C;AAEA,YAAQ,KAAK,SAAS;AAEtB,SAAK,SAAS,QAAQ,OAAO,CAAC,MAAM,SAAS,KAAK,OAAO,IAAI,GAAG,CAAC,CAAC;AAClE,WAAO;AAAA,EACT;AAAA,EAEA,UAAU;AACR,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,UAAU;AACR,UAAM,MAAM,CAAC;AACb,UAAM,MAAM,KAAK;AAGjB,WAAO,IAAI,QAAQ;AACjB,YAAM,MAAM,IAAI,MAAM;AACtB,YAAM,OAAO,IAAI,MAAM;AACvB,YAAM,MAAM,IAAI,MAAM;AACtB,YAAM,SAAS,IAAI,OAAO,GAAG,GAAG;AAChC,UAAI,GAAG,IAAI,IAAI,KAAK,MAAM;AAAA,IAC5B;AAEA,WAAO;AAAA,EACT;AACF;AAEA,IAAM,iBAAiB,CAAC,cAAc,cAAc,SAAS;AAEtD,SAAS,sBAAsB,OAAO,CAAC,GAAG;AAC/C,iBAAe,KAAK,GAAG,CAAC,EAAE,OAAO,IAAI,CAAC;AACxC;AAEO,SAAS,gBAAgB;AAC9B,SAAO,gBAAgB;AAAA,IACrB,GAAG,KAAK;AACN,aAAO,IAAI,UAAU,EAClB,KAAK,KAAK,WAAW,EACrB,KAAK,KAAK,QAAQ,CAAC,EACnB,GAAG,GAAG;AAAA,IACX;AAAA,IACA,UAAU,KAAK;AACb,WAAK,KAAK,GAAG;AACb,aAAO;AAAA,IACT;AAAA,IACA,eAAe;AACb,aAAO,KAAK,QAAQ;AAAA,IACtB;AAAA,IACA,MAAMC,OAAMC,KAAI,KAAK,SAAS,SAAS;AACrC,YAAM,SAAS,SAAU,GAAG,OAAO;AACjC,eAAO,QAAQ,KAAK,GAAGA,IAAG,KAAK,GAAG,KAAK,QAAQ,KAAK,GAAG,OAAO;AAAA,MAChE;AAEA,aAAO,KAAK,UAAUD,MAAK,IAAI,MAAM,CAAC;AAAA,IACxC;AAAA,EACF,CAAC;AACH;;;ACzUA,IAAqB,OAArB,cAAkC,MAAM;AAAA;AAAA,EAEtC,YAAY,MAAME,SAAQ,MAAM;AAC9B,UAAM,UAAU,QAAQ,IAAI,GAAGA,MAAK;AAAA,EACtC;AAAA;AAAA,EAGA,QAAQ;AACN,WAAO,KAAK,WAAW,KAAK,SAAS,IAAI,UAAU,KAAK,KAAK,GAAG,CAAC;AAAA,EACnE;AAAA;AAAA,EAGA,QAAQ;AACN,WAAO,KAAK;AACZ,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,OAAOC,SAAQ;AACb,WAAOA,WAAU,OACb,KAAK,KAAK,EAAE,SACZ,KAAK,KAAK,KAAK,KAAK,EAAE,OAAOA,OAAM;AAAA,EACzC;AAAA;AAAA,EAGA,KAAKC,IAAGC,IAAG;AACT,WAAO,KAAK,KAAK,KAAK,KAAK,MAAM,EAAE,KAAKD,IAAGC,EAAC,CAAC;AAAA,EAC/C;AAAA;AAAA,EAGA,KAAK,GAAG;AACN,WAAO,KAAK,OACR,KAAK,MAAM,IACX,KAAK,MAAM,EAAE;AAAA,MACX;AAAA,MACA,OAAO,MAAM,WAAW,IAAK,KAAK,SAAS,IAAI,UAAU,CAAC;AAAA,IAC5D;AAAA,EACN;AAAA;AAAA,EAGA,KAAKC,QAAOH,SAAQ;AAClB,UAAM,IAAI,iBAAiB,MAAMG,QAAOH,OAAM;AAC9C,WAAO,KAAK,KAAK,KAAK,KAAK,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;AAAA,EAC5D;AAAA;AAAA,EAGA,MAAMG,QAAO;AACX,WAAOA,UAAS,OACZ,KAAK,KAAK,EAAE,QACZ,KAAK,KAAKA,QAAO,KAAK,KAAK,EAAE,MAAM;AAAA,EACzC;AAAA;AAAA,EAGA,EAAEF,IAAG;AACH,WAAOA,MAAK,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAKA,IAAG,KAAK,KAAK,EAAE,CAAC;AAAA,EAC/D;AAAA;AAAA,EAGA,EAAEC,IAAG;AACH,WAAOA,MAAK,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,KAAK,KAAK,EAAE,GAAGA,EAAC;AAAA,EAC/D;AACF;AAGA,KAAK,UAAU,aAAa;AAG5B,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,MAAM,kBAAkB,SAAU,GAAG;AAEnC,aAAO,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,IAAI,UAAU,CAAC;AAAA,IACvD,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,MAAM,MAAM;;;ACnFrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIO,SAAS,QAAQ;AACtB,SAAO,KAAK,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,KAAK,QAAQ,CAAC;AACzE;AAGO,SAAS,QAAQ;AACtB,SAAO,KAAK;AACZ,SAAO;AACT;AAGO,SAAS,KAAKE,IAAGC,IAAG;AACzB,SAAO,KAAK,KAAK,UAAU,KAAK,MAAM,EAAE,KAAKD,IAAGC,EAAC,CAAC;AACpD;AAGO,SAAS,KAAK,GAAG;AACtB,SAAO,KAAK,OACR,KAAK,MAAM,IACX,KAAK,MAAM,EAAE;AAAA,IACX;AAAA,IACA,OAAO,MAAM,WAAW,IAAK,KAAK,SAAS,IAAI,WAAW,CAAC;AAAA,EAC7D;AACN;AAGO,SAAS,KAAKC,QAAOC,SAAQ;AAClC,QAAM,IAAI,iBAAiB,MAAMD,QAAOC,OAAM;AAC9C,SAAO,KAAK,KAAK,UAAU,KAAK,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;AACjE;;;ACrBA,IAAqB,UAArB,cAAqC,MAAM;AAAA;AAAA,EAEzC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,WAAW,IAAI,GAAGA,MAAK;AAAA,EACzC;AACF;AAEA,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,SAAS,kBAAkB,SAAU,GAAG;AAEtC,aAAO,KAAK,IAAI,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,IAAI,WAAW,CAAC;AAAA,IAC3D,CAAC;AAAA,EACH;AACF,CAAC;AAED,OAAO,SAAS,eAAO;AACvB,OAAO,SAAS,YAAI;AACpB,SAAS,SAAS,SAAS;;;ACnB3B,IAAqB,WAArB,cAAsC,MAAM;AAAA;AAAA,EAE1C,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,YAAY,IAAI,GAAGA,MAAK;AAAA,EAC1C;AACF;AAEA,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,UAAU,kBAAkB,SAAU,GAAG;AAEvC,aAAO,KAAK,IAAI,IAAI,SAAS,CAAC,EAAE,KAAK,KAAK,IAAI,WAAW,CAAC;AAAA,IAC5D,CAAC;AAAA,EACH;AACF,CAAC;AAED,OAAO,UAAU,eAAO;AACxB,OAAO,UAAU,YAAI;AACrB,SAAS,UAAU,UAAU;;;ACrB7B,IAAqB,OAArB,cAAkC,MAAM;AAAA;AAAA,EAEtC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,QAAQ,IAAI,GAAGA,MAAK;AAAA,EACtC;AACF;AAEA,OAAO,MAAM,EAAE,IAAI,GAAG,CAAC;AAEvB,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,MAAM,kBAAkB,SAAUC,QAAOC,SAAQ;AAC/C,aAAO,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,KAAKD,QAAOC,OAAM;AAAA,IAChD,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,MAAM,MAAM;;;AC5BrB,IAAqB,QAArB,MAA2B;AAAA,EACzB,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA,EAGA,QAAQ;AACN,WAAO,KAAK,UAAU,KAAK,OAAO;AAAA,EACpC;AAAA;AAAA,EAGA,OAAO;AACL,WAAO,KAAK,SAAS,KAAK,MAAM;AAAA,EAClC;AAAA,EAEA,KAAK,OAAO;AAEV,UAAM,OACJ,OAAO,MAAM,SAAS,cAClB,QACA,EAAE,OAAc,MAAM,MAAM,MAAM,KAAK;AAG7C,QAAI,KAAK,OAAO;AACd,WAAK,OAAO,KAAK;AACjB,WAAK,MAAM,OAAO;AAClB,WAAK,QAAQ;AAAA,IACf,OAAO;AACL,WAAK,QAAQ;AACb,WAAK,SAAS;AAAA,IAChB;AAGA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,OAAO,MAAM;AAEX,QAAI,KAAK,KAAM,MAAK,KAAK,OAAO,KAAK;AACrC,QAAI,KAAK,KAAM,MAAK,KAAK,OAAO,KAAK;AACrC,QAAI,SAAS,KAAK,MAAO,MAAK,QAAQ,KAAK;AAC3C,QAAI,SAAS,KAAK,OAAQ,MAAK,SAAS,KAAK;AAG7C,SAAK,OAAO;AACZ,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,QAAQ;AAEN,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,OAAQ,QAAO;AAGpB,SAAK,SAAS,OAAO;AACrB,QAAI,KAAK,OAAQ,MAAK,OAAO,OAAO;AACpC,SAAK,QAAQ,KAAK,SAAS,KAAK,QAAQ;AACxC,WAAO,OAAO;AAAA,EAChB;AACF;;;AC1DA,IAAM,WAAW;AAAA,EACf,UAAU;AAAA,EACV,QAAQ,IAAI,MAAM;AAAA,EAClB,UAAU,IAAI,MAAM;AAAA,EACpB,YAAY,IAAI,MAAM;AAAA,EACtB,OAAO,MAAM,QAAQ,OAAO,eAAe,QAAQ,OAAO;AAAA,EAC1D,YAAY,CAAC;AAAA,EAEb,MAAM,IAAI;AAER,UAAM,OAAO,SAAS,OAAO,KAAK,EAAE,KAAK,GAAG,CAAC;AAG7C,QAAI,SAAS,aAAa,MAAM;AAC9B,eAAS,WAAW,QAAQ,OAAO,sBAAsB,SAAS,KAAK;AAAA,IACzE;AAGA,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,IAAI,OAAO;AACjB,YAAQ,SAAS;AAGjB,UAAM,OAAO,SAAS,MAAM,EAAE,IAAI,IAAI;AAGtC,UAAM,OAAO,SAAS,SAAS,KAAK,EAAE,KAAK,IAAI,KAAW,CAAC;AAG3D,QAAI,SAAS,aAAa,MAAM;AAC9B,eAAS,WAAW,QAAQ,OAAO,sBAAsB,SAAS,KAAK;AAAA,IACzE;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,UAAU,IAAI;AAEZ,UAAM,OAAO,SAAS,WAAW,KAAK,EAAE;AAExC,QAAI,SAAS,aAAa,MAAM;AAC9B,eAAS,WAAW,QAAQ,OAAO,sBAAsB,SAAS,KAAK;AAAA,IACzE;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,YAAY,MAAM;AAChB,YAAQ,QAAQ,SAAS,OAAO,OAAO,IAAI;AAAA,EAC7C;AAAA,EAEA,aAAa,MAAM;AACjB,YAAQ,QAAQ,SAAS,SAAS,OAAO,IAAI;AAAA,EAC/C;AAAA,EAEA,gBAAgB,MAAM;AACpB,YAAQ,QAAQ,SAAS,WAAW,OAAO,IAAI;AAAA,EACjD;AAAA,EAEA,MAAM,KAAK;AAGT,QAAI,cAAc;AAClB,UAAM,cAAc,SAAS,SAAS,KAAK;AAC3C,WAAQ,cAAc,SAAS,SAAS,MAAM,GAAI;AAEhD,UAAI,OAAO,YAAY,MAAM;AAC3B,oBAAY,IAAI;AAAA,MAClB,OAAO;AACL,iBAAS,SAAS,KAAK,WAAW;AAAA,MACpC;AAGA,UAAI,gBAAgB,YAAa;AAAA,IACnC;AAGA,QAAI,YAAY;AAChB,UAAM,YAAY,SAAS,OAAO,KAAK;AACvC,WAAO,cAAc,cAAc,YAAY,SAAS,OAAO,MAAM,IAAI;AACvE,gBAAU,IAAI,GAAG;AAAA,IACnB;AAEA,QAAI,gBAAgB;AACpB,WAAQ,gBAAgB,SAAS,WAAW,MAAM,GAAI;AACpD,oBAAc;AAAA,IAChB;AAGA,aAAS,WACP,SAAS,SAAS,MAAM,KAAK,SAAS,OAAO,MAAM,IAC/C,QAAQ,OAAO,sBAAsB,SAAS,KAAK,IACnD;AAAA,EACR;AACF;AAEA,IAAO,mBAAQ;;;AChGf,IAAM,eAAe,SAAU,YAAY;AACzC,QAAM,QAAQ,WAAW;AACzB,QAAM,WAAW,WAAW,OAAO,SAAS;AAC5C,QAAM,MAAM,QAAQ;AACpB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ,WAAW;AAAA,EACrB;AACF;AAEA,IAAM,gBAAgB,WAAY;AAChC,QAAM,IAAI,QAAQ;AAClB,UAAQ,EAAE,eAAe,EAAE,MAAM,IAAI;AACvC;AAEA,IAAqB,WAArB,cAAsC,YAAY;AAAA;AAAA,EAEhD,YAAY,aAAa,eAAe;AACtC,UAAM;AAEN,SAAK,cAAc;AAGnB,SAAK,UAAU;AAAA,EACjB;AAAA,EAEA,SAAS;AACP,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA,EAEA,SAAS;AAEP,SAAK,KAAK,KAAK,qBAAqB,IAAI,CAAC;AACzC,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAGA,aAAa;AACX,UAAM,iBAAiB,KAAK,kBAAkB;AAC9C,UAAM,eAAe,iBAAiB,eAAe,OAAO,SAAS,IAAI;AACzE,UAAM,gBAAgB,iBAAiB,eAAe,QAAQ,KAAK;AACnE,WAAO,gBAAgB;AAAA,EACzB;AAAA,EAEA,uBAAuB;AACrB,UAAM,WAAW,KAAK,SAAS,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,SAAS,CAAC;AACvE,WAAO,KAAK,IAAI,GAAG,GAAG,QAAQ;AAAA,EAChC;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,kBAAkB,KAAK,aAAa;AAAA,EAClD;AAAA,EAEA,kBAAkB,IAAI;AACpB,WAAO,KAAK,SAAS,KAAK,WAAW,QAAQ,EAAE,CAAC,KAAK;AAAA,EACvD;AAAA,EAEA,QAAQ;AACN,SAAK,UAAU;AACf,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EAEA,QAAQ,aAAa;AACnB,QAAI,eAAe,KAAM,QAAO,KAAK;AACrC,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA,EAEA,OAAO;AAEL,SAAK,UAAU;AACf,WAAO,KAAK,WAAW,EAAE,UAAU;AAAA,EACrC;AAAA,EAEA,QAAQ,KAAK;AACX,UAAM,eAAe,KAAK,MAAM;AAChC,QAAI,OAAO,KAAM,QAAO,KAAK,MAAM,CAAC,YAAY;AAEhD,UAAM,WAAW,KAAK,IAAI,YAAY;AACtC,WAAO,KAAK,MAAM,MAAM,CAAC,WAAW,QAAQ;AAAA,EAC9C;AAAA;AAAA,EAGA,SAAS,QAAQ,OAAO,MAAM;AAC5B,QAAI,UAAU,MAAM;AAClB,aAAO,KAAK,SAAS,IAAI,YAAY;AAAA,IACvC;AAMA,QAAI,oBAAoB;AACxB,UAAM,UAAU,KAAK,WAAW;AAChC,YAAQ,SAAS;AAGjB,QAAI,QAAQ,QAAQ,SAAS,UAAU,SAAS,SAAS;AAEvD,0BAAoB;AAAA,IACtB,WAAW,SAAS,cAAc,SAAS,SAAS;AAClD,0BAAoB;AACpB,cAAQ;AAAA,IACV,WAAW,SAAS,OAAO;AACzB,0BAAoB,KAAK;AAAA,IAC3B,WAAW,SAAS,YAAY;AAC9B,YAAMC,cAAa,KAAK,kBAAkB,OAAO,EAAE;AACnD,UAAIA,aAAY;AACd,4BAAoBA,YAAW,QAAQ;AACvC,gBAAQ;AAAA,MACV;AAAA,IACF,WAAW,SAAS,aAAa;AAC/B,YAAM,iBAAiB,KAAK,kBAAkB;AAC9C,YAAM,gBAAgB,iBAAiB,eAAe,QAAQ,KAAK;AACnE,0BAAoB;AAAA,IACtB,OAAO;AACL,YAAM,IAAI,MAAM,wCAAwC;AAAA,IAC1D;AAGA,WAAO,WAAW;AAClB,WAAO,SAAS,IAAI;AAEpB,UAAM,UAAU,OAAO,QAAQ;AAC/B,UAAM,aAAa;AAAA,MACjB,SAAS,YAAY,OAAO,KAAK,WAAW;AAAA,MAC5C,OAAO,oBAAoB;AAAA,MAC3B;AAAA,IACF;AAEA,SAAK,gBAAgB,OAAO;AAE5B,SAAK,SAAS,KAAK,UAAU;AAC7B,SAAK,SAAS,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;AAC9C,SAAK,aAAa,KAAK,SAAS,IAAI,CAAC,SAAS,KAAK,OAAO,EAAE;AAE5D,SAAK,WAAW,EAAE,UAAU;AAC5B,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,IAAI;AACP,WAAO,KAAK,KAAK,KAAK,QAAQ,EAAE;AAAA,EAClC;AAAA,EAEA,OAAO,IAAI;AACT,QAAI,MAAM,KAAM,QAAO,KAAK;AAC5B,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,OAAO;AACX,QAAI,SAAS,KAAM,QAAO,KAAK;AAC/B,SAAK,SAAS;AACd,WAAO;AAAA,EACT;AAAA,EAEA,OAAO;AAEL,SAAK,KAAK,CAAC;AACX,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA,EAEA,KAAK,MAAM;AACT,QAAI,QAAQ,KAAM,QAAO,KAAK;AAC9B,SAAK,QAAQ;AACb,WAAO,KAAK,UAAU,IAAI;AAAA,EAC5B;AAAA;AAAA,EAGA,WAAW,QAAQ;AACjB,UAAM,QAAQ,KAAK,WAAW,QAAQ,OAAO,EAAE;AAC/C,QAAI,QAAQ,EAAG,QAAO;AAEtB,SAAK,SAAS,OAAO,OAAO,CAAC;AAC7B,SAAK,WAAW,OAAO,OAAO,CAAC;AAE/B,WAAO,SAAS,IAAI;AACpB,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,aAAa;AACX,QAAI,CAAC,KAAK,OAAO,GAAG;AAClB,WAAK,kBAAkB,KAAK,YAAY;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,UAAU,gBAAgB,OAAO;AAC/B,qBAAS,YAAY,KAAK,UAAU;AACpC,SAAK,aAAa;AAElB,QAAI,cAAe,QAAO,KAAK,eAAe;AAC9C,QAAI,KAAK,QAAS,QAAO;AAEzB,SAAK,aAAa,iBAAS,MAAM,KAAK,KAAK;AAC3C,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,gBAAgB,OAAO;AAE7B,UAAM,OAAO,KAAK,YAAY;AAC9B,QAAI,WAAW,OAAO,KAAK;AAE3B,QAAI,cAAe,YAAW;AAE9B,UAAM,SAAS,KAAK,SAAS,YAAY,KAAK,QAAQ,KAAK;AAC3D,SAAK,kBAAkB;AAIvB,QAAI,CAAC,eAAe;AAElB,WAAK,SAAS;AACd,WAAK,QAAQ,KAAK,QAAQ,IAAI,IAAI,KAAK;AAAA,IACzC;AACA,SAAK,gBAAgB,KAAK;AAC1B,SAAK,KAAK,QAAQ,KAAK,KAAK;AAa5B,aAAS,IAAI,KAAK,SAAS,QAAQ,OAAO;AAExC,YAAM,aAAa,KAAK,SAAS,CAAC;AAClC,YAAM,SAAS,WAAW;AAI1B,YAAM,YAAY,KAAK,QAAQ,WAAW;AAI1C,UAAI,aAAa,GAAG;AAClB,eAAO,MAAM;AAAA,MACf;AAAA,IACF;AAGA,QAAI,cAAc;AAClB,aAAS,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,IAAI,KAAK,KAAK;AAExD,YAAM,aAAa,KAAK,SAAS,CAAC;AAClC,YAAM,SAAS,WAAW;AAC1B,UAAI,KAAK;AAIT,YAAM,YAAY,KAAK,QAAQ,WAAW;AAG1C,UAAI,aAAa,GAAG;AAClB,sBAAc;AACd;AAAA,MACF,WAAW,YAAY,IAAI;AAEzB,aAAK;AAAA,MACP;AAEA,UAAI,CAAC,OAAO,OAAO,EAAG;AAItB,YAAM,WAAW,OAAO,KAAK,EAAE,EAAE;AACjC,UAAI,CAAC,UAAU;AACb,sBAAc;AAAA,MAEhB,WAAW,WAAW,YAAY,MAAM;AAEtC,cAAM,UAAU,OAAO,SAAS,IAAI,OAAO,KAAK,IAAI,KAAK;AAEzD,YAAI,UAAU,WAAW,UAAU,KAAK,OAAO;AAE7C,iBAAO,WAAW;AAClB,YAAE;AACF,YAAE;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AAIA,QACG,eAAe,EAAE,KAAK,SAAS,KAAK,KAAK,UAAU,MACnD,KAAK,WAAW,UAAU,KAAK,SAAS,KAAK,KAAK,QAAQ,GAC3D;AACA,WAAK,UAAU;AAAA,IACjB,OAAO;AACL,WAAK,MAAM;AACX,WAAK,KAAK,UAAU;AAAA,IACtB;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,YAAY;AAIV,SAAK,aAAa;AAClB,SAAK,SAAS;AAGd,SAAK,WAAW;AAGhB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,WAAW,CAAC;AACjB,SAAK,aAAa,CAAC;AACnB,SAAK,gBAAgB;AACrB,SAAK,QAAQ;AACb,SAAK,kBAAkB;AACvB,SAAK,gBAAgB;AAGrB,SAAK,QAAQ,KAAK,QAAQ,KAAK,MAAM,KAAK;AAC1C,SAAK,iBAAiB,KAAK,QAAQ,KAAK,MAAM,IAAI;AAAA,EACpD;AACF;AAEA,gBAAgB;AAAA,EACd,SAAS;AAAA,IACP,UAAU,SAAUC,WAAU;AAC5B,UAAIA,aAAY,MAAM;AACpB,aAAK,YAAY,KAAK,aAAa,IAAI,SAAS;AAChD,eAAO,KAAK;AAAA,MACd,OAAO;AACL,aAAK,YAAYA;AACjB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF,CAAC;;;AC7UD,IAAqB,SAArB,MAAqB,gBAAe,YAAY;AAAA,EAC9C,YAAY,SAAS;AACnB,UAAM;AAGN,SAAK,KAAK,QAAO;AAGjB,cAAU,WAAW,OAAO,SAAS,WAAW;AAGhD,cAAU,OAAO,YAAY,aAAa,IAAI,WAAW,OAAO,IAAI;AAGpE,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,SAAS,CAAC;AAGf,SAAK,YAAY,OAAO,YAAY,YAAY;AAChD,SAAK,iBAAiB,mBAAmB;AACzC,SAAK,WAAW,KAAK,iBAAiB,UAAU,IAAI,KAAK;AAGzD,SAAK,WAAW,CAAC;AAGjB,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,YAAY;AAGjB,SAAK,WAAW;AAGhB,SAAK,aAAa,IAAI,OAAO;AAC7B,SAAK,cAAc;AAGnB,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,SAAS;AAEd,SAAK,WAAW;AAGhB,SAAK,WAAW,KAAK,iBAAiB,OAAO;AAAA,EAC/C;AAAA,EAEA,OAAO,SAAS,UAAU,OAAO,MAAM;AAErC,QAAI,QAAQ;AACZ,QAAI,QAAQ;AACZ,QAAI,OAAO;AACX,eAAW,YAAY,SAAS;AAChC,YAAQ,SAAS,SAAS;AAC1B,WAAO,QAAQ;AAGf,QAAI,OAAO,aAAa,YAAY,EAAE,oBAAoB,UAAU;AAClE,cAAQ,SAAS,SAAS;AAC1B,aAAO,SAAS,QAAQ;AACxB,cAAQ,SAAS,SAAS;AAC1B,cAAQ,SAAS,SAAS;AAC1B,aAAO,SAAS,QAAQ;AACxB,iBAAW,SAAS,YAAY,SAAS;AAAA,IAC3C;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,OAAO,SAAS;AACd,QAAI,WAAW,KAAM,QAAO,KAAK;AACjC,SAAK,UAAU;AACf,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAaC,YAAW;AACtB,SAAK,WAAW,WAAWA,UAAS;AACpC,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,IAAI;AACR,WAAO,KAAK,GAAG,YAAY,EAAE;AAAA,EAC/B;AAAA,EAEA,QAAQ,UAAU,OAAO,MAAM;AAC7B,UAAM,IAAI,QAAO,SAAS,UAAU,OAAO,IAAI;AAC/C,UAAM,SAAS,IAAI,QAAO,EAAE,QAAQ;AACpC,QAAI,KAAK,UAAW,QAAO,SAAS,KAAK,SAAS;AAClD,QAAI,KAAK,SAAU,QAAO,QAAQ,KAAK,QAAQ;AAC/C,WAAO,OAAO,KAAK,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI;AAAA,EAChD;AAAA,EAEA,iBAAiB;AACf,SAAK,aAAa,IAAI,OAAO;AAC7B,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,2BAA2B;AACzB,QACE,CAAC,KAAK,QACN,CAAC,KAAK,aACN,CAAC,KAAK,UAAU,WAAW,SAAS,KAAK,EAAE,GAC3C;AACA,WAAK,SAAS,KAAK,OAAO,OAAO,CAAC,SAAS;AACzC,eAAO,CAAC,KAAK;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,MAAM,OAAO;AACX,WAAO,KAAK,QAAQ,GAAG,KAAK;AAAA,EAC9B;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,UAAU,KAAK,QAAQ,KAAK,aAAa,KAAK;AAAA,EAC5D;AAAA,EAEA,OAAO,IAAI;AACT,WAAO,KAAK,MAAM,MAAM,EAAE;AAAA,EAC5B;AAAA,EAEA,KAAK,IAAI;AACP,SAAK,WAAW,IAAI,KAAK,EAAE;AAC3B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,SAAS;AACf,QAAI,WAAW,KAAM,QAAO,KAAK;AACjC,SAAK,WAAW;AAChB,YAAQ,eAAe;AACvB,WAAO;AAAA,EACT;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,KAAK,QAAQ;AAAA,EAC3B;AAAA,EAEA,KAAK,OAAO,OAAO,MAAM;AAEvB,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ,MAAM;AACd,aAAO,MAAM;AACb,cAAQ,MAAM;AAAA,IAChB;AAGA,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,SAAS;AACvB,SAAK,QAAQ,QAAQ;AAGrB,QAAI,KAAK,WAAW,MAAM;AACxB,WAAK,SAAS;AAAA,IAChB;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAM,GAAG;AACP,UAAM,eAAe,KAAK,YAAY,KAAK;AAC3C,QAAI,KAAK,MAAM;AACb,YAAM,YAAY,KAAK,MAAM,KAAK,QAAQ,YAAY;AACtD,YAAM,eAAe,KAAK,QAAQ,YAAY;AAC9C,YAAMC,YAAW,eAAe,KAAK;AACrC,aAAO,KAAK,IAAI,YAAYA,WAAU,KAAK,MAAM;AAAA,IACnD;AACA,UAAM,QAAQ,KAAK,MAAM,CAAC;AAC1B,UAAM,UAAU,IAAI;AACpB,UAAM,OAAO,eAAe,QAAQ,KAAK,YAAY;AACrD,WAAO,KAAK,KAAK,IAAI;AAAA,EACvB;AAAA,EAEA,QAAQ,aAAa;AACnB,QAAI,eAAe,KAAM,QAAO,KAAK;AACrC,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,GAAG;AAEV,UAAMC,KAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,UAAM,IAAI,KAAK;AACf,QAAID;AAEJ,QAAI,KAAK,MAAM;AASb,YAAM,IAAI,SAAUC,IAAG;AACrB,cAAM,WAAW,IAAI,KAAK,MAAOA,MAAK,KAAK,IAAI,OAAQ,IAAI,EAAE;AAC7D,cAAM,YAAa,YAAY,CAAC,KAAO,CAAC,YAAY;AACpD,cAAM,WACH,KAAK,IAAI,IAAI,SAAS,KAAKA,MAAK,IAAI,MAAO,IAAI;AAClD,cAAM,UAAU,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,CAAC;AACjD,eAAO;AAAA,MACT;AAGA,YAAM,UAAU,KAAK,IAAI,KAAK;AAC9B,MAAAD,YACEC,MAAK,IACD,KAAK,MAAM,EAAE,IAAI,CAAC,IAClBA,KAAI,UACF,EAAEA,EAAC,IACH,KAAK,MAAM,EAAE,UAAU,IAAI,CAAC;AACpC,aAAOD;AAAA,IACT;AAGA,UAAM,YAAY,KAAK,MAAM,KAAK,MAAM,CAAC;AACzC,UAAM,eAAe,KAAK,YAAY,MAAM;AAC5C,UAAM,WAAY,gBAAgB,CAAC,KAAO,KAAK;AAC/C,IAAAA,YAAW,aAAa,WAAW,IAAI,IAAI;AAC3C,WAAO,KAAK,MAAMA,SAAQ;AAAA,EAC5B;AAAA,EAEA,SAAS,GAAG;AACV,QAAI,KAAK,MAAM;AACb,aAAO,KAAK,IAAI,GAAG,KAAK,QAAQ,KAAK,SAAS,CAAC;AAAA,IACjD;AACA,WAAO,KAAK,KAAK,IAAI,KAAK,SAAS,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,QAAQ,OAAO,YAAY,aAAa;AAC5C,SAAK,OAAO,KAAK;AAAA,MACf,aAAa,UAAU;AAAA,MACvB,QAAQ,SAAS;AAAA,MACjB,UAAU;AAAA,MACV;AAAA,MACA,aAAa;AAAA,MACb,UAAU;AAAA,IACZ,CAAC;AACD,UAAME,YAAW,KAAK,SAAS;AAC/B,IAAAA,aAAY,KAAK,SAAS,EAAE,UAAU;AACtC,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ;AACN,QAAI,KAAK,SAAU,QAAO;AAC1B,SAAK,KAAK,CAAC;AACX,SAAK,WAAW;AAChB,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,SAAS;AACf,SAAK,WAAW,WAAW,OAAO,CAAC,KAAK,WAAW;AACnD,WAAO;AAAA,EACT;AAAA,EAEA,SAASA,WAAU,OAAO,MAAM;AAE9B,QAAI,EAAEA,qBAAoB,WAAW;AACnC,aAAO;AACP,cAAQA;AACR,MAAAA,YAAW,KAAK,SAAS;AAAA,IAC3B;AAGA,QAAI,CAACA,WAAU;AACb,YAAM,MAAM,6CAA6C;AAAA,IAC3D;AAGA,IAAAA,UAAS,SAAS,MAAM,OAAO,IAAI;AACnC,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,IAAI;AAEP,QAAI,CAAC,KAAK,QAAS,QAAO;AAG1B,SAAK,MAAM,OAAO,KAAK;AACvB,SAAK,SAAS;AACd,UAAMF,YAAW,KAAK,SAAS;AAG/B,UAAM,UAAU,KAAK,kBAAkBA,aAAY,KAAK,SAAS;AACjE,SAAK,gBAAgBA;AAGrB,UAAM,WAAW,KAAK,SAAS;AAC/B,UAAM,cAAc,KAAK,aAAa,KAAK,KAAK,QAAQ;AACxD,UAAM,eAAe,KAAK,YAAY,YAAY,KAAK,SAAS;AAEhE,SAAK,YAAY,KAAK;AACtB,QAAI,aAAa;AACf,WAAK,KAAK,SAAS,IAAI;AAAA,IACzB;AAKA,UAAM,cAAc,KAAK;AACzB,SAAK,OAAO,CAAC,eAAe,CAAC,gBAAgB,KAAK,SAAS;AAG3D,SAAK,WAAW;AAEhB,QAAI,YAAY;AAEhB,QAAI,WAAW,aAAa;AAC1B,WAAK,YAAY,OAAO;AAGxB,WAAK,aAAa,IAAI,OAAO;AAC7B,kBAAY,KAAK,KAAK,cAAc,KAAKA,SAAQ;AAEjD,WAAK,KAAK,QAAQ,IAAI;AAAA,IACxB;AAGA,SAAK,OAAO,KAAK,QAAS,aAAa;AACvC,QAAI,cAAc;AAChB,WAAK,KAAK,YAAY,IAAI;AAAA,IAC5B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,MAAM;AACT,QAAI,QAAQ,MAAM;AAChB,aAAO,KAAK;AAAA,IACd;AACA,UAAM,KAAK,OAAO,KAAK;AACvB,SAAK,KAAK,EAAE;AACZ,WAAO;AAAA,EACT;AAAA,EAEA,SAASE,WAAU;AAEjB,QAAI,OAAOA,cAAa,YAAa,QAAO,KAAK;AACjD,SAAK,YAAYA;AACjB,WAAO;AAAA,EACT;AAAA,EAEA,aAAa;AACX,UAAMA,YAAW,KAAK,SAAS;AAC/B,IAAAA,aAAYA,UAAS,WAAW,IAAI;AACpC,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,YAAY,SAAS;AAEnB,QAAI,CAAC,WAAW,CAAC,KAAK,eAAgB;AAGtC,aAAS,IAAI,GAAG,MAAM,KAAK,OAAO,QAAQ,IAAI,KAAK,EAAE,GAAG;AAEtD,YAAM,UAAU,KAAK,OAAO,CAAC;AAG7B,YAAM,UAAU,KAAK,kBAAmB,CAAC,QAAQ,eAAe;AAChE,gBAAU,CAAC,QAAQ;AAGnB,UAAI,WAAW,SAAS;AACtB,gBAAQ,YAAY,KAAK,IAAI;AAC7B,gBAAQ,cAAc;AAAA,MACxB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAGA,iBAAiB,QAAQ,SAAS;AAChC,SAAK,SAAS,MAAM,IAAI;AAAA,MACtB;AAAA,MACA,QAAQ,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC;AAAA,IAC5C;AAQA,QAAI,KAAK,gBAAgB;AACvB,YAAMA,YAAW,KAAK,SAAS;AAC/B,MAAAA,aAAYA,UAAS,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA,EAIA,KAAK,cAAc;AAEjB,QAAI,cAAc;AAClB,aAAS,IAAI,GAAG,MAAM,KAAK,OAAO,QAAQ,IAAI,KAAK,EAAE,GAAG;AAEtD,YAAM,UAAU,KAAK,OAAO,CAAC;AAI7B,YAAM,YAAY,QAAQ,OAAO,KAAK,MAAM,YAAY;AACxD,cAAQ,WAAW,QAAQ,YAAY,cAAc;AACrD,oBAAc,eAAe,QAAQ;AAAA,IACvC;AAGA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,aAAa,QAAQ,QAAQ,OAAO;AAClC,QAAI,KAAK,SAAS,MAAM,GAAG;AAEzB,UAAI,CAAC,KAAK,SAAS,MAAM,EAAE,OAAO,aAAa;AAC7C,cAAM,QAAQ,KAAK,OAAO,QAAQ,KAAK,SAAS,MAAM,EAAE,MAAM;AAC9D,aAAK,OAAO,OAAO,OAAO,CAAC;AAC3B,eAAO;AAAA,MACT;AAIA,UAAI,KAAK,SAAS,MAAM,EAAE,OAAO,UAAU;AACzC,aAAK,SAAS,MAAM,EAAE,OAAO,SAAS,KAAK,MAAM,QAAQ,KAAK;AAAA,MAEhE,OAAO;AACL,aAAK,SAAS,MAAM,EAAE,QAAQ,GAAG,MAAM;AAAA,MACzC;AAEA,WAAK,SAAS,MAAM,EAAE,OAAO,WAAW;AACxC,YAAMA,YAAW,KAAK,SAAS;AAC/B,MAAAA,aAAYA,UAAS,KAAK;AAC1B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AAEA,OAAO,KAAK;AAEL,IAAM,aAAN,MAAiB;AAAA,EACtB,YAAYC,cAAa,IAAI,OAAO,GAAG,KAAK,IAAI,OAAO,MAAM;AAC3D,SAAK,aAAaA;AAClB,SAAK,KAAK;AACV,SAAK,OAAO;AAAA,EACd;AAAA,EAEA,2BAA2B;AAAA,EAAC;AAC9B;AAEA,OAAO,CAAC,QAAQ,UAAU,GAAG;AAAA,EAC3B,UAAU,QAAQ;AAChB,WAAO,IAAI;AAAA,MACT,OAAO,WAAW,UAAU,KAAK,UAAU;AAAA,MAC3C,OAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;AAID,IAAM,YAAY,CAAC,MAAM,SAAS,KAAK,WAAW,IAAI;AACtD,IAAM,qBAAqB,CAAC,WAAW,OAAO;AAE9C,SAAS,kBAAkB;AAEzB,QAAM,UAAU,KAAK,uBAAuB;AAC5C,QAAM,eAAe,QAClB,IAAI,kBAAkB,EACtB,OAAO,WAAW,IAAI,OAAO,CAAC;AAEjC,OAAK,UAAU,YAAY;AAE3B,OAAK,uBAAuB,MAAM;AAElC,MAAI,KAAK,uBAAuB,OAAO,MAAM,GAAG;AAC9C,SAAK,WAAW;AAAA,EAClB;AACF;AAEO,IAAM,cAAN,MAAkB;AAAA,EACvB,cAAc;AACZ,SAAK,UAAU,CAAC;AAChB,SAAK,MAAM,CAAC;AAAA,EACd;AAAA,EAEA,IAAI,QAAQ;AACV,QAAI,KAAK,QAAQ,SAAS,MAAM,EAAG;AACnC,UAAM,KAAK,OAAO,KAAK;AAEvB,SAAK,QAAQ,KAAK,MAAM;AACxB,SAAK,IAAI,KAAK,EAAE;AAEhB,WAAO;AAAA,EACT;AAAA,EAEA,YAAY,IAAI;AACd,UAAM,YAAY,KAAK,IAAI,QAAQ,KAAK,CAAC,KAAK;AAC9C,SAAK,IAAI,OAAO,GAAG,WAAW,CAAC;AAC/B,SAAK,QACF,OAAO,GAAG,WAAW,IAAI,WAAW,CAAC,EACrC,QAAQ,CAAC,MAAM,EAAE,yBAAyB,CAAC;AAC9C,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,IAAI,WAAW;AAClB,UAAM,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC;AACrC,SAAK,IAAI,OAAO,OAAO,GAAG,KAAK,CAAC;AAChC,SAAK,QAAQ,OAAO,OAAO,GAAG,SAAS;AACvC,WAAO;AAAA,EACT;AAAA,EAEA,QAAQ,IAAI;AACV,WAAO,KAAK,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,CAAC;AAAA,EAC9C;AAAA,EAEA,SAAS;AACP,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EAEA,QAAQ;AACN,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,EAAE,GAAG;AAC5C,YAAM,SAAS,KAAK,QAAQ,CAAC;AAE7B,YAAM,YACJ,cACA,OAAO,QACP,WAAW;AAAA,OAEV,CAAC,OAAO,aACP,CAAC,OAAO,UAAU,WAAW,SAAS,OAAO,EAAE,OAChD,CAAC,WAAW,aACX,CAAC,WAAW,UAAU,WAAW,SAAS,WAAW,EAAE;AAE3D,UAAI,WAAW;AAEb,aAAK,OAAO,OAAO,EAAE;AACrB,cAAM,YAAY,OAAO,UAAU,UAAU;AAC7C,aAAK,KAAK,WAAW,IAAI,SAAS;AAClC,qBAAa;AACb,UAAE;AAAA,MACJ,OAAO;AACL,qBAAa;AAAA,MACf;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,IAAI;AACT,UAAM,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC;AACrC,SAAK,IAAI,OAAO,OAAO,CAAC;AACxB,SAAK,QAAQ,OAAO,OAAO,CAAC;AAC5B,WAAO;AAAA,EACT;AACF;AAEA,gBAAgB;AAAA,EACd,SAAS;AAAA,IACP,QAAQ,UAAU,OAAO,MAAM;AAC7B,YAAM,IAAI,OAAO,SAAS,UAAU,OAAO,IAAI;AAC/C,YAAMD,YAAW,KAAK,SAAS;AAC/B,aAAO,IAAI,OAAO,EAAE,QAAQ,EACzB,KAAK,CAAC,EACN,QAAQ,IAAI,EACZ,SAASA,UAAS,KAAK,CAAC,EACxB,SAAS,EAAE,OAAO,EAAE,IAAI;AAAA,IAC7B;AAAA,IAEA,MAAM,IAAI,MAAM;AACd,aAAO,KAAK,QAAQ,GAAG,IAAI,IAAI;AAAA,IACjC;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,6BAA6B,eAAe;AAC1C,WAAK,uBAAuB,YAAY,cAAc,EAAE;AAAA,IAC1D;AAAA,IAEA,kBAAkB,SAAS;AACzB,aACE,KAAK,uBAAuB,QAIzB,OAAO,CAAC,WAAW,OAAO,MAAM,QAAQ,EAAE,EAC1C,IAAI,kBAAkB,EACtB,OAAO,WAAW,IAAI,OAAO,CAAC;AAAA,IAErC;AAAA,IAEA,WAAW,QAAQ;AACjB,WAAK,uBAAuB,IAAI,MAAM;AAKtC,uBAAS,gBAAgB,KAAK,QAAQ;AACtC,WAAK,WAAW,iBAAS,UAAU,gBAAgB,KAAK,IAAI,CAAC;AAAA,IAC/D;AAAA,IAEA,iBAAiB;AACf,UAAI,KAAK,YAAY,MAAM;AACzB,aAAK,yBAAyB,IAAI,YAAY,EAAE;AAAA,UAC9C,IAAI,WAAW,IAAI,OAAO,IAAI,CAAC;AAAA,QACjC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF,CAAC;AAGD,IAAM,aAAa,CAAC,GAAG,MAAM,EAAE,OAAO,CAACD,OAAM,CAAC,EAAE,SAASA,EAAC,CAAC;AAE3D,OAAO,QAAQ;AAAA,EACb,KAAK,GAAG,GAAG;AACT,WAAO,KAAK,UAAU,QAAQ,GAAG,CAAC;AAAA,EACpC;AAAA;AAAA,EAGA,IAAI,GAAG,GAAG;AACR,WAAO,KAAK,UAAU,OAAO,GAAG,CAAC;AAAA,EACnC;AAAA,EAEA,UAAU,MAAM,aAAa,KAAK;AAChC,QAAI,OAAO,gBAAgB,UAAU;AACnC,aAAO,KAAK,UAAU,MAAM,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC;AAAA,IACpD;AAEA,QAAIG,SAAQ;AACZ,QAAI,KAAK,aAAa,MAAMA,MAAK,EAAG,QAAO;AAE3C,QAAI,UAAU,IAAI,UAAU,KAAK,QAAQ,EAAE,GAAGA,MAAK;AACnD,QAAI,OAAO,OAAO,KAAKA,MAAK;AAE5B,SAAK;AAAA,MACH,WAAY;AACV,kBAAU,QAAQ,KAAK,KAAK,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC;AAAA,MACnD;AAAA,MACA,SAAU,KAAK;AACb,aAAK,QAAQ,EAAE,IAAI,EAAE,QAAQ,GAAG,GAAG,EAAE,QAAQ,CAAC;AAC9C,eAAO,QAAQ,KAAK;AAAA,MACtB;AAAA,MACA,SAAU,YAAY;AAEpB,cAAM,UAAU,OAAO,KAAK,UAAU;AACtC,cAAM,cAAc,WAAW,SAAS,IAAI;AAG5C,YAAI,YAAY,QAAQ;AAEtB,gBAAM,iBAAiB,KAAK,QAAQ,EAAE,IAAI,EAAE,WAAW;AAGvD,gBAAM,eAAe,IAAI,UAAU,QAAQ,KAAK,CAAC,EAAE,QAAQ;AAG3D,iBAAO,OAAO,cAAc,cAAc;AAC1C,kBAAQ,KAAK,YAAY;AAAA,QAC3B;AAGA,cAAM,aAAa,IAAI,UAAU,QAAQ,GAAG,CAAC,EAAE,QAAQ;AAGvD,eAAO,OAAO,YAAY,UAAU;AAGpC,gBAAQ,GAAG,UAAU;AAGrB,eAAO;AACP,QAAAA,SAAQ;AAAA,MACV;AAAA,IACF;AAEA,SAAK,iBAAiB,MAAM,OAAO;AACnC,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,OAAOC,QAAO;AACjB,QAAI,KAAK,aAAa,QAAQ,OAAOA,MAAK,EAAG,QAAO;AAEpD,QAAI,UAAU,IAAI,UAAU,KAAK,QAAQ,EAAE,GAAG,IAAI,UAAU,KAAK,CAAC;AAElE,SAAK;AAAA,MACH,WAAY;AACV,kBAAU,QAAQ,KAAK,KAAK,QAAQ,EAAE,KAAK,CAAC;AAAA,MAC9C;AAAA,MACA,SAAU,KAAK;AACb,aAAK,QAAQ,EAAE,KAAK,QAAQ,GAAG,GAAG,GAAGA,MAAK;AAC1C,eAAO,QAAQ,KAAK;AAAA,MACtB;AAAA,MACA,SAAU,UAAU,UAAU;AAC5B,QAAAA,SAAQ;AACR,gBAAQ,GAAG,QAAQ;AAAA,MACrB;AAAA,IACF;AAEA,SAAK,iBAAiB,QAAQ,OAAO;AACrC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,UAAUF,aAAY,UAAU,QAAQ;AAEtC,eAAWA,YAAW,YAAY;AAClC,QACE,KAAK,kBACL,CAAC,YACD,KAAK,aAAa,aAAaA,WAAU,GACzC;AACA,aAAO;AAAA,IACT;AAGA,UAAM,WAAW,OAAO,aAAaA,WAAU;AAC/C,aACEA,YAAW,UAAU,OACjBA,YAAW,SACX,UAAU,OACR,SACA,CAAC;AAGT,UAAM,UAAU,IAAI,UAAU,KAAK,QAAQ,EAAE;AAAA,MAC3C,SAAS,eAAe;AAAA,IAC1B;AAEA,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AAEJ,aAAS,QAAQ;AAEf,gBAAU,WAAW,KAAK,QAAQ;AAClC,eAAS,UAAU,UAAUA,aAAY,OAAO;AAEhD,uBAAiB,IAAI,OAAO,WAAW,SAAY,OAAO;AAG1D,cAAQ,WAAW,IAAI;AAGvB,UAAI,CAAC,UAAU;AACb,gBAAQ,6BAA6B,IAAI;AAAA,MAC3C;AAAA,IACF;AAEA,aAAS,IAAI,KAAK;AAGhB,UAAI,CAAC,SAAU,MAAK,eAAe;AAEnC,YAAM,EAAE,GAAAF,IAAG,GAAAK,GAAE,IAAI,IAAI,MAAM,MAAM,EAAE;AAAA,QACjC,QAAQ,kBAAkB,IAAI;AAAA,MAChC;AAEA,UAAI,SAAS,IAAI,OAAO,EAAE,GAAGH,aAAY,QAAQ,CAACF,IAAGK,EAAC,EAAE,CAAC;AACzD,UAAI,QAAQ,KAAK,kBAAkB,UAAU,UAAU;AAEvD,UAAI,QAAQ;AACV,iBAAS,OAAO,UAAUL,IAAGK,EAAC;AAC9B,gBAAQ,MAAM,UAAUL,IAAGK,EAAC;AAG5B,cAAM,UAAU,OAAO;AACvB,cAAM,WAAW,MAAM;AAGvB,cAAM,gBAAgB,CAAC,UAAU,KAAK,SAAS,UAAU,GAAG;AAC5D,cAAM,YAAY,cAAc,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC;AACjE,cAAM,WAAW,KAAK,IAAI,GAAG,SAAS;AACtC,cAAM,QAAQ,UAAU,QAAQ,QAAQ;AACxC,eAAO,SAAS,cAAc,KAAK;AAAA,MACrC;AAEA,UAAI,UAAU;AAGZ,YAAI,CAAC,UAAU;AACb,iBAAO,SAASH,YAAW,UAAU;AAAA,QACvC;AACA,YAAI,KAAK,kBAAkB,cAAc;AACvC,gBAAM,SAAS;AAAA,QACjB;AAAA,MACF;AAEA,cAAQ,KAAK,KAAK;AAClB,cAAQ,GAAG,MAAM;AAEjB,YAAM,mBAAmB,QAAQ,GAAG,GAAG;AACvC,qBAAe,iBAAiB;AAChC,gBAAU,IAAI,OAAO,gBAAgB;AAErC,WAAK,aAAa,OAAO;AACzB,cAAQ,WAAW,IAAI;AACvB,aAAO,QAAQ,KAAK;AAAA,IACtB;AAEA,aAAS,SAAS,eAAe;AAE/B,WACG,cAAc,UAAU,UAAU,SAAS,OAC3CA,YAAW,UAAU,UAAU,SAAS,GACzC;AACA,iBAAS,UAAU,eAAe,OAAO;AAAA,MAC3C;AAGA,MAAAA,cAAa,EAAE,GAAG,eAAe,OAAO;AAAA,IAC1C;AAEA,SAAK,MAAM,OAAO,KAAK,UAAU,IAAI;AACrC,SAAK,kBAAkB,KAAK,iBAAiB,aAAa,OAAO;AACjE,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,EAAEF,IAAG;AACH,WAAO,KAAK,aAAa,KAAKA,EAAC;AAAA,EACjC;AAAA;AAAA,EAGA,EAAEK,IAAG;AACH,WAAO,KAAK,aAAa,KAAKA,EAAC;AAAA,EACjC;AAAA,EAEA,GAAGL,IAAG;AACJ,WAAO,KAAK,aAAa,MAAMA,EAAC;AAAA,EAClC;AAAA,EAEA,GAAGK,IAAG;AACJ,WAAO,KAAK,aAAa,MAAMA,EAAC;AAAA,EAClC;AAAA,EAEA,GAAGL,KAAI,GAAG;AACR,WAAO,KAAK,kBAAkB,KAAKA,EAAC;AAAA,EACtC;AAAA,EAEA,GAAGK,KAAI,GAAG;AACR,WAAO,KAAK,kBAAkB,KAAKA,EAAC;AAAA,EACtC;AAAA,EAEA,MAAML,IAAGK,IAAG;AACV,WAAO,KAAK,GAAGL,EAAC,EAAE,GAAGK,EAAC;AAAA,EACxB;AAAA,EAEA,kBAAkB,QAAQC,KAAI;AAC5B,IAAAA,MAAK,IAAI,UAAUA,GAAE;AAGrB,QAAI,KAAK,aAAa,QAAQA,GAAE,EAAG,QAAO;AAG1C,UAAM,UAAU,IAAI,UAAU,KAAK,QAAQ,EAAE,GAAGA,GAAE;AAClD,QAAIC,QAAO;AACX,SAAK;AAAA,MACH,WAAY;AACV,QAAAA,QAAO,KAAK,QAAQ,EAAE,MAAM,EAAE;AAC9B,gBAAQ,KAAKA,KAAI;AACjB,gBAAQ,GAAGA,QAAOD,GAAE;AAAA,MACtB;AAAA,MACA,SAAU,KAAK;AACb,aAAK,QAAQ,EAAE,MAAM,EAAE,QAAQ,GAAG,GAAG,CAAC;AACtC,eAAO,QAAQ,KAAK;AAAA,MACtB;AAAA,MACA,SAAU,OAAO;AACf,gBAAQ,GAAGC,QAAO,IAAI,UAAU,KAAK,CAAC;AAAA,MACxC;AAAA,IACF;AAGA,SAAK,iBAAiB,QAAQ,OAAO;AACrC,WAAO;AAAA,EACT;AAAA,EAEA,aAAa,QAAQD,KAAI;AAEvB,QAAI,KAAK,aAAa,QAAQA,GAAE,EAAG,QAAO;AAG1C,UAAM,UAAU,IAAI,UAAU,KAAK,QAAQ,EAAE,GAAGA,GAAE;AAClD,SAAK;AAAA,MACH,WAAY;AACV,gBAAQ,KAAK,KAAK,QAAQ,EAAE,MAAM,EAAE,CAAC;AAAA,MACvC;AAAA,MACA,SAAU,KAAK;AACb,aAAK,QAAQ,EAAE,MAAM,EAAE,QAAQ,GAAG,GAAG,CAAC;AACtC,eAAO,QAAQ,KAAK;AAAA,MACtB;AAAA,IACF;AAGA,SAAK,iBAAiB,QAAQ,OAAO;AACrC,WAAO;AAAA,EACT;AAAA,EAEA,aAAa,QAAQ,OAAO;AAC1B,WAAO,KAAK,aAAa,QAAQ,IAAI,UAAU,KAAK,CAAC;AAAA,EACvD;AAAA;AAAA,EAGA,GAAGN,IAAG;AACJ,WAAO,KAAK,aAAa,MAAMA,EAAC;AAAA,EAClC;AAAA;AAAA,EAGA,GAAGK,IAAG;AACJ,WAAO,KAAK,aAAa,MAAMA,EAAC;AAAA,EAClC;AAAA;AAAA,EAGA,KAAKL,IAAGK,IAAG;AACT,WAAO,KAAK,EAAEL,EAAC,EAAE,EAAEK,EAAC;AAAA,EACtB;AAAA,EAEA,MAAML,IAAGK,IAAG;AACV,WAAO,KAAK,GAAGL,EAAC,EAAE,GAAGK,EAAC;AAAA,EACxB;AAAA;AAAA,EAGA,OAAOL,IAAGK,IAAG;AACX,WAAO,KAAK,GAAGL,EAAC,EAAE,GAAGK,EAAC;AAAA,EACxB;AAAA;AAAA,EAGA,KAAKG,QAAOC,SAAQ;AAElB,QAAI;AAEJ,QAAI,CAACD,UAAS,CAACC,SAAQ;AACrB,YAAM,KAAK,SAAS,KAAK;AAAA,IAC3B;AAEA,QAAI,CAACD,QAAO;AACV,MAAAA,SAAS,IAAI,QAAQ,IAAI,SAAUC;AAAA,IACrC;AAEA,QAAI,CAACA,SAAQ;AACX,MAAAA,UAAU,IAAI,SAAS,IAAI,QAASD;AAAA,IACtC;AAEA,WAAO,KAAK,MAAMA,MAAK,EAAE,OAAOC,OAAM;AAAA,EACxC;AAAA;AAAA,EAGA,MAAMD,QAAO;AACX,WAAO,KAAK,aAAa,SAASA,MAAK;AAAA,EACzC;AAAA;AAAA,EAGA,OAAOC,SAAQ;AACb,WAAO,KAAK,aAAa,UAAUA,OAAM;AAAA,EAC3C;AAAA;AAAA,EAGA,KAAK,GAAG,GAAG,GAAG,GAAG;AAEf,QAAI,UAAU,WAAW,GAAG;AAC1B,aAAO,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAAA,IAC/B;AAEA,QAAI,KAAK,aAAa,QAAQ,CAAC,EAAG,QAAO;AAEzC,UAAM,UAAU,IAAI,UAAU,KAAK,QAAQ,EACxC,KAAK,KAAK,SAAS,UAAU,EAC7B,GAAG,CAAC;AAEP,SAAK;AAAA,MACH,WAAY;AACV,gBAAQ,KAAK,KAAK,SAAS,MAAM,CAAC;AAAA,MACpC;AAAA,MACA,SAAU,KAAK;AACb,aAAK,SAAS,KAAK,QAAQ,GAAG,GAAG,CAAC;AAClC,eAAO,QAAQ,KAAK;AAAA,MACtB;AAAA,IACF;AAEA,SAAK,iBAAiB,QAAQ,OAAO;AACrC,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,QAAQ,OAAO;AACb,WAAO,KAAK,aAAa,WAAW,KAAK;AAAA,EAC3C;AAAA;AAAA,EAGA,QAAQT,IAAGK,IAAGG,QAAOC,SAAQ;AAC3B,WAAO,KAAK,aAAa,WAAW,IAAI,IAAIT,IAAGK,IAAGG,QAAOC,OAAM,CAAC;AAAA,EAClE;AAAA,EAEA,OAAO,GAAG;AACR,QAAI,OAAO,MAAM,UAAU;AACzB,aAAO,KAAK,OAAO;AAAA,QACjB,QAAQ,UAAU,CAAC;AAAA,QACnB,OAAO,UAAU,CAAC;AAAA,QAClB,SAAS,UAAU,CAAC;AAAA,MACtB,CAAC;AAAA,IACH;AAEA,QAAI,EAAE,WAAW,KAAM,MAAK,KAAK,gBAAgB,EAAE,OAAO;AAC1D,QAAI,EAAE,SAAS,KAAM,MAAK,KAAK,cAAc,EAAE,KAAK;AACpD,QAAI,EAAE,UAAU,KAAM,MAAK,KAAK,UAAU,EAAE,MAAM;AAElD,WAAO;AAAA,EACT;AACF,CAAC;AAED,OAAO,QAAQ,EAAE,IAAI,IAAI,MAAM,GAAG,CAAC;AACnC,SAAS,QAAQ,QAAQ;;;AChjCzB,IAAqB,MAArB,cAAiC,UAAU;AAAA,EACzC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,OAAO,IAAI,GAAGA,MAAK;AACnC,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA,EAGA,OAAO;AACL,QAAI,CAAC,KAAK,OAAO,EAAG,QAAO,KAAK,KAAK,EAAE,KAAK;AAE5C,WAAO,MAAM,KAAK,KAAK,cAAc,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,CAAC;AAAA,EACtE;AAAA,EAEA,SAAS;AACP,WACE,CAAC,KAAK,KAAK,cACV,EAAE,KAAK,KAAK,sBAAsB,QAAQ,OAAO,eAChD,KAAK,KAAK,WAAW,aAAa;AAAA,EAExC;AAAA;AAAA,EAGA,YAAY;AACV,QAAI,CAAC,KAAK,OAAO,EAAG,QAAO,KAAK,KAAK,EAAE,UAAU;AACjD,WAAO,KAAK,KAAK,EAAE,OAAO,KAAK,SAAS,MAAM,CAAC,EAAE;AAAA,MAC/C;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,KAAK,EAAE,OAAO,MAAM,SAAS,KAAK,CAAC,EAC5C,KAAK,eAAe,MAAM,KAAK,EAC/B,KAAK,eAAe,MAAM,KAAK;AAAA,EACpC;AAAA;AAAA;AAAA,EAIA,OAAO;AACL,QAAI,KAAK,OAAO,EAAG,QAAO;AAC1B,WAAO,MAAM,KAAK;AAAA,EACpB;AACF;AAEA,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,QAAQ,kBAAkB,WAAY;AACpC,aAAO,KAAK,IAAI,IAAI,IAAI,CAAC;AAAA,IAC3B,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,KAAK,OAAO,IAAI;;;AC9DzB,IAAqB,SAArB,cAAoC,UAAU;AAAA;AAAA,EAE5C,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,UAAU,IAAI,GAAGA,MAAK;AAAA,EACxC;AACF;AAEA,gBAAgB;AAAA,EACd,WAAW;AAAA,IACT,QAAQ,kBAAkB,WAAY;AACpC,aAAO,KAAK,IAAI,IAAI,OAAO,CAAC;AAAA,IAC9B,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,QAAQ,QAAQ;;;ACnBzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAAAC;AAAA,EAAA,UAAAC;AAAA,EAAA;AAAA,cAAAC;AAAA,EAAA;AAAA,WAAAC;AAAA,EAAA,SAAAC;AAAA;AAGO,SAAS,MAAM,MAAM;AAE1B,MAAI,KAAK,WAAW,OAAO;AACzB,SAAK,MAAM;AAAA,EACb;AAGA,OAAK,KAAK,YAAY,QAAQ,SAAS,eAAe,IAAI,CAAC;AAE3D,SAAO;AACT;AAGO,SAAS,SAAS;AACvB,SAAO,KAAK,KAAK,sBAAsB;AACzC;AAKO,SAASC,GAAEA,IAAG,MAAM,KAAK,KAAK,GAAG;AACtC,MAAIA,MAAK,MAAM;AACb,WAAO,IAAI;AAAA,EACb;AAEA,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAIA,KAAI,IAAI,CAAC;AAClD;AAGO,SAASC,GAAEA,IAAG,MAAM,KAAK,KAAK,GAAG;AACtC,MAAIA,MAAK,MAAM;AACb,WAAO,IAAI;AAAA,EACb;AAEA,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAIA,KAAI,IAAI,CAAC;AAClD;AAEO,SAASC,MAAKF,IAAGC,IAAG,MAAM,KAAK,KAAK,GAAG;AAC5C,SAAO,KAAK,EAAED,IAAG,GAAG,EAAE,EAAEC,IAAG,GAAG;AAChC;AAGO,SAASE,IAAGH,IAAG,MAAM,KAAK,KAAK,GAAG;AACvC,MAAIA,MAAK,MAAM;AACb,WAAO,IAAI;AAAA,EACb;AAEA,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAIA,KAAI,IAAI,EAAE;AACnD;AAGO,SAASI,IAAGH,IAAG,MAAM,KAAK,KAAK,GAAG;AACvC,MAAIA,MAAK,MAAM;AACb,WAAO,IAAI;AAAA,EACb;AAEA,SAAO,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG,IAAIA,KAAI,IAAI,EAAE;AACnD;AAEO,SAAS,OAAOD,IAAGC,IAAG,MAAM,KAAK,KAAK,GAAG;AAC9C,SAAO,KAAK,GAAGD,IAAG,GAAG,EAAE,GAAGC,IAAG,GAAG;AAClC;AAEO,SAAS,GAAGD,IAAG;AACpB,SAAO,KAAK,KAAK,KAAKA,EAAC;AACzB;AAEO,SAAS,GAAGC,IAAG;AACpB,SAAO,KAAK,KAAK,KAAKA,EAAC;AACzB;AAEO,SAAS,MAAMD,IAAGC,IAAG;AAC1B,SAAO,KAAK,GAAGD,EAAC,EAAE,GAAGC,EAAC;AACxB;AAGO,SAAS,MAAMI,QAAO;AAC3B,OAAK,SAAS,CAAC,CAACA;AAChB,SAAO;AACT;;;ACpEA,IAAqB,OAArB,cAAkC,MAAM;AAAA;AAAA,EAEtC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,QAAQ,IAAI,GAAGA,MAAK;AAEpC,SAAK,IAAI,UAAU,KAAK,IAAI,WAAW,IAAI,UAAU,GAAG;AACxD,SAAK,WAAW;AAChB,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA,EAGA,QAAQ,OAAO;AAEb,QAAI,SAAS,MAAM;AACjB,aAAO,KAAK,IAAI;AAAA,IAClB;AAGA,SAAK,IAAI,UAAU,IAAI,UAAU,KAAK;AAEtC,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA;AAAA,EAGA,QAAQ,SAAS;AAEf,QAAI,OAAO,YAAY,WAAW;AAChC,WAAK,WAAW;AAAA,IAClB;AAGA,QAAI,KAAK,UAAU;AACjB,YAAM,OAAO;AACb,UAAI,kBAAkB;AACtB,YAAM,UAAU,KAAK,IAAI;AAEzB,WAAK,KAAK,SAAU,GAAG;AACrB,YAAI,cAAc,KAAK,IAAI,EAAG;AAE9B,cAAM,WAAW,QAAQ,OACtB,iBAAiB,KAAK,IAAI,EAC1B,iBAAiB,WAAW;AAE/B,cAAMC,MAAK,UAAU,IAAI,UAAU,QAAQ;AAE3C,YAAI,KAAK,IAAI,UAAU;AACrB,eAAK,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAE7B,cAAI,KAAK,KAAK,MAAM,MAAM;AACxB,+BAAmBA;AAAA,UACrB,OAAO;AACL,iBAAK,KAAK,MAAM,IAAIA,MAAK,kBAAkB,CAAC;AAC5C,8BAAkB;AAAA,UACpB;AAAA,QACF;AAAA,MACF,CAAC;AAED,WAAK,KAAK,SAAS;AAAA,IACrB;AAEA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,QAAQ,GAAG;AACT,SAAK,MAAM;AACX,SAAK,IAAI,UAAU,IAAI,UAAU,EAAE,WAAW,GAAG;AACjD,WAAO;AAAA,EACT;AAAA,EAEA,iBAAiB;AACf,mBAAe,MAAM,KAAK,KAAK,EAAE,SAAS,IAAI,CAAC;AAC/C,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,KAAK,MAAM;AAET,QAAI,SAAS,QAAW;AACtB,YAAM,WAAW,KAAK,KAAK;AAC3B,UAAI,YAAY;AAChB,aAAO;AAEP,eAAS,IAAI,GAAG,MAAM,SAAS,QAAQ,IAAI,KAAK,EAAE,GAAG;AAEnD,YAAI,SAAS,CAAC,EAAE,aAAa,cAAc,cAAc,SAAS,CAAC,CAAC,GAAG;AACrE,cAAI,MAAM,EAAG,aAAY,IAAI;AAC7B;AAAA,QACF;AAGA,YACE,MAAM,aACN,SAAS,CAAC,EAAE,aAAa,KACzB,MAAM,SAAS,CAAC,CAAC,EAAE,IAAI,aAAa,MACpC;AACA,kBAAQ;AAAA,QACV;AAGA,gBAAQ,SAAS,CAAC,EAAE;AAAA,MACtB;AAEA,aAAO;AAAA,IACT;AAGA,SAAK,MAAM,EAAE,MAAM,IAAI;AAEvB,QAAI,OAAO,SAAS,YAAY;AAE9B,WAAK,KAAK,MAAM,IAAI;AAAA,IACtB,OAAO;AAEL,cAAQ,OAAO,IAAI,MAAM,IAAI;AAG7B,eAAS,IAAI,GAAG,KAAK,KAAK,QAAQ,IAAI,IAAI,KAAK;AAC7C,aAAK,QAAQ,KAAK,CAAC,CAAC;AAAA,MACtB;AAAA,IACF;AAGA,WAAO,KAAK,MAAM,KAAK,EAAE,QAAQ;AAAA,EACnC;AACF;AAEA,OAAO,MAAM,gBAAQ;AAErB,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,MAAM,kBAAkB,SAAU,OAAO,IAAI;AAC3C,aAAO,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,KAAK,IAAI;AAAA,IACvC,CAAC;AAAA;AAAA,IAGD,OAAO,kBAAkB,SAAU,OAAO,IAAI;AAC5C,aAAO,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,MAAM,IAAI;AAAA,IACxC,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,MAAM,MAAM;;;AChJrB,IAAqB,QAArB,cAAmC,MAAM;AAAA;AAAA,EAEvC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,SAAS,IAAI,GAAGA,MAAK;AACrC,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA,EAGA,GAAGC,KAAI;AACL,WAAO,KAAK,KAAK,MAAMA,GAAE;AAAA,EAC3B;AAAA;AAAA,EAGA,GAAGC,KAAI;AACL,WAAO,KAAK,KAAK,MAAMA,GAAE;AAAA,EAC3B;AAAA;AAAA,EAGA,UAAU;AAER,SAAK,IAAI,WAAW;AAGpB,UAAM,OAAO,KAAK,OAAO;AAGzB,QAAI,EAAE,gBAAgB,OAAO;AAC3B,aAAO;AAAA,IACT;AAEA,UAAM,IAAI,KAAK,MAAM,IAAI;AAEzB,UAAM,WAAW,QAAQ,OACtB,iBAAiB,KAAK,IAAI,EAC1B,iBAAiB,WAAW;AAC/B,UAAMA,MAAK,KAAK,IAAI,UAAU,IAAI,UAAU,QAAQ;AAGpD,WAAO,KAAK,GAAG,IAAIA,MAAK,CAAC,EAAE,KAAK,KAAK,KAAK,EAAE,CAAC;AAAA,EAC/C;AAAA;AAAA,EAGA,KAAK,MAAM;AACT,QAAI,QAAQ;AACV,aAAO,KAAK,KAAK,eAAe,KAAK,IAAI,WAAW,OAAO;AAE7D,QAAI,OAAO,SAAS,YAAY;AAC9B,WAAK,MAAM,EAAE,MAAM,IAAI;AACvB,WAAK,KAAK,MAAM,IAAI;AACpB,WAAK,MAAM,KAAK;AAAA,IAClB,OAAO;AACL,WAAK,MAAM,IAAI;AAAA,IACjB;AAEA,WAAO;AAAA,EACT;AACF;AAEA,OAAO,OAAO,gBAAQ;AAEtB,gBAAgB;AAAA,EACd,OAAO;AAAA,IACL,OAAO,kBAAkB,SAAU,OAAO,IAAI;AAC5C,YAAM,QAAQ,IAAI,MAAM;AAGxB,UAAI,CAAC,KAAK,QAAQ;AAChB,aAAK,MAAM;AAAA,MACb;AAGA,aAAO,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI;AAAA,IAClC,CAAC;AAAA,EACH;AAAA,EACA,MAAM;AAAA,IACJ,SAAS,SAAU,OAAO,IAAI;AAC5B,aAAO,KAAK,MAAM,IAAI,EAAE,QAAQ;AAAA,IAClC;AAAA,EACF;AACF,CAAC;AAED,SAAS,OAAO,OAAO;;;ACnFvB,IAAqB,SAArB,cAAoC,MAAM;AAAA,EACxC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,UAAU,IAAI,GAAGA,MAAK;AAAA,EACxC;AAAA,EAEA,OAAO,GAAG;AACR,WAAO,KAAK,KAAK,KAAK,CAAC;AAAA,EACzB;AAAA;AAAA,EAGA,GAAGC,KAAI;AACL,WAAO,KAAK,KAAK,KAAKA,GAAE;AAAA,EAC1B;AAAA;AAAA,EAGA,GAAGC,KAAI;AACL,WAAO,KAAK,GAAGA,GAAE;AAAA,EACnB;AAAA,EAEA,KAAKC,OAAM;AACT,WAAO,KAAK,OAAO,IAAI,UAAUA,KAAI,EAAE,OAAO,CAAC,CAAC;AAAA,EAClD;AACF;AAEA,OAAO,QAAQ,EAAE,GAAG,GAAG,IAAI,IAAI,OAAO,OAAO,CAAC;AAE9C,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,QAAQ,kBAAkB,SAAUA,QAAO,GAAG;AAC5C,aAAO,KAAK,IAAI,IAAI,OAAO,CAAC,EAAE,KAAKA,KAAI,EAAE,KAAK,GAAG,CAAC;AAAA,IACpD,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,QAAQ,QAAQ;;;ACzCzB,IAAqB,WAArB,cAAsC,UAAU;AAAA,EAC9C,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,YAAY,IAAI,GAAGA,MAAK;AAAA,EAC1C;AAAA;AAAA,EAGA,SAAS;AAEP,SAAK,QAAQ,EAAE,QAAQ,SAAU,IAAI;AACnC,SAAG,OAAO;AAAA,IACZ,CAAC;AAGD,WAAO,MAAM,OAAO;AAAA,EACtB;AAAA,EAEA,UAAU;AACR,WAAO,SAAS,qBAAqB,KAAK,GAAG,IAAI,GAAG;AAAA,EACtD;AACF;AAEA,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,MAAM,kBAAkB,WAAY;AAClC,aAAO,KAAK,KAAK,EAAE,IAAI,IAAI,SAAS,CAAC;AAAA,IACvC,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AAAA;AAAA,IAEP,UAAU;AACR,aAAO,KAAK,UAAU,WAAW;AAAA,IACnC;AAAA,IAEA,SAAS,SAAS;AAEhB,YAAM,UACJ,mBAAmB,WACf,UACA,KAAK,OAAO,EAAE,KAAK,EAAE,IAAI,OAAO;AAGtC,aAAO,KAAK,KAAK,aAAa,UAAU,QAAQ,GAAG,IAAI,GAAG;AAAA,IAC5D;AAAA;AAAA,IAGA,SAAS;AACP,aAAO,KAAK,KAAK,aAAa,IAAI;AAAA,IACpC;AAAA,EACF;AACF,CAAC;AAED,SAAS,UAAU,UAAU;;;ACrD7B,IAAqB,gBAArB,cAA2C,QAAQ;AAAA,EACjD,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,iBAAiB,IAAI,GAAGA,MAAK;AAAA,EAC/C;AACF;AAEA,gBAAgB;AAAA,EACd,WAAW;AAAA,IACT,eAAe,kBAAkB,SAAUC,QAAOC,SAAQ;AACxD,aAAO,KAAK,IAAI,IAAI,cAAc,CAAC,EAAE,KAAKD,QAAOC,OAAM;AAAA,IACzD,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,eAAe,eAAe;;;AClBvC;AAAA;AAAA;AAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA,aAAAC;AAAA,EAAA,SAAAC;AAAA,EAAA,SAAAC;AAAA;AAMO,SAAS,MAAMC,KAAIC,KAAI;AAC5B,OAAK,SAAS,EAAE,QAAQ,CAAC,UAAU;AACjC,QAAIC;AAIJ,QAAI;AAOF,MAAAA,QACE,MAAM,gBAAgB,UAAU,EAAE,gBAC9B,IAAI,IAAI,MAAM,KAAK,CAAC,KAAK,KAAK,SAAS,QAAQ,CAAC,CAAC,IACjD,MAAM,KAAK;AAAA,IACnB,SAAS,GAAG;AACV;AAAA,IACF;AAGA,UAAM,IAAI,IAAI,OAAO,KAAK;AAG1B,UAAM,SAAS,EAAE,UAAUF,KAAIC,GAAE,EAAE,UAAU,EAAE,QAAQ,CAAC;AAExD,UAAM,IAAI,IAAI,MAAMC,MAAK,GAAGA,MAAK,CAAC,EAAE,UAAU,MAAM;AAEpD,UAAM,KAAK,EAAE,GAAG,EAAE,CAAC;AAAA,EACrB,CAAC;AAED,SAAO;AACT;AAEO,SAAS,GAAGF,KAAI;AACrB,SAAO,KAAK,MAAMA,KAAI,CAAC;AACzB;AAEO,SAAS,GAAGC,KAAI;AACrB,SAAO,KAAK,MAAM,GAAGA,GAAE;AACzB;AAEO,SAASE,QAAOA,SAAQ,MAAM,KAAK,KAAK,GAAG;AAChD,MAAIA,WAAU,KAAM,QAAO,IAAI;AAC/B,SAAO,KAAK,KAAK,IAAI,OAAOA,SAAQ,GAAG;AACzC;AAEO,SAASC,MAAKC,KAAI,GAAGC,KAAI,GAAG,MAAM,KAAK,KAAK,GAAG;AACpD,QAAMN,MAAKK,KAAI,IAAI;AACnB,QAAMJ,MAAKK,KAAI,IAAI;AAEnB,SAAO,KAAK,MAAMN,KAAIC,GAAE;AAC1B;AAEO,SAASM,MAAKC,QAAOL,SAAQ,MAAM,KAAK,KAAK,GAAG;AACrD,QAAM,IAAI,iBAAiB,MAAMK,QAAOL,SAAQ,GAAG;AACnD,QAAM,SAAS,EAAE,QAAQ,IAAI;AAC7B,QAAM,SAAS,EAAE,SAAS,IAAI;AAE9B,OAAK,SAAS,EAAE,QAAQ,CAAC,UAAU;AACjC,UAAM,IAAI,IAAI,MAAM,GAAG,EAAE,UAAU,IAAI,OAAO,KAAK,EAAE,QAAQ,CAAC;AAC9D,UAAM,MAAM,QAAQ,QAAQ,EAAE,GAAG,EAAE,CAAC;AAAA,EACtC,CAAC;AAED,SAAO;AACT;AAEO,SAASK,OAAMA,QAAO,MAAM,KAAK,KAAK,GAAG;AAC9C,MAAIA,UAAS,KAAM,QAAO,IAAI;AAC9B,SAAO,KAAK,KAAKA,QAAO,IAAI,QAAQ,GAAG;AACzC;AAEO,SAASH,GAAEA,IAAG,MAAM,KAAK,KAAK,GAAG;AACtC,MAAIA,MAAK,KAAM,QAAO,IAAI;AAC1B,SAAO,KAAK,KAAKA,IAAG,IAAI,GAAG,GAAG;AAChC;AAEO,SAASC,GAAEA,IAAG,MAAM,KAAK,KAAK,GAAG;AACtC,MAAIA,MAAK,KAAM,QAAO,IAAI;AAC1B,SAAO,KAAK,KAAK,IAAI,GAAGA,IAAG,GAAG;AAChC;;;AC7EA,IAAqB,IAArB,cAA+B,UAAU;AAAA,EACvC,YAAY,MAAMG,SAAQ,MAAM;AAC9B,UAAM,UAAU,KAAK,IAAI,GAAGA,MAAK;AAAA,EACnC;AACF;AAEA,OAAO,GAAG,yBAAiB;AAE3B,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,OAAO,kBAAkB,WAAY;AACnC,aAAO,KAAK,IAAI,IAAI,EAAE,CAAC;AAAA,IACzB,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,GAAG,GAAG;;;AChBf,IAAqB,IAArB,cAA+B,UAAU;AAAA,EACvC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,KAAK,IAAI,GAAGA,MAAK;AAAA,EACnC;AAAA;AAAA,EAGA,OAAO,QAAQ;AACb,WAAO,KAAK,KAAK,UAAU,MAAM;AAAA,EACnC;AAAA;AAAA,EAGA,GAAG,KAAK;AACN,WAAO,KAAK,KAAK,QAAQ,KAAK,KAAK;AAAA,EACrC;AACF;AAEA,OAAO,GAAG,yBAAiB;AAE3B,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,MAAM,kBAAkB,SAAU,KAAK;AACrC,aAAO,KAAK,IAAI,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AACP,YAAM,OAAO,KAAK,OAAO;AAEzB,UAAI,CAAC,KAAM,QAAO;AAElB,YAAM,SAAS,KAAK,OAAO;AAE3B,UAAI,CAAC,QAAQ;AACX,eAAO,KAAK,OAAO;AAAA,MACrB;AAEA,YAAM,QAAQ,OAAO,MAAM,IAAI;AAC/B,aAAO,IAAI,MAAM,KAAK;AAEtB,WAAK,OAAO;AACZ,aAAO;AAAA,IACT;AAAA,IACA,OAAO,KAAK;AAEV,UAAI,OAAO,KAAK,OAAO;AAEvB,UAAI,CAAC,MAAM;AACT,eAAO,IAAI,EAAE;AACb,aAAK,KAAK,IAAI;AAAA,MAChB;AAEA,UAAI,OAAO,QAAQ,YAAY;AAC7B,YAAI,KAAK,MAAM,IAAI;AAAA,MACrB,OAAO;AACL,aAAK,GAAG,GAAG;AAAA,MACb;AAEA,aAAO;AAAA,IACT;AAAA,IACA,SAAS;AACP,YAAM,OAAO,KAAK,OAAO;AACzB,UAAI,QAAQ,KAAK,KAAK,SAAS,YAAY,MAAM,KAAK;AACpD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACF,CAAC;AAED,SAAS,GAAG,GAAG;;;AC7Ef,IAAqB,OAArB,cAAkC,UAAU;AAAA;AAAA,EAE1C,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,QAAQ,IAAI,GAAGA,MAAK;AAAA,EACtC;AAAA;AAAA,EAGA,SAAS;AAEP,SAAK,QAAQ,EAAE,QAAQ,SAAU,IAAI;AACnC,SAAG,OAAO;AAAA,IACZ,CAAC;AAGD,WAAO,MAAM,OAAO;AAAA,EACtB;AAAA,EAEA,UAAU;AACR,WAAO,SAAS,gBAAgB,KAAK,GAAG,IAAI,GAAG;AAAA,EACjD;AACF;AAEA,gBAAgB;AAAA,EACd,WAAW;AAAA,IACT,MAAM,kBAAkB,WAAY;AAClC,aAAO,KAAK,KAAK,EAAE,IAAI,IAAI,KAAK,CAAC;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AAAA;AAAA,IAEP,SAAS;AACP,aAAO,KAAK,UAAU,MAAM;AAAA,IAC9B;AAAA,IAEA,SAAS,SAAS;AAEhB,YAAM,SACJ,mBAAmB,OAAO,UAAU,KAAK,OAAO,EAAE,KAAK,EAAE,IAAI,OAAO;AAGtE,aAAO,KAAK,KAAK,QAAQ,UAAU,OAAO,GAAG,IAAI,GAAG;AAAA,IACtD;AAAA;AAAA,IAGA,SAAS;AACP,aAAO,KAAK,KAAK,QAAQ,IAAI;AAAA,IAC/B;AAAA,EACF;AACF,CAAC;AAED,SAAS,MAAM,MAAM;;;AClDrB,IAAqB,OAArB,cAAkC,QAAQ;AAAA,EACxC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,QAAQ,IAAI,GAAGA,MAAK;AAAA,EACtC;AAAA;AAAA,EAGA,OAAO,GAAG;AACR,QAAI,OAAO,MAAM,YAAY,aAAa,WAAW;AACnD,UAAI;AAAA,QACF,QAAQ,UAAU,CAAC;AAAA,QACnB,OAAO,UAAU,CAAC;AAAA,QAClB,SAAS,UAAU,CAAC;AAAA,MACtB;AAAA,IACF;AAGA,QAAI,EAAE,WAAW,KAAM,MAAK,KAAK,gBAAgB,EAAE,OAAO;AAC1D,QAAI,EAAE,SAAS,KAAM,MAAK,KAAK,cAAc,EAAE,KAAK;AACpD,QAAI,EAAE,UAAU,KAAM,MAAK,KAAK,UAAU,IAAI,UAAU,EAAE,MAAM,CAAC;AAEjE,WAAO;AAAA,EACT;AACF;AAEA,gBAAgB;AAAA,EACd,UAAU;AAAA;AAAA,IAER,MAAM,SAAU,QAAQ,OAAO,SAAS;AACtC,aAAO,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,OAAO,QAAQ,OAAO,OAAO;AAAA,IAC3D;AAAA,EACF;AACF,CAAC;AAED,SAAS,MAAM,MAAM;;;ACjCrB,SAAS,QAAQ,UAAU,MAAM;AAC/B,MAAI,CAAC,SAAU,QAAO;AACtB,MAAI,CAAC,KAAM,QAAO;AAElB,MAAI,MAAM,WAAW;AAErB,aAAW,KAAK,MAAM;AACpB,WAAO,YAAY,CAAC,IAAI,MAAM,KAAK,CAAC,IAAI;AAAA,EAC1C;AAEA,SAAO;AAEP,SAAO;AACT;AAEA,IAAqB,QAArB,cAAmC,QAAQ;AAAA,EACzC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,SAAS,IAAI,GAAGA,MAAK;AAAA,EACvC;AAAA,EAEA,QAAQ,IAAI,IAAI;AACd,SAAK,KAAK,eAAe;AACzB,WAAO;AAAA,EACT;AAAA,EAEA,KAAK,MAAM,KAAK,SAAS,CAAC,GAAG;AAC3B,WAAO,KAAK,KAAK,cAAc;AAAA,MAC7B,YAAY;AAAA,MACZ;AAAA,MACA,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA,EAEA,KAAK,UAAU,KAAK;AAClB,WAAO,KAAK,QAAQ,QAAQ,UAAU,GAAG,CAAC;AAAA,EAC5C;AACF;AAEA,gBAAgB,OAAO;AAAA,EACrB,MAAM,UAAU,KAAK;AACnB,WAAO,KAAK,IAAI,IAAI,MAAM,CAAC,EAAE,KAAK,UAAU,GAAG;AAAA,EACjD;AAAA,EACA,SAAS,MAAM,KAAK,QAAQ;AAC1B,WAAO,KAAK,IAAI,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM,KAAK,MAAM;AAAA,EACrD;AACF,CAAC;AAED,SAAS,OAAO,OAAO;;;AC5CvB,IAAqB,WAArB,cAAsC,KAAK;AAAA;AAAA,EAEzC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,YAAY,IAAI,GAAGA,MAAK;AAAA,EAC1C;AAAA;AAAA,EAGA,QAAQ;AACN,UAAM,QAAQ,KAAK,MAAM;AAEzB,WAAO,QAAQ,MAAM,MAAM,IAAI;AAAA,EACjC;AAAA;AAAA,EAGA,KAAK,GAAG;AACN,UAAM,QAAQ,KAAK,MAAM;AACzB,QAAI,YAAY;AAEhB,QAAI,OAAO;AACT,kBAAY,MAAM,KAAK,CAAC;AAAA,IAC1B;AAEA,WAAO,KAAK,OAAO,YAAY;AAAA,EACjC;AAAA;AAAA,EAGA,QAAQ;AACN,WAAO,KAAK,UAAU,MAAM;AAAA,EAC9B;AACF;AAEA,gBAAgB;AAAA,EACd,WAAW;AAAA,IACT,UAAU,kBAAkB,SAAU,MAAM,MAAM;AAEhD,UAAI,EAAE,gBAAgB,OAAO;AAC3B,eAAO,KAAK,KAAK,IAAI;AAAA,MACvB;AAEA,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,MAAM;AAAA;AAAA,IAEJ,MAAM,kBAAkB,SAAU,OAAO,cAAc,MAAM;AAC3D,YAAM,WAAW,IAAI,SAAS;AAG9B,UAAI,EAAE,iBAAiB,OAAO;AAE5B,gBAAQ,KAAK,KAAK,EAAE,KAAK,KAAK;AAAA,MAChC;AAGA,eAAS,KAAK,QAAQ,MAAM,OAAO,KAAK;AAGxC,UAAI;AACJ,UAAI,aAAa;AACf,eAAQ,OAAO,KAAK,KAAK,YAAa;AACpC,mBAAS,KAAK,YAAY,IAAI;AAAA,QAChC;AAAA,MACF;AAGA,aAAO,KAAK,IAAI,QAAQ;AAAA,IAC1B,CAAC;AAAA;AAAA,IAGD,WAAW;AACT,aAAO,KAAK,QAAQ,UAAU;AAAA,IAChC;AAAA,EACF;AAAA,EACA,MAAM;AAAA;AAAA,IAEJ,MAAM,kBAAkB,SAAU,MAAM;AAEtC,UAAI,EAAE,gBAAgB,OAAO;AAC3B,eAAO,IAAI,KAAK,EAAE,MAAM,KAAK,OAAO,CAAC,EAAE,KAAK,IAAI;AAAA,MAClD;AAGA,aAAO,KAAK,KAAK,IAAI;AAAA,IACvB,CAAC;AAAA,IAED,UAAU;AACR,aAAO,SAAS,cAAc,EAAE,OAAO,CAAC,SAAS;AAC/C,gBAAQ,KAAK,KAAK,MAAM,KAAK,IAAI,SAAS,KAAK,GAAG,CAAC;AAAA,MACrD,CAAC;AAAA,IAIH;AAAA,EACF;AACF,CAAC;AAED,SAAS,UAAU,aAAa;AAChC,SAAS,UAAU,UAAU;;;ACpG7B,IAAqB,MAArB,cAAiC,MAAM;AAAA,EACrC,YAAY,MAAMC,SAAQ,MAAM;AAC9B,UAAM,UAAU,OAAO,IAAI,GAAGA,MAAK;AAAA,EACrC;AAAA;AAAA,EAGA,IAAI,SAAS,MAAM;AAEjB,WAAO,KAAK,KAAK,SAAS,QAAQ,MAAM,MAAM,SAAS,KAAK;AAAA,EAC9D;AACF;AAEA,gBAAgB;AAAA,EACd,WAAW;AAAA;AAAA,IAET,KAAK,kBAAkB,SAAU,SAAS,MAAM;AAC9C,aAAO,KAAK,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,SAAS,IAAI;AAAA,IAC9C,CAAC;AAAA,EACH;AACF,CAAC;AAED,SAAS,KAAK,KAAK;;;ACsCZ,IAAM,MAAM;AAsEnB,OAAO,CAAC,KAAK,QAAQ,OAAO,SAAS,MAAM,GAAG,cAAc,SAAS,CAAC;AAEtE,OAAO,CAAC,MAAM,UAAU,SAAS,IAAI,GAAG,cAAc,QAAQ,CAAC;AAE/D,OAAO,MAAM,cAAc,MAAM,CAAC;AAClC,OAAO,MAAM,cAAc,MAAM,CAAC;AAElC,OAAO,MAAM,cAAc,MAAM,CAAC;AAElC,OAAO,CAAC,MAAM,KAAK,GAAG,cAAc,OAAO,CAAC;AAE5C,OAAO,CAAC,MAAM,SAAS,UAAU,MAAM,GAAG,cAAc,QAAQ,CAAC;AAEjE,OAAO,aAAa,cAAc,aAAa,CAAC;AAChD,OAAO,KAAK,cAAc,KAAK,CAAC;AAChC,OAAO,SAAS,cAAc,SAAS,CAAC;AACxC,OAAO,OAAO,cAAc,OAAO,CAAC;AACpC,OAAO,CAAC,WAAW,gBAAQ,GAAG,cAAc,WAAW,CAAC;AACxD,OAAO,UAAU,cAAc,UAAU,CAAC;AAE1C,OAAO,QAAQ,cAAc,QAAQ,CAAC;AAEtC,aAAK,OAAO,eAAe,CAAC;AAE5B,sBAAsB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAED,cAAc;", "names": ["array", "width", "height", "x", "y", "data", "methods", "attr", "array", "data", "hex", "y", "_a", "_b", "_c", "x", "b", "color", "x", "y", "position", "rx", "ry", "cx", "cy", "dx", "dy", "x", "y", "svg", "node", "x", "y", "width", "height", "box", "bbox", "el", "rbox", "point", "methods", "attrs", "data", "data", "array", "attr", "attrs", "map", "position", "attrs", "x", "y", "root", "height", "attr", "width", "cx", "cy", "x", "y", "length", "methods", "str", "matrix", "transform", "ctm", "attrs", "rx", "ry", "x", "y", "width", "height", "attrs", "width", "height", "x", "y", "attrs", "attrs", "width", "height", "attrs", "attr", "x", "y", "array", "width", "height", "height", "width", "x", "y", "x", "y", "width", "height", "attrs", "x", "y", "x2", "y2", "width", "height", "attrs", "height", "x", "y", "width", "attr", "x2", "y2", "from", "to", "i", "parser", "length", "x", "y", "width", "height", "from", "to", "attrs", "height", "x", "y", "width", "x", "y", "width", "height", "attrs", "attrs", "attrs", "width", "height", "runnerInfo", "timeline", "transform", "position", "x", "timeline", "transforms", "attrs", "point", "y", "to", "from", "width", "height", "attrs", "attrs", "cx", "cy", "move", "x", "y", "x", "y", "move", "cx", "cy", "build", "attrs", "dy", "attrs", "dx", "dy", "attrs", "rx", "ry", "size", "attrs", "attrs", "width", "height", "height", "move", "size", "width", "x", "y", "dx", "dy", "bbox", "height", "move", "x", "y", "size", "width", "attrs", "attrs", "attrs", "attrs", "attrs", "attrs", "attrs"]}