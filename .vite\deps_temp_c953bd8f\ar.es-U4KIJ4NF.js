import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/ar.es.js
var weekDays = [
  "الإثنين",
  "الثلاثاء",
  "الأربعاء",
  "الخميس",
  "الجمعة",
  "السبت",
  "الأحد"
];
var months = [
  "يناير",
  "فبراير",
  "مارس",
  "أبريل",
  "مايو",
  "يونيو",
  "يوليو",
  "أغسطس",
  "سبتمبر",
  "أكتوبر",
  "نوفمبر",
  " ديسمبر"
];
var years = "سنوات";
var year = "سنة";
var month = "شهر";
var week = "أسبوع";
var day = "يوم";
var today = "اليوم";
var noEvent = "لا حدث";
var allDay = "طوال اليوم";
var deleteEvent = "حذف";
var createEvent = "إنشاء حدث";
var dateFormat = "dddd D MMMM YYYY";
var ar = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  ar as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/ar.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=ar.es-U4KIJ4NF.js.map
