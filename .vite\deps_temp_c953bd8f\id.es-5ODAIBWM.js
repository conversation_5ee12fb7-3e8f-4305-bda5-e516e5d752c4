import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/id.es.js
var weekDays = [
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>"
];
var months = [
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "April",
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "Agustus",
  "September",
  "Oktober",
  "November",
  "Des<PERSON>ber"
];
var years = "Tahunan";
var year = "Tahun";
var month = "Bulan";
var week = "Minggu";
var day = "Hari";
var today = "Hari Ini";
var noEvent = "Tidak Ada Kegiatan";
var allDay = "Sepanjang Hari";
var deleteEvent = "Hapus";
var createEvent = "Tambah Kegiatan";
var dateFormat = "dddd, D MMMM YYYY";
var id = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  id as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/id.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=id.es-5ODAIBWM.js.map
