{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/de.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"<PERSON>ag\",\n  \"Dienstag\",\n  \"<PERSON>tt<PERSON><PERSON>\",\n  \"Donnerstag\",\n  \"Freitag\",\n  \"Samstag\",\n  \"Sonntag\"\n];\nconst months = [\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"April\",\n  \"<PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"August\",\n  \"September\",\n  \"Oktober\",\n  \"November\",\n  \"Dezember\"\n];\nconst years = \"Jahre\";\nconst year = \"Jahr\";\nconst month = \"Monat\";\nconst week = \"Woche\";\nconst day = \"Tag\";\nconst today = \"Heute\";\nconst noEvent = \"Keine Events\";\nconst allDay = \"Ganztägig\";\nconst deleteEvent = \"Löschen\";\nconst createEvent = \"Event erstellen\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst de = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  de as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}