{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/cs.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"Pondělí\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"Čtvrtek\",\n  \"Pátek\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\"\n];\nconst months = [\n  \"<PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"B<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"Červen\",\n  \"Červenec\",\n  \"Srpen\",\n  \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"Říjen\",\n  \"Listopad\",\n  \"Prosinec\"\n];\nconst years = \"Roky\";\nconst year = \"Rok\";\nconst month = \"Měsíc\";\nconst week = \"Týden\";\nconst day = \"Den\";\nconst today = \"Dnes\";\nconst noEvent = \"Bez událostí\";\nconst allDay = \"Celý den\";\nconst deleteEvent = \"Odstranit\";\nconst createEvent = \"Vytvořit událost\";\nconst dateFormat = \"dddd D. MMMM YYYY\";\nconst cs = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  cs as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}