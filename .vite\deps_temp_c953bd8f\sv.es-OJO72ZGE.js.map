{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/sv.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"Måndag\",\n  \"Tisdag\",\n  \"Onsdag\",\n  \"Torsdag\",\n  \"Fredag\",\n  \"Lördag\",\n  \"Söndag\"\n];\nconst months = [\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON><PERSON><PERSON>\",\n  \"<PERSON>\",\n  \"April\",\n  \"<PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"<PERSON><PERSON>\",\n  \"September\",\n  \"Oktober\",\n  \"November\",\n  \"December\"\n];\nconst years = \"År\";\nconst year = \"År\";\nconst month = \"Månad\";\nconst week = \"Vecka\";\nconst day = \"Dag\";\nconst today = \"Idag\";\nconst noEvent = \"Ingen händelse\";\nconst allDay = \"Heldag\";\nconst deleteEvent = \"Ta bort\";\nconst createEvent = \"Skapa händelse\";\nconst dateFormat = \"dddd den D MMMM YYYY\";\nconst sv = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  sv as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}