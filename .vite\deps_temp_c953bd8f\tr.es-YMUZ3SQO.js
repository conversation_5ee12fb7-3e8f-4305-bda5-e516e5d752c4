import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/tr.es.js
var weekDays = [
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>"
];
var months = [
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "Ha<PERSON><PERSON>",
  "Temmuz",
  "A<PERSON>ustos",
  "E<PERSON><PERSON><PERSON>",
  "Ekim",
  "Kasım",
  "Aralık"
];
var years = "Yıllar";
var year = "Yıl";
var month = "Ay";
var week = "Hafta";
var day = "Gün";
var today = "Bugün";
var noEvent = "Etkinlik Yok";
var allDay = "Tüm gün";
var deleteEvent = "Sil";
var createEvent = "Etkinlik ekle";
var dateFormat = "dddd D MMMM YYYY";
var tr = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  tr as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/tr.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=tr.es-YMUZ3SQO.js.map
