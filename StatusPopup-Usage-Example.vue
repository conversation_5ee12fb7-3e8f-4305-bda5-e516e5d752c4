<template>
  <div class="p-8 space-y-4">
    <h1 class="text-2xl font-bold mb-6">StatusPopup Component Usage Examples</h1>
    
    <!-- Trigger Buttons -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
      <button
        @click="showSuccessPopup"
        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
      >
        Show Success
      </button>
      
      <button
        @click="showErrorPopup"
        class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
      >
        Show Error
      </button>
      
      <button
        @click="showWarningPopup"
        class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
      >
        Show Warning
      </button>
      
      <button
        @click="showInfoPopup"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Show Info
      </button>
    </div>

    <!-- Advanced Examples -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <button
        @click="showCustomPopup"
        class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
      >
        Custom Button Action
      </button>
      
      <button
        @click="showNoClosePopup"
        class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
      >
        No Close Button
      </button>
    </div>

    <!-- Status Popups -->
    <StatusPopup
      :show="popups.success.show"
      type="success"
      :title="popups.success.title"
      :message="popups.success.message"
      :button-text="popups.success.buttonText"
      @close="popups.success.show = false"
    />

    <StatusPopup
      :show="popups.error.show"
      type="error"
      :title="popups.error.title"
      :message="popups.error.message"
      :button-text="popups.error.buttonText"
      @close="popups.error.show = false"
    />

    <StatusPopup
      :show="popups.warning.show"
      type="warning"
      :title="popups.warning.title"
      :message="popups.warning.message"
      :button-text="popups.warning.buttonText"
      @close="popups.warning.show = false"
    />

    <StatusPopup
      :show="popups.info.show"
      type="info"
      :title="popups.info.title"
      :message="popups.info.message"
      :button-text="popups.info.buttonText"
      @close="popups.info.show = false"
    />

    <StatusPopup
      :show="popups.custom.show"
      type="success"
      :title="popups.custom.title"
      :message="popups.custom.message"
      :button-text="popups.custom.buttonText"
      @close="popups.custom.show = false"
      @button-click="handleCustomAction"
    />

    <StatusPopup
      :show="popups.noClose.show"
      type="info"
      :title="popups.noClose.title"
      :message="popups.noClose.message"
      :button-text="popups.noClose.buttonText"
      :show-close-button="false"
      :close-on-backdrop="false"
      @close="popups.noClose.show = false"
    />
  </div>
</template>

<script setup>
import { reactive } from 'vue';
import StatusPopup from './src/components/common/StatusPopup.vue';

const popups = reactive({
  success: {
    show: false,
    title: 'Success!',
    message: 'Your action has been completed successfully.',
    buttonText: 'Continue',
  },
  error: {
    show: false,
    title: 'Error Occurred',
    message: 'Something went wrong. Please try again later.',
    buttonText: 'Try Again',
  },
  warning: {
    show: false,
    title: 'Warning',
    message: 'Please review your input before proceeding.',
    buttonText: 'Review',
  },
  info: {
    show: false,
    title: 'Information',
    message: 'Here is some important information you should know.',
    buttonText: 'Got It',
  },
  custom: {
    show: false,
    title: 'Custom Action',
    message: 'This popup has a custom button action.',
    buttonText: 'Custom Action',
  },
  noClose: {
    show: false,
    title: 'Important Notice',
    message: 'This popup cannot be closed by clicking outside or the close button.',
    buttonText: 'Acknowledge',
  },
});

const showSuccessPopup = () => {
  popups.success.show = true;
};

const showErrorPopup = () => {
  popups.error.show = true;
};

const showWarningPopup = () => {
  popups.warning.show = true;
};

const showInfoPopup = () => {
  popups.info.show = true;
};

const showCustomPopup = () => {
  popups.custom.show = true;
};

const showNoClosePopup = () => {
  popups.noClose.show = true;
};

const handleCustomAction = () => {
  alert('Custom action executed!');
  // Note: The popup will still close due to the default behavior
  // If you want to prevent closing, handle it in the parent component
};
</script>

<style scoped>
/* Add any additional styles here */
</style>
