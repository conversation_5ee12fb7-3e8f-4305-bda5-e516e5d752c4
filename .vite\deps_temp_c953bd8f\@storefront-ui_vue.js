import {
  Fragment,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createTextVNode,
  createVNode,
  defineComponent,
  getCurrentScope,
  h,
  isRef,
  mergeProps,
  normalizeClass,
  normalizeProps,
  normalizeStyle,
  onBeforeUpdate,
  onMounted,
  onScopeDispose,
  onUnmounted,
  openBlock,
  reactive,
  ref,
  renderList,
  renderSlot,
  resolveDynamicComponent,
  shallowReadonly,
  shallowRef,
  toDisplayString,
  toRefs,
  unref,
  useAttrs,
  useSlots,
  vModelCheckbox,
  vModelDynamic,
  vModelRadio,
  vModelSelect,
  vModelText,
  watch,
  withCtx,
  withDirectives,
  withKeys,
  withModifiers
} from "./chunk-IJV5NOMV.js";
import {
  __commonJS,
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/jw-paginate/lib/jw-paginate.js
var require_jw_paginate = __commonJS({
  "node_modules/jw-paginate/lib/jw-paginate.js"(exports, module) {
    "use strict";
    function paginate(totalItems, currentPage, pageSize, maxPages) {
      if (currentPage === void 0) {
        currentPage = 1;
      }
      if (pageSize === void 0) {
        pageSize = 10;
      }
      if (maxPages === void 0) {
        maxPages = 10;
      }
      var totalPages = Math.ceil(totalItems / pageSize);
      if (currentPage < 1) {
        currentPage = 1;
      } else if (currentPage > totalPages) {
        currentPage = totalPages;
      }
      var startPage, endPage;
      if (totalPages <= maxPages) {
        startPage = 1;
        endPage = totalPages;
      } else {
        var maxPagesBeforeCurrentPage = Math.floor(maxPages / 2);
        var maxPagesAfterCurrentPage = Math.ceil(maxPages / 2) - 1;
        if (currentPage <= maxPagesBeforeCurrentPage) {
          startPage = 1;
          endPage = maxPages;
        } else if (currentPage + maxPagesAfterCurrentPage >= totalPages) {
          startPage = totalPages - maxPages + 1;
          endPage = totalPages;
        } else {
          startPage = currentPage - maxPagesBeforeCurrentPage;
          endPage = currentPage + maxPagesAfterCurrentPage;
        }
      }
      var startIndex = (currentPage - 1) * pageSize;
      var endIndex = Math.min(startIndex + pageSize - 1, totalItems - 1);
      var pages = Array.from(Array(endPage + 1 - startPage).keys()).map(function(i2) {
        return startPage + i2;
      });
      return {
        totalItems,
        currentPage,
        pageSize,
        totalPages,
        startPage,
        endPage,
        startIndex,
        endIndex,
        pages
      };
    }
    module.exports = paginate;
  }
});

// node_modules/@storefront-ui/vue/dist/shared/props.mjs
var t = {
  type: [Array, String, Object],
  default: ""
};

// node_modules/@storefront-ui/vue/dist/components/SfAccordionItem/SfAccordionItem.vue.mjs
var u = ["open"];
var C = defineComponent({
  __name: "SfAccordionItem",
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    summaryClass: t
  },
  emits: ["update:modelValue"],
  setup(e5) {
    return (o4, s12) => (openBlock(), createElementBlock("details", {
      open: e5.modelValue,
      "data-testid": "accordion-item"
    }, [
      createBaseVNode("summary", {
        class: normalizeClass([
          e5.summaryClass,
          "list-none [&::-webkit-details-marker]:hidden cursor-pointer focus-visible:outline focus-visible:outline-offset focus-visible:rounded-sm"
        ]),
        onClick: s12[0] || (s12[0] = withModifiers((f29) => o4.$emit("update:modelValue", !e5.modelValue), ["prevent"]))
      }, [
        renderSlot(o4.$slots, "summary")
      ], 2),
      renderSlot(o4.$slots, "default")
    ], 8, u));
  }
});

// node_modules/@storefront-ui/shared/dist/index.mjs
var D = Object.defineProperty;
var E = (t4, e5, r4) => e5 in t4 ? D(t4, e5, { enumerable: true, configurable: true, writable: true, value: r4 }) : t4[e5] = r4;
var a = (t4, e5, r4) => (E(t4, typeof e5 != "symbol" ? e5 + "" : e5, r4), r4);
var u2 = ((t4) => (t4.vertical = "vertical", t4.horizontal = "horizontal", t4))(u2 || {});
var T = ((t4) => (t4.none = "none", t4.floating = "floating", t4.block = "block", t4))(T || {});
var M = typeof window < "u";
var H = M && (window == null ? void 0 : window.matchMedia("(prefers-reduced-motion: reduce)").matches);
function F(t4) {
  var e5;
  return typeof window < "u" && window.navigator != null ? t4.test(((e5 = window.navigator.userAgentData) == null ? void 0 : e5.platform) || window.navigator.platform) : false;
}
function L(t4) {
  var e5;
  return typeof window > "u" || window.navigator == null ? false : ((e5 = window.navigator.userAgentData) == null ? void 0 : e5.brands.some(
    (r4) => t4.test(r4.brand)
  )) || t4.test(window.navigator.userAgent);
}
var k = F(/^Mac/i);
var W = L(/Android/i);
var m = L(/^((?!chrome|android).)*safari/i);
var yt = (t4) => t4.key === "Tab" && t4.shiftKey;
var bt = (t4) => t4.key === "Tab" && !t4.shiftKey;
function Lt(t4, e5, r4) {
  return Math.min(Math.max(t4, e5), r4);
}
function Dt(t4, e5) {
  return Math.round(t4 / e5) * e5;
}
var w = {
  reduceMotion: H,
  direction: u2.horizontal
};
var Et = class {
  constructor(e5, r4) {
    a(this, "container");
    a(this, "options");
    a(this, "debounceId");
    a(this, "dragScrollX");
    a(this, "dragScrollLeft");
    a(this, "dragScrollY");
    a(this, "dragScrollTop");
    a(this, "pointerDownOffsetLeft");
    a(this, "pointerDownOffsetTop");
    a(this, "dragDistance");
    a(this, "isDraggedPreviously", false);
    a(this, "resizeObserver", new ResizeObserver(() => {
      this.container && this.refresh(this.options.onScroll);
    }));
    if (!(e5 instanceof HTMLElement))
      throw new Error(`SfScrollable: Container is not a HTMLElement! Received: ${e5}`);
    this.container = e5, this.options = {
      ...r4,
      reduceMotion: (r4 == null ? void 0 : r4.reduceMotion) ?? w.reduceMotion,
      direction: (r4 == null ? void 0 : r4.direction) ?? w.direction
    }, this.dragScrollX = 0, this.dragScrollLeft = 0, this.dragScrollY = 0, this.dragScrollTop = 0, this.pointerDownOffsetLeft = 0, this.pointerDownOffsetTop = 0, this.dragDistance = 0;
  }
  get isDragged() {
    return this.isDraggedPreviously;
  }
  set isDragged(e5) {
    var r4, s12;
    e5 !== this.isDraggedPreviously && (this.isDraggedPreviously = e5, (s12 = (r4 = this.options).onDragStart) == null || s12.call(r4, {
      isDragged: this.isDraggedPreviously
    }));
  }
  register() {
    const e5 = this.addListeners();
    return this.onScrollHandler(), e5;
  }
  addListeners() {
    const e5 = this.onScroll.bind(this);
    if (this.container.addEventListener("scroll", e5, { passive: !this.options.drag }), this.resizeObserver.observe(this.container), this.options.drag) {
      const r4 = this.onMouseDown.bind(this), s12 = this.onMouseUp.bind(this), n6 = this.onMouseMove.bind(this), o4 = this.onMouseLeave.bind(this);
      return this.container.addEventListener("mousedown", r4, { passive: false }), this.container.addEventListener("mouseup", s12, { passive: true }), this.container.addEventListener("mousemove", n6, { passive: false }), this.container.addEventListener("mouseleave", o4, { passive: true }), () => {
        this.container.removeEventListener("scroll", e5), this.container.removeEventListener("mousedown", r4), this.container.removeEventListener("mouseup", s12), this.container.removeEventListener("mousemove", n6), this.container.removeEventListener("mouseleave", o4), this.resizeObserver.unobserve(this.container);
      };
    }
    return () => {
      this.container.removeEventListener("scroll", e5), this.resizeObserver.unobserve(this.container);
    };
  }
  prev() {
    var n6, o4;
    let e5 = false;
    if ((o4 = (n6 = this.options) == null ? void 0 : n6.onPrev) == null || o4.call(n6, { preventDefault: () => e5 = true }), e5)
      return;
    const { container: r4, options: s12 } = this;
    s12.direction === u2.vertical ? this.scrollTo({ top: r4.scrollTop - r4.clientHeight }) : this.scrollTo({ left: r4.scrollLeft - r4.clientWidth });
  }
  next() {
    var n6, o4;
    let e5 = false;
    if ((o4 = (n6 = this.options) == null ? void 0 : n6.onNext) == null || o4.call(n6, { preventDefault: () => e5 = true }), e5)
      return;
    const { container: r4, options: s12 } = this;
    s12.direction === u2.vertical ? this.scrollTo({ top: r4.scrollTop + r4.clientHeight }) : this.scrollTo({ left: r4.scrollLeft + r4.clientWidth });
  }
  scrollToIndex(e5) {
    const r4 = this.container.children;
    if (r4[e5]) {
      const { container: s12 } = this, { top: n6, left: o4 } = s12.getBoundingClientRect(), {
        top: l18,
        left: d26,
        width: c20,
        height: f29
      } = r4[e5].getBoundingClientRect();
      if (this.options.direction === u2.vertical) {
        const h12 = l18 - n6, g4 = (s12.clientHeight - f29) / 2;
        this.scrollTo({ top: s12.scrollTop + h12 - g4 });
      } else {
        const h12 = d26 - o4, g4 = (s12.clientWidth - c20) / 2;
        this.scrollTo({ left: s12.scrollLeft + h12 - g4 });
      }
    }
  }
  refresh(e5) {
    e5 && requestAnimationFrame(() => {
      e5(this.calculate());
    });
  }
  onMouseUp(e5) {
    var d26, c20;
    const { container: r4, options: s12 } = this;
    this.isDragged = false;
    const n6 = typeof this.options.drag == "object" && this.options.drag.sensitivity ? this.options.drag.sensitivity : 4, o4 = typeof this.options.drag == "object" ? this.options.drag.containerWidth : false, l18 = 10;
    if (s12.direction === u2.vertical) {
      const h12 = (e5.pageY - r4.offsetTop - this.dragScrollY) * n6;
      if (o4) {
        if (Math.abs(this.dragDistance) < 10)
          return;
        r4.scrollLeft = this.dragScrollLeft - (this.dragDistance < 0 ? r4.clientHeight - l18 : -r4.clientHeight + l18);
      } else
        r4.scrollTop = this.dragScrollTop - h12;
    } else {
      const h12 = (e5.pageX - r4.offsetLeft - this.dragScrollX) * n6;
      if (o4) {
        if (Math.abs(this.dragDistance) < 10)
          return;
        r4.scrollLeft = this.dragScrollLeft - (this.dragDistance < 0 ? r4.clientWidth - l18 : -r4.clientWidth + l18);
      } else
        r4.scrollLeft = this.dragScrollLeft - h12;
    }
    (c20 = (d26 = this.options).onDragEnd) == null || c20.call(d26, {
      isDragged: false,
      swipeLeft: this.dragDistance > -10,
      swipeRight: this.dragDistance < 10
    });
  }
  onMouseLeave() {
    this.isDragged = false;
  }
  onMouseDown(e5) {
    e5.preventDefault();
    const { container: r4, options: s12 } = this;
    this.isDragged = true, this.pointerDownOffsetLeft = e5.offsetX, this.pointerDownOffsetTop = e5.offsetY, s12.direction === u2.vertical ? (this.dragScrollY = e5.pageY - r4.offsetTop, this.dragScrollTop = r4.scrollTop) : (this.dragScrollX = e5.pageX - r4.offsetLeft, this.dragScrollLeft = r4.scrollLeft);
  }
  onMouseMove(e5) {
    if (!this.isDragged)
      return;
    e5.preventDefault();
    const { options: r4 } = this;
    r4.direction === u2.vertical ? this.dragDistance = this.pointerDownOffsetTop - e5.offsetY : this.dragDistance = this.pointerDownOffsetLeft - e5.offsetX;
  }
  scrollTo({ left: e5, top: r4 }) {
    const s12 = this.options.reduceMotion ? "auto" : "smooth";
    this.container.scrollTo({ left: e5, top: r4, behavior: s12 });
  }
  onScroll(e5) {
    this.container && (this.options.drag && e5.preventDefault(), clearTimeout(this.debounceId), this.debounceId = setTimeout(this.onScrollHandler.bind(this), 50));
  }
  onScrollHandler() {
    this.refresh(this.options.onScroll);
  }
  get hasNext() {
    return this.options.direction === u2.vertical ? this.container.scrollHeight > this.container.scrollTop + this.container.clientHeight : this.container.scrollWidth > this.container.scrollLeft + this.container.clientWidth;
  }
  get hasPrev() {
    return this.options.direction === u2.vertical ? !!this.container.scrollTop : !!this.container.scrollLeft;
  }
  calculate() {
    return {
      left: this.container.scrollLeft,
      width: this.container.clientWidth,
      scrollWidth: this.container.scrollWidth,
      hasPrev: this.hasPrev,
      hasNext: this.hasNext
    };
  }
};
var K = ((t4) => (t4.neutral = "neutral", t4.secondary = "secondary", t4.positive = "positive", t4.warning = "warning", t4.error = "error", t4))(K || {});
var I = ((t4) => (t4.temporary = "temporary", t4.persistent = "persistent", t4))(I || {});
var X = ((t4) => (t4.standard = "standard", t4.dot = "dot", t4))(X || {});
var Y = ((t4) => (t4["top-right"] = "top-right", t4["top-left"] = "top-left", t4["bottom-right"] = "bottom-right", t4["bottom-left"] = "bottom-left", t4))(Y || {});
var j = ((t4) => (t4.sm = "sm", t4.base = "base", t4.lg = "lg", t4))(j || {});
var G = ((t4) => (t4.primary = "primary", t4.secondary = "secondary", t4.tertiary = "tertiary", t4))(G || {});
var C2 = ((t4) => (t4.sm = "sm", t4.base = "base", t4))(C2 || {});
var U = ((t4) => (t4["3xs"] = "3xs", t4["2xs"] = "2xs", t4.xs = "xs", t4.sm = "sm", t4.base = "base", t4.lg = "lg", t4))(U || {});
var A = ((t4) => (t4.top = "top", t4.bottom = "bottom", t4.left = "left", t4.right = "right", t4))(A || {});
var $ = ((t4) => (t4.sm = "sm", t4.base = "base", t4.lg = "lg", t4))($ || {});
var q = ((t4) => (t4.xs = "xs", t4.sm = "sm", t4.base = "base", t4.lg = "lg", t4.xl = "xl", t4["2xl"] = "2xl", t4["3xl"] = "3xl", t4["4xl"] = "4xl", t4))(q || {});
var R = ((t4) => (t4.primary = "primary", t4.secondary = "secondary", t4))(R || {});
var J = ((t4) => (t4.sm = "sm", t4.base = "base", t4.lg = "lg", t4))(J || {});
var Q = ((t4) => (t4.xs = "xs", t4.sm = "sm", t4.base = "base", t4.lg = "lg", t4.xl = "xl", t4["2xl"] = "2xl", t4["3xl"] = "3xl", t4["4xl"] = "4xl", t4))(Q || {});
var Z = ((t4) => (t4.xs = "xs", t4.sm = "sm", t4.base = "base", t4.lg = "lg", t4.xl = "xl", t4["2xl"] = "2xl", t4["3xl"] = "3xl", t4["4xl"] = "4xl", t4))(Z || {});
var _ = ((t4) => (t4.xs = "xs", t4.sm = "sm", t4.base = "base", t4.lg = "lg", t4.xl = "xl", t4))(_ || {});
var V = ((t4) => (t4.sm = "sm", t4.base = "base", t4.lg = "lg", t4))(V || {});
var B = ((t4) => (t4.sm = "sm", t4.base = "base", t4.lg = "lg", t4))(B || {});
var P = ((t4) => (t4.sm = "sm", t4.base = "base", t4.lg = "lg", t4))(P || {});
var z = ((t4) => (t4.sm = "sm", t4.base = "base", t4.lg = "lg", t4.xl = "xl", t4))(z || {});
var S = ((t4) => (t4["top-start"] = "top-start", t4.top = "top", t4["top-end"] = "top-end", t4["bottom-start"] = "bottom-start", t4.bottom = "bottom", t4["bottom-end"] = "bottom-end", t4["left-start"] = "left-start", t4.left = "left", t4["left-end"] = "left-end", t4["right-start"] = "right-start", t4.right = "right", t4["right-end"] = "right-end", t4))(S || {});
var tt = ((t4) => (t4.fixed = "fixed", t4.absolute = "absolute", t4))(tt || {});
var x = (t4, e5) => e5.findIndex((r4) => r4 === t4);
var et = (t4, e5) => {
  const r4 = x(t4, e5);
  return e5[r4 + 1];
};
var rt = (t4, e5) => {
  const r4 = x(t4, e5);
  return e5[r4 - 1];
};
var p = ({ focusables: t4, event: e5 }) => {
  e5 == null || e5.preventDefault();
  const r4 = t4[0];
  return r4 == null || r4.focus(), r4;
};
var b = ({ focusables: t4, event: e5 }) => {
  e5 == null || e5.preventDefault();
  const r4 = t4[t4.length - 1];
  return r4 == null || r4.focus(), r4;
};
var st = (t4, e5) => {
  const r4 = et(t4, e5);
  return r4 == null || r4.focus(), r4;
};
var nt = (t4, e5) => {
  const r4 = rt(t4, e5);
  return r4 == null || r4.focus(), r4;
};
var ot = (t4, e5) => x(t4, e5) === 0;
var it = (t4, e5) => x(t4, e5) === e5.length - 1;
var lt = (t4, e5, r4) => {
  const s12 = [...r4].reverse(), n6 = s12.findIndex((o4) => o4 === t4);
  return s12.find((o4, l18) => {
    var d26;
    if (l18 > n6 && o4.closest(e5) && t4.closest(e5) !== o4.closest(e5) && ((d26 = s12[l18 + 1]) == null ? void 0 : d26.closest(e5)) !== o4.closest(e5))
      return o4;
  });
};
var at = (t4, e5, r4) => {
  const s12 = r4.findIndex((n6) => n6 === t4);
  return r4.find((n6, o4) => {
    if (o4 > s12 && n6.closest(e5) && t4.closest(e5) !== n6.closest(e5))
      return n6;
  });
};
var ct = ({
  current: t4,
  focusables: e5,
  arrowFocusGroupSelector: r4
}) => {
  if (!t4)
    return p({ focusables: e5 });
  const s12 = at(t4, r4, e5);
  return t4 && s12 ? (s12 == null || s12.focus(), s12) : p({ focusables: e5 });
};
var dt = ({
  current: t4,
  focusables: e5,
  arrowFocusGroupSelector: r4
}) => {
  if (!t4)
    return p({ focusables: e5 });
  const s12 = lt(t4, r4, e5);
  return t4 && s12 ? (s12 == null || s12.focus(), s12) : b({ focusables: e5 });
};
var ut = ({
  current: t4,
  focusables: e5,
  event: r4
}) => {
  if (m && (r4 == null || r4.preventDefault()), t4) {
    if (it(t4, e5))
      return p({ focusables: e5, event: r4 });
    if (m || !r4)
      return st(t4, e5);
  } else
    return p({ focusables: e5, event: r4 });
};
var ht = ({
  current: t4,
  focusables: e5,
  event: r4
}) => {
  if (m && (r4 == null || r4.preventDefault()), t4) {
    if (ot(t4, e5))
      return b({ focusables: e5, event: r4 });
    if (m || !r4)
      return nt(t4, e5);
  } else
    return b({ focusables: e5, event: r4 });
};
var Tt = ({
  current: t4,
  event: e5,
  focusables: r4,
  arrowFocusGroupSelector: s12
}) => s12 ? ct({ current: t4, focusables: r4, arrowFocusGroupSelector: s12 }) : ut({ current: t4, focusables: r4, event: e5 });
var Mt = ({
  current: t4,
  focusables: e5,
  event: r4,
  arrowFocusGroupSelector: s12
}) => s12 ? dt({ current: t4, focusables: e5, arrowFocusGroupSelector: s12 }) : ht({ current: t4, focusables: e5, event: r4 });
function ft(t4) {
  return t4.mozInputSource === 0 && t4.isTrusted ? true : W && t4.pointerType ? t4.type === "click" && t4.buttons === 1 : t4.detail === 0 && !t4.pointerType;
}
function gt(t4) {
  return !(t4.metaKey || !k && t4.altKey || t4.ctrlKey || t4.key === "Control" || t4.key === "Shift" || t4.key === "Meta");
}
var Ht = () => {
  let t4 = null;
  const e5 = /* @__PURE__ */ new Set();
  let r4 = false, s12 = false, n6 = false;
  const o4 = () => t4 !== "pointer", l18 = (i2, y5) => {
    for (const v3 of e5)
      v3(i2, y5);
  }, d26 = (i2) => {
    s12 = true, gt(i2) && (t4 = "keyboard", l18("keyboard", i2));
  }, c20 = (i2) => {
    t4 = "pointer", (i2.type === "mousedown" || i2.type === "pointerdown") && (s12 = true, l18("pointer", i2));
  }, f29 = (i2) => {
    ft(i2) && (s12 = true, t4 = "virtual");
  }, h12 = (i2) => {
    i2.target === window || i2.target === document || (!s12 && !n6 && (t4 = "virtual", l18("virtual", i2)), s12 = false, n6 = false);
  }, g4 = () => {
    s12 = false, n6 = true;
  };
  return {
    isFocusVisible: o4,
    changeHandlers: e5,
    setupGlobalFocusEvents: () => {
      if (typeof window > "u" || r4)
        return;
      const i2 = HTMLElement.prototype.focus;
      HTMLElement.prototype.focus = function() {
        s12 = true, i2.apply(this, arguments);
      }, document.addEventListener("keydown", d26, true), document.addEventListener("keyup", d26, true), document.addEventListener("click", f29, true), window.addEventListener("focus", h12, true), window.addEventListener("blur", g4, false), typeof PointerEvent < "u" ? (document.addEventListener("pointerdown", c20, true), document.addEventListener("pointermove", c20, true), document.addEventListener("pointerup", c20, true)) : (document.addEventListener("mousedown", c20, true), document.addEventListener("mousemove", c20, true), document.addEventListener("mouseup", c20, true)), r4 = true;
    },
    isKeyboardFocusEvent: (i2, y5, v3) => !(i2 && y5 === "keyboard" && v3 instanceof KeyboardEvent && // Only Tab or Esc keys will make focus visible on text input elements
    !["Tab", "Escape"].includes(v3.key))
  };
};

// node_modules/@storefront-ui/vue/dist/components/SfBadge/SfBadge.vue.mjs
var g = defineComponent({
  __name: "SfBadge",
  props: {
    content: {
      type: [String, Number],
      default: ""
    },
    max: {
      type: Number,
      default: 99
    },
    placement: {
      type: String,
      default: Y["top-right"]
    },
    variant: {
      type: String,
      default: X.standard
    }
  },
  setup(e5) {
    const t4 = e5, a2 = computed(() => t4.variant === "dot"), r4 = computed(() => a2.value ? "" : !Number.isNaN(t4.content) && Number(t4.content) > t4.max ? `${t4.max}+` : t4.content);
    return (u45, d26) => (openBlock(), createElementBlock("span", {
      class: normalizeClass([
        "block absolute py-0.5 px-1 bg-secondary-700 font-medium text-white text-[8px] leading-[8px] rounded-xl",
        {
          "min-w-[12px] min-h-[12px]": !a2.value,
          "w-[10px] h-[10px]": a2.value,
          "top-0 right-0 -translate-x-0.5 translate-y-0.5": e5.placement === "top-right",
          "top-0 left-0 translate-x-0.5 translate-y-0.5": e5.placement === "top-left",
          "bottom-0 right-0 -translate-x-0.5 -translate-y-0.5": e5.placement === "bottom-right",
          "bottom-0 left-0 translate-x-0.5 -translate-y-0.5": e5.placement === "bottom-left"
        }
      ]),
      "data-testid": "badge"
    }, toDisplayString(r4.value), 3));
  }
});

// node_modules/@storefront-ui/vue/dist/shared/reactiveContext.mjs
var n = () => {
  const t4 = ref(useSlots());
  return onBeforeUpdate(() => {
    t4.value = useSlots();
  }), t4;
};
var f = () => {
  const t4 = ref(useAttrs());
  return onBeforeUpdate(() => {
    t4.value = useAttrs();
  }), t4;
};

// node_modules/@storefront-ui/vue/dist/components/SfButton/SfButton.vue.mjs
var S2 = {
  [G.primary]: "text-white shadow hover:shadow-md active:shadow bg-primary-700 hover:bg-primary-800 active:bg-primary-900 disabled:bg-disabled-300",
  [G.secondary]: "text-primary-700 hover:bg-primary-100 hover:text-primary-800 active:bg-primary-200 active:text-primary-900 ring-1 ring-inset ring-primary-700 shadow hover:shadow-md active:shadow hover:ring-primary-800 active:ring-primary-900 disabled:ring-1 disabled:ring-disabled-300 disabled:bg-white/50",
  [G.tertiary]: "text-primary-700 hover:bg-primary-100 hover:text-primary-800 active:bg-primary-200 active:text-primary-900 disabled:bg-transparent"
};
var j2 = defineComponent({
  __name: "SfButton",
  props: {
    size: {
      type: String,
      default: j.base
    },
    variant: {
      type: String,
      default: G.primary
    },
    disabled: {
      type: Boolean,
      default: false
    },
    square: {
      type: Boolean,
      default: false
    },
    tag: {
      type: [String, Object],
      default: void 0
    }
  },
  setup(a2) {
    const p14 = a2, { size: u45, tag: r4, square: i2 } = toRefs(p14), m17 = computed(() => {
      switch (u45.value) {
        case j.sm:
          return [i2.value ? "p-1.5" : "leading-5 text-sm py-1.5 px-3", "gap-1.5"];
        case j.lg:
          return [i2.value ? "p-4" : "py-3 leading-6 px-6", "gap-3"];
        default:
          return [i2.value ? "p-2" : "py-2 leading-6 px-4", "gap-2"];
      }
    }), s12 = computed(() => (r4 == null ? void 0 : r4.value) || "button"), b3 = f(), y5 = computed(
      () => b3.value.type ?? (typeof s12.value == "string" && s12.value.toLowerCase() === "button" ? "button" : void 0)
    );
    return (e5, B4) => (openBlock(), createBlock(resolveDynamicComponent(s12.value), {
      type: y5.value,
      disabled: a2.disabled,
      class: normalizeClass([
        "inline-flex items-center justify-center font-medium text-base focus-visible:outline focus-visible:outline-offset rounded-md disabled:text-disabled-500 disabled:bg-disabled-300 disabled:shadow-none disabled:ring-0 disabled:cursor-not-allowed",
        m17.value,
        S2[a2.variant]
      ]),
      "data-testid": "button"
    }, {
      default: withCtx(() => [
        e5.$slots.prefix ? renderSlot(e5.$slots, "prefix", { key: 0 }) : createCommentVNode("", true),
        renderSlot(e5.$slots, "default"),
        e5.$slots.suffix ? renderSlot(e5.$slots, "suffix", { key: 1 }) : createCommentVNode("", true)
      ]),
      _: 3
    }, 8, ["type", "disabled", "class"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfCheckbox/SfCheckbox.vue.mjs
var y = defineComponent({
  __name: "SfCheckbox",
  props: {
    modelValue: {
      type: [String, Array, Boolean],
      default: false
    },
    invalid: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue"],
  setup(t4, { emit: o4 }) {
    const i2 = t4, d26 = o4, { modelValue: e5 } = toRefs(i2), r4 = computed({
      get: () => e5 == null ? void 0 : e5.value,
      set: (a2) => d26("update:modelValue", a2)
    });
    return (a2, n6) => withDirectives((openBlock(), createElementBlock("input", {
      "onUpdate:modelValue": n6[0] || (n6[0] = (l18) => r4.value = l18),
      class: normalizeClass(["h-[18px] min-w-[18px] border-2 rounded-sm appearance-none cursor-pointer text-gray-500 hover:indeterminate:text-primary-800 enabled:active:checked:text-primary-900 checked:text-primary-700 checked:bg-checked-checkbox-current border-current indeterminate:bg-indeterminate-checkbox-current indeterminate:text-primary-700 disabled:text-gray-300 hover:text-gray-300 disabled:cursor-not-allowed enabled:hover:border-primary-800 enabled:active:border-primary-900 enabled:hover:checked:text-primary-800 enabled:hover:indeterminate:text-primary-800 enabled:checked:text-primary-700 enabled:indeterminate:text-primary-700 enabled:focus-visible:outline enabled:focus-visible:outline-offset", {
        "border-negative-700 enabled:hover:border-negative-800 enabled:active:border-negative-900 indeterminate:bg-none": t4.invalid
      }]),
      type: "checkbox",
      "data-testid": "checkbox"
    }, null, 2)), [
      [vModelCheckbox, r4.value]
    ]);
  }
});

// node_modules/@storefront-ui/vue/dist/shared/useId.mjs
var t2 = -1;
var e = () => String(++t2);

// node_modules/@storefront-ui/vue/dist/components/SfChip/SfChip.vue.mjs
var j3 = ["id"];
var q2 = ["for"];
var E2 = {
  [C2.sm]: "text-sm py-1.5 gap-1.5",
  [C2.base]: "text-base py-2 gap-2"
};
var A2 = defineComponent({
  __name: "SfChip",
  props: {
    size: {
      type: String,
      default: C2.base
    },
    modelValue: {
      type: [String, Array, Boolean],
      default: false
    },
    inputProps: {
      type: Object,
      default: null
    },
    square: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue"],
  setup(o4, { emit: g4 }) {
    const b3 = o4, { size: a2, square: l18, modelValue: i2 } = toRefs(b3), v3 = g4, t4 = n(), p14 = e(), u45 = computed({
      get: () => i2 == null ? void 0 : i2.value,
      set: (e5) => v3("update:modelValue", e5)
    }), y5 = computed(() => {
      switch (a2.value) {
        case C2.sm:
          return l18.value ? "px-1.5" : [t4.value.prefix ? "pl-1.5" : "pl-3", t4.value.suffix ? "pr-1.5" : "pr-3"];
        default:
          return l18.value ? "px-2" : [t4.value.prefix ? "pl-2" : "pl-4", t4.value.suffix ? "pr-2" : "pr-4"];
      }
    });
    return (e5, d26) => (openBlock(), createElementBlock(Fragment, null, [
      withDirectives(createBaseVNode("input", mergeProps({
        id: unref(p14),
        "onUpdate:modelValue": d26[0] || (d26[0] = (h12) => u45.value = h12),
        class: "absolute w-0 outline-none appearance-none peer",
        type: "checkbox"
      }, o4.inputProps), null, 16, j3), [
        [vModelCheckbox, u45.value]
      ]),
      createBaseVNode("label", mergeProps({
        for: unref(p14),
        class: [
          "cursor-pointer ring-1 ring-neutral-200 ring-inset rounded-full inline-flex items-center transition duration-300 justify-center outline-offset-2 outline-secondary-600 peer-next-checked:ring-2 peer-next-checked:ring-primary-700 hover:bg-primary-100 peer-next-hover:ring-primary-200 active:bg-primary-200 peer-next-active:ring-primary-300 peer-next-disabled:cursor-not-allowed peer-next-disabled:bg-disabled-100 peer-next-disabled:opacity-50 peer-next-disabled:ring-1 peer-next-disabled:ring-disabled-200 peer-next-disabled:hover:ring-disabled-200 peer-next-checked:hover:ring-primary-700 peer-next-checked:active:ring-primary-700 peer-next-focus-visible:outline",
          E2[unref(a2)],
          y5.value
        ],
        "data-testid": "chip"
      }, e5.$attrs), [
        e5.$slots.prefix ? renderSlot(e5.$slots, "prefix", { key: 0 }) : createCommentVNode("", true),
        renderSlot(e5.$slots, "default"),
        e5.$slots.suffix ? renderSlot(e5.$slots, "suffix", { key: 1 }) : createCommentVNode("", true)
      ], 16, q2)
    ], 64));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfCounter/SfCounter.vue.mjs
var C3 = defineComponent({
  __name: "SfCounter",
  props: {
    size: {
      type: String,
      default: U.base
    },
    pill: {
      type: Boolean,
      default: false
    }
  },
  setup(r4) {
    const s12 = r4, { size: n6, pill: e5 } = toRefs(s12), a2 = computed(() => {
      switch (n6 == null ? void 0 : n6.value) {
        case U["3xs"]:
          return ["text-3xs", { "px-1": e5.value }];
        case U["2xs"]:
          return ["text-2xs", { "px-1.5": e5.value }];
        case U.xs:
          return ["text-xs", { "px-2": e5.value }];
        case U.sm:
          return ["text-sm", { "px-2.5": e5.value }];
        case U.lg:
          return ["text-lg", { "px-3.5": e5.value }];
        default:
          return ["text-base", { "px-3": e5.value }];
      }
    });
    return (l18, d26) => (openBlock(), createElementBlock("span", {
      class: normalizeClass(["inline-flex items-center before:content-['('] after:content-[')'] text-neutral-500", [
        a2.value,
        {
          "rounded-full py-0.5 font-medium ring-1 ring-neutral-200 ring-inset before:content-none after:content-none": unref(e5)
        }
      ]]),
      "data-testid": "counter"
    }, [
      renderSlot(l18.$slots, "default")
    ], 2));
  }
});

// node_modules/@storefront-ui/vue/node_modules/@vueuse/shared/index.mjs
function tryOnScopeDispose(fn) {
  if (getCurrentScope()) {
    onScopeDispose(fn);
    return true;
  }
  return false;
}
function toValue(r4) {
  return typeof r4 === "function" ? r4() : unref(r4);
}
var isClient = typeof window !== "undefined" && typeof document !== "undefined";
var isWorker = typeof WorkerGlobalScope !== "undefined" && globalThis instanceof WorkerGlobalScope;
var toString = Object.prototype.toString;
var isObject = (val) => toString.call(val) === "[object Object]";
var noop = () => {
};
var isIOS = getIsIOS();
function getIsIOS() {
  var _a, _b;
  return isClient && ((_a = window == null ? void 0 : window.navigator) == null ? void 0 : _a.userAgent) && (/iP(?:ad|hone|od)/.test(window.navigator.userAgent) || ((_b = window == null ? void 0 : window.navigator) == null ? void 0 : _b.maxTouchPoints) > 2 && /iPad|Macintosh/.test(window == null ? void 0 : window.navigator.userAgent));
}
function cacheStringFunction(fn) {
  const cache = /* @__PURE__ */ Object.create(null);
  return (str) => {
    const hit = cache[str];
    return hit || (cache[str] = fn(str));
  };
}
var hyphenateRE = /\B([A-Z])/g;
var hyphenate = cacheStringFunction((str) => str.replace(hyphenateRE, "-$1").toLowerCase());
var camelizeRE = /-(\w)/g;
var camelize = cacheStringFunction((str) => {
  return str.replace(camelizeRE, (_7, c20) => c20 ? c20.toUpperCase() : "");
});
function identity(arg) {
  return arg;
}
function syncRefs(source, targets, options = {}) {
  const {
    flush = "sync",
    deep = false,
    immediate = true
  } = options;
  if (!Array.isArray(targets))
    targets = [targets];
  return watch(
    source,
    (newValue) => targets.forEach((target) => target.value = newValue),
    { flush, deep, immediate }
  );
}

// node_modules/@storefront-ui/vue/node_modules/@vueuse/core/index.mjs
function unrefElement(elRef) {
  var _a;
  const plain = toValue(elRef);
  return (_a = plain == null ? void 0 : plain.$el) != null ? _a : plain;
}
var defaultWindow = isClient ? window : void 0;
var defaultDocument = isClient ? window.document : void 0;
var defaultNavigator = isClient ? window.navigator : void 0;
var defaultLocation = isClient ? window.location : void 0;
function useEventListener(...args) {
  let target;
  let events;
  let listeners;
  let options;
  if (typeof args[0] === "string" || Array.isArray(args[0])) {
    [events, listeners, options] = args;
    target = defaultWindow;
  } else {
    [target, events, listeners, options] = args;
  }
  if (!target)
    return noop;
  if (!Array.isArray(events))
    events = [events];
  if (!Array.isArray(listeners))
    listeners = [listeners];
  const cleanups = [];
  const cleanup = () => {
    cleanups.forEach((fn) => fn());
    cleanups.length = 0;
  };
  const register = (el, event, listener, options2) => {
    el.addEventListener(event, listener, options2);
    return () => el.removeEventListener(event, listener, options2);
  };
  const stopWatch = watch(
    () => [unrefElement(target), toValue(options)],
    ([el, options2]) => {
      cleanup();
      if (!el)
        return;
      const optionsClone = isObject(options2) ? { ...options2 } : options2;
      cleanups.push(
        ...events.flatMap((event) => {
          return listeners.map((listener) => register(el, event, listener, optionsClone));
        })
      );
    },
    { immediate: true, flush: "post" }
  );
  const stop = () => {
    stopWatch();
    cleanup();
  };
  tryOnScopeDispose(stop);
  return stop;
}
var _iOSWorkaround = false;
function onClickOutside(target, handler, options = {}) {
  const { window: window2 = defaultWindow, ignore = [], capture = true, detectIframe = false } = options;
  if (!window2)
    return noop;
  if (isIOS && !_iOSWorkaround) {
    _iOSWorkaround = true;
    Array.from(window2.document.body.children).forEach((el) => el.addEventListener("click", noop));
    window2.document.documentElement.addEventListener("click", noop);
  }
  let shouldListen = true;
  const shouldIgnore = (event) => {
    return ignore.some((target2) => {
      if (typeof target2 === "string") {
        return Array.from(window2.document.querySelectorAll(target2)).some((el) => el === event.target || event.composedPath().includes(el));
      } else {
        const el = unrefElement(target2);
        return el && (event.target === el || event.composedPath().includes(el));
      }
    });
  };
  const listener = (event) => {
    const el = unrefElement(target);
    if (!el || el === event.target || event.composedPath().includes(el))
      return;
    if (event.detail === 0)
      shouldListen = !shouldIgnore(event);
    if (!shouldListen) {
      shouldListen = true;
      return;
    }
    handler(event);
  };
  const cleanup = [
    useEventListener(window2, "click", listener, { passive: true, capture }),
    useEventListener(window2, "pointerdown", (e5) => {
      const el = unrefElement(target);
      shouldListen = !shouldIgnore(e5) && !!(el && !e5.composedPath().includes(el));
    }, { passive: true }),
    detectIframe && useEventListener(window2, "blur", (event) => {
      setTimeout(() => {
        var _a;
        const el = unrefElement(target);
        if (((_a = window2.document.activeElement) == null ? void 0 : _a.tagName) === "IFRAME" && !(el == null ? void 0 : el.contains(window2.document.activeElement))) {
          handler(event);
        }
      }, 0);
    })
  ].filter(Boolean);
  const stop = () => cleanup.forEach((fn) => fn());
  return stop;
}
function createKeyPredicate(keyFilter) {
  if (typeof keyFilter === "function")
    return keyFilter;
  else if (typeof keyFilter === "string")
    return (event) => event.key === keyFilter;
  else if (Array.isArray(keyFilter))
    return (event) => keyFilter.includes(event.key);
  return () => true;
}
function onKeyStroke(...args) {
  let key;
  let handler;
  let options = {};
  if (args.length === 3) {
    key = args[0];
    handler = args[1];
    options = args[2];
  } else if (args.length === 2) {
    if (typeof args[1] === "object") {
      key = true;
      handler = args[0];
      options = args[1];
    } else {
      key = args[0];
      handler = args[1];
    }
  } else {
    key = true;
    handler = args[0];
  }
  const {
    target = defaultWindow,
    eventName = "keydown",
    passive = false,
    dedupe = false
  } = options;
  const predicate = createKeyPredicate(key);
  const listener = (e5) => {
    if (e5.repeat && toValue(dedupe))
      return;
    if (predicate(e5))
      handler(e5);
  };
  return useEventListener(target, eventName, listener, passive);
}
var _global = typeof globalThis !== "undefined" ? globalThis : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : {};
var globalKey = "__vueuse_ssr_handlers__";
var handlers = getHandlers();
function getHandlers() {
  if (!(globalKey in _global))
    _global[globalKey] = _global[globalKey] || {};
  return _global[globalKey];
}
var defaultState = {
  x: 0,
  y: 0,
  pointerId: 0,
  pressure: 0,
  tiltX: 0,
  tiltY: 0,
  width: 0,
  height: 0,
  twist: 0,
  pointerType: null
};
var keys = Object.keys(defaultState);
var DEFAULT_UNITS = [
  { max: 6e4, value: 1e3, name: "second" },
  { max: 276e4, value: 6e4, name: "minute" },
  { max: 72e6, value: 36e5, name: "hour" },
  { max: 5184e5, value: 864e5, name: "day" },
  { max: 24192e5, value: 6048e5, name: "week" },
  { max: 28512e6, value: 2592e6, name: "month" },
  { max: Number.POSITIVE_INFINITY, value: 31536e6, name: "year" }
];
var _TransitionPresets = {
  easeInSine: [0.12, 0, 0.39, 0],
  easeOutSine: [0.61, 1, 0.88, 1],
  easeInOutSine: [0.37, 0, 0.63, 1],
  easeInQuad: [0.11, 0, 0.5, 0],
  easeOutQuad: [0.5, 1, 0.89, 1],
  easeInOutQuad: [0.45, 0, 0.55, 1],
  easeInCubic: [0.32, 0, 0.67, 0],
  easeOutCubic: [0.33, 1, 0.68, 1],
  easeInOutCubic: [0.65, 0, 0.35, 1],
  easeInQuart: [0.5, 0, 0.75, 0],
  easeOutQuart: [0.25, 1, 0.5, 1],
  easeInOutQuart: [0.76, 0, 0.24, 1],
  easeInQuint: [0.64, 0, 0.78, 0],
  easeOutQuint: [0.22, 1, 0.36, 1],
  easeInOutQuint: [0.83, 0, 0.17, 1],
  easeInExpo: [0.7, 0, 0.84, 0],
  easeOutExpo: [0.16, 1, 0.3, 1],
  easeInOutExpo: [0.87, 0, 0.13, 1],
  easeInCirc: [0.55, 0, 1, 0.45],
  easeOutCirc: [0, 0.55, 0.45, 1],
  easeInOutCirc: [0.85, 0, 0.15, 1],
  easeInBack: [0.36, 0, 0.66, -0.56],
  easeOutBack: [0.34, 1.56, 0.64, 1],
  easeInOutBack: [0.68, -0.6, 0.32, 1.6]
};
var TransitionPresets = Object.assign({}, { linear: identity }, _TransitionPresets);

// node_modules/@storefront-ui/vue/dist/components/SfDrawer/SfDrawer.vue.mjs
var K2 = defineComponent({
  __name: "SfDrawer",
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    placement: {
      type: String,
      default: A.left
    },
    tag: {
      type: [String, Object],
      default: "aside"
    },
    disableClickAway: {
      type: Boolean,
      default: false
    },
    disableEsc: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue"],
  setup(a2, { emit: s12 }) {
    const r4 = a2, { disableClickAway: n6, disableEsc: d26, placement: t4 } = toRefs(r4), l18 = s12, o4 = ref();
    onClickOutside(o4, () => {
      n6.value || l18("update:modelValue", false);
    });
    const f29 = () => {
      d26.value || l18("update:modelValue", false);
    }, u45 = computed(() => ({
      "left-0": t4.value !== A.right,
      "right-0": t4.value !== A.left,
      "top-0": t4.value !== A.bottom,
      "bottom-0": t4.value !== A.top
    }));
    return (c20, S3) => a2.modelValue ? (openBlock(), createBlock(resolveDynamicComponent(a2.tag), {
      key: 0,
      ref_key: "drawerRef",
      ref: o4,
      class: normalizeClass(["fixed", u45.value]),
      "data-testid": "drawer",
      tabindex: "-1",
      onKeydown: withKeys(f29, ["esc"])
    }, {
      default: withCtx(() => [
        renderSlot(c20.$slots, "default")
      ]),
      _: 3
    }, 40, ["class"])) : createCommentVNode("", true);
  }
});

// node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs
var sides = ["top", "right", "bottom", "left"];
var alignments = ["start", "end"];
var placements = sides.reduce((acc, side) => acc.concat(side, side + "-" + alignments[0], side + "-" + alignments[1]), []);
var min = Math.min;
var max = Math.max;
var round = Math.round;
var floor = Math.floor;
var createCoords = (v3) => ({
  x: v3,
  y: v3
});
var oppositeSideMap = {
  left: "right",
  right: "left",
  bottom: "top",
  top: "bottom"
};
var oppositeAlignmentMap = {
  start: "end",
  end: "start"
};
function clamp2(start, value, end) {
  return max(start, min(value, end));
}
function evaluate(value, param) {
  return typeof value === "function" ? value(param) : value;
}
function getSide(placement) {
  return placement.split("-")[0];
}
function getAlignment(placement) {
  return placement.split("-")[1];
}
function getOppositeAxis(axis) {
  return axis === "x" ? "y" : "x";
}
function getAxisLength(axis) {
  return axis === "y" ? "height" : "width";
}
function getSideAxis(placement) {
  return ["top", "bottom"].includes(getSide(placement)) ? "y" : "x";
}
function getAlignmentAxis(placement) {
  return getOppositeAxis(getSideAxis(placement));
}
function getAlignmentSides(placement, rects, rtl) {
  if (rtl === void 0) {
    rtl = false;
  }
  const alignment = getAlignment(placement);
  const alignmentAxis = getAlignmentAxis(placement);
  const length = getAxisLength(alignmentAxis);
  let mainAlignmentSide = alignmentAxis === "x" ? alignment === (rtl ? "end" : "start") ? "right" : "left" : alignment === "start" ? "bottom" : "top";
  if (rects.reference[length] > rects.floating[length]) {
    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);
  }
  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];
}
function getExpandedPlacements(placement) {
  const oppositePlacement = getOppositePlacement(placement);
  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];
}
function getOppositeAlignmentPlacement(placement) {
  return placement.replace(/start|end/g, (alignment) => oppositeAlignmentMap[alignment]);
}
function getSideList(side, isStart, rtl) {
  const lr = ["left", "right"];
  const rl = ["right", "left"];
  const tb = ["top", "bottom"];
  const bt2 = ["bottom", "top"];
  switch (side) {
    case "top":
    case "bottom":
      if (rtl) return isStart ? rl : lr;
      return isStart ? lr : rl;
    case "left":
    case "right":
      return isStart ? tb : bt2;
    default:
      return [];
  }
}
function getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {
  const alignment = getAlignment(placement);
  let list = getSideList(getSide(placement), direction === "start", rtl);
  if (alignment) {
    list = list.map((side) => side + "-" + alignment);
    if (flipAlignment) {
      list = list.concat(list.map(getOppositeAlignmentPlacement));
    }
  }
  return list;
}
function getOppositePlacement(placement) {
  return placement.replace(/left|right|bottom|top/g, (side) => oppositeSideMap[side]);
}
function expandPaddingObject(padding) {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
    ...padding
  };
}
function getPaddingObject(padding) {
  return typeof padding !== "number" ? expandPaddingObject(padding) : {
    top: padding,
    right: padding,
    bottom: padding,
    left: padding
  };
}
function rectToClientRect(rect) {
  const {
    x: x3,
    y: y5,
    width,
    height
  } = rect;
  return {
    width,
    height,
    top: y5,
    left: x3,
    right: x3 + width,
    bottom: y5 + height,
    x: x3,
    y: y5
  };
}

// node_modules/@floating-ui/core/dist/floating-ui.core.mjs
function computeCoordsFromPlacement(_ref, placement, rtl) {
  let {
    reference,
    floating
  } = _ref;
  const sideAxis = getSideAxis(placement);
  const alignmentAxis = getAlignmentAxis(placement);
  const alignLength = getAxisLength(alignmentAxis);
  const side = getSide(placement);
  const isVertical = sideAxis === "y";
  const commonX = reference.x + reference.width / 2 - floating.width / 2;
  const commonY = reference.y + reference.height / 2 - floating.height / 2;
  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;
  let coords;
  switch (side) {
    case "top":
      coords = {
        x: commonX,
        y: reference.y - floating.height
      };
      break;
    case "bottom":
      coords = {
        x: commonX,
        y: reference.y + reference.height
      };
      break;
    case "right":
      coords = {
        x: reference.x + reference.width,
        y: commonY
      };
      break;
    case "left":
      coords = {
        x: reference.x - floating.width,
        y: commonY
      };
      break;
    default:
      coords = {
        x: reference.x,
        y: reference.y
      };
  }
  switch (getAlignment(placement)) {
    case "start":
      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);
      break;
    case "end":
      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);
      break;
  }
  return coords;
}
var computePosition = async (reference, floating, config) => {
  const {
    placement = "bottom",
    strategy = "absolute",
    middleware = [],
    platform: platform2
  } = config;
  const validMiddleware = middleware.filter(Boolean);
  const rtl = await (platform2.isRTL == null ? void 0 : platform2.isRTL(floating));
  let rects = await platform2.getElementRects({
    reference,
    floating,
    strategy
  });
  let {
    x: x3,
    y: y5
  } = computeCoordsFromPlacement(rects, placement, rtl);
  let statefulPlacement = placement;
  let middlewareData = {};
  let resetCount = 0;
  for (let i2 = 0; i2 < validMiddleware.length; i2++) {
    const {
      name,
      fn
    } = validMiddleware[i2];
    const {
      x: nextX,
      y: nextY,
      data,
      reset
    } = await fn({
      x: x3,
      y: y5,
      initialPlacement: placement,
      placement: statefulPlacement,
      strategy,
      middlewareData,
      rects,
      platform: platform2,
      elements: {
        reference,
        floating
      }
    });
    x3 = nextX != null ? nextX : x3;
    y5 = nextY != null ? nextY : y5;
    middlewareData = {
      ...middlewareData,
      [name]: {
        ...middlewareData[name],
        ...data
      }
    };
    if (reset && resetCount <= 50) {
      resetCount++;
      if (typeof reset === "object") {
        if (reset.placement) {
          statefulPlacement = reset.placement;
        }
        if (reset.rects) {
          rects = reset.rects === true ? await platform2.getElementRects({
            reference,
            floating,
            strategy
          }) : reset.rects;
        }
        ({
          x: x3,
          y: y5
        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));
      }
      i2 = -1;
    }
  }
  return {
    x: x3,
    y: y5,
    placement: statefulPlacement,
    strategy,
    middlewareData
  };
};
async function detectOverflow(state, options) {
  var _await$platform$isEle;
  if (options === void 0) {
    options = {};
  }
  const {
    x: x3,
    y: y5,
    platform: platform2,
    rects,
    elements,
    strategy
  } = state;
  const {
    boundary = "clippingAncestors",
    rootBoundary = "viewport",
    elementContext = "floating",
    altBoundary = false,
    padding = 0
  } = evaluate(options, state);
  const paddingObject = getPaddingObject(padding);
  const altContext = elementContext === "floating" ? "reference" : "floating";
  const element = elements[altBoundary ? altContext : elementContext];
  const clippingClientRect = rectToClientRect(await platform2.getClippingRect({
    element: ((_await$platform$isEle = await (platform2.isElement == null ? void 0 : platform2.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || await (platform2.getDocumentElement == null ? void 0 : platform2.getDocumentElement(elements.floating)),
    boundary,
    rootBoundary,
    strategy
  }));
  const rect = elementContext === "floating" ? {
    x: x3,
    y: y5,
    width: rects.floating.width,
    height: rects.floating.height
  } : rects.reference;
  const offsetParent = await (platform2.getOffsetParent == null ? void 0 : platform2.getOffsetParent(elements.floating));
  const offsetScale = await (platform2.isElement == null ? void 0 : platform2.isElement(offsetParent)) ? await (platform2.getScale == null ? void 0 : platform2.getScale(offsetParent)) || {
    x: 1,
    y: 1
  } : {
    x: 1,
    y: 1
  };
  const elementClientRect = rectToClientRect(platform2.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform2.convertOffsetParentRelativeRectToViewportRelativeRect({
    elements,
    rect,
    offsetParent,
    strategy
  }) : rect);
  return {
    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,
    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,
    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,
    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x
  };
}
var arrow = (options) => ({
  name: "arrow",
  options,
  async fn(state) {
    const {
      x: x3,
      y: y5,
      placement,
      rects,
      platform: platform2,
      elements,
      middlewareData
    } = state;
    const {
      element,
      padding = 0
    } = evaluate(options, state) || {};
    if (element == null) {
      return {};
    }
    const paddingObject = getPaddingObject(padding);
    const coords = {
      x: x3,
      y: y5
    };
    const axis = getAlignmentAxis(placement);
    const length = getAxisLength(axis);
    const arrowDimensions = await platform2.getDimensions(element);
    const isYAxis = axis === "y";
    const minProp = isYAxis ? "top" : "left";
    const maxProp = isYAxis ? "bottom" : "right";
    const clientProp = isYAxis ? "clientHeight" : "clientWidth";
    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];
    const startDiff = coords[axis] - rects.reference[axis];
    const arrowOffsetParent = await (platform2.getOffsetParent == null ? void 0 : platform2.getOffsetParent(element));
    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;
    if (!clientSize || !await (platform2.isElement == null ? void 0 : platform2.isElement(arrowOffsetParent))) {
      clientSize = elements.floating[clientProp] || rects.floating[length];
    }
    const centerToReference = endDiff / 2 - startDiff / 2;
    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;
    const minPadding = min(paddingObject[minProp], largestPossiblePadding);
    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);
    const min$1 = minPadding;
    const max2 = clientSize - arrowDimensions[length] - maxPadding;
    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;
    const offset3 = clamp2(min$1, center, max2);
    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset3 && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;
    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max2 : 0;
    return {
      [axis]: coords[axis] + alignmentOffset,
      data: {
        [axis]: offset3,
        centerOffset: center - offset3 - alignmentOffset,
        ...shouldAddOffset && {
          alignmentOffset
        }
      },
      reset: shouldAddOffset
    };
  }
});
var flip = function(options) {
  if (options === void 0) {
    options = {};
  }
  return {
    name: "flip",
    options,
    async fn(state) {
      var _middlewareData$arrow, _middlewareData$flip;
      const {
        placement,
        middlewareData,
        rects,
        initialPlacement,
        platform: platform2,
        elements
      } = state;
      const {
        mainAxis: checkMainAxis = true,
        crossAxis: checkCrossAxis = true,
        fallbackPlacements: specifiedFallbackPlacements,
        fallbackStrategy = "bestFit",
        fallbackAxisSideDirection = "none",
        flipAlignment = true,
        ...detectOverflowOptions
      } = evaluate(options, state);
      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {
        return {};
      }
      const side = getSide(placement);
      const initialSideAxis = getSideAxis(initialPlacement);
      const isBasePlacement = getSide(initialPlacement) === initialPlacement;
      const rtl = await (platform2.isRTL == null ? void 0 : platform2.isRTL(elements.floating));
      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));
      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== "none";
      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {
        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));
      }
      const placements2 = [initialPlacement, ...fallbackPlacements];
      const overflow = await detectOverflow(state, detectOverflowOptions);
      const overflows = [];
      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];
      if (checkMainAxis) {
        overflows.push(overflow[side]);
      }
      if (checkCrossAxis) {
        const sides2 = getAlignmentSides(placement, rects, rtl);
        overflows.push(overflow[sides2[0]], overflow[sides2[1]]);
      }
      overflowsData = [...overflowsData, {
        placement,
        overflows
      }];
      if (!overflows.every((side2) => side2 <= 0)) {
        var _middlewareData$flip2, _overflowsData$filter;
        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;
        const nextPlacement = placements2[nextIndex];
        if (nextPlacement) {
          return {
            data: {
              index: nextIndex,
              overflows: overflowsData
            },
            reset: {
              placement: nextPlacement
            }
          };
        }
        let resetPlacement = (_overflowsData$filter = overflowsData.filter((d26) => d26.overflows[0] <= 0).sort((a2, b3) => a2.overflows[1] - b3.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;
        if (!resetPlacement) {
          switch (fallbackStrategy) {
            case "bestFit": {
              var _overflowsData$filter2;
              const placement2 = (_overflowsData$filter2 = overflowsData.filter((d26) => {
                if (hasFallbackAxisSideDirection) {
                  const currentSideAxis = getSideAxis(d26.placement);
                  return currentSideAxis === initialSideAxis || // Create a bias to the `y` side axis due to horizontal
                  // reading directions favoring greater width.
                  currentSideAxis === "y";
                }
                return true;
              }).map((d26) => [d26.placement, d26.overflows.filter((overflow2) => overflow2 > 0).reduce((acc, overflow2) => acc + overflow2, 0)]).sort((a2, b3) => a2[1] - b3[1])[0]) == null ? void 0 : _overflowsData$filter2[0];
              if (placement2) {
                resetPlacement = placement2;
              }
              break;
            }
            case "initialPlacement":
              resetPlacement = initialPlacement;
              break;
          }
        }
        if (placement !== resetPlacement) {
          return {
            reset: {
              placement: resetPlacement
            }
          };
        }
      }
      return {};
    }
  };
};
async function convertValueToCoords(state, options) {
  const {
    placement,
    platform: platform2,
    elements
  } = state;
  const rtl = await (platform2.isRTL == null ? void 0 : platform2.isRTL(elements.floating));
  const side = getSide(placement);
  const alignment = getAlignment(placement);
  const isVertical = getSideAxis(placement) === "y";
  const mainAxisMulti = ["left", "top"].includes(side) ? -1 : 1;
  const crossAxisMulti = rtl && isVertical ? -1 : 1;
  const rawValue = evaluate(options, state);
  let {
    mainAxis,
    crossAxis,
    alignmentAxis
  } = typeof rawValue === "number" ? {
    mainAxis: rawValue,
    crossAxis: 0,
    alignmentAxis: null
  } : {
    mainAxis: rawValue.mainAxis || 0,
    crossAxis: rawValue.crossAxis || 0,
    alignmentAxis: rawValue.alignmentAxis
  };
  if (alignment && typeof alignmentAxis === "number") {
    crossAxis = alignment === "end" ? alignmentAxis * -1 : alignmentAxis;
  }
  return isVertical ? {
    x: crossAxis * crossAxisMulti,
    y: mainAxis * mainAxisMulti
  } : {
    x: mainAxis * mainAxisMulti,
    y: crossAxis * crossAxisMulti
  };
}
var offset = function(options) {
  if (options === void 0) {
    options = 0;
  }
  return {
    name: "offset",
    options,
    async fn(state) {
      var _middlewareData$offse, _middlewareData$arrow;
      const {
        x: x3,
        y: y5,
        placement,
        middlewareData
      } = state;
      const diffCoords = await convertValueToCoords(state, options);
      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {
        return {};
      }
      return {
        x: x3 + diffCoords.x,
        y: y5 + diffCoords.y,
        data: {
          ...diffCoords,
          placement
        }
      };
    }
  };
};
var shift = function(options) {
  if (options === void 0) {
    options = {};
  }
  return {
    name: "shift",
    options,
    async fn(state) {
      const {
        x: x3,
        y: y5,
        placement
      } = state;
      const {
        mainAxis: checkMainAxis = true,
        crossAxis: checkCrossAxis = false,
        limiter = {
          fn: (_ref) => {
            let {
              x: x4,
              y: y6
            } = _ref;
            return {
              x: x4,
              y: y6
            };
          }
        },
        ...detectOverflowOptions
      } = evaluate(options, state);
      const coords = {
        x: x3,
        y: y5
      };
      const overflow = await detectOverflow(state, detectOverflowOptions);
      const crossAxis = getSideAxis(getSide(placement));
      const mainAxis = getOppositeAxis(crossAxis);
      let mainAxisCoord = coords[mainAxis];
      let crossAxisCoord = coords[crossAxis];
      if (checkMainAxis) {
        const minSide = mainAxis === "y" ? "top" : "left";
        const maxSide = mainAxis === "y" ? "bottom" : "right";
        const min2 = mainAxisCoord + overflow[minSide];
        const max2 = mainAxisCoord - overflow[maxSide];
        mainAxisCoord = clamp2(min2, mainAxisCoord, max2);
      }
      if (checkCrossAxis) {
        const minSide = crossAxis === "y" ? "top" : "left";
        const maxSide = crossAxis === "y" ? "bottom" : "right";
        const min2 = crossAxisCoord + overflow[minSide];
        const max2 = crossAxisCoord - overflow[maxSide];
        crossAxisCoord = clamp2(min2, crossAxisCoord, max2);
      }
      const limitedCoords = limiter.fn({
        ...state,
        [mainAxis]: mainAxisCoord,
        [crossAxis]: crossAxisCoord
      });
      return {
        ...limitedCoords,
        data: {
          x: limitedCoords.x - x3,
          y: limitedCoords.y - y5,
          enabled: {
            [mainAxis]: checkMainAxis,
            [crossAxis]: checkCrossAxis
          }
        }
      };
    }
  };
};

// node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs
function hasWindow() {
  return typeof window !== "undefined";
}
function getNodeName(node) {
  if (isNode(node)) {
    return (node.nodeName || "").toLowerCase();
  }
  return "#document";
}
function getWindow(node) {
  var _node$ownerDocument;
  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;
}
function getDocumentElement(node) {
  var _ref;
  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;
}
function isNode(value) {
  if (!hasWindow()) {
    return false;
  }
  return value instanceof Node || value instanceof getWindow(value).Node;
}
function isElement(value) {
  if (!hasWindow()) {
    return false;
  }
  return value instanceof Element || value instanceof getWindow(value).Element;
}
function isHTMLElement(value) {
  if (!hasWindow()) {
    return false;
  }
  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;
}
function isShadowRoot(value) {
  if (!hasWindow() || typeof ShadowRoot === "undefined") {
    return false;
  }
  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;
}
function isOverflowElement(element) {
  const {
    overflow,
    overflowX,
    overflowY,
    display
  } = getComputedStyle2(element);
  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !["inline", "contents"].includes(display);
}
function isTableElement(element) {
  return ["table", "td", "th"].includes(getNodeName(element));
}
function isTopLayer(element) {
  return [":popover-open", ":modal"].some((selector) => {
    try {
      return element.matches(selector);
    } catch (e5) {
      return false;
    }
  });
}
function isContainingBlock(elementOrCss) {
  const webkit = isWebKit();
  const css = isElement(elementOrCss) ? getComputedStyle2(elementOrCss) : elementOrCss;
  return ["transform", "translate", "scale", "rotate", "perspective"].some((value) => css[value] ? css[value] !== "none" : false) || (css.containerType ? css.containerType !== "normal" : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== "none" : false) || !webkit && (css.filter ? css.filter !== "none" : false) || ["transform", "translate", "scale", "rotate", "perspective", "filter"].some((value) => (css.willChange || "").includes(value)) || ["paint", "layout", "strict", "content"].some((value) => (css.contain || "").includes(value));
}
function getContainingBlock(element) {
  let currentNode = getParentNode(element);
  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {
    if (isContainingBlock(currentNode)) {
      return currentNode;
    } else if (isTopLayer(currentNode)) {
      return null;
    }
    currentNode = getParentNode(currentNode);
  }
  return null;
}
function isWebKit() {
  if (typeof CSS === "undefined" || !CSS.supports) return false;
  return CSS.supports("-webkit-backdrop-filter", "none");
}
function isLastTraversableNode(node) {
  return ["html", "body", "#document"].includes(getNodeName(node));
}
function getComputedStyle2(element) {
  return getWindow(element).getComputedStyle(element);
}
function getNodeScroll(element) {
  if (isElement(element)) {
    return {
      scrollLeft: element.scrollLeft,
      scrollTop: element.scrollTop
    };
  }
  return {
    scrollLeft: element.scrollX,
    scrollTop: element.scrollY
  };
}
function getParentNode(node) {
  if (getNodeName(node) === "html") {
    return node;
  }
  const result = (
    // Step into the shadow DOM of the parent of a slotted node.
    node.assignedSlot || // DOM Element detected.
    node.parentNode || // ShadowRoot detected.
    isShadowRoot(node) && node.host || // Fallback.
    getDocumentElement(node)
  );
  return isShadowRoot(result) ? result.host : result;
}
function getNearestOverflowAncestor(node) {
  const parentNode = getParentNode(node);
  if (isLastTraversableNode(parentNode)) {
    return node.ownerDocument ? node.ownerDocument.body : node.body;
  }
  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {
    return parentNode;
  }
  return getNearestOverflowAncestor(parentNode);
}
function getOverflowAncestors(node, list, traverseIframes) {
  var _node$ownerDocument2;
  if (list === void 0) {
    list = [];
  }
  if (traverseIframes === void 0) {
    traverseIframes = true;
  }
  const scrollableAncestor = getNearestOverflowAncestor(node);
  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);
  const win = getWindow(scrollableAncestor);
  if (isBody) {
    const frameElement = getFrameElement(win);
    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);
  }
  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));
}
function getFrameElement(win) {
  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;
}

// node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs
function getCssDimensions(element) {
  const css = getComputedStyle2(element);
  let width = parseFloat(css.width) || 0;
  let height = parseFloat(css.height) || 0;
  const hasOffset = isHTMLElement(element);
  const offsetWidth = hasOffset ? element.offsetWidth : width;
  const offsetHeight = hasOffset ? element.offsetHeight : height;
  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;
  if (shouldFallback) {
    width = offsetWidth;
    height = offsetHeight;
  }
  return {
    width,
    height,
    $: shouldFallback
  };
}
function unwrapElement(element) {
  return !isElement(element) ? element.contextElement : element;
}
function getScale(element) {
  const domElement = unwrapElement(element);
  if (!isHTMLElement(domElement)) {
    return createCoords(1);
  }
  const rect = domElement.getBoundingClientRect();
  const {
    width,
    height,
    $: $3
  } = getCssDimensions(domElement);
  let x3 = ($3 ? round(rect.width) : rect.width) / width;
  let y5 = ($3 ? round(rect.height) : rect.height) / height;
  if (!x3 || !Number.isFinite(x3)) {
    x3 = 1;
  }
  if (!y5 || !Number.isFinite(y5)) {
    y5 = 1;
  }
  return {
    x: x3,
    y: y5
  };
}
var noOffsets = createCoords(0);
function getVisualOffsets(element) {
  const win = getWindow(element);
  if (!isWebKit() || !win.visualViewport) {
    return noOffsets;
  }
  return {
    x: win.visualViewport.offsetLeft,
    y: win.visualViewport.offsetTop
  };
}
function shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {
  if (isFixed === void 0) {
    isFixed = false;
  }
  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {
    return false;
  }
  return isFixed;
}
function getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {
  if (includeScale === void 0) {
    includeScale = false;
  }
  if (isFixedStrategy === void 0) {
    isFixedStrategy = false;
  }
  const clientRect = element.getBoundingClientRect();
  const domElement = unwrapElement(element);
  let scale = createCoords(1);
  if (includeScale) {
    if (offsetParent) {
      if (isElement(offsetParent)) {
        scale = getScale(offsetParent);
      }
    } else {
      scale = getScale(element);
    }
  }
  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);
  let x3 = (clientRect.left + visualOffsets.x) / scale.x;
  let y5 = (clientRect.top + visualOffsets.y) / scale.y;
  let width = clientRect.width / scale.x;
  let height = clientRect.height / scale.y;
  if (domElement) {
    const win = getWindow(domElement);
    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;
    let currentWin = win;
    let currentIFrame = getFrameElement(currentWin);
    while (currentIFrame && offsetParent && offsetWin !== currentWin) {
      const iframeScale = getScale(currentIFrame);
      const iframeRect = currentIFrame.getBoundingClientRect();
      const css = getComputedStyle2(currentIFrame);
      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;
      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;
      x3 *= iframeScale.x;
      y5 *= iframeScale.y;
      width *= iframeScale.x;
      height *= iframeScale.y;
      x3 += left;
      y5 += top;
      currentWin = getWindow(currentIFrame);
      currentIFrame = getFrameElement(currentWin);
    }
  }
  return rectToClientRect({
    width,
    height,
    x: x3,
    y: y5
  });
}
function getWindowScrollBarX(element, rect) {
  const leftScroll = getNodeScroll(element).scrollLeft;
  if (!rect) {
    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;
  }
  return rect.left + leftScroll;
}
function getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {
  if (ignoreScrollbarX === void 0) {
    ignoreScrollbarX = false;
  }
  const htmlRect = documentElement.getBoundingClientRect();
  const x3 = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 : (
    // RTL <body> scrollbar.
    getWindowScrollBarX(documentElement, htmlRect)
  ));
  const y5 = htmlRect.top + scroll.scrollTop;
  return {
    x: x3,
    y: y5
  };
}
function convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {
  let {
    elements,
    rect,
    offsetParent,
    strategy
  } = _ref;
  const isFixed = strategy === "fixed";
  const documentElement = getDocumentElement(offsetParent);
  const topLayer = elements ? isTopLayer(elements.floating) : false;
  if (offsetParent === documentElement || topLayer && isFixed) {
    return rect;
  }
  let scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  let scale = createCoords(1);
  const offsets = createCoords(0);
  const isOffsetParentAnElement = isHTMLElement(offsetParent);
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || isOverflowElement(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isHTMLElement(offsetParent)) {
      const offsetRect = getBoundingClientRect(offsetParent);
      scale = getScale(offsetParent);
      offsets.x = offsetRect.x + offsetParent.clientLeft;
      offsets.y = offsetRect.y + offsetParent.clientTop;
    }
  }
  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);
  return {
    width: rect.width * scale.x,
    height: rect.height * scale.y,
    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,
    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y
  };
}
function getClientRects(element) {
  return Array.from(element.getClientRects());
}
function getDocumentRect(element) {
  const html = getDocumentElement(element);
  const scroll = getNodeScroll(element);
  const body = element.ownerDocument.body;
  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);
  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);
  let x3 = -scroll.scrollLeft + getWindowScrollBarX(element);
  const y5 = -scroll.scrollTop;
  if (getComputedStyle2(body).direction === "rtl") {
    x3 += max(html.clientWidth, body.clientWidth) - width;
  }
  return {
    width,
    height,
    x: x3,
    y: y5
  };
}
function getViewportRect(element, strategy) {
  const win = getWindow(element);
  const html = getDocumentElement(element);
  const visualViewport = win.visualViewport;
  let width = html.clientWidth;
  let height = html.clientHeight;
  let x3 = 0;
  let y5 = 0;
  if (visualViewport) {
    width = visualViewport.width;
    height = visualViewport.height;
    const visualViewportBased = isWebKit();
    if (!visualViewportBased || visualViewportBased && strategy === "fixed") {
      x3 = visualViewport.offsetLeft;
      y5 = visualViewport.offsetTop;
    }
  }
  return {
    width,
    height,
    x: x3,
    y: y5
  };
}
function getInnerBoundingClientRect(element, strategy) {
  const clientRect = getBoundingClientRect(element, true, strategy === "fixed");
  const top = clientRect.top + element.clientTop;
  const left = clientRect.left + element.clientLeft;
  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);
  const width = element.clientWidth * scale.x;
  const height = element.clientHeight * scale.y;
  const x3 = left * scale.x;
  const y5 = top * scale.y;
  return {
    width,
    height,
    x: x3,
    y: y5
  };
}
function getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {
  let rect;
  if (clippingAncestor === "viewport") {
    rect = getViewportRect(element, strategy);
  } else if (clippingAncestor === "document") {
    rect = getDocumentRect(getDocumentElement(element));
  } else if (isElement(clippingAncestor)) {
    rect = getInnerBoundingClientRect(clippingAncestor, strategy);
  } else {
    const visualOffsets = getVisualOffsets(element);
    rect = {
      x: clippingAncestor.x - visualOffsets.x,
      y: clippingAncestor.y - visualOffsets.y,
      width: clippingAncestor.width,
      height: clippingAncestor.height
    };
  }
  return rectToClientRect(rect);
}
function hasFixedPositionAncestor(element, stopNode) {
  const parentNode = getParentNode(element);
  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {
    return false;
  }
  return getComputedStyle2(parentNode).position === "fixed" || hasFixedPositionAncestor(parentNode, stopNode);
}
function getClippingElementAncestors(element, cache) {
  const cachedResult = cache.get(element);
  if (cachedResult) {
    return cachedResult;
  }
  let result = getOverflowAncestors(element, [], false).filter((el) => isElement(el) && getNodeName(el) !== "body");
  let currentContainingBlockComputedStyle = null;
  const elementIsFixed = getComputedStyle2(element).position === "fixed";
  let currentNode = elementIsFixed ? getParentNode(element) : element;
  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {
    const computedStyle = getComputedStyle2(currentNode);
    const currentNodeIsContaining = isContainingBlock(currentNode);
    if (!currentNodeIsContaining && computedStyle.position === "fixed") {
      currentContainingBlockComputedStyle = null;
    }
    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === "static" && !!currentContainingBlockComputedStyle && ["absolute", "fixed"].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);
    if (shouldDropCurrentNode) {
      result = result.filter((ancestor) => ancestor !== currentNode);
    } else {
      currentContainingBlockComputedStyle = computedStyle;
    }
    currentNode = getParentNode(currentNode);
  }
  cache.set(element, result);
  return result;
}
function getClippingRect(_ref) {
  let {
    element,
    boundary,
    rootBoundary,
    strategy
  } = _ref;
  const elementClippingAncestors = boundary === "clippingAncestors" ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);
  const clippingAncestors = [...elementClippingAncestors, rootBoundary];
  const firstClippingAncestor = clippingAncestors[0];
  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {
    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);
    accRect.top = max(rect.top, accRect.top);
    accRect.right = min(rect.right, accRect.right);
    accRect.bottom = min(rect.bottom, accRect.bottom);
    accRect.left = max(rect.left, accRect.left);
    return accRect;
  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));
  return {
    width: clippingRect.right - clippingRect.left,
    height: clippingRect.bottom - clippingRect.top,
    x: clippingRect.left,
    y: clippingRect.top
  };
}
function getDimensions(element) {
  const {
    width,
    height
  } = getCssDimensions(element);
  return {
    width,
    height
  };
}
function getRectRelativeToOffsetParent(element, offsetParent, strategy) {
  const isOffsetParentAnElement = isHTMLElement(offsetParent);
  const documentElement = getDocumentElement(offsetParent);
  const isFixed = strategy === "fixed";
  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);
  let scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  const offsets = createCoords(0);
  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== "body" || isOverflowElement(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }
    if (isOffsetParentAnElement) {
      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);
      offsets.x = offsetRect.x + offsetParent.clientLeft;
      offsets.y = offsetRect.y + offsetParent.clientTop;
    } else if (documentElement) {
      offsets.x = getWindowScrollBarX(documentElement);
    }
  }
  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);
  const x3 = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;
  const y5 = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;
  return {
    x: x3,
    y: y5,
    width: rect.width,
    height: rect.height
  };
}
function isStaticPositioned(element) {
  return getComputedStyle2(element).position === "static";
}
function getTrueOffsetParent(element, polyfill) {
  if (!isHTMLElement(element) || getComputedStyle2(element).position === "fixed") {
    return null;
  }
  if (polyfill) {
    return polyfill(element);
  }
  let rawOffsetParent = element.offsetParent;
  if (getDocumentElement(element) === rawOffsetParent) {
    rawOffsetParent = rawOffsetParent.ownerDocument.body;
  }
  return rawOffsetParent;
}
function getOffsetParent(element, polyfill) {
  const win = getWindow(element);
  if (isTopLayer(element)) {
    return win;
  }
  if (!isHTMLElement(element)) {
    let svgOffsetParent = getParentNode(element);
    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {
      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {
        return svgOffsetParent;
      }
      svgOffsetParent = getParentNode(svgOffsetParent);
    }
    return win;
  }
  let offsetParent = getTrueOffsetParent(element, polyfill);
  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {
    offsetParent = getTrueOffsetParent(offsetParent, polyfill);
  }
  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {
    return win;
  }
  return offsetParent || getContainingBlock(element) || win;
}
var getElementRects = async function(data) {
  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;
  const getDimensionsFn = this.getDimensions;
  const floatingDimensions = await getDimensionsFn(data.floating);
  return {
    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),
    floating: {
      x: 0,
      y: 0,
      width: floatingDimensions.width,
      height: floatingDimensions.height
    }
  };
};
function isRTL(element) {
  return getComputedStyle2(element).direction === "rtl";
}
var platform = {
  convertOffsetParentRelativeRectToViewportRelativeRect,
  getDocumentElement,
  getClippingRect,
  getOffsetParent,
  getElementRects,
  getClientRects,
  getDimensions,
  getScale,
  isElement,
  isRTL
};
function rectsAreEqual(a2, b3) {
  return a2.x === b3.x && a2.y === b3.y && a2.width === b3.width && a2.height === b3.height;
}
function observeMove(element, onMove) {
  let io = null;
  let timeoutId;
  const root = getDocumentElement(element);
  function cleanup() {
    var _io;
    clearTimeout(timeoutId);
    (_io = io) == null || _io.disconnect();
    io = null;
  }
  function refresh(skip, threshold) {
    if (skip === void 0) {
      skip = false;
    }
    if (threshold === void 0) {
      threshold = 1;
    }
    cleanup();
    const elementRectForRootMargin = element.getBoundingClientRect();
    const {
      left,
      top,
      width,
      height
    } = elementRectForRootMargin;
    if (!skip) {
      onMove();
    }
    if (!width || !height) {
      return;
    }
    const insetTop = floor(top);
    const insetRight = floor(root.clientWidth - (left + width));
    const insetBottom = floor(root.clientHeight - (top + height));
    const insetLeft = floor(left);
    const rootMargin = -insetTop + "px " + -insetRight + "px " + -insetBottom + "px " + -insetLeft + "px";
    const options = {
      rootMargin,
      threshold: max(0, min(1, threshold)) || 1
    };
    let isFirstUpdate = true;
    function handleObserve(entries) {
      const ratio = entries[0].intersectionRatio;
      if (ratio !== threshold) {
        if (!isFirstUpdate) {
          return refresh();
        }
        if (!ratio) {
          timeoutId = setTimeout(() => {
            refresh(false, 1e-7);
          }, 1e3);
        } else {
          refresh(false, ratio);
        }
      }
      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {
        refresh();
      }
      isFirstUpdate = false;
    }
    try {
      io = new IntersectionObserver(handleObserve, {
        ...options,
        // Handle <iframe>s
        root: root.ownerDocument
      });
    } catch (e5) {
      io = new IntersectionObserver(handleObserve, options);
    }
    io.observe(element);
  }
  refresh(true);
  return cleanup;
}
function autoUpdate(reference, floating, update, options) {
  if (options === void 0) {
    options = {};
  }
  const {
    ancestorScroll = true,
    ancestorResize = true,
    elementResize = typeof ResizeObserver === "function",
    layoutShift = typeof IntersectionObserver === "function",
    animationFrame = false
  } = options;
  const referenceEl = unwrapElement(reference);
  const ancestors = ancestorScroll || ancestorResize ? [...referenceEl ? getOverflowAncestors(referenceEl) : [], ...getOverflowAncestors(floating)] : [];
  ancestors.forEach((ancestor) => {
    ancestorScroll && ancestor.addEventListener("scroll", update, {
      passive: true
    });
    ancestorResize && ancestor.addEventListener("resize", update);
  });
  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;
  let reobserveFrame = -1;
  let resizeObserver = null;
  if (elementResize) {
    resizeObserver = new ResizeObserver((_ref) => {
      let [firstEntry] = _ref;
      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {
        resizeObserver.unobserve(floating);
        cancelAnimationFrame(reobserveFrame);
        reobserveFrame = requestAnimationFrame(() => {
          var _resizeObserver;
          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);
        });
      }
      update();
    });
    if (referenceEl && !animationFrame) {
      resizeObserver.observe(referenceEl);
    }
    resizeObserver.observe(floating);
  }
  let frameId;
  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;
  if (animationFrame) {
    frameLoop();
  }
  function frameLoop() {
    const nextRefRect = getBoundingClientRect(reference);
    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {
      update();
    }
    prevRefRect = nextRefRect;
    frameId = requestAnimationFrame(frameLoop);
  }
  update();
  return () => {
    var _resizeObserver2;
    ancestors.forEach((ancestor) => {
      ancestorScroll && ancestor.removeEventListener("scroll", update);
      ancestorResize && ancestor.removeEventListener("resize", update);
    });
    cleanupIo == null || cleanupIo();
    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();
    resizeObserver = null;
    if (animationFrame) {
      cancelAnimationFrame(frameId);
    }
  };
}
var offset2 = offset;
var shift2 = shift;
var flip2 = flip;
var arrow2 = arrow;
var computePosition2 = (reference, floating, options) => {
  const cache = /* @__PURE__ */ new Map();
  const mergedOptions = {
    platform,
    ...options
  };
  const platformWithCache = {
    ...mergedOptions.platform,
    _c: cache
  };
  return computePosition(reference, floating, {
    ...mergedOptions,
    platform: platformWithCache
  });
};

// node_modules/@floating-ui/vue/dist/floating-ui.vue.mjs
function isComponentPublicInstance(target) {
  return target != null && typeof target === "object" && "$el" in target;
}
function unwrapElement2(target) {
  if (isComponentPublicInstance(target)) {
    const element = target.$el;
    return isNode(element) && getNodeName(element) === "#comment" ? null : element;
  }
  return target;
}
function toValue2(source) {
  return typeof source === "function" ? source() : unref(source);
}
function arrow3(options) {
  return {
    name: "arrow",
    options,
    fn(args) {
      const element = unwrapElement2(toValue2(options.element));
      if (element == null) {
        return {};
      }
      return arrow2({
        element,
        padding: options.padding
      }).fn(args);
    }
  };
}
function getDPR(element) {
  if (typeof window === "undefined") {
    return 1;
  }
  const win = element.ownerDocument.defaultView || window;
  return win.devicePixelRatio || 1;
}
function roundByDPR(element, value) {
  const dpr = getDPR(element);
  return Math.round(value * dpr) / dpr;
}
function useFloating(reference, floating, options) {
  if (options === void 0) {
    options = {};
  }
  const whileElementsMountedOption = options.whileElementsMounted;
  const openOption = computed(() => {
    var _toValue;
    return (_toValue = toValue2(options.open)) != null ? _toValue : true;
  });
  const middlewareOption = computed(() => toValue2(options.middleware));
  const placementOption = computed(() => {
    var _toValue2;
    return (_toValue2 = toValue2(options.placement)) != null ? _toValue2 : "bottom";
  });
  const strategyOption = computed(() => {
    var _toValue3;
    return (_toValue3 = toValue2(options.strategy)) != null ? _toValue3 : "absolute";
  });
  const transformOption = computed(() => {
    var _toValue4;
    return (_toValue4 = toValue2(options.transform)) != null ? _toValue4 : true;
  });
  const referenceElement = computed(() => unwrapElement2(reference.value));
  const floatingElement = computed(() => unwrapElement2(floating.value));
  const x3 = ref(0);
  const y5 = ref(0);
  const strategy = ref(strategyOption.value);
  const placement = ref(placementOption.value);
  const middlewareData = shallowRef({});
  const isPositioned = ref(false);
  const floatingStyles = computed(() => {
    const initialStyles = {
      position: strategy.value,
      left: "0",
      top: "0"
    };
    if (!floatingElement.value) {
      return initialStyles;
    }
    const xVal = roundByDPR(floatingElement.value, x3.value);
    const yVal = roundByDPR(floatingElement.value, y5.value);
    if (transformOption.value) {
      return {
        ...initialStyles,
        transform: "translate(" + xVal + "px, " + yVal + "px)",
        ...getDPR(floatingElement.value) >= 1.5 && {
          willChange: "transform"
        }
      };
    }
    return {
      position: strategy.value,
      left: xVal + "px",
      top: yVal + "px"
    };
  });
  let whileElementsMountedCleanup;
  function update() {
    if (referenceElement.value == null || floatingElement.value == null) {
      return;
    }
    const open = openOption.value;
    computePosition2(referenceElement.value, floatingElement.value, {
      middleware: middlewareOption.value,
      placement: placementOption.value,
      strategy: strategyOption.value
    }).then((position) => {
      x3.value = position.x;
      y5.value = position.y;
      strategy.value = position.strategy;
      placement.value = position.placement;
      middlewareData.value = position.middlewareData;
      isPositioned.value = open !== false;
    });
  }
  function cleanup() {
    if (typeof whileElementsMountedCleanup === "function") {
      whileElementsMountedCleanup();
      whileElementsMountedCleanup = void 0;
    }
  }
  function attach() {
    cleanup();
    if (whileElementsMountedOption === void 0) {
      update();
      return;
    }
    if (referenceElement.value != null && floatingElement.value != null) {
      whileElementsMountedCleanup = whileElementsMountedOption(referenceElement.value, floatingElement.value, update);
      return;
    }
  }
  function reset() {
    if (!openOption.value) {
      isPositioned.value = false;
    }
  }
  watch([middlewareOption, placementOption, strategyOption, openOption], update, {
    flush: "sync"
  });
  watch([referenceElement, floatingElement], attach, {
    flush: "sync"
  });
  watch(openOption, reset, {
    flush: "sync"
  });
  if (getCurrentScope()) {
    onScopeDispose(cleanup);
  }
  return {
    x: shallowReadonly(x3),
    y: shallowReadonly(y5),
    strategy: shallowReadonly(strategy),
    placement: shallowReadonly(placement),
    middlewareData: shallowReadonly(middlewareData),
    isPositioned: shallowReadonly(isPositioned),
    floatingStyles,
    update
  };
}

// node_modules/@storefront-ui/vue/dist/composables/usePopover/usePopover.mjs
function b2(a2) {
  const {
    referenceRef: e5 = ref(),
    floatingRef: t4 = ref(),
    isOpen: r4 = false,
    middleware: n6,
    placement: l18 = S.bottom,
    strategy: i2 = tt.absolute
  } = a2, { strategy: p14, x: m17, y: s12, middlewareData: f29, placement: u45 } = useFloating(e5, t4, {
    strategy: i2,
    placement: l18,
    open: r4,
    middleware: n6,
    whileElementsMounted: autoUpdate
  }), c20 = computed(() => ({
    position: p14.value,
    top: `${s12.value ?? 0}px`,
    left: `${m17.value ?? 0}px`
  }));
  return { referenceRef: e5, floatingRef: t4, style: c20, middlewareData: f29, placement: u45 };
}

// node_modules/@storefront-ui/vue/dist/composables/useDropdown/useDropdown.mjs
function C4(t4) {
  const { onClose: o4, placement: r4 = "bottom", middleware: n6, isOpen: p14, ...f29 } = t4, { floatingRef: i2, referenceRef: e5, style: m17 } = b2({
    placement: r4,
    middleware: computed(() => unref(n6) || [offset2(8), shift2(), flip2()]),
    isOpen: p14,
    ...f29
  });
  return onClickOutside(e5, o4), onKeyStroke("Escape", o4, { target: e5 }), { floatingRef: i2, referenceRef: e5, style: m17 };
}

// node_modules/@storefront-ui/vue/dist/components/SfDropdown/SfDropdown.vue.mjs
var k2 = ["aria-hidden"];
var B2 = defineComponent({
  __name: "SfDropdown",
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    placement: {
      type: String,
      default: void 0
    },
    middleware: {
      type: Array,
      default: void 0
    },
    strategy: {
      type: String,
      default: void 0
    }
  },
  emits: ["update:modelValue"],
  setup(n6, { emit: l18 }) {
    const s12 = n6, i2 = l18, { modelValue: e5, placement: f29, middleware: p14, strategy: m17 } = toRefs(s12), {
      referenceRef: c20,
      floatingRef: u45,
      style: y5
    } = C4({
      isOpen: e5,
      placement: f29,
      middleware: p14,
      strategy: m17,
      onClose: () => i2("update:modelValue", false)
    });
    return (o4, R2) => (openBlock(), createElementBlock("div", {
      ref_key: "referenceRef",
      ref: c20,
      class: "w-max",
      "data-testid": "dropdown"
    }, [
      renderSlot(o4.$slots, "trigger"),
      unref(e5) ? (openBlock(), createElementBlock("div", {
        key: 0,
        ref_key: "floatingRef",
        ref: u45,
        style: normalizeStyle(unref(y5)),
        "aria-hidden": !unref(e5) || void 0,
        "data-testid": "dropdown-content"
      }, [
        renderSlot(o4.$slots, "default")
      ], 12, k2)) : createCommentVNode("", true)
    ], 512));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIconBase/SfIconBase.vue.mjs
var m2 = defineComponent({
  name: "SfIconBase",
  props: {
    content: {
      type: String,
      default: void 0
    },
    size: {
      type: String,
      default: q.base
    }
  },
  setup(l18, o4) {
    const { size: u45, content: n6 } = toRefs(l18), r4 = computed(() => {
      var t4, a2;
      return (a2 = (t4 = o4.slots).default) == null ? void 0 : a2.call(t4);
    }), c20 = computed(() => {
      switch (u45.value) {
        case q.xs:
          return "w-4 h-4";
        case q.sm:
          return "w-5 h-5";
        case q.lg:
          return "w-8 h-8";
        case q.xl:
          return "w-10 h-10";
        case q["2xl"]:
          return "w-14 h-14";
        case q["3xl"]:
          return "w-24 h-24";
        case q["4xl"]:
          return "w-48 h-48";
        default:
          return "w-6 h-6";
      }
    }), i2 = computed(() => {
      const t4 = {
        xmlns: "http://www.w3.org/2000/svg",
        class: `inline-block fill-current ${c20.value}`
      };
      return !r4.value && n6.value && (t4.innerHTML = n6.value), t4;
    });
    return () => h("svg", i2.value, r4.value);
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconAdd.vue.mjs
var d = createBaseVNode("path", { d: "M12 19a.97.97 0 0 1-.712-.288A.97.97 0 0 1 11 18v-5H6a.97.97 0 0 1-.713-.288A.97.97 0 0 1 5 12a.97.97 0 0 1 .287-.713A.97.97 0 0 1 6 11h5V6q0-.425.288-.713A.97.97 0 0 1 12 5a.97.97 0 0 1 .713.287A.97.97 0 0 1 13 6v5h5q.424 0 .712.287.288.288.288.713 0 .424-.288.712A.97.97 0 0 1 18 13h-5v5q0 .424-.287.712A.97.97 0 0 1 12 19" }, null, -1);
var u3 = defineComponent({
  __name: "SfIconAdd",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "add"
    }, {
      default: withCtx(() => [
        d
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconAddShoppingCart.vue.mjs
var p2 = createBaseVNode("path", { d: "M12 9a.97.97 0 0 1-.713-.287A.97.97 0 0 1 11 8V6H9a.97.97 0 0 1-.713-.287A.97.97 0 0 1 8 5q0-.424.287-.713A.97.97 0 0 1 9 4h2V2q0-.424.287-.712A.97.97 0 0 1 12 1q.424 0 .713.288Q13 1.575 13 2v2h2q.424 0 .713.287Q16 4.576 16 5t-.287.713A.97.97 0 0 1 15 6h-2v2q0 .424-.287.713A.97.97 0 0 1 12 9M7 22q-.824 0-1.412-.587A1.93 1.93 0 0 1 5 20q0-.824.588-1.413A1.93 1.93 0 0 1 7 18q.824 0 1.412.587Q9 19.176 9 20t-.588 1.413A1.93 1.93 0 0 1 7 22m10 0q-.825 0-1.412-.587A1.93 1.93 0 0 1 15 20q0-.824.588-1.413A1.93 1.93 0 0 1 17 18q.824 0 1.413.587Q19 19.176 19 20t-.587 1.413A1.93 1.93 0 0 1 17 22M7 17q-1.15 0-1.737-.988-.588-.987-.013-1.962L6.6 11.6 3 4H2a.97.97 0 0 1-.712-.288A.97.97 0 0 1 1 3q0-.424.288-.712A.97.97 0 0 1 2 2h1.65q.275 0 .525.15t.375.425L8.525 11h7.025l3.6-6.5A.97.97 0 0 1 20 4q.574 0 .863.487a.94.94 0 0 1 .012.988L17.3 11.95q-.275.5-.738.775A1.95 1.95 0 0 1 15.55 13H8.1L7 15h11q.424 0 .712.287.288.288.288.713 0 .424-.288.712A.97.97 0 0 1 18 17z" }, null, -1);
var m3 = defineComponent({
  __name: "SfIconAddShoppingCart",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(t4) {
    return (s12, c20) => (openBlock(), createBlock(unref(m2), {
      size: t4.size,
      viewBox: "0 0 24 24",
      "data-testid": "add-shopping-cart"
    }, {
      default: withCtx(() => [
        p2
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconAlokai.vue.mjs
var f2 = createBaseVNode("path", { d: "m28.2 10.681-3.712-.014c-1.732 0-3.155 1.423-3.17 3.169v4.343a3.16 3.16 0 0 0 3.155 3.154h7.512V32H21.32v-7.512a3.16 3.16 0 0 0-3.155-3.155h-4.343a3.16 3.16 0 0 0-3.154 3.155v6.236c0 .69-.558 1.261-1.262 1.261H1.262c-.69 0-1.262-.557-1.262-1.261V22.58c0-.69.558-1.262 1.262-1.262h6.235a3.16 3.16 0 0 0 3.155-3.155V13.19a2.54 2.54 0 0 1 2.538-2.538h4.974a3.16 3.16 0 0 0 3.155-3.155V3.8a3.8 3.8 0 0 1 3.8-3.8h3.08A3.8 3.8 0 0 1 32 3.8v3.081a3.8 3.8 0 0 1-3.8 3.8M10.681 5.34a5.34 5.34 0 1 1-10.68 0 5.34 5.34 0 0 1 10.68 0" }, null, -1);
var h2 = defineComponent({
  __name: "SfIconAlokai",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 32 32",
      "data-testid": "alokai"
    }, {
      default: withCtx(() => [
        f2
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconAlokaiFull.vue.mjs
var n2 = createBaseVNode("path", { d: "m21.427 9.333 3.248.013A3.326 3.326 0 0 0 28 6.021V3.325A3.326 3.326 0 0 0 24.675 0h-2.696a3.326 3.326 0 0 0-3.325 3.325V6.56a2.764 2.764 0 0 1-2.76 2.76H11.54a2.22 2.22 0 0 0-2.22 2.221v4.353a2.764 2.764 0 0 1-2.76 2.76H1.103c-.616 0-1.104.5-1.104 1.104v7.125c0 .616.5 1.104 1.104 1.104H8.23c.616 0 1.104-.5 1.104-1.104v-5.456a2.764 2.764 0 0 1 2.76-2.76h3.8a2.764 2.764 0 0 1 2.76 2.76V28h9.334v-9.333h-6.573a2.764 2.764 0 0 1-2.76-2.76v-3.8c.013-1.528 1.258-2.774 2.773-2.774m-16.755.013a4.673 4.673 0 1 0 0-9.346 4.673 4.673 0 0 0 0 9.346M65.012 3.12h2.555v21.76h-2.555zm63.023 6.213h-2.555V24.88h2.555zM70.276 17.1a8.054 8.054 0 0 1 8.05-8.05 8.054 8.054 0 0 1 8.049 8.05 8.054 8.054 0 0 1-8.05 8.05 8.054 8.054 0 0 1-8.049-8.05m2.208.013a5.846 5.846 0 0 0 5.842 5.842 5.846 5.846 0 0 0 5.84-5.842 5.846 5.846 0 0 0-5.84-5.841 5.846 5.846 0 0 0-5.842 5.841M127.01 6.305a1.605 1.605 0 1 0-.514-3.168 1.605 1.605 0 0 0 .514 3.168m-68.982 5.429c0-1.322 1.079-2.4 2.401-2.4V24.88h-2.555v-2.234a8 8 0 0 1-5.841 2.517c-4.558 0-8.23-3.788-8.05-8.384.167-4.16 3.557-7.548 7.73-7.715a8.01 8.01 0 0 1 6.315 2.67m-5.995 11.22c3.21 0 5.828-2.618 5.841-5.828V17.1a5.844 5.844 0 0 0-5.841-5.828 5.846 5.846 0 0 0-5.841 5.841 5.846 5.846 0 0 0 5.841 5.842m69.108-13.622a2.405 2.405 0 0 0-2.401 2.401 8.02 8.02 0 0 0-6.316-2.67c-4.173.167-7.562 3.556-7.729 7.715a8.06 8.06 0 0 0 8.05 8.384 8 8 0 0 0 5.841-2.517v2.234h2.555zm-2.542 7.793c-.013 3.21-2.632 5.829-5.842 5.829a5.846 5.846 0 0 1-5.841-5.842 5.846 5.846 0 0 1 5.841-5.841 5.844 5.844 0 0 1 5.842 5.828zm-15.881-7.793-5.854 5.97c-.103.103-.116.27-.026.41l6.997 9.18h-2.927l-5.867-7.716v-.012l-.013.012h-2.619a.29.29 0 0 0-.295.296v7.407h-2.555V3.12h2.555v11.477a.29.29 0 0 0 .295.295h1.926l5.456-5.559z" }, null, -1);
var _2 = defineComponent({
  __name: "SfIconAlokaiFull",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(a2) {
    return (r4, s12) => (openBlock(), createBlock(unref(m2), {
      size: a2.size,
      viewBox: "0 0 129 28",
      "data-testid": "alokai-full"
    }, {
      default: withCtx(() => [
        n2
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconArrowBack.vue.mjs
var f3 = createBaseVNode("path", { d: "m10.875 19.3-6.6-6.6a.9.9 0 0 1-.213-.325A1.1 1.1 0 0 1 4 12q0-.2.062-.375a.9.9 0 0 1 .213-.325l6.6-6.6a.98.98 0 0 1 .687-.288.93.93 0 0 1 .713.288q.3.274.313.687a.93.93 0 0 1-.288.713L7.4 11h11.175a.97.97 0 0 1 .713.287.97.97 0 0 1 .287.713q0 .424-.287.712a.97.97 0 0 1-.713.288H7.4l4.9 4.9q.275.275.288.7a.87.87 0 0 1-.288.7q-.275.3-.7.3a1 1 0 0 1-.725-.3" }, null, -1);
var u4 = defineComponent({
  __name: "SfIconArrowBack",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "arrow-back"
    }, {
      default: withCtx(() => [
        f3
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconArrowDownward.vue.mjs
var d2 = createBaseVNode("path", { d: "M12 19.575a1.1 1.1 0 0 1-.375-.063.9.9 0 0 1-.325-.212l-6.6-6.6q-.3-.3-.3-.713 0-.412.3-.712t.7-.3.7.3l4.9 4.9v-11.2q0-.425.288-.7A1 1 0 0 1 12 4a.97.97 0 0 1 .713.287A.97.97 0 0 1 13 5v11.175l4.9-4.9q.3-.3.7-.3t.7.3.3.712q0 .413-.3.713l-6.6 6.6q-.15.15-.325.212a1.1 1.1 0 0 1-.375.063" }, null, -1);
var u5 = defineComponent({
  __name: "SfIconArrowDownward",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "arrow-downward"
    }, {
      default: withCtx(() => [
        d2
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconArrowForward.vue.mjs
var f4 = createBaseVNode("path", { d: "M11.3 19.3a1 1 0 0 1-.288-.7.9.9 0 0 1 .263-.7l4.9-4.9H5a.97.97 0 0 1-.713-.288A.97.97 0 0 1 4 12a.97.97 0 0 1 .287-.713A.97.97 0 0 1 5 11h11.175l-4.9-4.9a.9.9 0 0 1-.263-.7 1 1 0 0 1 .288-.7.95.95 0 0 1 .7-.275q.425 0 .7.275l6.6 6.6q.15.125.213.312.062.188.062.388t-.062.375a.9.9 0 0 1-.213.325l-6.6 6.6a.95.95 0 0 1-.7.275.95.95 0 0 1-.7-.275" }, null, -1);
var u6 = defineComponent({
  __name: "SfIconArrowForward",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (l18, d26) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "arrow-forward"
    }, {
      default: withCtx(() => [
        f4
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconArrowUpward.vue.mjs
var p3 = createBaseVNode("path", { d: "M12 20a.97.97 0 0 1-.712-.288A.97.97 0 0 1 11 19V7.825L6.125 12.7q-.3.3-.713.3a.97.97 0 0 1-.712-.3.96.96 0 0 1-.3-.7q0-.4.3-.7l6.6-6.6q.15-.15.325-.213.175-.062.375-.062t.388.062a.7.7 0 0 1 .312.213l6.6 6.6q.3.3.3.7t-.3.7-.713.3a.97.97 0 0 1-.712-.3L13 7.825V19q0 .424-.287.712A.97.97 0 0 1 12 20" }, null, -1);
var u7 = defineComponent({
  __name: "SfIconArrowUpward",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, d26) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "arrow-upward"
    }, {
      default: withCtx(() => [
        p3
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconBlock.vue.mjs
var m4 = createBaseVNode("path", { d: "M12 22a9.7 9.7 0 0 1-3.9-.788 10.1 10.1 0 0 1-3.175-2.137q-1.35-1.35-2.137-3.175A9.7 9.7 0 0 1 2 12q0-2.075.788-3.9a10.1 10.1 0 0 1 2.137-3.175q1.35-1.35 3.175-2.138A9.7 9.7 0 0 1 12 2q2.075 0 3.9.787a10.1 10.1 0 0 1 3.175 2.138q1.35 1.35 2.137 3.175A9.7 9.7 0 0 1 22 12a9.7 9.7 0 0 1-.788 3.9 10.1 10.1 0 0 1-2.137 3.175q-1.35 1.35-3.175 2.137A9.7 9.7 0 0 1 12 22m0-2q3.35 0 5.675-2.325T20 12q0-1.35-.438-2.6A8 8 0 0 0 18.3 7.1L7.1 18.3q1.05.825 2.3 1.262T12 20m-6.3-3.1L16.9 5.7q-1.05-.824-2.3-1.262A7.8 7.8 0 0 0 12 4Q8.65 4 6.325 6.325T4 12q0 1.35.438 2.6.437 1.25 1.262 2.3" }, null, -1);
var d3 = defineComponent({
  __name: "SfIconBlock",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "block"
    }, {
      default: withCtx(() => [
        m4
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconCalendarToday.vue.mjs
var d4 = createBaseVNode("path", { d: "M5 22q-.825 0-1.413-.587A1.93 1.93 0 0 1 3 20V6q0-.824.587-1.412A1.93 1.93 0 0 1 5 4h1V2.975q0-.425.287-.7A1 1 0 0 1 7 2a.97.97 0 0 1 .713.287A.97.97 0 0 1 8 3v1h8V2.975q0-.425.288-.7A1 1 0 0 1 17 2q.424 0 .712.287Q18 2.575 18 3v1h1q.825 0 1.413.588Q21 5.175 21 6v14q0 .825-.587 1.413A1.93 1.93 0 0 1 19 22zm0-2h14V10H5z" }, null, -1);
var h3 = defineComponent({
  __name: "SfIconCalendarToday",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "calendar-today"
    }, {
      default: withCtx(() => [
        d4
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconCall.vue.mjs
var s = createBaseVNode("path", { d: "M19.95 21q-3.225 0-6.287-1.438a19 19 0 0 1-5.425-3.8 19 19 0 0 1-3.8-5.425Q3 7.275 3 4.05q0-.45.3-.75t.75-.3H8.1a.96.96 0 0 1 .625.225.88.88 0 0 1 .325.575l.65 3.5q.05.35-.012.637a1.03 1.03 0 0 1-.288.513L6.975 10.9q1.05 1.8 2.638 3.375A18.6 18.6 0 0 0 13.1 17l2.35-2.35q.225-.225.588-.338.362-.112.712-.062l3.45.7q.35.075.575.337.225.263.225.613v4.05q0 .45-.3.75t-.75.3M6.025 9l1.65-1.65L7.25 5H5.025q.125 1.024.35 2.025T6.025 9M19 18.95v-2.2l-2.35-.475-1.675 1.675q.975.424 1.988.675 1.012.25 2.037.325" }, null, -1);
var q3 = defineComponent({
  __name: "SfIconCall",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "call"
    }, {
      default: withCtx(() => [
        s
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconCancel.vue.mjs
var s2 = createBaseVNode("path", {
  "fill-rule": "evenodd",
  d: "M2 12C2 6.47 6.47 2 12 2s10 4.47 10 10-4.47 10-10 10S2 17.53 2 12m14.295 4.295a.997.997 0 0 0 0-1.41L13.41 12l2.885-2.885a.997.997 0 1 0-1.41-1.41L12 10.59 9.115 7.705a.997.997 0 0 0-1.41 1.41L10.59 12l-2.885 2.885a.997.997 0 0 0 1.41 1.41L12 13.41l2.885 2.885c.39.39 1.02.39 1.41 0",
  "clip-rule": "evenodd"
}, null, -1);
var u8 = defineComponent({
  __name: "SfIconCancel",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (d26, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "cancel"
    }, {
      default: withCtx(() => [
        s2
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconCheck.vue.mjs
var f5 = createBaseVNode("path", { d: "M4.535 12.705 8 16.17l9.885-9.875a.997.997 0 0 1 1.41 1.41L8.707 18.293a1 1 0 0 1-1.414 0L3.12 14.12a1 1 0 0 1 1.415-1.415" }, null, -1);
var u9 = defineComponent({
  __name: "SfIconCheck",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "check"
    }, {
      default: withCtx(() => [
        f5
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconCheckBox.vue.mjs
var f6 = createBaseVNode("path", { d: "M5 21q-.825 0-1.413-.587A1.93 1.93 0 0 1 3 19V5q0-.825.587-1.413A1.93 1.93 0 0 1 5 3h14q.825 0 1.413.587Q21 4.175 21 5v14q0 .825-.587 1.413A1.93 1.93 0 0 1 19 21zm5.6-5.225q.2 0 .375-.062a.9.9 0 0 0 .325-.213l5.675-5.675a.92.92 0 0 0 .275-.675q0-.4-.3-.7a.95.95 0 0 0-.7-.275.95.95 0 0 0-.7.275L10.6 13.4l-2.175-2.175a.92.92 0 0 0-.675-.275q-.4 0-.7.3a.95.95 0 0 0-.275.7q0 .425.275.7L9.9 15.5q.15.15.325.213.175.062.375.062" }, null, -1);
var q4 = defineComponent({
  __name: "SfIconCheckBox",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "check-box"
    }, {
      default: withCtx(() => [
        f6
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconCheckBoxOutlineBlank.vue.mjs
var s3 = createBaseVNode("path", {
  "fill-rule": "evenodd",
  d: "M3 5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2zm16 .5a.5.5 0 0 0-.5-.5h-13a.5.5 0 0 0-.5.5v13a.5.5 0 0 0 .5.5h13a.5.5 0 0 0 .5-.5z",
  "clip-rule": "evenodd"
}, null, -1);
var _3 = defineComponent({
  __name: "SfIconCheckBoxOutlineBlank",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (d26, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "check-box-outline-blank"
    }, {
      default: withCtx(() => [
        s3
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconCheckCircle.vue.mjs
var f7 = createBaseVNode("path", { d: "m10.6 13.8-2.175-2.175a.92.92 0 0 0-.675-.275q-.4 0-.7.3a.95.95 0 0 0-.275.7q0 .425.275.7L9.9 15.9a.95.95 0 0 0 .7.275.95.95 0 0 0 .7-.275l5.675-5.675a.92.92 0 0 0 .275-.675q0-.4-.3-.7a.95.95 0 0 0-.7-.275.95.95 0 0 0-.7.275zM12 22a9.7 9.7 0 0 1-3.9-.788 10.1 10.1 0 0 1-3.175-2.137q-1.35-1.35-2.137-3.175A9.7 9.7 0 0 1 2 12q0-2.075.788-3.9a10.1 10.1 0 0 1 2.137-3.175q1.35-1.35 3.175-2.138A9.7 9.7 0 0 1 12 2q2.075 0 3.9.787a10.1 10.1 0 0 1 3.175 2.138q1.35 1.35 2.137 3.175A9.7 9.7 0 0 1 22 12a9.7 9.7 0 0 1-.788 3.9 10.1 10.1 0 0 1-2.137 3.175q-1.35 1.35-3.175 2.137A9.7 9.7 0 0 1 12 22" }, null, -1);
var q5 = defineComponent({
  __name: "SfIconCheckCircle",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "check-circle"
    }, {
      default: withCtx(() => [
        f7
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconChevronLeft.vue.mjs
var s4 = createBaseVNode("path", { d: "M14.706 17.297a1 1 0 0 0 0-1.41l-3.876-3.885 3.877-3.885a.998.998 0 0 0-1.412-1.41l-4.588 4.588a1 1 0 0 0 0 1.414l4.588 4.588a.997.997 0 0 0 1.41 0" }, null, -1);
var u10 = defineComponent({
  __name: "SfIconChevronLeft",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (l18, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "chevron-left"
    }, {
      default: withCtx(() => [
        s4
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconChevronRight.vue.mjs
var f8 = createBaseVNode("path", { d: "M8.705 17.297a1 1 0 0 1-.001-1.41l3.876-3.885-3.876-3.885a.998.998 0 0 1 1.412-1.41l4.587 4.588a1 1 0 0 1 0 1.414l-4.587 4.588a.997.997 0 0 1-1.411 0" }, null, -1);
var h4 = defineComponent({
  __name: "SfIconChevronRight",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (l18, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "chevron-right"
    }, {
      default: withCtx(() => [
        f8
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconCircle.vue.mjs
var f9 = createBaseVNode("path", { d: "M22 12c0 5.523-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2s10 4.477 10 10" }, null, -1);
var u11 = defineComponent({
  __name: "SfIconCircle",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "circle"
    }, {
      default: withCtx(() => [
        f9
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconClose.vue.mjs
var l = createBaseVNode("path", { d: "M18.295 5.705a1 1 0 0 1 0 1.41L13.41 12l4.885 4.885a.997.997 0 1 1-1.41 1.41L12 13.41l-4.885 4.885a.997.997 0 1 1-1.41-1.41L10.59 12 5.705 7.115a.997.997 0 0 1 1.41-1.41L12 10.59l4.885-4.885a.997.997 0 0 1 1.41 0" }, null, -1);
var u12 = defineComponent({
  __name: "SfIconClose",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "close"
    }, {
      default: withCtx(() => [
        l
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconCloseSm.vue.mjs
var c = createBaseVNode("path", {
  "fill-rule": "evenodd",
  d: "M16.615 8.564a.974.974 0 0 0-1.378-1.378L11.9 10.522 8.563 7.186a.974.974 0 1 0-1.378 1.378l3.337 3.336-3.337 3.337a.974.974 0 1 0 1.378 1.378l3.337-3.337 3.336 3.337a.974.974 0 1 0 1.379-1.378L13.278 11.9z",
  "clip-rule": "evenodd"
}, null, -1);
var u13 = defineComponent({
  __name: "SfIconCloseSm",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (d26, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "close-sm"
    }, {
      default: withCtx(() => [
        c
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconCompareArrows.vue.mjs
var c2 = createBaseVNode("path", { d: "m15.3 13.3-3.6-3.6a.9.9 0 0 1-.212-.325A1.1 1.1 0 0 1 11.425 9q0-.2.063-.375A.9.9 0 0 1 11.7 8.3l3.6-3.6q.3-.3.7-.3t.7.3.3.712q0 .413-.3.713L14.825 8H21q.424 0 .712.287Q22 8.575 22 9q0 .424-.288.712A.97.97 0 0 1 21 10h-6.175l1.875 1.875q.3.3.3.7t-.3.7-.687.325-.713-.3m-8 5.975q.3.3.7.312t.7-.287l3.6-3.6q.15-.15.213-.325.062-.176.062-.375 0-.2-.062-.375a.9.9 0 0 0-.213-.325l-3.6-3.6a.96.96 0 0 0-.7-.3q-.4 0-.7.3t-.3.712.3.713L9.175 14H3a.97.97 0 0 0-.712.287A.97.97 0 0 0 2 15q0 .424.288.712A.97.97 0 0 0 3 16h6.175L7.3 17.875q-.3.3-.3.7t.3.7" }, null, -1);
var d5 = defineComponent({
  __name: "SfIconCompareArrows",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (p14, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "compare-arrows"
    }, {
      default: withCtx(() => [
        c2
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconContactSupport.vue.mjs
var p4 = createBaseVNode("path", { d: "m13 22-.25-3h-.25q-3.55 0-6.025-2.475T4 10.5t2.475-6.025T12.5 2q1.774 0 3.312.662a8.6 8.6 0 0 1 2.701 1.825 8.6 8.6 0 0 1 1.824 2.7A8.3 8.3 0 0 1 21 10.5q0 3.8-2.275 6.812T13 22m-.525-6.025q.425 0 .725-.3t.3-.725-.3-.725-.725-.3-.725.3-.3.725.3.725.725.3M12.5 12.8q.275 0 .5-.2a1 1 0 0 0 .3-.55q.05-.326.275-.588t.775-.812q.45-.45.75-.975t.3-1.125q0-1.275-.862-1.913Q13.675 6 12.5 6q-.874 0-1.512.4t-1.038 1a.62.62 0 0 0-.063.575q.113.3.463.425a.65.65 0 0 0 .5 0q.25-.1.45-.375a1.7 1.7 0 0 1 .488-.45q.287-.175.712-.175.675 0 1.013.375t.337.825q0 .425-.25.762a6 6 0 0 1-.6.688q-.7.6-.938.975-.237.375-.287 1.025-.025.3.188.525.211.225.537.225M15 17v1.35q1.775-1.5 2.887-3.512A8.8 8.8 0 0 0 19 10.5q0-2.725-1.887-4.613Q15.225 4 12.5 4T7.888 5.887Q6 7.775 6 10.5t1.888 4.613T12.5 17z" }, null, -1);
var d6 = defineComponent({
  __name: "SfIconContactSupport",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(t4) {
    return (s12, m17) => (openBlock(), createBlock(unref(m2), {
      size: t4.size,
      viewBox: "0 0 24 24",
      "data-testid": "contact-support"
    }, {
      default: withCtx(() => [
        p4
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconCreditCard.vue.mjs
var d7 = createBaseVNode("path", { d: "M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2m0 14H4v-6h16zm0-10H4V6h16z" }, null, -1);
var u14 = defineComponent({
  __name: "SfIconCreditCard",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "credit-card"
    }, {
      default: withCtx(() => [
        d7
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconDelete.vue.mjs
var c3 = createBaseVNode("path", { d: "M7 20q-.824 0-1.412-.587A1.93 1.93 0 0 1 5 18V5a.97.97 0 0 1-.713-.287A.97.97 0 0 1 4 4a.97.97 0 0 1 .287-.713A.97.97 0 0 1 5 3h4q0-.425.288-.713A.97.97 0 0 1 10 2h4a.97.97 0 0 1 .713.287A.97.97 0 0 1 15 3h4q.424 0 .712.287Q20 3.575 20 4a.97.97 0 0 1-.288.713A.97.97 0 0 1 19 5v13q0 .825-.587 1.413A1.93 1.93 0 0 1 17 20zM7 5v13h10V5zm2 10q0 .424.288.712A.97.97 0 0 0 10 16a.97.97 0 0 0 .713-.288A.97.97 0 0 0 11 15V8a.97.97 0 0 0-.287-.713A.97.97 0 0 0 10 7a.97.97 0 0 0-.712.287A.97.97 0 0 0 9 8zm4 0q0 .424.288.712A.97.97 0 0 0 14 16a.97.97 0 0 0 .713-.288A.97.97 0 0 0 15 15V8a.97.97 0 0 0-.287-.713A.97.97 0 0 0 14 7a.97.97 0 0 0-.712.287A.97.97 0 0 0 13 8z" }, null, -1);
var l2 = defineComponent({
  __name: "SfIconDelete",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "delete"
    }, {
      default: withCtx(() => [
        c3
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconDownload.vue.mjs
var l3 = createBaseVNode("path", { d: "M12 15.575q-.2 0-.375-.062a.9.9 0 0 1-.325-.213l-3.6-3.6a.92.92 0 0 1-.288-.7q.014-.4.288-.7.3-.3.712-.312a.93.93 0 0 1 .713.287L11 12.15V5q0-.424.287-.713A.97.97 0 0 1 12 4q.424 0 .713.287Q13 4.576 13 5v7.15l1.875-1.875a.93.93 0 0 1 .713-.287Q16 10 16.3 10.3q.276.3.287.7a.92.92 0 0 1-.287.7l-3.6 3.6q-.15.15-.325.212a1.1 1.1 0 0 1-.375.063M6 20q-.824 0-1.412-.587A1.93 1.93 0 0 1 4 18v-2q0-.424.287-.713A.97.97 0 0 1 5 15q.424 0 .713.287Q6 15.576 6 16v2h12v-2q0-.424.288-.713A.97.97 0 0 1 19 15q.424 0 .712.287.288.288.288.713v2q0 .824-.587 1.413A1.93 1.93 0 0 1 18 20z" }, null, -1);
var _4 = defineComponent({
  __name: "SfIconDownload",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (d26, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "download"
    }, {
      default: withCtx(() => [
        l3
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconEmail.vue.mjs
var c4 = createBaseVNode("path", { d: "M4 20q-.824 0-1.412-.587A1.93 1.93 0 0 1 2 18V6q0-.824.587-1.412A1.93 1.93 0 0 1 4 4h16q.824 0 1.413.588Q22 5.175 22 6v12q0 .824-.587 1.413A1.93 1.93 0 0 1 20 20zM20 8l-7.475 4.675a1.04 1.04 0 0 1-.525.15 1.04 1.04 0 0 1-.525-.15L4 8v10h16zm-8 3 8-5H4zM4 8.25V6.775 6.8v-.013z" }, null, -1);
var z2 = defineComponent({
  __name: "SfIconEmail",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "email"
    }, {
      default: withCtx(() => [
        c4
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconError.vue.mjs
var d8 = createBaseVNode("path", {
  "fill-rule": "evenodd",
  d: "M22 12c0 5.52-4.48 10-10 10S2 17.52 2 12 6.48 2 12 2s10 4.48 10 10M12 7a1 1 0 0 1 1 1v4a1 1 0 1 1-2 0V8a1 1 0 0 1 1-1m0 7.75a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5",
  "clip-rule": "evenodd"
}, null, -1);
var u15 = defineComponent({
  __name: "SfIconError",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "error"
    }, {
      default: withCtx(() => [
        d8
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconExpandLess.vue.mjs
var p5 = createBaseVNode("path", { d: "M17 15a1 1 0 0 1-1.41 0l-3.885-3.875L7.82 15a.998.998 0 0 1-1.41-1.412l4.588-4.587a1 1 0 0 1 1.414 0L17 13.589A1 1 0 0 1 17 15" }, null, -1);
var u16 = defineComponent({
  __name: "SfIconExpandLess",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, d26) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "expand-less"
    }, {
      default: withCtx(() => [
        p5
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconExpandMore.vue.mjs
var p6 = createBaseVNode("path", { d: "M17 9.003a1 1 0 0 0-1.41 0l-3.885 3.876L7.82 9.003a.998.998 0 0 0-1.41 1.411l4.588 4.588a1 1 0 0 0 1.414 0L17 10.414a.997.997 0 0 0 0-1.41" }, null, -1);
var u17 = defineComponent({
  __name: "SfIconExpandMore",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "expand-more"
    }, {
      default: withCtx(() => [
        p6
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconFacebook.vue.mjs
var f10 = createBaseVNode("path", { d: "m17.01 13.25.555-3.62h-3.473V7.282c0-.99.485-1.956 2.04-1.956h1.58V2.245S16.279 2 14.909 2c-2.86 0-4.73 1.734-4.73 4.872V9.63H7v3.62h3.18V22h3.912v-8.75z" }, null, -1);
var l4 = defineComponent({
  __name: "SfIconFacebook",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "facebook"
    }, {
      default: withCtx(() => [
        f10
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconFavorite.vue.mjs
var f11 = createBaseVNode("path", {
  "fill-rule": "evenodd",
  d: "M19.664 4.99c-2.64-1.8-5.9-.96-7.66 1.1-1.76-2.06-5.02-2.91-7.66-1.1-1.4.96-2.28 2.58-2.34 4.29-.14 3.88 3.3 6.99 8.55 11.76l.1.09c.76.69 1.93.69 2.69-.01l.11-.1c5.25-4.76 8.68-7.87 8.55-11.75-.06-1.7-.94-3.32-2.34-4.28m-7.56 14.56-.1.1-.1-.1c-4.76-4.31-7.9-7.16-7.9-10.05 0-2 1.5-3.5 3.5-3.5 1.54 0 3.04.99 3.57 2.36h1.87c.52-1.37 2.02-2.36 3.56-2.36 2 0 3.5 1.5 3.5 3.5 0 2.89-3.14 5.74-7.9 10.05",
  "clip-rule": "evenodd"
}, null, -1);
var u18 = defineComponent({
  __name: "SfIconFavorite",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (s12, d26) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "favorite"
    }, {
      default: withCtx(() => [
        f11
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconFavoriteFilled.vue.mjs
var s5 = createBaseVNode("path", { d: "M10.67 21.133c-5.52-4.95-8.72-7.64-8.67-11.39.04-2.97 2.3-4.39 2.35-4.43 3.61-2.46 6.89.22 7.65 1.11.75-.88 3.99-3.51 7.56-********** 2.23 1.65 2.42 4.12.32 4.28-4.14 7.76-8.65 11.76-.38.34-.86.5-1.34.5-.47 0-.94-.17-1.32-.51" }, null, -1);
var u19 = defineComponent({
  __name: "SfIconFavoriteFilled",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (l18, d26) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "favorite-filled"
    }, {
      default: withCtx(() => [
        s5
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconGridView.vue.mjs
var h5 = createBaseVNode("path", { d: "M5 11q-.825 0-1.413-.588A1.93 1.93 0 0 1 3 9V5q0-.825.587-1.413A1.93 1.93 0 0 1 5 3h4q.825 0 1.413.587Q11 4.175 11 5v4q0 .825-.587 1.412A1.93 1.93 0 0 1 9 11zm0 10q-.825 0-1.413-.587A1.93 1.93 0 0 1 3 19v-4q0-.825.587-1.413A1.93 1.93 0 0 1 5 13h4q.825 0 1.413.587Q11 14.175 11 15v4q0 .825-.587 1.413A1.93 1.93 0 0 1 9 21zm10-10q-.825 0-1.412-.588A1.92 1.92 0 0 1 13 9V5q0-.825.588-1.413A1.93 1.93 0 0 1 15 3h4q.825 0 1.413.587Q21 4.175 21 5v4q0 .825-.587 1.412A1.93 1.93 0 0 1 19 11zm0 10q-.825 0-1.412-.587A1.93 1.93 0 0 1 13 19v-4q0-.825.588-1.413A1.93 1.93 0 0 1 15 13h4q.825 0 1.413.587Q21 14.175 21 15v4q0 .825-.587 1.413A1.93 1.93 0 0 1 19 21zM5 9h4V5H5zm10 0h4V5h-4zm0 10h4v-4h-4zM5 19h4v-4H5z" }, null, -1);
var d9 = defineComponent({
  __name: "SfIconGridView",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (s12, c20) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "grid-view"
    }, {
      default: withCtx(() => [
        h5
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconHelp.vue.mjs
var c5 = createBaseVNode("path", { d: "M11.95 18q.525 0 .888-.363.362-.362.362-.887t-.362-.887a1.2 1.2 0 0 0-.888-.363q-.525 0-.888.363a1.2 1.2 0 0 0-.362.887q0 .525.362.887.363.363.888.363m.15-10.3q.7 0 1.125.387.425.388.425 1.013 0 .424-.287.862a5 5 0 0 1-.813.913q-.75.65-1.1 1.25t-.35 1.2q0 .35.263.587A.904.904 0 0 0 12.6 13.9q.275-.25.35-.625.075-.425.338-.787.262-.363.862-.938.776-.725 1.088-1.325T15.55 8.9q0-1.275-.962-2.088Q13.625 6 12.1 6q-1.05 0-1.862.4a2.84 2.84 0 0 0-1.263 1.225 1 1 0 0 0-.125.637q.05.314.35.513a.97.97 0 0 0 .713.125 1 1 0 0 0 .637-.425q.274-.375.663-.575.387-.2.887-.2M12 22a9.7 9.7 0 0 1-3.875-.788 10.2 10.2 0 0 1-3.187-2.137 10 10 0 0 1-2.15-3.175A9.7 9.7 0 0 1 2 12q0-2.075.788-3.9a10 10 0 0 1 2.15-3.175Q6.3 3.575 8.125 2.787A9.7 9.7 0 0 1 12 2q2.1 0 3.925.787A10.1 10.1 0 0 1 19.1 4.925q1.35 1.35 2.125 3.175A9.9 9.9 0 0 1 22 12a9.9 9.9 0 0 1-.775 3.9 9.9 9.9 0 0 1-2.125 3.175q-1.35 1.35-3.175 2.137Q14.1 22 12 22" }, null, -1);
var d10 = defineComponent({
  __name: "SfIconHelp",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (p14, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "help"
    }, {
      default: withCtx(() => [
        c5
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconHome.vue.mjs
var c6 = createBaseVNode("path", { d: "M6 19h3v-6h6v6h3v-9l-6-4.5L6 10zm0 2q-.824 0-1.412-.587A1.93 1.93 0 0 1 4 19v-9q0-.476.213-.9.212-.425.587-.7l6-4.5a2.1 2.1 0 0 1 .575-.3q.3-.1.625-.1t.625.1.575.3l6 4.5q.375.275.588.7T20 10v9q0 .825-.587 1.413A1.93 1.93 0 0 1 18 21h-5v-6h-2v6z" }, null, -1);
var d11 = defineComponent({
  __name: "SfIconHome",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "home"
    }, {
      default: withCtx(() => [
        c6
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconIndeterminateCheckBox.vue.mjs
var m5 = createBaseVNode("path", {
  "fill-rule": "evenodd",
  d: "M3 5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2zm14 7a1 1 0 0 1-1 1H8a1 1 0 1 1 0-2h8a1 1 0 0 1 1 1",
  "clip-rule": "evenodd"
}, null, -1);
var u20 = defineComponent({
  __name: "SfIconIndeterminateCheckBox",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (s12, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "indeterminate-check-box"
    }, {
      default: withCtx(() => [
        m5
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconInfo.vue.mjs
var d12 = createBaseVNode("path", { d: "M13.25 8a1.25 1.25 0 1 1-2.5 0 1.25 1.25 0 0 1 2.5 0M11 16a1 1 0 1 0 2 0v-4a1 1 0 1 0-2 0z" }, null, -1);
var f12 = createBaseVNode("path", {
  "fill-rule": "evenodd",
  d: "M2 12C2 6.48 6.48 2 12 2s10 4.48 10 10-4.48 10-10 10S2 17.52 2 12m2 0c0 4.41 3.59 8 8 8s8-3.59 8-8-3.59-8-8-8-8 3.59-8 8",
  "clip-rule": "evenodd"
}, null, -1);
var h6 = defineComponent({
  __name: "SfIconInfo",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(t4) {
    return (l18, p14) => (openBlock(), createBlock(unref(m2), {
      size: t4.size,
      viewBox: "0 0 24 24",
      "data-testid": "info"
    }, {
      default: withCtx(() => [
        d12,
        f12
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconInstagram.vue.mjs
var m6 = createBaseVNode("path", { d: "M12.003 6.871a5.12 5.12 0 0 0-5.127 5.127 5.12 5.12 0 0 0 5.127 5.126 5.12 5.12 0 0 0 5.126-5.126 5.12 5.12 0 0 0-5.126-5.127m0 8.46a3.34 3.34 0 0 1-3.333-3.333 3.336 3.336 0 0 1 3.333-3.333 3.336 3.336 0 0 1 3.333 3.333 3.34 3.34 0 0 1-3.333 3.333m6.532-8.67c0 .665-.536 1.196-1.196 1.196a1.196 1.196 0 1 1 1.196-1.196m3.395 1.214c-.076-1.602-.442-3.02-1.615-4.19-1.169-1.169-2.588-1.534-4.19-1.615-1.65-.093-6.599-.093-8.25 0-1.597.076-3.016.442-4.19 1.611-1.173 1.169-1.534 2.588-1.614 4.19-.094 1.65-.094 6.599 0 8.25.076 1.601.441 3.02 1.615 4.19 1.173 1.168 2.588 1.534 4.19 1.614 1.65.094 6.599.094 8.25 0 1.601-.076 3.02-.442 4.189-1.615 1.169-1.169 1.535-2.588 1.615-4.19.094-1.65.094-6.594 0-8.245m-2.133 10.017a3.38 3.38 0 0 1-1.9 1.9c-1.316.523-4.44.402-5.894.402-1.455 0-4.583.116-5.894-.401a3.37 3.37 0 0 1-1.901-1.901c-.522-1.316-.402-4.44-.402-5.894 0-1.455-.116-4.582.402-5.894a3.37 3.37 0 0 1 1.9-1.901c1.317-.522 4.44-.402 5.895-.402s4.582-.116 5.894.402a3.37 3.37 0 0 1 1.9 1.9c.523 1.317.402 4.44.402 5.895s.12 4.582-.402 5.894" }, null, -1);
var u21 = defineComponent({
  __name: "SfIconInstagram",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "instagram"
    }, {
      default: withCtx(() => [
        m6
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconLanguage.vue.mjs
var h7 = createBaseVNode("path", {
  "fill-rule": "evenodd",
  d: "M12.534 21.98a10.032 10.032 0 0 0 8.708-13.84 9.95 9.95 0 0 0-9.661-6.132 9.8 9.8 0 0 0-9.58 9.787 10.127 10.127 0 0 0 9.412 10.177q.143.03.289.016.127.006.257.008h.017q.187 0 .373-.007.093.005.185-.009M14.81 4.493a13.4 13.4 0 0 1 1.604 3.502h2.535a7.95 7.95 0 0 0-4.139-3.502m-2.805-.421c1.063 1.236 1.83 2.553 2.31 3.923h-4.62c.48-1.37 1.247-2.687 2.31-3.923m2.8 5.923h-5.6a11 11 0 0 0-.113 1.565q-.001 1.323.319 2.696h5.188a11.8 11.8 0 0 0 .319-2.697q0-.789-.112-1.564m-.84 6.261h-3.92a17.6 17.6 0 0 0 1.96 3.571 17.8 17.8 0 0 0 1.96-3.57m-6.6-2a13.7 13.7 0 0 1-.273-2.696q0-.79.095-1.565H4.214A7.8 7.8 0 0 0 4 11.801v.006a8.1 8.1 0 0 0 .358 2.45zm-2.079 2h2.624c.38 1.101.893 2.199 1.532 3.285a8.13 8.13 0 0 1-4.156-3.285m10.813 0h2.66a8.03 8.03 0 0 1-4.21 3.315 18.2 18.2 0 0 0 1.55-3.315m3.569-2h-3.022q.271-1.354.272-2.697 0-.79-.095-1.564h2.935a8 8 0 0 1 .243 1.965v.002a8 8 0 0 1-.333 2.294M9.233 4.441a13.4 13.4 0 0 0-1.637 3.554H4.995a7.8 7.8 0 0 1 4.238-3.554M12 2h.003v.086l-.005-.002V2z",
  "clip-rule": "evenodd"
}, null, -1);
var u22 = defineComponent({
  __name: "SfIconLanguage",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (l18, s12) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "language"
    }, {
      default: withCtx(() => [
        h7
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconLocalShipping.vue.mjs
var s6 = createBaseVNode("path", { d: "M6 20a2.9 2.9 0 0 1-2.125-.875A2.9 2.9 0 0 1 3 17q-.824 0-1.412-.587A1.93 1.93 0 0 1 1 15V6q0-.824.588-1.412A1.92 1.92 0 0 1 3 4h12q.825 0 1.413.588Q17 5.175 17 6v2h2.5q.25 0 .45.1t.35.3l2.5 3.325a.9.9 0 0 1 .15.275q.05.15.05.325V16q0 .424-.288.712A.97.97 0 0 1 22 17h-1q0 1.25-.875 2.125A2.9 2.9 0 0 1 18 20a2.9 2.9 0 0 1-2.125-.875A2.9 2.9 0 0 1 15 17H9q0 1.25-.875 2.125A2.9 2.9 0 0 1 6 20m0-2a.97.97 0 0 0 .713-.288A.97.97 0 0 0 7 17a.97.97 0 0 0-.287-.712A.97.97 0 0 0 6 16a.97.97 0 0 0-.713.288A.97.97 0 0 0 5 17q0 .424.287.712Q5.575 18 6 18M3 6v9h.8q.425-.45.975-.725A2.7 2.7 0 0 1 6 14q.675 0 1.225.275T8.2 15H15V6zm15 12q.424 0 .712-.288A.97.97 0 0 0 19 17a.97.97 0 0 0-.288-.712A.97.97 0 0 0 18 16a.97.97 0 0 0-.712.288A.97.97 0 0 0 17 17q0 .424.288.712A.97.97 0 0 0 18 18m-1-5h4.25L19 10h-2z" }, null, -1);
var l5 = defineComponent({
  __name: "SfIconLocalShipping",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (A5, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "local-shipping"
    }, {
      default: withCtx(() => [
        s6
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconLocal_fire_department.vue.mjs
var _5 = createBaseVNode("path", { d: "M12.359 21.42q-3.35 0-5.675-2.325T4.359 13.42q0-2.824 1.675-5.425t4.6-4.55q.55-.375 1.137-.037.588.337.588 1.012v1.3q0 .85.587 1.425.588.576 1.438.575.425 0 .812-.187t.688-.538a.85.85 0 0 1 .512-.312.72.72 0 0 1 .588.137 7.94 7.94 0 0 1 2.475 2.875q.9 1.75.9 3.725 0 3.35-2.325 5.675t-5.675 2.325m-6-8q0 1.3.525 2.463t1.5 2.037a1 1 0 0 1-.025-.225v-.225q0-.8.3-1.5t.875-1.275l2.825-2.775 2.825 2.775q.575.575.875 1.275t.3 1.5v.225q0 .1-.025.225a6.1 6.1 0 0 0 1.5-2.037q.525-1.162.525-2.463 0-1.25-.463-2.362A6.1 6.1 0 0 0 16.56 9.07q-.5.326-1.05.488a4 4 0 0 1-1.125.162q-1.55 0-2.688-1.025a3.9 3.9 0 0 1-1.312-2.525q-1.95 1.65-2.988 3.513Q6.36 11.545 6.36 13.42m6 1.3-1.425 1.4a2 2 0 0 0-.425.625q-.15.35-.15.725 0 .8.587 1.375.588.576 1.413.575.824 0 1.412-.575.588-.574.588-1.375 0-.4-.15-.737-.15-.338-.425-.613z" }, null, -1);
var d13 = defineComponent({
  __name: "SfIconLocal_fire_department",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, q12) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "local_fire_department"
    }, {
      default: withCtx(() => [
        _5
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconLocationOn.vue.mjs
var l6 = createBaseVNode("path", { d: "M10 10c0 1.1.9 2 2 2s2-.9 2-2-.9-2-2-2-2 .9-2 2" }, null, -1);
var d14 = createBaseVNode("path", {
  "fill-rule": "evenodd",
  d: "M4 10.2c0 3.18 2.45 6.92 7.34 ***********.95.33 1.33 0C17.55 17.12 20 13.38 20 10.2 20 5.22 16.2 2 12 2s-8 3.22-8 8.2m2 0C6 6.386 8.842 4 12 4s6 2.386 6 6.2c0 1.073-.416 2.424-1.51 4.097-.986 1.51-2.465 3.191-4.486 5.042-2.025-1.851-3.506-3.532-4.494-5.042C6.416 12.623 6 11.273 6 10.2",
  "clip-rule": "evenodd"
}, null, -1);
var h8 = defineComponent({
  __name: "SfIconLocationOn",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(t4) {
    return (f29, p14) => (openBlock(), createBlock(unref(m2), {
      size: t4.size,
      viewBox: "0 0 24 24",
      "data-testid": "location-on"
    }, {
      default: withCtx(() => [
        l6,
        d14
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconLocationOnFilled.vue.mjs
var d15 = createBaseVNode("path", {
  "fill-rule": "evenodd",
  d: "M12 2c-4.2 0-8 3.22-8 8.2 0 3.18 2.45 6.92 7.34 ***********.95.33 1.33 0C17.55 17.12 20 13.38 20 10.2 20 5.22 16.2 2 12 2m0 10c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2",
  "clip-rule": "evenodd"
}, null, -1);
var u23 = defineComponent({
  __name: "SfIconLocationOnFilled",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (s12, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "location-on-filled"
    }, {
      default: withCtx(() => [
        d15
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconLock.vue.mjs
var f13 = createBaseVNode("path", { d: "M6 23q-.824 0-1.412-.587A1.93 1.93 0 0 1 4 21V11q0-.825.588-1.413A1.93 1.93 0 0 1 6 9h1V7q0-2.075 1.463-3.538T12 2t3.538 1.462Q17 4.925 17 7v2h1q.825 0 1.413.587Q20 10.175 20 11v10q0 .825-.587 1.413A1.93 1.93 0 0 1 18 23zm6-5q.825 0 1.413-.587Q14 16.825 14 16t-.587-1.413A1.93 1.93 0 0 0 12 14q-.825 0-1.412.587A1.93 1.93 0 0 0 10 16q0 .825.588 1.413Q11.175 18 12 18M9 9h6V7q0-1.25-.875-2.125A2.9 2.9 0 0 0 12 4q-1.25 0-2.125.875A2.9 2.9 0 0 0 9 7z" }, null, -1);
var l7 = defineComponent({
  __name: "SfIconLock",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "lock"
    }, {
      default: withCtx(() => [
        f13
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconLockOpen.vue.mjs
var m7 = createBaseVNode("path", { d: "M6 22q-.824 0-1.412-.587A1.93 1.93 0 0 1 4 20V10q0-.825.588-1.413A1.93 1.93 0 0 1 6 8h9V6q0-1.25-.875-2.125A2.9 2.9 0 0 0 12 3a2.87 2.87 0 0 0-1.812.612A3 3 0 0 0 9.125 5.15q-.125.375-.387.612A.9.9 0 0 1 8.125 6q-.5 0-.8-.338a.81.81 0 0 1-.2-.762Q7.5 3.225 8.85 2.112T12 1q2.075 0 3.538 1.462Q17 3.925 17 6v2h1q.825 0 1.413.587Q20 9.175 20 10v10q0 .825-.587 1.413A1.93 1.93 0 0 1 18 22zm0-2h12V10H6zm6-3q.825 0 1.413-.587Q14 15.825 14 15t-.587-1.413A1.93 1.93 0 0 0 12 13q-.825 0-1.412.587A1.93 1.93 0 0 0 10 15q0 .825.588 1.413Q11.175 17 12 17" }, null, -1);
var l8 = defineComponent({
  __name: "SfIconLockOpen",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (p14, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "lock-open"
    }, {
      default: withCtx(() => [
        m7
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconLogin.vue.mjs
var l9 = createBaseVNode("path", { d: "M9.325 16.275a1.1 1.1 0 0 1-.275-.738q0-.412.275-.687l1.85-1.85H4a.97.97 0 0 1-.712-.288A.97.97 0 0 1 3 12q0-.425.288-.713A.97.97 0 0 1 4 11h7.175l-1.85-1.85q-.3-.3-.3-.712 0-.413.3-.713.275-.3.688-.3t.687.275l3.6 3.6q.15.15.213.325.062.175.062.375t-.062.375a.9.9 0 0 1-.213.325l-3.6 3.6q-.3.3-.7.275a1 1 0 0 1-.675-.3M13 21a.97.97 0 0 1-.712-.288A.97.97 0 0 1 12 20q0-.424.288-.712A.97.97 0 0 1 13 19h6V5h-6a.97.97 0 0 1-.712-.288A.97.97 0 0 1 12 4q0-.425.288-.713A.97.97 0 0 1 13 3h6q.825 0 1.413.587Q21 4.175 21 5v14q0 .825-.587 1.413A1.93 1.93 0 0 1 19 21z" }, null, -1);
var q6 = defineComponent({
  __name: "SfIconLogin",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "login"
    }, {
      default: withCtx(() => [
        l9
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconLogout.vue.mjs
var l10 = createBaseVNode("path", { d: "M15.325 16.275a1.1 1.1 0 0 1-.275-.738q0-.412.275-.687l1.85-1.85H10a.97.97 0 0 1-.712-.288A.97.97 0 0 1 9 12q0-.425.288-.713A.97.97 0 0 1 10 11h7.175l-1.85-1.85q-.3-.3-.3-.712 0-.413.3-.713.275-.3.688-.3t.687.275l3.6 3.6q.15.15.213.325.062.175.062.375t-.062.375a.9.9 0 0 1-.213.325l-3.6 3.6q-.325.325-.712.287a1.05 1.05 0 0 1-.663-.312M5 21q-.825 0-1.413-.587A1.93 1.93 0 0 1 3 19V5q0-.825.587-1.413A1.93 1.93 0 0 1 5 3h6a.97.97 0 0 1 .713.287A.97.97 0 0 1 12 4q0 .424-.287.712A.97.97 0 0 1 11 5H5v14h6q.425 0 .713.288A.97.97 0 0 1 12 20q0 .424-.287.712A.97.97 0 0 1 11 21z" }, null, -1);
var u24 = defineComponent({
  __name: "SfIconLogout",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "logout"
    }, {
      default: withCtx(() => [
        l10
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconMenu.vue.mjs
var m8 = createBaseVNode("path", { d: "M4 18a1 1 0 1 1 0-2h16a1 1 0 1 1 0 2zm0-5a1 1 0 1 1 0-2h16a1 1 0 1 1 0 2zM3 7a1 1 0 0 1 1-1h16a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1" }, null, -1);
var l11 = defineComponent({
  __name: "SfIconMenu",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "menu"
    }, {
      default: withCtx(() => [
        m8
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconMoreHoriz.vue.mjs
var c7 = createBaseVNode("path", { d: "M12 14a2 2 0 1 1 .001-4.001A2 2 0 0 1 12 14m4-2a2 2 0 1 0 4.001-.001A2 2 0 0 0 16 12m-8 0a2 2 0 1 0-4.001.001A2 2 0 0 0 8 12" }, null, -1);
var u25 = defineComponent({
  __name: "SfIconMoreHoriz",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "more-horiz"
    }, {
      default: withCtx(() => [
        c7
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconMoreVert.vue.mjs
var c8 = createBaseVNode("path", { d: "M14 12a2 2 0 1 1-4.001-.001A2 2 0 0 1 14 12m-2-4a2 2 0 1 0-.001-4.001A2 2 0 0 0 12 8m0 8a2 2 0 1 0 .001 4.001A2 2 0 0 0 12 16" }, null, -1);
var u26 = defineComponent({
  __name: "SfIconMoreVert",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "more-vert"
    }, {
      default: withCtx(() => [
        c8
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconOpenInNew.vue.mjs
var p7 = createBaseVNode("path", { d: "M19 19H5V5h6a1 1 0 1 0 0-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14c1.1 0 2-.9 2-2v-6a1 1 0 1 0-2 0zM15 3a1 1 0 1 0 0 2h2.59l-9.125 9.125a.997.997 0 0 0 1.41 1.41L19 6.41V9a1 1 0 1 0 2 0V3z" }, null, -1);
var u27 = defineComponent({
  __name: "SfIconOpenInNew",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "open-in-new"
    }, {
      default: withCtx(() => [
        p7
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconOpenSource.vue.mjs
var p8 = createBaseVNode("path", { d: "M12 2C5.913 2 1 6.913 1 13c0 3.887 1.98 7.407 5.28 9.46.073.073.22.073.293.073.074 0 .147-.073.22-.146l3.447-5.647a.387.387 0 0 0-.147-.513c-1.1-.66-1.76-1.834-1.76-3.154A3.63 3.63 0 0 1 12 9.407a3.63 3.63 0 0 1 3.667 3.666c0 1.394-.807 2.64-1.98 3.3l-.22.22c0 .074 0 .22.073.294l3.447 5.646c.073.147.22.147.293.147s.147 0 .147-.073C20.873 20.627 23 16.96 23 13.073 23 6.987 18.087 2 12 2" }, null, -1);
var d16 = defineComponent({
  __name: "SfIconOpenSource",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "open-source"
    }, {
      default: withCtx(() => [
        p8
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconPackage.vue.mjs
var s7 = createBaseVNode("path", { d: "m10 9.75 1.325-.65a1.6 1.6 0 0 1 1.35 0L14 9.75V5h-4zM8 17a.97.97 0 0 1-.713-.288A.97.97 0 0 1 7 16a.97.97 0 0 1 .287-.713A.97.97 0 0 1 8 15h3a.97.97 0 0 1 .713.287A.97.97 0 0 1 12 16q0 .424-.287.712A.97.97 0 0 1 11 17zm-3 4q-.825 0-1.413-.587A1.93 1.93 0 0 1 3 19V5q0-.825.587-1.413A1.93 1.93 0 0 1 5 3h14q.825 0 1.413.587Q21 4.175 21 5v14q0 .825-.587 1.413A1.93 1.93 0 0 1 19 21zm0-2h14V5h-3v6.375q0 .574-.475.862a.95.95 0 0 1-.975.038L12 11l-2.55 1.275a.95.95 0 0 1-.975-.038Q8 11.95 8 11.375V5H5z" }, null, -1);
var l12 = defineComponent({
  __name: "SfIconPackage",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "package"
    }, {
      default: withCtx(() => [
        s7
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconPercent.vue.mjs
var q7 = createBaseVNode("path", { d: "M8 11a3.37 3.37 0 0 1-2.475-1.025A3.37 3.37 0 0 1 4.5 7.5q0-1.45 1.025-2.475A3.37 3.37 0 0 1 8 4q1.45 0 2.475 1.025A3.37 3.37 0 0 1 11.5 7.5q0 1.45-1.025 2.475A3.37 3.37 0 0 1 8 11m0-2q.624 0 1.063-.437Q9.5 8.124 9.5 7.5t-.437-1.062A1.45 1.45 0 0 0 8 6q-.625 0-1.062.438A1.45 1.45 0 0 0 6.5 7.5q0 .624.438 1.063Q7.374 9 8 9m9 11a3.37 3.37 0 0 1-2.475-1.025A3.37 3.37 0 0 1 13.5 16.5q0-1.45 1.025-2.475A3.37 3.37 0 0 1 17 13q1.45 0 2.475 1.025A3.37 3.37 0 0 1 20.5 16.5q0 1.45-1.025 2.475A3.37 3.37 0 0 1 17 20m0-2q.625 0 1.063-.437.437-.438.437-1.063t-.437-1.062A1.45 1.45 0 0 0 17 15q-.625 0-1.062.438A1.45 1.45 0 0 0 15.5 16.5q0 .625.438 1.063Q16.375 18 17 18M5.2 19.3a.95.95 0 0 1-.275-.7q0-.425.275-.7L18.4 4.7a.95.95 0 0 1 .7-.275q.425 0 .7.275a.95.95 0 0 1 .275.7.95.95 0 0 1-.275.7L6.6 19.3a.95.95 0 0 1-.7.275.95.95 0 0 1-.7-.275" }, null, -1);
var d17 = defineComponent({
  __name: "SfIconPercent",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (s12, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "percent"
    }, {
      default: withCtx(() => [
        q7
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconPerson.vue.mjs
var m9 = createBaseVNode("path", { d: "M12 12q-1.65 0-2.825-1.175T8 8t1.175-2.825T12 4t2.825 1.175T16 8t-1.175 2.825T12 12m6 8H6q-.824 0-1.412-.587A1.93 1.93 0 0 1 4 18v-.8q0-.85.438-1.563A2.9 2.9 0 0 1 5.6 14.55a15 15 0 0 1 3.15-1.163A13.8 13.8 0 0 1 12 13q1.65 0 3.25.387 1.6.388 3.15 1.163.724.375 1.162 1.087T20 17.2v.8q0 .825-.587 1.413A1.93 1.93 0 0 1 18 20M6 18h12v-.8a.94.94 0 0 0-.137-.5 1 1 0 0 0-.363-.35q-1.35-.675-2.725-1.013a11.6 11.6 0 0 0-5.55 0Q7.85 15.675 6.5 16.35a.97.97 0 0 0-.5.85zm6-8q.825 0 1.413-.588Q14 8.825 14 8q0-.824-.587-1.412A1.93 1.93 0 0 0 12 6q-.825 0-1.412.588A1.92 1.92 0 0 0 10 8q0 .825.588 1.412Q11.175 10 12 10" }, null, -1);
var l13 = defineComponent({
  __name: "SfIconPerson",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "person"
    }, {
      default: withCtx(() => [
        m9
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconPinterest.vue.mjs
var f14 = createBaseVNode("path", { d: "M12.175 2C8.063 2 4 4.74 4 9.177c0 2.82 1.587 4.424 2.549 4.424.396 0 .625-1.106.625-1.419 0-.373-.95-1.166-.95-2.717 0-3.221 2.452-5.505 5.626-5.505 2.729 0 4.749 1.55 4.749 4.4 0 2.127-.854 6.118-3.619 6.118-.998 0-1.851-.721-1.851-1.755 0-1.515 1.058-2.981 1.058-4.544 0-2.653-3.763-2.172-3.763 1.034 0 .673.084 1.418.385 2.032-.553 2.38-1.683 5.926-1.683 8.378 0 .758.108 1.503.18 2.26.136.153.068.137.276.06 2.02-2.764 1.948-3.305 2.862-6.924.492.938 1.767 1.443 2.776 1.443 4.256 0 6.167-4.148 6.167-7.886C19.388 4.596 15.95 2 12.176 2" }, null, -1);
var u28 = defineComponent({
  __name: "SfIconPinterest",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (p14, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "pinterest"
    }, {
      default: withCtx(() => [
        f14
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconPublishedWithChanges.vue.mjs
var c9 = createBaseVNode("path", { d: "M14.175 2.225q3.375.75 5.6 3.45T22 12q0 2.275-.913 4.2a10 10 0 0 1-2.487 3.3H20q.424 0 .712.288A.97.97 0 0 1 21 20.5q0 .425-.288.712A.97.97 0 0 1 20 21.5h-4a.97.97 0 0 1-.712-.288A.97.97 0 0 1 15 20.5v-4q0-.424.288-.713A.97.97 0 0 1 16 15.5q.424 0 .712.287.288.288.288.713v1.725a8.4 8.4 0 0 0 2.188-2.725Q20 13.9 20 12q0-2.875-1.762-5.013T13.8 4.225a1 1 0 0 1-.575-.35A.96.96 0 0 1 13 3.25q0-.5.35-.825a.83.83 0 0 1 .825-.2m-4.35 19.55q-3.375-.75-5.6-3.45T2 12q0-2.275.913-4.2A10 10 0 0 1 5.4 4.5H4a.97.97 0 0 1-.712-.288A.97.97 0 0 1 3 3.5q0-.424.288-.713A.97.97 0 0 1 4 2.5h4a.97.97 0 0 1 .713.287A.97.97 0 0 1 9 3.5v4a.97.97 0 0 1-.287.713A.97.97 0 0 1 8 8.5a.97.97 0 0 1-.713-.287A.97.97 0 0 1 7 7.5V5.775a8.1 8.1 0 0 0-2.188 2.713Q4 10.101 4 12q0 2.876 1.763 5.012 1.762 2.138 4.437 2.763a1 1 0 0 1 .575.35.96.96 0 0 1 .225.625q0 .5-.35.825a.83.83 0 0 1-.825.2m.75-5.6q-.2 0-.375-.062a.9.9 0 0 1-.325-.213L7.05 13.05a.98.98 0 0 1-.287-.688.93.93 0 0 1 .287-.712.95.95 0 0 1 .7-.275q.425 0 .7.275l2.125 2.125L15.55 8.8q.3-.3.7-.288t.7.313q.275.3.288.7a.92.92 0 0 1-.288.7L11.275 15.9q-.15.15-.325.213a1.1 1.1 0 0 1-.375.062" }, null, -1);
var l14 = defineComponent({
  __name: "SfIconPublishedWithChanges",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "published-with-changes"
    }, {
      default: withCtx(() => [
        c9
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconRadioButtonChecked.vue.mjs
var d18 = createBaseVNode("path", { d: "M12 17q2.075 0 3.538-1.463Q17 14.075 17 12t-1.462-3.538Q14.075 7 12 7T8.463 8.462Q7 9.925 7 12t1.463 3.537T12 17m0 5a9.7 9.7 0 0 1-3.9-.788 10.1 10.1 0 0 1-3.175-2.137q-1.35-1.35-2.137-3.175A9.7 9.7 0 0 1 2 12q0-2.075.788-3.9a10.1 10.1 0 0 1 2.137-3.175q1.35-1.35 3.175-2.138A9.7 9.7 0 0 1 12 2q2.075 0 3.9.787a10.1 10.1 0 0 1 3.175 2.138q1.35 1.35 2.137 3.175A9.7 9.7 0 0 1 22 12a9.7 9.7 0 0 1-.788 3.9 10.1 10.1 0 0 1-2.137 3.175q-1.35 1.35-3.175 2.137A9.7 9.7 0 0 1 12 22m0-2q3.35 0 5.675-2.325T20 12t-2.325-5.675T12 4 6.325 6.325 4 12t2.325 5.675T12 20" }, null, -1);
var l15 = defineComponent({
  __name: "SfIconRadioButtonChecked",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "radio-button-checked"
    }, {
      default: withCtx(() => [
        d18
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconRadioButtonUnchecked.vue.mjs
var d19 = createBaseVNode("path", { d: "M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8" }, null, -1);
var l16 = defineComponent({
  __name: "SfIconRadioButtonUnchecked",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "radio-button-unchecked"
    }, {
      default: withCtx(() => [
        d19
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconReact.vue.mjs
var r = createBaseVNode("path", { d: "M13.96 11.952c0 1.099-.877 1.99-1.96 1.99s-1.96-.891-1.96-1.99.877-1.989 1.96-1.989 1.96.89 1.96 1.99" }, null, -1);
var q8 = createBaseVNode("path", {
  "fill-rule": "evenodd",
  d: "M12 4.467a15 15 0 0 0-.958-.868c-.818-.674-1.63-1.17-2.388-1.418-.758-.25-1.516-.266-2.154.108S5.497 3.337 5.33 4.127c-.166.79-.149 1.752.018 2.807q.097.613.262 1.276-.649.186-1.22.407c-.985.382-1.814.847-2.405 1.389-.592.542-.985 1.199-.985 1.946 0 .748.393 1.405.985 1.947.591.542 1.42 1.007 2.404 1.388q.573.222 1.22.408-.165.661-.261 1.276c-.167 1.055-.184 2.016-.017 2.807s.53 1.464 1.169 1.838c.638.374 1.396.357 2.154.108s1.57-.744 2.388-1.418q.475-.392.958-.868.483.476.958.868c.817.674 1.63 1.17 2.388 1.418.758.249 1.516.266 2.154-.108s1.003-1.048 1.17-1.838c.166-.79.149-1.752-.018-2.807a15 15 0 0 0-.262-1.276q.649-.186 1.22-.408c.985-.38 1.814-.847 2.405-1.388.592-.542.985-1.199.985-1.947 0-.747-.393-1.404-.985-1.946-.591-.542-1.42-1.007-2.404-1.389a14 14 0 0 0-1.22-.407q.165-.663.261-1.276c.167-1.055.184-2.016.017-2.807S18.14 2.663 17.5 2.29c-.638-.374-1.396-.357-2.154-.108s-1.57.744-2.388 1.418q-.475.391-.958.868M8.36 3.104c-.607-.2-1.06-.164-1.382.025s-.579.569-.712 1.201-.13 1.464.026 2.45q.09.57.245 1.191a23 23 0 0 1 2.934-.463 23 23 0 0 1 1.862-2.346q-.453-.446-.895-.81c-.764-.63-1.472-1.05-2.078-1.248M12 5.86c-.428.472-.858.995-1.281 1.561a28 28 0 0 1 2.562 0A21 21 0 0 0 12 5.86m-3.228 2.7a21 21 0 0 0-1.974.345c.19.613.421 1.251.693 1.906a29 29 0 0 1 1.281-2.25M8 11.952a27.4 27.4 0 0 1 2-3.515 26.5 26.5 0 0 1 4.002 0 27 27 0 0 1 2 3.515 27.4 27.4 0 0 1-2 3.516 26.5 26.5 0 0 1-4.002 0 27 27 0 0 1-2-3.516m-1.058 0A23 23 0 0 1 5.87 9.144q-.607.174-1.138.38c-.92.356-1.632.769-2.105 1.202-.474.434-.67.849-.67 1.226 0 .378.196.793.67 1.226.473.434 1.185.846 2.105 1.203q.53.206 1.138.38a24 24 0 0 1 1.072-2.809M6.798 15c.19-.613.421-1.252.693-1.907a29 29 0 0 0 1.281 2.251A21 21 0 0 1 6.798 15m-.261.935q-.155.62-.245 1.19c-.156.987-.16 1.818-.026 2.45.133.633.39 1.013.712 1.202.323.188.775.224 1.382.025.606-.2 1.314-.618 2.079-1.248q.44-.364.894-.81a23 23 0 0 1-1.862-2.347 23 23 0 0 1-2.934-.462m6.13 2.809q.453.446.895.81c.764.63 1.472 1.049 2.078 1.248.607.199 1.06.163 1.382-.025.322-.19.579-.569.712-1.201s.13-1.464-.026-2.45q-.09-.57-.245-1.191a23 23 0 0 1-2.934.462 23 23 0 0 1-1.862 2.346m.614-2.26c-.423.567-.853 1.09-1.281 1.562a21 21 0 0 1-1.281-1.562 28 28 0 0 0 2.562 0m1.947-1.14A21 21 0 0 0 17.202 15a22 22 0 0 0-.693-1.907 29 29 0 0 1-1.281 2.251m1.83-3.39c.439.964.797 1.91 1.073 2.808q.607-.174 1.138-.38c.92-.357 1.632-.77 2.105-1.203s.67-.848.67-1.226c0-.377-.196-.792-.67-1.226-.473-.433-1.184-.846-2.105-1.202a13 13 0 0 0-1.138-.38 23 23 0 0 1-1.072 2.808m.405-3.982a23 23 0 0 0-2.934-.463 23 23 0 0 0-1.862-2.346q.453-.446.895-.81c.764-.63 1.472-1.049 2.078-1.248.607-.2 1.06-.164 1.382.025s.579.569.712 1.201.13 1.464-.026 2.45q-.09.57-.245 1.191m-.261.935a22 22 0 0 1-.693 1.906 29 29 0 0 0-1.281-2.25c.695.088 1.356.204 1.974.344",
  "clip-rule": "evenodd"
}, null, -1);
var u29 = defineComponent({
  __name: "SfIconReact",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (d26, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "react"
    }, {
      default: withCtx(() => [
        r,
        q8
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconRemove.vue.mjs
var m10 = createBaseVNode("path", { d: "M6 13a.97.97 0 0 1-.713-.288A.97.97 0 0 1 5 12a.97.97 0 0 1 .287-.713A.97.97 0 0 1 6 11h12q.424 0 .712.287.288.288.288.713 0 .424-.288.712A.97.97 0 0 1 18 13z" }, null, -1);
var u30 = defineComponent({
  __name: "SfIconRemove",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "remove"
    }, {
      default: withCtx(() => [
        m10
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconRemoveShoppingCart.vue.mjs
var s8 = createBaseVNode("path", { d: "m15.823 12.995-2-2h1.725l2.75-5H8.823l-2-2h13.125q.575 0 .887.488.313.487.013 1.062l-3.55 6.4a2.07 2.07 0 0 1-.712.775q-.438.275-.763.275m-8.825 9q-.824 0-1.412-.588a1.93 1.93 0 0 1-.588-1.412q0-.824.588-1.413a1.93 1.93 0 0 1 1.412-.587q.824 0 1.413.587.587.588.587 1.413t-.587 1.412a1.93 1.93 0 0 1-1.413.588m12.8.6-5.65-5.6h-6.55q-1.1 0-1.675-.937-.575-.939-.075-1.963l1.05-2.15-1.8-4.05L1.373 4.17a.9.9 0 0 1-.262-.687.98.98 0 0 1 .287-.688.95.95 0 0 1 .7-.275q.425 0 .7.275L21.223 21.22q.274.275.262.688a.98.98 0 0 1-.287.687.93.93 0 0 1-.687.275q-.413 0-.713-.275m-7.65-7.6-2-2h-1.55l-1 2zm4.85 7q-.824 0-1.413-.588a1.93 1.93 0 0 1-.587-1.412q0-.824.588-1.413a1.93 1.93 0 0 1 1.412-.587q.825 0 1.412.587.588.588.588 1.413t-.588 1.412a1.93 1.93 0 0 1-1.412.588" }, null, -1);
var h9 = defineComponent({
  __name: "SfIconRemoveShoppingCart",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (c20, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "remove-shopping-cart"
    }, {
      default: withCtx(() => [
        s8
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconSafetyCheck.vue.mjs
var f15 = createBaseVNode("path", { d: "M12 16.5q2.075 0 3.538-1.463Q17 13.575 17 11.5t-1.462-3.538Q14.075 6.5 12 6.5T8.463 7.962Q7 9.425 7 11.5t1.463 3.537T12 16.5m1.65-2.65L11.5 11.7V8.5h1v2.8l1.85 1.85zM12 21.5q-3.475-.875-5.737-3.988T4 10.6V4.5l8-3 8 3v6.1q0 3.8-2.262 6.912T12 21.5m0-2.1q2.6-.825 4.3-3.3t1.7-5.5V5.875l-6-2.25-6 2.25V10.6q0 3.025 1.7 5.5t4.3 3.3" }, null, -1);
var u31 = defineComponent({
  __name: "SfIconSafetyCheck",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "safety-check"
    }, {
      default: withCtx(() => [
        f15
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconSchedule.vue.mjs
var f16 = createBaseVNode("path", { d: "M14.625 16.025a.92.92 0 0 0 .675.275q.4 0 .7-.3a.95.95 0 0 0 .275-.7.95.95 0 0 0-.275-.7l-3-3V7.975a.93.93 0 0 0-.287-.7A1 1 0 0 0 12 7a.97.97 0 0 0-.712.287A.97.97 0 0 0 11 8v3.975a1.03 1.03 0 0 0 .3.725zM12 22a9.7 9.7 0 0 1-3.9-.788 10.1 10.1 0 0 1-3.175-2.137q-1.35-1.35-2.137-3.175A9.7 9.7 0 0 1 2 12q0-2.075.788-3.9a10.1 10.1 0 0 1 2.137-3.175q1.35-1.35 3.175-2.138A9.7 9.7 0 0 1 12 2q2.075 0 3.9.787a10.1 10.1 0 0 1 3.175 2.138q1.35 1.35 2.137 3.175A9.7 9.7 0 0 1 22 12a9.7 9.7 0 0 1-.788 3.9 10.1 10.1 0 0 1-2.137 3.175q-1.35 1.35-3.175 2.137A9.7 9.7 0 0 1 12 22m0-2q3.325 0 5.663-2.337T20 12q0-3.325-2.337-5.663T12 4Q8.675 4 6.338 6.337 4 8.675 4 12t2.338 5.663Q8.675 20 12 20" }, null, -1);
var u32 = defineComponent({
  __name: "SfIconSchedule",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, d26) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "schedule"
    }, {
      default: withCtx(() => [
        f16
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconSearch.vue.mjs
var f17 = createBaseVNode("path", { d: "m18.9 20.3-5.6-5.6q-.75.6-1.725.95T9.5 16q-2.725 0-4.612-1.887T3 9.5t1.888-4.613Q6.775 3 9.5 3t4.613 1.887T16 9.5a6.1 6.1 0 0 1-1.3 3.8l5.625 5.625a.92.92 0 0 1 .275.675q0 .4-.3.7a.95.95 0 0 1-.7.275.95.95 0 0 1-.7-.275M9.5 14q1.875 0 3.188-1.312Q14 11.375 14 9.5t-1.312-3.188Q11.375 5 9.5 5T6.312 6.312 5 9.5t1.312 3.188Q7.625 14 9.5 14" }, null, -1);
var u33 = defineComponent({
  __name: "SfIconSearch",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "search"
    }, {
      default: withCtx(() => [
        f17
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconSell.vue.mjs
var c10 = createBaseVNode("path", { d: "M14.25 21.4q-.575.575-1.425.575T11.4 21.4l-8.8-8.8a2.07 2.07 0 0 1-.6-1.45V4q0-.825.588-1.413A1.93 1.93 0 0 1 4 2h7.15q.425 0 .8.162.375.163.65.438l8.8 8.825q.575.575.575 1.412a1.92 1.92 0 0 1-.575 1.413zM6.5 8q.625 0 1.062-.438Q8 7.125 8 6.5t-.438-1.062A1.44 1.44 0 0 0 6.5 5q-.625 0-1.062.438A1.44 1.44 0 0 0 5 6.5q0 .625.438 1.062Q5.875 8 6.5 8" }, null, -1);
var u34 = defineComponent({
  __name: "SfIconSell",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "sell"
    }, {
      default: withCtx(() => [
        c10
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconShare.vue.mjs
var f18 = createBaseVNode("path", { d: "M18 22a2.9 2.9 0 0 1-2.125-.875A2.9 2.9 0 0 1 15 19q0-.175.025-.363.025-.187.075-.337l-7.05-4.1q-.425.375-.95.588T6 15a2.9 2.9 0 0 1-2.125-.875A2.9 2.9 0 0 1 3 12q0-1.25.875-2.125A2.9 2.9 0 0 1 6 9q.575 0 1.1.213.525.212.95.587l7.05-4.1a2 2 0 0 1-.075-.338A3 3 0 0 1 15 5q0-1.25.875-2.125A2.9 2.9 0 0 1 18 2q1.25 0 2.125.875T21 5t-.875 2.125A2.9 2.9 0 0 1 18 8q-.575 0-1.1-.213a3.3 3.3 0 0 1-.95-.587L8.9 11.3q.05.15.075.337a2.7 2.7 0 0 1 0 .726 2 2 0 0 1-.075.337l7.05 4.1q.425-.375.95-.588T18 16q1.25 0 2.125.875T21 19t-.875 2.125A2.9 2.9 0 0 1 18 22" }, null, -1);
var d20 = defineComponent({
  __name: "SfIconShare",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (l18, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "share"
    }, {
      default: withCtx(() => [
        f18
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconShoppingCart.vue.mjs
var c11 = createBaseVNode("path", { d: "M7 22q-.824 0-1.412-.587A1.93 1.93 0 0 1 5 20q0-.824.588-1.413A1.93 1.93 0 0 1 7 18q.824 0 1.412.587Q9 19.176 9 20t-.588 1.413A1.93 1.93 0 0 1 7 22m10 0q-.825 0-1.412-.587A1.93 1.93 0 0 1 15 20q0-.824.588-1.413A1.93 1.93 0 0 1 17 18q.824 0 1.413.587Q19 19.176 19 20t-.587 1.413A1.93 1.93 0 0 1 17 22M6.15 6l2.4 5h7l2.75-5zM7 17q-1.125 0-1.7-.988-.575-.987-.05-1.962L6.6 11.6 3 4H1.975a.93.93 0 0 1-.7-.288A1 1 0 0 1 1 3q0-.424.288-.712A.97.97 0 0 1 2 2h1.625q.274 0 .525.15.25.15.375.425L5.2 4h14.75q.675 0 .925.5t-.025 1.05l-3.55 6.4a2.03 2.03 0 0 1-.725.775q-.45.275-1.025.275H8.1L7 15h11.025q.425 0 .7.287.275.288.275.713 0 .424-.288.712A.97.97 0 0 1 18 17z" }, null, -1);
var h10 = defineComponent({
  __name: "SfIconShoppingCart",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "shopping-cart"
    }, {
      default: withCtx(() => [
        c11
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconShoppingCartCheckout.vue.mjs
var p9 = createBaseVNode("path", { d: "M7 22q-.824 0-1.412-.587A1.93 1.93 0 0 1 5 20q0-.824.588-1.413A1.93 1.93 0 0 1 7 18q.824 0 1.412.587Q9 19.176 9 20t-.588 1.413A1.93 1.93 0 0 1 7 22m10 0q-.825 0-1.412-.587A1.93 1.93 0 0 1 15 20q0-.824.588-1.413A1.93 1.93 0 0 1 17 18q.824 0 1.413.587Q19 19.176 19 20t-.587 1.413A1.93 1.93 0 0 1 17 22M11.3 9.3a.95.95 0 0 1-.275-.7q0-.425.275-.7l.875-.9H9a.97.97 0 0 1-.713-.287A.97.97 0 0 1 8 6q0-.424.287-.713A.97.97 0 0 1 9 5h3.175l-.9-.9a.92.92 0 0 1-.287-.7Q11 3 11.3 2.7q.3-.275.7-.288t.7.288l2.6 2.6q.15.15.212.325.063.175.063.375t-.062.375a.9.9 0 0 1-.213.325l-2.6 2.6a.98.98 0 0 1-.687.287.93.93 0 0 1-.713-.287M7 17q-1.15 0-1.737-.988-.588-.987-.013-1.962L6.6 11.6 3 4H2a.97.97 0 0 1-.712-.288A.97.97 0 0 1 1 3q0-.424.288-.712A.97.97 0 0 1 2 2h1.65q.275 0 .525.15t.375.425L8.525 11h7.025l3.6-6.5A.97.97 0 0 1 20 4q.574 0 .863.487a.94.94 0 0 1 .012.988L17.3 11.95q-.275.5-.738.775A1.95 1.95 0 0 1 15.55 13H8.1L7 15h11q.424 0 .712.287.288.288.288.713 0 .424-.288.712A.97.97 0 0 1 18 17z" }, null, -1);
var m11 = defineComponent({
  __name: "SfIconShoppingCartCheckout",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(t4) {
    return (s12, l18) => (openBlock(), createBlock(unref(m2), {
      size: t4.size,
      viewBox: "0 0 24 24",
      "data-testid": "shopping-cart-checkout"
    }, {
      default: withCtx(() => [
        p9
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconSort.vue.mjs
var f19 = createBaseVNode("path", { d: "M4 18a1 1 0 1 1 0-2h4a1 1 0 1 1 0 2zM3 7a1 1 0 0 1 1-1h16a1 1 0 1 1 0 2H4a1 1 0 0 1-1-1m1 6a1 1 0 1 1 0-2h10a1 1 0 1 1 0 2z" }, null, -1);
var u35 = defineComponent({
  __name: "SfIconSort",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "sort"
    }, {
      default: withCtx(() => [
        f19
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconStar.vue.mjs
var m12 = createBaseVNode("path", { d: "m8.85 17.825 3.15-1.9 3.15 1.925-.825-3.6 2.775-2.4-3.65-.325-1.45-3.4-1.45 3.375-3.65.325 2.775 2.425zm3.15.45-4.15 2.5a.9.9 0 0 1-.575.15.97.97 0 0 1-.525-.2 1.2 1.2 0 0 1-.35-.437.88.88 0 0 1-.05-.588l1.1-4.725L3.775 11.8a.96.96 0 0 1-.312-.513 1 1 0 0 1 .037-.562 1.1 1.1 0 0 1 .3-.45q.2-.176.55-.225l4.85-.425 1.875-4.45q.125-.3.388-.45t.537-.15.538.15q.262.15.387.45l1.875 4.45 4.85.425q.35.05.55.225t.3.45.038.562a.96.96 0 0 1-.313.513l-3.675 3.175 1.1 4.725a.88.88 0 0 1-.05.588 1.2 1.2 0 0 1-.35.437.97.97 0 0 1-.525.2.9.9 0 0 1-.575-.15z" }, null, -1);
var u36 = defineComponent({
  __name: "SfIconStar",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "star"
    }, {
      default: withCtx(() => [
        m12
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconStarFilled.vue.mjs
var c12 = createBaseVNode("path", { d: "m12 18.275-4.15 2.5a.9.9 0 0 1-.575.15.97.97 0 0 1-.525-.2 1.2 1.2 0 0 1-.35-.437.88.88 0 0 1-.05-.588l1.1-4.725L3.775 11.8a.96.96 0 0 1-.312-.513 1 1 0 0 1 .037-.562 1.1 1.1 0 0 1 .3-.45q.2-.176.55-.225l4.85-.425 1.875-4.45q.125-.3.388-.45t.537-.15.538.15q.262.15.387.45l1.875 4.45 4.85.425q.35.05.55.225t.3.45.038.562a.96.96 0 0 1-.313.513l-3.675 3.175 1.1 4.725a.88.88 0 0 1-.05.588 1.2 1.2 0 0 1-.35.437.97.97 0 0 1-.525.2.9.9 0 0 1-.575-.15z" }, null, -1);
var u37 = defineComponent({
  __name: "SfIconStarFilled",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "star-filled"
    }, {
      default: withCtx(() => [
        c12
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconStarHalf.vue.mjs
var f20 = createBaseVNode("path", { d: "M12 8.125v7.8l3.15 1.925-.825-3.6 2.775-2.4-3.65-.325zm0 10.15-4.15 2.5a.9.9 0 0 1-.575.15.97.97 0 0 1-.525-.2 1.2 1.2 0 0 1-.35-.437.88.88 0 0 1-.05-.588l1.1-4.725L3.775 11.8a.96.96 0 0 1-.312-.513 1 1 0 0 1 .037-.562 1.1 1.1 0 0 1 .3-.45q.2-.176.55-.225l4.85-.425 1.875-4.45q.125-.3.388-.45t.537-.15.538.15q.262.15.387.45l1.875 4.45 4.85.425q.35.05.55.225t.3.45.038.562a.96.96 0 0 1-.313.513l-3.675 3.175 1.1 4.725a.88.88 0 0 1-.05.588 1.2 1.2 0 0 1-.35.437.97.97 0 0 1-.525.2.9.9 0 0 1-.575-.15z" }, null, -1);
var u38 = defineComponent({
  __name: "SfIconStarHalf",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (c20, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "star-half"
    }, {
      default: withCtx(() => [
        f20
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconThumbDown.vue.mjs
var c13 = createBaseVNode("path", { d: "M3 16q-.8 0-1.4-.6T1 14v-2q0-.175.05-.375t.1-.375l3-7.05q.225-.5.75-.85T6 3h11v13l-6 5.95q-.375.375-.887.437a1.65 1.65 0 0 1-.988-.187 1.58 1.58 0 0 1-.7-.7q-.225-.45-.1-.925L9.45 16zm12-.85V5H6l-3 7v2h9l-1.35 5.5zM20 3q.825 0 1.413.587Q22 4.175 22 5v9q0 .825-.587 1.412A1.93 1.93 0 0 1 20 16h-3v-2h3V5h-3V3z" }, null, -1);
var d21 = defineComponent({
  __name: "SfIconThumbDown",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "thumb-down"
    }, {
      default: withCtx(() => [
        c13
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconThumbUp.vue.mjs
var m13 = createBaseVNode("path", { d: "M7 22V9l6-5.95q.375-.375.887-.438.513-.061.988.188.475.25.7.7t.1.925L14.55 9H21q.8 0 1.4.6T23 11v2q0 .176-.05.375-.05.2-.1.375l-3 7.05q-.225.5-.75.85T18 22zM9 9.85V20h9l3-7v-2h-9l1.35-5.5zM4 22q-.824 0-1.412-.587A1.93 1.93 0 0 1 2 20v-9q0-.824.588-1.413A1.93 1.93 0 0 1 4 9h3v2H4v9h3v2z" }, null, -1);
var h11 = defineComponent({
  __name: "SfIconThumbUp",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (p14, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "thumb-up"
    }, {
      default: withCtx(() => [
        m13
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconTune.vue.mjs
var m14 = createBaseVNode("path", { d: "M4 19a.97.97 0 0 1-.712-.288A.97.97 0 0 1 3 18q0-.424.288-.712A.97.97 0 0 1 4 17h4q.425 0 .713.288A.97.97 0 0 1 9 18q0 .424-.287.712A.97.97 0 0 1 8 19zM4 7a.97.97 0 0 1-.712-.287A.97.97 0 0 1 3 6q0-.425.288-.713A.97.97 0 0 1 4 5h8a.97.97 0 0 1 .713.287A.97.97 0 0 1 13 6a.97.97 0 0 1-.287.713A.97.97 0 0 1 12 7zm8 14a.97.97 0 0 1-.712-.288A.97.97 0 0 1 11 20v-4q0-.425.288-.713A.97.97 0 0 1 12 15a.97.97 0 0 1 .713.287A.97.97 0 0 1 13 16v1h7q.424 0 .712.288A.97.97 0 0 1 21 18q0 .424-.288.712A.97.97 0 0 1 20 19h-7v1q0 .424-.287.712A.97.97 0 0 1 12 21m-4-6a.97.97 0 0 1-.713-.288A.97.97 0 0 1 7 14v-1H4a.97.97 0 0 1-.712-.288A.97.97 0 0 1 3 12q0-.425.288-.713A.97.97 0 0 1 4 11h3v-1a.97.97 0 0 1 .287-.713A.97.97 0 0 1 8 9a.97.97 0 0 1 .713.287A.97.97 0 0 1 9 10v4q0 .424-.287.712A.97.97 0 0 1 8 15m4-2a.97.97 0 0 1-.712-.288A.97.97 0 0 1 11 12q0-.425.288-.713A.97.97 0 0 1 12 11h8q.424 0 .712.287.288.288.288.713 0 .424-.288.712A.97.97 0 0 1 20 13zm4-4a.97.97 0 0 1-.712-.288A.97.97 0 0 1 15 8V4q0-.425.288-.713A.97.97 0 0 1 16 3q.424 0 .712.287Q17 3.575 17 4v1h3q.424 0 .712.287Q21 5.575 21 6a.97.97 0 0 1-.288.713A.97.97 0 0 1 20 7h-3v1q0 .424-.288.712A.97.97 0 0 1 16 9" }, null, -1);
var _6 = defineComponent({
  __name: "SfIconTune",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (s12, c20) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "tune"
    }, {
      default: withCtx(() => [
        m14
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconTwitter.vue.mjs
var f21 = createBaseVNode("path", { d: "M19.944 8.048c.013.178.013.356.013.533 0 5.419-4.124 11.663-11.663 11.663-2.322 0-4.48-.673-6.294-1.84.33.038.647.05.99.05 1.916 0 3.68-.647 5.089-1.75a4.11 4.11 0 0 1-3.833-2.844c.254.039.508.064.774.064.368 0 .736-.05 1.079-.14a4.1 4.1 0 0 1-3.287-4.023v-.05c.546.304 1.18.495 1.853.52a4.1 4.1 0 0 1-1.827-3.414c0-.761.203-1.46.558-2.068a11.65 11.65 0 0 0 8.452 4.29 4.6 4.6 0 0 1-.102-.94A4.097 4.097 0 0 1 15.846 4a4.1 4.1 0 0 1 2.994 1.294 8.1 8.1 0 0 0 2.602-.99 4.1 4.1 0 0 1-1.802 2.26A8.2 8.2 0 0 0 22 5.928a8.8 8.8 0 0 1-2.056 2.12" }, null, -1);
var u39 = defineComponent({
  __name: "SfIconTwitter",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "twitter"
    }, {
      default: withCtx(() => [
        f21
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconUndo.vue.mjs
var f22 = createBaseVNode("path", { d: "M8 19.5a.97.97 0 0 1-.712-.288A.97.97 0 0 1 7 18.5q0-.425.288-.713A.97.97 0 0 1 8 17.5h6.1q1.576 0 2.737-1Q18 15.5 18 14t-1.163-2.5-2.737-1H7.8l1.9 1.9a.95.95 0 0 1 .275.7.95.95 0 0 1-.275.7.95.95 0 0 1-.7.275.95.95 0 0 1-.7-.275l-3.6-3.6a.9.9 0 0 1-.212-.325 1.1 1.1 0 0 1-.063-.375q0-.201.063-.375A.9.9 0 0 1 4.7 8.8l3.6-3.6a.95.95 0 0 1 .7-.275q.425 0 .7.275a.95.95 0 0 1 .275.7.95.95 0 0 1-.275.7L7.8 8.5h6.3q2.425 0 4.163 1.575Q20 11.65 20 14t-1.738 3.925Q16.526 19.5 14.1 19.5z" }, null, -1);
var u40 = defineComponent({
  __name: "SfIconUndo",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (d26, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "undo"
    }, {
      default: withCtx(() => [
        f22
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconUnfoldMore.vue.mjs
var c14 = createBaseVNode("path", { d: "M8.225 8.325Q7.95 8.05 7.95 7.6t.275-.725L11.3 3.8q.15-.15.325-.213.175-.062.375-.062t.388.062a.7.7 0 0 1 .312.213l3.1 3.1q.274.274.262.712t-.287.713-.725.275-.725-.275L12 6 9.65 8.35q-.274.274-.712.263a1 1 0 0 1-.713-.288M12 20.575a.9.9 0 0 1-.375-.075 1.3 1.3 0 0 1-.325-.2l-3.075-3.075q-.275-.275-.275-.725t.275-.725.725-.275.725.275L12 18.1l2.35-2.35q.274-.274.712-.262t.713.287.275.725-.275.725L12.7 20.3a.9.9 0 0 1-.312.2q-.188.075-.388.075" }, null, -1);
var u41 = defineComponent({
  __name: "SfIconUnfoldMore",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (l18, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "unfold-more"
    }, {
      default: withCtx(() => [
        c14
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconUpload.vue.mjs
var p10 = createBaseVNode("path", { d: "M6 19.787q-.824 0-1.412-.587A1.93 1.93 0 0 1 4 17.787v-2q0-.424.287-.712A.97.97 0 0 1 5 14.787q.424 0 .713.288.287.288.287.712v2h12v-2q0-.424.288-.712a.97.97 0 0 1 .712-.288q.424 0 .712.288t.288.712v2q0 .825-.587 1.413a1.93 1.93 0 0 1-1.413.587zm5-12.15L9.125 9.512a.93.93 0 0 1-.713.288 1.02 1.02 0 0 1-.712-.313q-.275-.3-.288-.7a.92.92 0 0 1 .288-.7l3.6-3.6q.15-.15.325-.212.175-.063.375-.063t.375.063a.9.9 0 0 1 .325.212l3.6 3.6q.3.3.287.7-.012.4-.287.7-.3.3-.713.313a.93.93 0 0 1-.712-.288L13 7.637v7.15q0 .426-.287.713a.97.97 0 0 1-.713.287.97.97 0 0 1-.713-.287.97.97 0 0 1-.287-.713z" }, null, -1);
var q9 = defineComponent({
  __name: "SfIconUpload",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, l18) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "upload"
    }, {
      default: withCtx(() => [
        p10
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconViewList.vue.mjs
var c15 = createBaseVNode("path", { d: "M3 17V7q0-.824.587-1.412A1.93 1.93 0 0 1 5 5h14q.825 0 1.413.588Q21 6.175 21 7v10q0 .825-.587 1.413A1.93 1.93 0 0 1 19 19H5q-.825 0-1.413-.587A1.93 1.93 0 0 1 3 17m2-8h2V7H5zm4 0h10V7H9zm0 4h10v-2H9zm0 4h10v-2H9zm-4 0h2v-2H5zm0-4h2v-2H5z" }, null, -1);
var d22 = defineComponent({
  __name: "SfIconViewList",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (f29, h12) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "view-list"
    }, {
      default: withCtx(() => [
        c15
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconVisibility.vue.mjs
var m15 = createBaseVNode("path", { d: "M12 16q1.875 0 3.188-1.312Q16.5 13.375 16.5 11.5t-1.312-3.188Q13.875 7 12 7T8.812 8.312Q7.5 9.625 7.5 11.5t1.312 3.188Q10.125 16 12 16m0-1.8q-1.125 0-1.912-.788A2.6 2.6 0 0 1 9.3 11.5q0-1.125.788-1.913A2.6 2.6 0 0 1 12 8.8q1.125 0 1.913.787.787.788.787 1.913t-.787 1.912A2.6 2.6 0 0 1 12 14.2m0 4.8q-3.475 0-6.35-1.837Q2.775 15.325 1.3 12.2a.8.8 0 0 1-.1-.313 3 3 0 0 1 0-.775.8.8 0 0 1 .1-.312q1.475-3.125 4.35-4.962Q8.525 4 12 4t6.35 1.838T22.7 10.8q.075.124.1.312a3 3 0 0 1 0 .775.8.8 0 0 1-.1.313q-1.474 3.125-4.35 4.963Q15.475 19 12 19" }, null, -1);
var q10 = defineComponent({
  __name: "SfIconVisibility",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(t4) {
    return (f29, p14) => (openBlock(), createBlock(unref(m2), {
      size: t4.size,
      viewBox: "0 0 24 24",
      "data-testid": "visibility"
    }, {
      default: withCtx(() => [
        m15
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconVisibilityOff.vue.mjs
var m16 = createBaseVNode("path", { d: "m19.3 16.5-3.2-3.2q.2-.424.3-.862t.1-.938q0-1.875-1.312-3.188Q13.875 7 12 7q-.5 0-.938.1a4.3 4.3 0 0 0-.862.3L7.65 4.85a11 11 0 0 1 2.1-.638A11.6 11.6 0 0 1 12 4q3.575 0 6.425 1.887T22.7 10.8q.075.125.1.312.025.188.025.388a2 2 0 0 1-.125.7q-.574 1.275-1.437 2.375A10.5 10.5 0 0 1 19.3 16.5m-.2 5.4-3.5-3.45q-.874.275-1.762.413T12 19q-3.575 0-6.425-1.887T1.3 12.2a.8.8 0 0 1-.1-.313 3 3 0 0 1 0-.762.8.8 0 0 1 .1-.3Q1.825 9.7 2.55 8.75A13.3 13.3 0 0 1 4.15 7L2.075 4.9a.93.93 0 0 1-.275-.688q0-.412.3-.712a.95.95 0 0 1 .7-.275q.425 0 .7.275l17 17q.275.275.288.688a.93.93 0 0 1-.288.712.95.95 0 0 1-.7.275.95.95 0 0 1-.7-.275M12 16q.275 0 .512-.025.239-.025.513-.1l-5.4-5.4a3 3 0 0 0-.1.513 5 5 0 0 0-.025.512q0 1.875 1.312 3.188Q10.125 16 12 16m2.65-4.15-3-3q1.425-.225 2.325.8t.675 2.2" }, null, -1);
var d23 = defineComponent({
  __name: "SfIconVisibilityOff",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (q12, c20) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "visibility-off"
    }, {
      default: withCtx(() => [
        m16
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconVsfDiamond.vue.mjs
var f23 = createBaseVNode("path", { d: "M7.888 2.174c-.761.473-1.508 1.22-3 2.713-1.494 1.493-2.24 2.24-2.714 3a7.79 7.79 0 0 0 0 8.226c.473.76 1.22 1.506 2.712 2.999 1.494 1.494 2.24 2.24 3.002 2.714a7.79 7.79 0 0 0 8.224 0c.761-.474 1.508-1.22 3-2.713 1.494-1.493 2.24-2.24 2.714-3a7.79 7.79 0 0 0 0-8.225c-.474-.761-1.22-1.508-2.713-3-1.493-1.494-2.24-2.24-3-2.714a7.79 7.79 0 0 0-8.225 0" }, null, -1);
var u42 = defineComponent({
  __name: "SfIconVsfDiamond",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, d26) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "vsf-diamond"
    }, {
      default: withCtx(() => [
        f23
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconVuejs.vue.mjs
var f24 = createBaseVNode("path", { d: "M18.526 2.3H14.75L12 6.65 9.643 2.3H1l11 18.843L23 2.3zM3.736 3.871h2.641L12 13.605l5.618-9.734h2.642L12 18.024z" }, null, -1);
var d24 = defineComponent({
  __name: "SfIconVuejs",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (m17, p14) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "vuejs"
    }, {
      default: withCtx(() => [
        f24
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconVuestorefront.vue.mjs
var c16 = createBaseVNode("path", { d: "m9.747 2.824.839-.838a2 2 0 0 1 2.828 0l.838.838a2 2 0 0 1 0 2.828l-.838.839a2 2 0 0 1-2.828 0l-.839-.839a2 2 0 0 1 0-2.828m3.69 10.978 4.482-4.483a2 2 0 0 1 2.829 0L23 11.571l-11 11-11-11 2.276-2.276a2 2 0 0 1 2.827 0l4.506 4.507a2 2 0 0 0 2.828 0" }, null, -1);
var d25 = defineComponent({
  __name: "SfIconVuestorefront",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (l18, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "vuestorefront"
    }, {
      default: withCtx(() => [
        c16
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconWarehouse.vue.mjs
var c17 = createBaseVNode("path", { d: "m12 5.15-8 3.2V19h2v-6q0-.824.588-1.413A1.93 1.93 0 0 1 8 11h8q.825 0 1.413.587Q18 12.175 18 13v6h2V8.35zM8 21H4q-.824 0-1.412-.587A1.93 1.93 0 0 1 2 19V8.35A1.96 1.96 0 0 1 3.25 6.5l8-3.2q.35-.15.75-.15t.75.15l8 3.2q.575.225.913.725.337.5.337 1.125V19q0 .825-.587 1.413A1.93 1.93 0 0 1 20 21h-4v-8H8zm1 0v-2h2v2zm2-3v-2h2v2zm2 3v-2h2v2z" }, null, -1);
var u43 = defineComponent({
  __name: "SfIconWarehouse",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (h12, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "warehouse"
    }, {
      default: withCtx(() => [
        c17
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconWarning.vue.mjs
var s9 = createBaseVNode("path", {
  "fill-rule": "evenodd",
  d: "M10.258 4.067c.764-1.363 2.725-1.363 3.49 0l7.818 13.95c.748 1.333-.216 2.978-1.744 2.978H4.183c-1.528 0-2.492-1.645-1.745-2.978zM12 8a1 1 0 0 1 1 1v3.5a1 1 0 1 1-2 0V9a1 1 0 0 1 1-1m0 7.25a1.25 1.25 0 1 0 0 2.5 1.25 1.25 0 0 0 0-2.5",
  "clip-rule": "evenodd"
}, null, -1);
var u44 = defineComponent({
  __name: "SfIconWarning",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (d26, f29) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "warning"
    }, {
      default: withCtx(() => [
        s9
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfIcons/SfIconYoutube.vue.mjs
var f25 = createBaseVNode("path", { d: "M22.54 6.42a2.76 2.76 0 0 0-1.944-1.957C18.88 4 12 4 12 4s-6.88 0-8.596.463A2.76 2.76 0 0 0 1.46 6.42C1 8.147 1 11.75 1 11.75s0 3.603.46 5.33a2.72 2.72 0 0 0 1.945 1.926c1.716.463 8.596.463 8.596.463s6.879 0 8.595-.463a2.72 2.72 0 0 0 1.945-1.926c.46-1.727.46-5.33.46-5.33s0-3.603-.46-5.33M9.75 15.021V8.48l5.75 3.271z" }, null, -1);
var l17 = defineComponent({
  __name: "SfIconYoutube",
  props: {
    size: {
      type: String,
      default: q.base
    }
  },
  setup(e5) {
    return (u45, m17) => (openBlock(), createBlock(unref(m2), {
      size: e5.size,
      viewBox: "0 0 24 24",
      "data-testid": "youtube"
    }, {
      default: withCtx(() => [
        f25
      ]),
      _: 1
    }, 8, ["size"]));
  }
});

// node_modules/@storefront-ui/vue/dist/composables/useFocusVisible/useFocusVisible.mjs
var e2 = Ht();
var g2 = (n6 = {}) => {
  const o4 = computed(() => unref(n6.isTextInput)), t4 = ref(n6.autoFocus || e2.isFocusVisible());
  onMounted(() => {
    e2.setupGlobalFocusEvents();
  });
  let s12 = () => {
  };
  return watch(
    o4,
    () => {
      s12();
      const u45 = (i2, a2) => {
        e2.isKeyboardFocusEvent(o4 == null ? void 0 : o4.value, i2, a2) && (t4.value = e2.isFocusVisible());
      };
      e2.changeHandlers.add(u45), s12 = () => {
        e2.changeHandlers.delete(u45);
      };
    },
    { immediate: true }
  ), onUnmounted(() => {
    s12();
  }), { isFocusVisible: t4 };
};

// node_modules/@storefront-ui/vue/dist/components/SfInput/SfInput.vue.mjs
var T2 = {
  inheritAttrs: false
};
var $2 = {
  [$.sm]: " h-[32px]",
  [$.base]: "h-[40px]",
  [$.lg]: "h-[48px]"
};
var N = defineComponent({
  ...T2,
  __name: "SfInput",
  props: {
    modelValue: {
      type: [String, Number],
      default: ""
    },
    wrapperTag: {
      type: [String, Object],
      default: "span"
    },
    size: {
      type: String,
      default: $.base
    },
    invalid: {
      type: Boolean,
      default: false
    },
    wrapperClass: {
      type: [String, Object],
      default: ""
    }
  },
  emits: ["update:modelValue"],
  setup(t4, { emit: u45 }) {
    const p14 = t4, d26 = u45, { modelValue: c20, invalid: n6 } = toRefs(p14), { isFocusVisible: m17 } = g2({ isTextInput: true }), r4 = ref(), s12 = computed({
      get: () => c20.value ?? r4.value,
      set: (e5) => {
        d26("update:modelValue", e5), r4.value = e5;
      }
    });
    return (e5, l18) => (openBlock(), createBlock(resolveDynamicComponent(t4.wrapperTag), {
      class: normalizeClass([
        "flex items-center gap-2 px-4 bg-white rounded-md ring-1 text-neutral-500 hover:ring-primary-700 focus-within:caret-primary-700 active:caret-primary-700 active:ring-primary-700 active:ring-2 focus-within:ring-primary-700 focus-within:ring-2",
        {
          "ring-2 ring-negative-700": unref(n6),
          "ring-1 ring-neutral-300": !unref(n6),
          "focus-within:outline focus-within:outline-offset": unref(m17)
        },
        $2[t4.size],
        t4.wrapperClass
      ]),
      "data-testid": "input"
    }, {
      default: withCtx(() => [
        renderSlot(e5.$slots, "prefix"),
        withDirectives(createBaseVNode("input", mergeProps({
          "onUpdate:modelValue": l18[0] || (l18[0] = (f29) => s12.value = f29),
          class: "min-w-[80px] w-full text-base outline-none appearance-none text-neutral-900 disabled:cursor-not-allowed disabled:bg-transparent read-only:bg-transparent",
          size: 1,
          "data-testid": "input-field"
        }, e5.$attrs), null, 16), [
          [vModelDynamic, s12.value]
        ]),
        renderSlot(e5.$slots, "suffix")
      ]),
      _: 3
    }, 8, ["class"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfLink/SfLink.vue.mjs
var f26 = {
  [R.primary]: "text-primary-700 underline hover:text-primary-800 active:text-primary-900 focus-visible:outline focus-visible:outline-offset focus-visible:rounded-sm",
  [R.secondary]: "underline hover:text-primary-800 active:text-primary-900 focus-visible:outline focus-visible:outline-offset focus-visible:rounded-sm"
};
var v = defineComponent({
  __name: "SfLink",
  props: {
    tag: {
      type: [String, Object],
      default: "a"
    },
    variant: {
      type: String,
      default: R.primary
    }
  },
  setup(t4) {
    return (i2, c20) => (openBlock(), createBlock(resolveDynamicComponent(t4.tag), {
      class: normalizeClass(["focus-visible:outline focus-visible:outline-offset focus-visible:rounded-sm", f26[t4.variant]]),
      "data-testid": "link"
    }, {
      default: withCtx(() => [
        renderSlot(i2.$slots, "default")
      ]),
      _: 3
    }, 8, ["class"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfListItem/SfListItem.vue.mjs
var r2 = {
  [J.sm]: "text-sm px-4 py-1",
  [J.base]: "px-4 py-2",
  [J.lg]: "p-4"
};
var g3 = defineComponent({
  __name: "SfListItem",
  props: {
    size: {
      type: String,
      default: J.base
    },
    disabled: {
      type: Boolean,
      default: void 0
    },
    selected: {
      type: Boolean,
      default: false
    },
    tag: {
      type: [String, Object],
      default: void 0
    },
    childrenTag: {
      type: String,
      default: "span"
    }
  },
  setup(e5) {
    return (t4, c20) => (openBlock(), createBlock(resolveDynamicComponent(e5.tag || "li"), {
      class: normalizeClass([
        "inline-flex items-center gap-2 w-full hover:bg-neutral-100 active:bg-neutral-200 cursor-pointer focus-visible:outline focus-visible:outline-offset focus-visible:relative focus-visible:z-10",
        r2[e5.size],
        { "cursor-not-allowed pointer-events-none text-disabled-900": e5.disabled, "font-medium": e5.selected }
      ]),
      disabled: e5.disabled,
      "data-testid": "list-item"
    }, {
      default: withCtx(() => [
        t4.$slots.prefix ? (openBlock(), createBlock(resolveDynamicComponent(e5.childrenTag), {
          key: 0,
          class: normalizeClass(e5.disabled ? "text-disabled-500" : "text-neutral-500")
        }, {
          default: withCtx(() => [
            renderSlot(t4.$slots, "prefix")
          ]),
          _: 3
        }, 8, ["class"])) : createCommentVNode("", true),
        (openBlock(), createBlock(resolveDynamicComponent(e5.childrenTag), { class: "flex flex-col w-full min-w-0" }, {
          default: withCtx(() => [
            renderSlot(t4.$slots, "default")
          ]),
          _: 3
        })),
        t4.$slots.suffix ? (openBlock(), createBlock(resolveDynamicComponent(e5.childrenTag), {
          key: 1,
          class: normalizeClass(e5.disabled ? "text-disabled-500" : "text-neutral-500")
        }, {
          default: withCtx(() => [
            renderSlot(t4.$slots, "suffix")
          ]),
          _: 3
        }, 8, ["class"])) : createCommentVNode("", true)
      ]),
      _: 3
    }, 8, ["class", "disabled"]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfLoaderCircular/SfLoaderCircular.vue.mjs
var n3 = ["aria-label"];
var o = {
  [Q.xs]: "h-4 w-4 ring-2",
  [Q.sm]: "h-5 w-5 ring-2",
  [Q.base]: "h-6 w-6 ring-2",
  [Q.lg]: "h-8 w-8 ring-2",
  [Q.xl]: "h-10 w-10 ring-2",
  [Q["2xl"]]: "h-14 w-14 ring-[3px]",
  [Q["3xl"]]: "h-24 w-24 ring-4",
  [Q["4xl"]]: "h-48 w-48 ring-8"
};
var c18 = {
  [Q.xs]: "stroke-[10px]",
  [Q.sm]: "stroke-[8px]",
  [Q.base]: "stroke-[6px]",
  [Q.lg]: "stroke-[4px]",
  [Q.xl]: "stroke-[3px]",
  [Q["2xl"]]: "stroke-[3px]",
  [Q["3xl"]]: "stroke-2",
  [Q["4xl"]]: "stroke-2"
};
var k3 = defineComponent({
  __name: "SfLoaderCircular",
  props: {
    size: {
      type: String,
      default: Q.base
    },
    ariaLabel: {
      type: String,
      default: "loading"
    }
  },
  setup(r4) {
    return (x3, g4) => (openBlock(), createElementBlock("svg", {
      class: normalizeClass(["inline-block rounded-full ring-inset ring-neutral-300 text-primary-700 animate-spin-slow", o[r4.size]]),
      viewBox: "25 25 50 50",
      "aria-live": "polite",
      "aria-label": r4.ariaLabel,
      "data-testid": "loader-circular"
    }, [
      createBaseVNode("circle", {
        class: normalizeClass([c18[r4.size], "stroke-current stroke-2 fill-none animate-stroke-loader-circular"]),
        cx: "50",
        cy: "50",
        r: "24"
      }, null, 2)
    ], 10, n3));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfLoaderLinear/types.mjs
var e3 = ((r4) => (r4.minimal = "minimal", r4))(e3 || {});

// node_modules/@storefront-ui/vue/dist/components/SfLoaderLinear/SfLoaderLinear.vue.mjs
var o2 = ["aria-label"];
var s10 = {
  [e3.minimal]: "h-1",
  [Q.xs]: "h-4",
  [Q.sm]: "h-5",
  [Q.base]: "h-6",
  [Q.lg]: "h-7",
  [Q.xl]: "h-10",
  [Q["2xl"]]: "h-14",
  [Q["3xl"]]: "h-24",
  [Q["4xl"]]: "h-48"
};
var p11 = defineComponent({
  __name: "SfLoaderLinear",
  props: {
    size: {
      type: String,
      default: Q.base
    },
    ariaLabel: {
      type: String,
      default: "loading"
    }
  },
  setup(a2) {
    return (f29, m17) => (openBlock(), createElementBlock("span", {
      class: normalizeClass(["relative inline-block overflow-hidden bg-neutral-300 text-primary-700 after:absolute after:w-2.5 after:h-full after:animate-line after:bg-current after:block", s10[a2.size]]),
      "aria-live": "polite",
      "aria-label": a2.ariaLabel,
      "data-testid": "loader-linear"
    }, null, 10, o2));
  }
});

// node_modules/tabbable/dist/index.esm.js
var candidateSelectors = ["input:not([inert])", "select:not([inert])", "textarea:not([inert])", "a[href]:not([inert])", "button:not([inert])", "[tabindex]:not(slot):not([inert])", "audio[controls]:not([inert])", "video[controls]:not([inert])", '[contenteditable]:not([contenteditable="false"]):not([inert])', "details>summary:first-of-type:not([inert])", "details:not([inert])"];
var candidateSelector = candidateSelectors.join(",");
var NoElement = typeof Element === "undefined";
var matches = NoElement ? function() {
} : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;
var getRootNode = !NoElement && Element.prototype.getRootNode ? function(element) {
  var _element$getRootNode;
  return element === null || element === void 0 ? void 0 : (_element$getRootNode = element.getRootNode) === null || _element$getRootNode === void 0 ? void 0 : _element$getRootNode.call(element);
} : function(element) {
  return element === null || element === void 0 ? void 0 : element.ownerDocument;
};
var isInert = function isInert2(node, lookUp) {
  var _node$getAttribute;
  if (lookUp === void 0) {
    lookUp = true;
  }
  var inertAtt = node === null || node === void 0 ? void 0 : (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, "inert");
  var inert = inertAtt === "" || inertAtt === "true";
  var result = inert || lookUp && node && isInert2(node.parentNode);
  return result;
};
var isContentEditable = function isContentEditable2(node) {
  var _node$getAttribute2;
  var attValue = node === null || node === void 0 ? void 0 : (_node$getAttribute2 = node.getAttribute) === null || _node$getAttribute2 === void 0 ? void 0 : _node$getAttribute2.call(node, "contenteditable");
  return attValue === "" || attValue === "true";
};
var getCandidates = function getCandidates2(el, includeContainer, filter) {
  if (isInert(el)) {
    return [];
  }
  var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));
  if (includeContainer && matches.call(el, candidateSelector)) {
    candidates.unshift(el);
  }
  candidates = candidates.filter(filter);
  return candidates;
};
var getCandidatesIteratively = function getCandidatesIteratively2(elements, includeContainer, options) {
  var candidates = [];
  var elementsToCheck = Array.from(elements);
  while (elementsToCheck.length) {
    var element = elementsToCheck.shift();
    if (isInert(element, false)) {
      continue;
    }
    if (element.tagName === "SLOT") {
      var assigned = element.assignedElements();
      var content = assigned.length ? assigned : element.children;
      var nestedCandidates = getCandidatesIteratively2(content, true, options);
      if (options.flatten) {
        candidates.push.apply(candidates, nestedCandidates);
      } else {
        candidates.push({
          scopeParent: element,
          candidates: nestedCandidates
        });
      }
    } else {
      var validCandidate = matches.call(element, candidateSelector);
      if (validCandidate && options.filter(element) && (includeContainer || !elements.includes(element))) {
        candidates.push(element);
      }
      var shadowRoot = element.shadowRoot || // check for an undisclosed shadow
      typeof options.getShadowRoot === "function" && options.getShadowRoot(element);
      var validShadowRoot = !isInert(shadowRoot, false) && (!options.shadowRootFilter || options.shadowRootFilter(element));
      if (shadowRoot && validShadowRoot) {
        var _nestedCandidates = getCandidatesIteratively2(shadowRoot === true ? element.children : shadowRoot.children, true, options);
        if (options.flatten) {
          candidates.push.apply(candidates, _nestedCandidates);
        } else {
          candidates.push({
            scopeParent: element,
            candidates: _nestedCandidates
          });
        }
      } else {
        elementsToCheck.unshift.apply(elementsToCheck, element.children);
      }
    }
  }
  return candidates;
};
var hasTabIndex = function hasTabIndex2(node) {
  return !isNaN(parseInt(node.getAttribute("tabindex"), 10));
};
var getTabIndex = function getTabIndex2(node) {
  if (!node) {
    throw new Error("No node provided");
  }
  if (node.tabIndex < 0) {
    if ((/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) || isContentEditable(node)) && !hasTabIndex(node)) {
      return 0;
    }
  }
  return node.tabIndex;
};
var getSortOrderTabIndex = function getSortOrderTabIndex2(node, isScope) {
  var tabIndex = getTabIndex(node);
  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {
    return 0;
  }
  return tabIndex;
};
var sortOrderedTabbables = function sortOrderedTabbables2(a2, b3) {
  return a2.tabIndex === b3.tabIndex ? a2.documentOrder - b3.documentOrder : a2.tabIndex - b3.tabIndex;
};
var isInput = function isInput2(node) {
  return node.tagName === "INPUT";
};
var isHiddenInput = function isHiddenInput2(node) {
  return isInput(node) && node.type === "hidden";
};
var isDetailsWithSummary = function isDetailsWithSummary2(node) {
  var r4 = node.tagName === "DETAILS" && Array.prototype.slice.apply(node.children).some(function(child) {
    return child.tagName === "SUMMARY";
  });
  return r4;
};
var getCheckedRadio = function getCheckedRadio2(nodes, form) {
  for (var i2 = 0; i2 < nodes.length; i2++) {
    if (nodes[i2].checked && nodes[i2].form === form) {
      return nodes[i2];
    }
  }
};
var isTabbableRadio = function isTabbableRadio2(node) {
  if (!node.name) {
    return true;
  }
  var radioScope = node.form || getRootNode(node);
  var queryRadios = function queryRadios2(name) {
    return radioScope.querySelectorAll('input[type="radio"][name="' + name + '"]');
  };
  var radioSet;
  if (typeof window !== "undefined" && typeof window.CSS !== "undefined" && typeof window.CSS.escape === "function") {
    radioSet = queryRadios(window.CSS.escape(node.name));
  } else {
    try {
      radioSet = queryRadios(node.name);
    } catch (err) {
      console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s", err.message);
      return false;
    }
  }
  var checked = getCheckedRadio(radioSet, node.form);
  return !checked || checked === node;
};
var isRadio = function isRadio2(node) {
  return isInput(node) && node.type === "radio";
};
var isNonTabbableRadio = function isNonTabbableRadio2(node) {
  return isRadio(node) && !isTabbableRadio(node);
};
var isNodeAttached = function isNodeAttached2(node) {
  var _nodeRoot;
  var nodeRoot = node && getRootNode(node);
  var nodeRootHost = (_nodeRoot = nodeRoot) === null || _nodeRoot === void 0 ? void 0 : _nodeRoot.host;
  var attached = false;
  if (nodeRoot && nodeRoot !== node) {
    var _nodeRootHost, _nodeRootHost$ownerDo, _node$ownerDocument;
    attached = !!((_nodeRootHost = nodeRootHost) !== null && _nodeRootHost !== void 0 && (_nodeRootHost$ownerDo = _nodeRootHost.ownerDocument) !== null && _nodeRootHost$ownerDo !== void 0 && _nodeRootHost$ownerDo.contains(nodeRootHost) || node !== null && node !== void 0 && (_node$ownerDocument = node.ownerDocument) !== null && _node$ownerDocument !== void 0 && _node$ownerDocument.contains(node));
    while (!attached && nodeRootHost) {
      var _nodeRoot2, _nodeRootHost2, _nodeRootHost2$ownerD;
      nodeRoot = getRootNode(nodeRootHost);
      nodeRootHost = (_nodeRoot2 = nodeRoot) === null || _nodeRoot2 === void 0 ? void 0 : _nodeRoot2.host;
      attached = !!((_nodeRootHost2 = nodeRootHost) !== null && _nodeRootHost2 !== void 0 && (_nodeRootHost2$ownerD = _nodeRootHost2.ownerDocument) !== null && _nodeRootHost2$ownerD !== void 0 && _nodeRootHost2$ownerD.contains(nodeRootHost));
    }
  }
  return attached;
};
var isZeroArea = function isZeroArea2(node) {
  var _node$getBoundingClie = node.getBoundingClientRect(), width = _node$getBoundingClie.width, height = _node$getBoundingClie.height;
  return width === 0 && height === 0;
};
var isHidden = function isHidden2(node, _ref) {
  var displayCheck = _ref.displayCheck, getShadowRoot = _ref.getShadowRoot;
  if (getComputedStyle(node).visibility === "hidden") {
    return true;
  }
  var isDirectSummary = matches.call(node, "details>summary:first-of-type");
  var nodeUnderDetails = isDirectSummary ? node.parentElement : node;
  if (matches.call(nodeUnderDetails, "details:not([open]) *")) {
    return true;
  }
  if (!displayCheck || displayCheck === "full" || displayCheck === "legacy-full") {
    if (typeof getShadowRoot === "function") {
      var originalNode = node;
      while (node) {
        var parentElement = node.parentElement;
        var rootNode = getRootNode(node);
        if (parentElement && !parentElement.shadowRoot && getShadowRoot(parentElement) === true) {
          return isZeroArea(node);
        } else if (node.assignedSlot) {
          node = node.assignedSlot;
        } else if (!parentElement && rootNode !== node.ownerDocument) {
          node = rootNode.host;
        } else {
          node = parentElement;
        }
      }
      node = originalNode;
    }
    if (isNodeAttached(node)) {
      return !node.getClientRects().length;
    }
    if (displayCheck !== "legacy-full") {
      return true;
    }
  } else if (displayCheck === "non-zero-area") {
    return isZeroArea(node);
  }
  return false;
};
var isDisabledFromFieldset = function isDisabledFromFieldset2(node) {
  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {
    var parentNode = node.parentElement;
    while (parentNode) {
      if (parentNode.tagName === "FIELDSET" && parentNode.disabled) {
        for (var i2 = 0; i2 < parentNode.children.length; i2++) {
          var child = parentNode.children.item(i2);
          if (child.tagName === "LEGEND") {
            return matches.call(parentNode, "fieldset[disabled] *") ? true : !child.contains(node);
          }
        }
        return true;
      }
      parentNode = parentNode.parentElement;
    }
  }
  return false;
};
var isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable2(options, node) {
  if (node.disabled || // we must do an inert look up to filter out any elements inside an inert ancestor
  //  because we're limited in the type of selectors we can use in JSDom (see related
  //  note related to `candidateSelectors`)
  isInert(node) || isHiddenInput(node) || isHidden(node, options) || // For a details element with a summary, the summary element gets the focus
  isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {
    return false;
  }
  return true;
};
var isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable2(options, node) {
  if (isNonTabbableRadio(node) || getTabIndex(node) < 0 || !isNodeMatchingSelectorFocusable(options, node)) {
    return false;
  }
  return true;
};
var isValidShadowRootTabbable = function isValidShadowRootTabbable2(shadowHostNode) {
  var tabIndex = parseInt(shadowHostNode.getAttribute("tabindex"), 10);
  if (isNaN(tabIndex) || tabIndex >= 0) {
    return true;
  }
  return false;
};
var sortByOrder = function sortByOrder2(candidates) {
  var regularTabbables = [];
  var orderedTabbables = [];
  candidates.forEach(function(item, i2) {
    var isScope = !!item.scopeParent;
    var element = isScope ? item.scopeParent : item;
    var candidateTabindex = getSortOrderTabIndex(element, isScope);
    var elements = isScope ? sortByOrder2(item.candidates) : element;
    if (candidateTabindex === 0) {
      isScope ? regularTabbables.push.apply(regularTabbables, elements) : regularTabbables.push(element);
    } else {
      orderedTabbables.push({
        documentOrder: i2,
        tabIndex: candidateTabindex,
        item,
        isScope,
        content: elements
      });
    }
  });
  return orderedTabbables.sort(sortOrderedTabbables).reduce(function(acc, sortable) {
    sortable.isScope ? acc.push.apply(acc, sortable.content) : acc.push(sortable.content);
    return acc;
  }, []).concat(regularTabbables);
};
var tabbable = function tabbable2(container, options) {
  options = options || {};
  var candidates;
  if (options.getShadowRoot) {
    candidates = getCandidatesIteratively([container], options.includeContainer, {
      filter: isNodeMatchingSelectorTabbable.bind(null, options),
      flatten: false,
      getShadowRoot: options.getShadowRoot,
      shadowRootFilter: isValidShadowRootTabbable
    });
  } else {
    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));
  }
  return sortByOrder(candidates);
};
var focusableCandidateSelector = candidateSelectors.concat("iframe").join(",");

// node_modules/@storefront-ui/vue/dist/shared/render.mjs
var t3 = async () => new Promise((e5) => {
  setTimeout(() => {
    requestAnimationFrame(e5);
  });
});
var n4 = async (e5) => {
  for (; !document.body.contains(e5); )
    await t3();
};

// node_modules/@storefront-ui/vue/dist/composables/useTrapFocus/useTrapFocus.mjs
var O = ((t4) => (t4.autofocus = "autofocus", t4.container = "container", t4))(O || {});
var P2 = {
  trapTabs: true,
  activeState: ref(true),
  initialFocus: 0,
  initialFocusContainerFallback: false,
  arrowKeysOn: false,
  arrowKeysLeftRight: false,
  arrowKeysUpDown: false
};
var E3 = (t4, K3) => {
  const {
    trapTabs: w3,
    arrowFocusGroupSelector: l18,
    includeContainer: d26,
    activeState: D3,
    initialFocus: u45,
    // eslint-disable-next-line etc/no-deprecated
    arrowKeysOn: y5,
    arrowKeysLeftRight: p14,
    arrowKeysUpDown: b3,
    initialFocusContainerFallback: g4
  } = {
    ...P2,
    ...K3
  }, i2 = ref(), s12 = ref([]);
  let e5;
  const v3 = () => {
    i2.value = document.activeElement;
  }, f29 = ({
    event: o4,
    additionalData: a2
  }) => Mt({
    current: i2.value,
    focusables: s12.value,
    event: o4,
    ...a2
  }), c20 = ({
    event: o4,
    additionalData: a2
  }) => Tt({
    current: i2.value,
    focusables: s12.value,
    event: o4,
    ...a2
  }), m17 = (o4) => {
    const r4 = l18 && (e5 == null ? void 0 : e5.querySelector(l18)) ? { arrowFocusGroupSelector: l18 } : {};
    y5 && (o4.key === "ArrowLeft" || o4.key === "ArrowUp") && f29({ additionalData: r4 }), y5 && (o4.key === "ArrowRight" || o4.key === "ArrowDown") && c20({ additionalData: r4 }), p14 && o4.key === "ArrowLeft" && f29({ additionalData: r4 }), p14 && o4.key === "ArrowRight" && c20({ additionalData: r4 }), b3 && o4.key === "ArrowUp" && f29({ additionalData: r4 }), b3 && o4.key === "ArrowDown" && c20({ additionalData: r4 }), w3 && bt(o4) && c20({ event: o4 }), w3 && yt(o4) && f29({ event: o4 });
  }, x3 = () => {
    e5 == null || e5.removeEventListener("keydown", m17), e5 == null || e5.removeEventListener("focus", v3, true);
  };
  return watch(
    [t4, D3],
    async ([o4, a2]) => {
      if (o4 && a2) {
        let r4 = false;
        if (await t3(), e5 = unrefElement(o4), e5 == null || e5.addEventListener("focus", v3, true), e5 == null || e5.addEventListener("keydown", m17), s12.value = tabbable(e5, { includeContainer: d26 }), typeof u45 == "number")
          s12.value[u45] ? s12.value[u45].focus() : (console.error(`There is no element with given index ${u45}`), r4 = true);
        else if (u45 === "autofocus") {
          const k5 = s12.value.find((S3) => S3.hasAttribute("autofocus"));
          k5 ? k5.focus() : r4 = true;
        }
        (g4 && r4 || u45 === "container") && (e5 == null || e5.focus());
      } else
        s12.value = [], i2.value = void 0, x3();
    },
    { immediate: true }
  ), {
    current: i2,
    focusables: s12,
    focusNext: Tt,
    focusPrev: Mt,
    updateFocusableElements: () => {
      s12.value = tabbable(t4.value, { includeContainer: d26 });
    }
  };
};

// node_modules/@storefront-ui/vue/dist/components/SfModal/SfModal.vue.mjs
var F2 = defineComponent({
  __name: "SfModal",
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    tag: {
      type: [String, Object],
      default: ""
    },
    disableClickAway: {
      type: Boolean,
      default: false
    },
    disableEsc: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue"],
  setup(t4, { emit: l18 }) {
    const n6 = t4, { disableClickAway: s12, disableEsc: i2, modelValue: a2 } = toRefs(n6), o4 = l18, e5 = ref();
    onClickOutside(e5, () => {
      s12.value || o4("update:modelValue", false);
    });
    const r4 = () => {
      i2.value || o4("update:modelValue", false);
    };
    return E3(e5, {
      trapTabs: true,
      activeState: a2,
      initialFocus: false,
      initialFocusContainerFallback: true
    }), (d26, g4) => unref(a2) ? (openBlock(), createBlock(resolveDynamicComponent(t4.tag || "div"), {
      key: 0,
      ref_key: "modalRef",
      ref: e5,
      "aria-modal": "true",
      "data-testid": "modal",
      tabindex: "-1",
      class: "fixed inset-0 w-fit h-fit m-auto p-6 pt-10 lg:p-10 border border-neutral-100 bg-white shadow-xl rounded-xl outline-none",
      onKeydown: withKeys(r4, ["esc"])
    }, {
      default: withCtx(() => [
        renderSlot(d26.$slots, "default")
      ]),
      _: 3
    }, 544)) : createCommentVNode("", true);
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfProgressCircular/SfProgressCircular.vue.mjs
var p12 = ["aria-valuenow", "aria-label", "stroke-dasharray"];
var k4 = {
  [Z.xs]: "h-4 w-4 ring-2",
  [Z.sm]: "h-5 w-5 ring-2",
  [Z.base]: "h-6 w-6 ring-2",
  [Z.lg]: "h-8 w-8 ring-2",
  [Z.xl]: "h-10 w-10 ring-2",
  [Z["2xl"]]: "h-14 w-14 ring-[3px]",
  [Z["3xl"]]: "h-24 w-24 ring-4",
  [Z["4xl"]]: "h-48 w-48 ring-8"
};
var f27 = {
  [Z.xs]: "stroke-[10px]",
  [Z.sm]: "stroke-[8px]",
  [Z.base]: "stroke-[6px]",
  [Z.lg]: "stroke-[4px]",
  [Z.xl]: "stroke-[3px]",
  [Z["2xl"]]: "stroke-[3px]",
  [Z["3xl"]]: "stroke-2",
  [Z["4xl"]]: "stroke-2"
};
var v2 = defineComponent({
  __name: "SfProgressCircular",
  props: {
    value: {
      type: Number,
      default: 0
    },
    size: {
      type: String,
      default: Z.base
    },
    ariaLabel: {
      type: String,
      default: "Progress element"
    }
  },
  setup(r4) {
    const t4 = r4, { value: a2 } = toRefs(t4), o4 = computed(() => `${a2.value / 100 * 151}, 150`);
    return (l18, h12) => (openBlock(), createElementBlock("svg", {
      role: "progressbar",
      "aria-valuemin": "0",
      "aria-valuemax": "100",
      "aria-valuenow": unref(a2),
      "aria-label": r4.ariaLabel,
      class: normalizeClass(["inline-block ring-inset ring-neutral-300 text-primary-700 rounded-full transition-[stroke-dasharray] ease-in-out duration-500 text-sm", k4[r4.size]]),
      viewBox: "25 25 50 50",
      "stroke-dasharray": o4.value,
      "data-testid": "progress"
    }, [
      createBaseVNode("circle", {
        class: normalizeClass([f27[r4.size], "origin-bottom-right -rotate-90 stroke-current fill-none"]),
        cx: "50",
        cy: "50",
        r: "24"
      }, null, 2),
      renderSlot(l18.$slots, "default")
    ], 10, p12));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfProgressLinear/types.mjs
var e4 = ((r4) => (r4.minimal = "minimal", r4))(e4 || {});

// node_modules/@storefront-ui/vue/dist/components/SfProgressLinear/SfProgressLinear.vue.mjs
var o3 = ["aria-label", "value"];
var n5 = {
  [e4.minimal]: "h-1",
  [Z.xs]: "h-4",
  [Z.sm]: "h-5",
  [Z.base]: "h-6",
  [Z.lg]: "h-7",
  [Z.xl]: "h-10",
  [Z["2xl"]]: "h-14",
  [Z["3xl"]]: "h-24",
  [Z["4xl"]]: "h-48"
};
var c19 = defineComponent({
  __name: "SfProgressLinear",
  props: {
    value: {
      type: Number,
      default: 0
    },
    size: {
      type: String,
      default: Z.base
    },
    ariaLabel: {
      type: String,
      default: "Progress element"
    }
  },
  setup(r4) {
    return (u45, g4) => (openBlock(), createElementBlock("progress", {
      "data-testid": "progress-linear",
      max: "100",
      class: normalizeClass(["bg-neutral-300 text-primary-700 [&::-webkit-progress-bar]:bg-inherit [&::-webkit-progress-value]:bg-current [&::-webkit-progress-value]:transition-[width] [&::-webkit-progress-value]:ease-in-out [&::-webkit-progress-value]:duration-200 [&::-moz-progress-bar]:bg-current", n5[r4.size]]),
      "aria-label": r4.ariaLabel,
      value: r4.value
    }, null, 10, o3));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfRadio/SfRadio.vue.mjs
var f28 = ["name", "value", "disabled"];
var y2 = defineComponent({
  __name: "SfRadio",
  props: {
    modelValue: {
      type: String,
      default: ""
    },
    name: {
      type: String,
      default: ""
    },
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ""
    },
    invalid: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue"],
  setup(e5, { emit: t4 }) {
    const i2 = e5, l18 = t4, { modelValue: o4 } = toRefs(i2), a2 = computed({
      get: () => o4.value,
      set: (d26) => {
        l18("update:modelValue", d26);
      }
    });
    return (d26, r4) => withDirectives((openBlock(), createElementBlock("input", {
      "onUpdate:modelValue": r4[0] || (r4[0] = (c20) => a2.value = c20),
      name: e5.name,
      type: "radio",
      value: e5.value,
      class: normalizeClass([
        "h-5 w-5 border-2 p-[3px] bg-clip-content rounded-full appearance-none cursor-pointer focus-visible:outline focus-visible:outline-offset disabled:border-disabled-500 disabled:cursor-not-allowed disabled:checked:bg-disabled-500 disabled:checked:border-disabled-500",
        e5.invalid && !e5.disabled ? "border-negative-700 checked:bg-negative-700 hover:border-negative-800 hover:checked:bg-negative-800 active:border-negative-900 active:checked:bg-negative-900" : "border-neutral-500 active:border-primary-900 hover:border-primary-700 checked:bg-primary-700 checked:border-primary-700 hover:checked:bg-primary-800 hover:checked:border-primary-800 active:checked:bg-primary-900 active:checked:border-primary-900"
      ]),
      disabled: e5.disabled
    }, null, 10, f28)), [
      [vModelRadio, a2.value]
    ]);
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfRating/SfRating.vue.mjs
var I2 = ["ariaLabel", "title"];
var L2 = {
  [_.xs]: "text-xs",
  [_.sm]: "text-sm",
  [_.base]: "text-base",
  [_.lg]: "text-lg",
  [_.xl]: "text-xl"
};
var q11 = defineComponent({
  __name: "SfRating",
  props: {
    size: {
      type: String,
      default: _.base
    },
    max: {
      type: Number,
      default: 5
    },
    value: {
      type: Number,
      default: 0
    },
    halfIncrement: {
      type: Boolean,
      default: false
    },
    ariaLabel: {
      type: String,
      default: void 0
    }
  },
  setup(m17) {
    const g4 = m17, { value: c20, max: l18, halfIncrement: _7, ariaLabel: r4 } = toRefs(g4), b3 = computed(() => _7.value ? 0.5 : 1), f29 = computed(() => Lt(Dt(c20.value, b3.value), 0, l18.value)), n6 = computed(() => +!Number.isInteger(f29.value)), d26 = computed(() => Math.ceil(f29.value - n6.value)), h12 = computed(() => l18.value - d26.value - n6.value), p14 = computed(() => (r4 == null ? void 0 : r4.value) ?? `${c20.value} out of ${l18.value}`);
    return (R2, F3) => (openBlock(), createElementBlock("span", {
      role: "img",
      ariaLabel: p14.value,
      title: p14.value,
      class: normalizeClass(["inline-flex items-center text-warning-500", L2[m17.size]]),
      "data-testid": "rating"
    }, [
      (openBlock(true), createElementBlock(Fragment, null, renderList(d26.value, (s12) => (openBlock(), createBlock(unref(u37), {
        key: s12,
        "aria-hidden": "true",
        class: "w-[1.5em] h-[1.5em]"
      }))), 128)),
      n6.value ? (openBlock(), createBlock(unref(u38), {
        key: 0,
        "aria-hidden": "true",
        class: "w-[1.5em] h-[1.5em]"
      })) : createCommentVNode("", true),
      (openBlock(true), createElementBlock(Fragment, null, renderList(h12.value, (s12) => (openBlock(), createBlock(unref(u36), {
        key: s12,
        "aria-hidden": "true",
        class: "text-disabled-500 w-[1.5em] h-[1.5em]"
      }))), 128))
    ], 10, I2));
  }
});

// node_modules/@storefront-ui/vue/dist/composables/useScrollable/useScrollable.mjs
function y3(e5) {
  const t4 = ref(), v3 = ref(null), a2 = ref({ hasPrev: false, hasNext: false, isDragged: false });
  let d26 = noop;
  watch(
    [t4, e5],
    () => {
      var h12, m17, s12;
      const l18 = unrefElement(t4);
      if (!l18)
        return;
      d26(), v3.value = new Et(l18, {
        ...e5 == null ? void 0 : e5.value,
        onScroll: (u45) => {
          var r4, c20;
          a2.value = { ...a2.value, hasNext: u45.hasNext, hasPrev: u45.hasPrev }, (c20 = (r4 = e5 == null ? void 0 : e5.value) == null ? void 0 : r4.onScroll) == null || c20.call(r4, u45);
        },
        onDragStart: (u45) => {
          var r4, c20;
          a2.value = { ...a2.value, isDragged: u45.isDragged }, (c20 = (r4 = e5 == null ? void 0 : e5.value) == null ? void 0 : r4.onDragStart) == null || c20.call(r4, u45);
        }
      }), d26 = (h12 = v3.value) == null ? void 0 : h12.register();
      const f29 = (m17 = e5 == null ? void 0 : e5.value) == null ? void 0 : m17.activeIndex;
      typeof f29 == "number" && f29 >= 0 && ((s12 = e5 == null ? void 0 : e5.value) != null && s12.isActiveIndexCentered) && v3.value.scrollToIndex(f29);
    },
    { immediate: true, deep: true }
  );
  const g4 = () => {
    var l18;
    (l18 = v3.value) == null || l18.prev();
  }, x3 = () => {
    var l18;
    (l18 = v3.value) == null || l18.next();
  }, b3 = computed(() => ({
    onClick: g4,
    disabled: !a2.value.hasPrev
  })), N2 = computed(() => ({
    onClick: x3,
    disabled: !a2.value.hasNext
  }));
  return {
    containerRef: t4,
    getPrevButtonProps: b3,
    getNextButtonProps: N2,
    showNext: x3,
    showPrev: g4,
    state: a2
  };
}

// node_modules/@storefront-ui/vue/dist/components/SfScrollable/SfScrollable.vue.mjs
var V2 = {
  inheritAttrs: false
};
var W2 = defineComponent({
  ...V2,
  __name: "SfScrollable",
  props: {
    tag: {
      type: [String, Object],
      default: "div"
    },
    direction: {
      type: String,
      default: u2.horizontal
    },
    buttonsPlacement: {
      type: String,
      default: T.block
    },
    wrapperClass: t,
    activeIndex: {
      type: Number,
      default: void 0
    },
    reduceMotion: {
      type: Boolean,
      default: void 0
    },
    drag: {
      type: [Object, Boolean],
      default: void 0
    },
    prevDisabled: {
      type: Boolean,
      default: void 0
    },
    nextDisabled: {
      type: Boolean,
      default: void 0
    },
    isActiveIndexCentered: {
      type: Boolean,
      default: false
    },
    buttonPrevAriaLabel: {
      type: String,
      default: "Previous"
    },
    buttonNextAriaLabel: {
      type: String,
      default: "Next"
    }
  },
  emits: ["onDragStart", "onDragEnd", "onScroll", "onPrev", "onNext"],
  setup(a2, { emit: B4 }) {
    const r4 = a2, i2 = B4, { direction: h12, activeIndex: D3, reduceMotion: k5, drag: z3, isActiveIndexCentered: C6 } = toRefs(r4), { containerRef: N2, state: $3, getNextButtonProps: b3, getPrevButtonProps: f29 } = y3(
      computed(() => ({
        ...reactive({
          direction: h12,
          activeIndex: D3,
          reduceMotion: k5,
          drag: z3,
          isActiveIndexCentered: C6
        }),
        onDragStart: (e5) => i2("onDragStart", e5),
        onDragEnd: (e5) => i2("onDragEnd", e5),
        onScroll: (e5) => i2("onScroll", e5),
        onPrev: (e5) => i2("onPrev", e5),
        onNext: (e5) => i2("onNext", e5)
      }))
    ), l18 = computed(() => r4.direction === u2.horizontal), n6 = computed(() => r4.buttonsPlacement === T.floating), d26 = computed(() => r4.buttonsPlacement === T.block);
    return (e5, F3) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(["items-center", "relative", l18.value ? "flex" : "flex-col h-full inline-flex", a2.wrapperClass])
    }, [
      e5.$slots.previousButton && a2.buttonsPlacement !== unref(T).none ? renderSlot(e5.$slots, "previousButton", normalizeProps(mergeProps({ key: 0 }, unref(f29)))) : a2.buttonsPlacement !== unref(T).none ? (openBlock(), createBlock(unref(j2), mergeProps({
        key: 1,
        variant: "secondary",
        size: "lg",
        square: "",
        class: [
          "!rounded-full bg-white hidden md:block !ring-neutral-500 !text-neutral-500",
          {
            "mr-4": d26.value && l18.value,
            "mb-4 rotate-90": d26.value && !l18.value,
            "absolute left-4 z-10": n6.value && l18.value,
            "absolute top-4 rotate-90 z-10": n6.value && !l18.value
          },
          n6.value ? "disabled:hidden" : "disabled:!ring-disabled-300 disabled:!text-disabled-500"
        ]
      }, unref(f29), {
        disabled: a2.prevDisabled || unref(f29).disabled,
        "aria-label": a2.buttonPrevAriaLabel
      }), {
        default: withCtx(() => [
          createVNode(unref(u10))
        ]),
        _: 1
      }, 16, ["class", "disabled", "aria-label"])) : createCommentVNode("", true),
      (openBlock(), createBlock(resolveDynamicComponent(a2.tag), mergeProps({
        ref_key: "containerRef",
        ref: N2,
        class: [
          "motion-safe:scroll-smooth",
          {
            "overflow-x-auto flex gap-4": l18.value,
            "overflow-y-auto flex flex-col gap-4": !l18.value,
            "cursor-grab": unref($3).isDragged
          }
        ]
      }, { ...e5.$attrs, ...r4 }, { disabled: a2.prevDisabled }), {
        default: withCtx(() => [
          renderSlot(e5.$slots, "default")
        ]),
        _: 3
      }, 16, ["class", "disabled"])),
      e5.$slots.nextButton && a2.buttonsPlacement !== unref(T).none ? renderSlot(e5.$slots, "nextButton", normalizeProps(mergeProps({ key: 2 }, unref(b3)))) : a2.buttonsPlacement !== unref(T).none ? (openBlock(), createBlock(unref(j2), mergeProps({
        key: 3,
        variant: "secondary",
        size: "lg",
        square: "",
        class: [
          "!rounded-full bg-white hidden md:block !ring-neutral-500 !text-neutral-500",
          {
            "ml-4": d26.value && l18.value,
            "mt-4 rotate-90": d26.value && !l18.value,
            "absolute right-4 z-10": n6.value && l18.value,
            "absolute bottom-4 rotate-90 z-10": n6.value && !l18.value
          },
          n6.value ? "disabled:hidden" : "disabled:!ring-disabled-300 disabled:!text-disabled-500"
        ]
      }, unref(b3), {
        disabled: a2.nextDisabled || unref(b3).disabled,
        "aria-label": a2.buttonNextAriaLabel
      }), {
        default: withCtx(() => [
          createVNode(unref(h4))
        ]),
        _: 1
      }, 16, ["class", "disabled", "aria-label"])) : createCommentVNode("", true)
    ], 2));
  }
});

// node_modules/@storefront-ui/vue/dist/composables/useDisclosure/useDisclosure.mjs
function i({ initialValue: o4 = false } = {}) {
  const e5 = ref(unref(o4)), s12 = (r4) => e5.value = r4 ?? !e5.value;
  return isRef(o4) && syncRefs(o4, e5, { immediate: true }), { isOpen: e5, open: () => s12(true), close: () => s12(false), toggle: s12 };
}

// node_modules/@storefront-ui/vue/dist/components/SfSelect/SfSelect.vue.mjs
var D2 = ["required", "disabled"];
var E4 = {
  inheritAttrs: false
};
var O2 = defineComponent({
  ...E4,
  __name: "SfSelect",
  props: {
    size: {
      type: String,
      default: B.base
    },
    placeholder: {
      type: String,
      default: ""
    },
    required: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    invalid: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: String,
      default: ""
    },
    wrapperClassName: {
      type: String,
      default: ""
    }
  },
  emits: ["update:modelValue"],
  setup(t4, { emit: c20 }) {
    const p14 = t4, g4 = c20, { isOpen: b3, close: n6, open: r4 } = i(), { isFocusVisible: y5 } = g2(), d26 = computed({
      get: () => p14.modelValue,
      set: (i2) => g4("update:modelValue", i2)
    });
    return (i2, l18) => (openBlock(), createElementBlock("span", {
      class: normalizeClass([
        "relative flex flex-col rounded-md",
        {
          "focus-within:outline focus-within:outline-offset": unref(y5)
        },
        t4.wrapperClassName
      ]),
      "data-testid": "select"
    }, [
      withDirectives(createBaseVNode("select", mergeProps({
        required: t4.required,
        "onUpdate:modelValue": l18[0] || (l18[0] = (a2) => d26.value = a2),
        disabled: t4.disabled,
        class: [
          "appearance-none disabled:cursor-not-allowed cursor-pointer pl-4 pr-3.5 text-neutral-900 ring-inset focus:ring-primary-700 focus:ring-2 outline-none bg-transparent rounded-md ring-1 ring-neutral-300 hover:ring-primary-700 active:ring-2 active:ring-primary-700 disabled:bg-disabled-100 disabled:text-disabled-900 disabled:ring-disabled-200",
          {
            "py-1.5": t4.size === unref(B).sm,
            "py-2": t4.size === unref(B).base,
            "py-3 text-base": t4.size === unref(B).lg,
            "!ring-negative-700 ring-2": t4.invalid && !t4.disabled
          }
        ],
        "data-testid": "select-input",
        onBlur: l18[1] || (l18[1] = //@ts-ignore
        (...a2) => unref(n6) && unref(n6)(...a2)),
        onChange: l18[2] || (l18[2] = //@ts-ignore
        (...a2) => unref(n6) && unref(n6)(...a2)),
        onClick: l18[3] || (l18[3] = //@ts-ignore
        (...a2) => unref(r4) && unref(r4)(...a2)),
        onKeydown: l18[4] || (l18[4] = withKeys(
          //@ts-ignore
          (...a2) => unref(r4) && unref(r4)(...a2),
          ["space"]
        ))
      }, i2.$attrs), [
        t4.placeholder ? (openBlock(), createElementBlock("option", {
          key: 0,
          disabled: "",
          hidden: "",
          class: normalizeClass(["text-sm bg-neutral-300", [
            "bg-neutral-300 text-sm",
            {
              "text-base": t4.size === unref(B).lg
            }
          ]]),
          value: "",
          "data-testid": "select-placeholder"
        }, toDisplayString(t4.placeholder), 3)) : createCommentVNode("", true),
        renderSlot(i2.$slots, "default")
      ], 16, D2), [
        [vModelSelect, d26.value]
      ]),
      renderSlot(i2.$slots, "chevron", {}, () => [
        createVNode(unref(u17), {
          class: normalizeClass([
            "absolute -translate-y-1 pointer-events-none top-1/3 right-4 transition easy-in-out duration-0.5",
            t4.disabled ? "text-disabled-500" : "text-neutral-500",
            unref(b3) ? "rotate-180" : ""
          ])
        }, null, 8, ["class"])
      ])
    ], 2));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfSwitch/SfSwitch.vue.mjs
var y4 = ["value", "aria-checked"];
var x2 = defineComponent({
  __name: "SfSwitch",
  props: {
    modelValue: {
      type: [String, Array, Boolean],
      default: false
    },
    value: {
      type: String,
      default: ""
    },
    invalid: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue"],
  setup(e5, { emit: c20 }) {
    const b3 = e5, { modelValue: r4 } = toRefs(b3), l18 = c20, o4 = ref(), t4 = computed({
      get: () => r4 == null ? void 0 : r4.value,
      set: (a2) => l18("update:modelValue", a2)
    });
    return (a2, d26) => {
      var i2;
      return withDirectives((openBlock(), createElementBlock("input", {
        ref_key: "switchRef",
        ref: o4,
        "onUpdate:modelValue": d26[0] || (d26[0] = (n6) => t4.value = n6),
        class: normalizeClass([
          "appearance-none h-5 min-w-[36px] bg-transparent border-2 border-gray-500 rounded-full relative ease-in-out duration-300 hover:border-primary-800 hover:before:checked:bg-white checked:before:left-1/2 checked:before:ml-0 checked:before:mr-0.5 disabled:before:bg-gray-500/50 hover:before:bg-primary-800 active:border-primary-800 active:before:bg-primary-800 checked:bg-none checked:bg-primary-700 checked:border-primary-700 checked:before:bg-white hover:checked:bg-primary-800 hover:checked:border-primary-800 disabled:border-gray-500/50 checked:disabled:before:bg-white checked:disabled:bg-gray-500/50 checked:disabled:border-0 before:transition-all  before:w-3.5 before:h-3.5 before:bg-gray-500 before:absolute before:top-0 before:bottom-0 before:my-auto before:rounded-full before:left-0 before:ml-0.5 before:ease-in-out before:duration-300 cursor-pointer disabled:cursor-not-allowed focus-visible:outline focus-visible:outline-offset",
          {
            "border-negative-700 hover:border-negative-800 active:border-negative-900 before:bg-negative-900": e5.invalid
          }
        ]),
        value: e5.value,
        type: "checkbox",
        role: "switch",
        "data-testid": "switch",
        "aria-checked": (i2 = o4.value) == null ? void 0 : i2.checked
      }, null, 10, y4)), [
        [vModelCheckbox, t4.value]
      ]);
    };
  }
});

// node_modules/@storefront-ui/vue/dist/composables/useTooltip/useTooltip.mjs
function A3(w3) {
  const {
    placement: g4 = "top",
    strategy: y5,
    middleware: d26,
    arrowSize: o4 = "6px",
    ...v3
  } = w3 || {}, t4 = ref(), { isOpen: r4, open: n6, close: l18, toggle: b3 } = i(), {
    style: a2,
    middlewareData: s12,
    placement: h12,
    referenceRef: i2,
    floatingRef: p14
  } = b2({
    isOpen: r4,
    placement: g4,
    strategy: y5,
    middleware: computed(() => [...unref(d26) || [offset2(8), shift2(), flip2()], arrow3({ element: t4 })]),
    ...v3
  });
  function f29() {
    if (s12.value.arrow) {
      const { x: c20, y: m17 } = s12.value.arrow, S3 = unref(h12).split("-")[0];
      return {
        position: "absolute",
        width: o4,
        height: o4,
        top: typeof m17 == "number" ? `${m17}px` : "",
        left: typeof c20 == "number" ? `${c20}px` : "",
        [{
          top: "bottom",
          right: "left",
          bottom: "top",
          left: "right"
        }[S3]]: `calc(${o4} / -2)`
      };
    }
    return {};
  }
  const P5 = computed(() => ({
    ref: i2,
    onMouseenter: n6,
    onMouseleave: l18
  })), x3 = computed(() => ({
    ref: p14,
    style: a2.value
  })), R2 = computed(() => ({
    ref: t4,
    style: f29()
  }));
  return {
    referenceRef: i2,
    floatingRef: p14,
    arrowRef: t4,
    style: computed(() => ({
      floating: a2.value,
      arrow: f29()
    })),
    isOpen: r4,
    open: n6,
    close: l18,
    toggle: b3,
    triggerProps: P5,
    tooltipProps: x3,
    arrowProps: R2
  };
}

// node_modules/@storefront-ui/vue/dist/components/SfTooltip/SfTooltip.vue.mjs
var P3 = defineComponent({
  __name: "SfTooltip",
  props: {
    placement: {
      type: String,
      default: void 0
    },
    middleware: {
      type: Array,
      default: void 0
    },
    strategy: {
      type: String,
      default: void 0
    },
    showArrow: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      required: true
    }
  },
  setup(e5) {
    const s12 = e5, { placement: n6, middleware: p14, strategy: d26 } = toRefs(s12), { isOpen: i2, triggerProps: c20, tooltipProps: m17, arrowProps: u45 } = A3({ placement: n6, middleware: p14, strategy: d26 });
    return (f29, S3) => (openBlock(), createElementBlock("span", mergeProps({ "data-testid": "tooltip" }, unref(c20)), [
      renderSlot(f29.$slots, "default"),
      e5.label && unref(i2) ? (openBlock(), createElementBlock("div", mergeProps({
        key: 0,
        role: "tooltip",
        class: "bg-black px-2 py-1.5 rounded-md text-white text-xs w-max max-w-[360px] drop-shadow"
      }, unref(m17)), [
        createTextVNode(toDisplayString(e5.label) + " ", 1),
        e5.showArrow ? (openBlock(), createElementBlock("span", mergeProps({ key: 0 }, unref(u45), { class: "bg-black rotate-45" }), null, 16)) : createCommentVNode("", true)
      ], 16)) : createCommentVNode("", true)
    ], 16));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfThumbnail/SfThumbnail.vue.mjs
var r3 = {
  [z.sm]: "w-5 h-5",
  [z.base]: "w-6 h-6",
  [z.lg]: "w-10 h-10",
  [z.xl]: "w-14 h-14"
};
var p13 = defineComponent({
  __name: "SfThumbnail",
  props: {
    size: {
      type: String,
      default: z.base
    }
  },
  setup(t4) {
    return (s12, m17) => (openBlock(), createElementBlock("div", {
      class: normalizeClass(["rounded-full overflow-hidden bg-clip-content p-0.5", r3[t4.size]]),
      "data-testid": "chip-thumbnail"
    }, [
      renderSlot(s12.$slots, "default")
    ], 2));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfRatingButton/SfRatingButton.vue.mjs
var w2 = {
  role: "radiogroup",
  class: "flex",
  "data-testid": "ratingbutton"
};
var A4 = ["onMouseenter"];
var C5 = ["name", "value", "disabled", "aria-label"];
var j4 = defineComponent({
  __name: "SfRatingButton",
  props: {
    modelValue: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 5
    },
    disabled: {
      type: Boolean,
      default: false
    },
    name: {
      type: String,
      default: "sf-rating-button"
    },
    size: {
      type: String,
      default: V.base
    },
    getLabelText: {
      type: Function,
      default: (e5) => `${e5} Star${e5 !== 1 ? "s" : ""}`
    }
  },
  emits: ["update:modelValue"],
  setup(e5, { emit: z3 }) {
    const m17 = e5, S3 = z3, { max: f29, disabled: r4, modelValue: h12 } = toRefs(m17), a2 = ref(0), g4 = computed(() => Array.from({ length: Math.floor(Math.abs(f29.value)) }, (t4, s12) => s12 + 1)), b3 = (t4) => t4 <= a2.value || a2.value === 0 && t4 <= h12.value, d26 = {
      [V.sm]: q.base,
      [V.base]: q.lg,
      [V.lg]: q.xl
    }, p14 = computed({
      set(t4) {
        S3("update:modelValue", t4);
      },
      get() {
        return m17.modelValue;
      }
    }), M2 = (t4) => {
      r4.value || (a2.value = t4);
    }, k5 = () => {
      r4.value || (a2.value = 0);
    };
    return (t4, s12) => (openBlock(), createElementBlock("div", w2, [
      (openBlock(true), createElementBlock(Fragment, null, renderList(g4.value, (l18) => (openBlock(), createElementBlock("label", {
        key: l18,
        onMouseenter: (v3) => M2(l18),
        onMouseleave: k5
      }, [
        withDirectives(createBaseVNode("input", {
          "onUpdate:modelValue": s12[0] || (s12[0] = (v3) => p14.value = v3),
          type: "radio",
          class: "sr-only peer",
          name: e5.name,
          value: l18,
          disabled: unref(r4),
          "aria-label": e5.getLabelText(l18)
        }, null, 8, C5), [
          [vModelRadio, p14.value]
        ]),
        renderSlot(t4.$slots, "default", {
          isFilled: b3(l18),
          iconSize: d26[e5.size],
          max: unref(f29)
        }, () => [
          b3(l18) ? (openBlock(), createBlock(unref(u37), {
            key: 0,
            role: "none",
            class: "text-primary-700 cursor-pointer peer-disabled:cursor-default peer-disabled:text-disabled-500 peer-focus-visible:outline",
            size: d26[e5.size]
          }, null, 8, ["size"])) : (openBlock(), createBlock(unref(u36), {
            key: 1,
            role: "none",
            class: "text-neutral-500 cursor-pointer peer-disabled:cursor-default peer-disabled:text-disabled-500 peer-focus-visible:outline",
            size: d26[e5.size]
          }, null, 8, ["size"]))
        ])
      ], 40, A4))), 128))
    ]));
  }
});

// node_modules/@storefront-ui/vue/dist/components/SfTextarea/SfTextarea.vue.mjs
var V3 = {
  [P.sm]: " h-[56px] py-[6px] pl-4 pr-3",
  [P.base]: "h-[64px] py-2 pl-4 pr-3",
  [P.lg]: "h-[72px], p-3 pl-4"
};
var B3 = defineComponent({
  __name: "SfTextarea",
  props: {
    modelValue: {
      type: [String, Number],
      default: ""
    },
    size: {
      type: String,
      default: P.base
    },
    invalid: {
      type: Boolean,
      default: false
    }
  },
  emits: ["update:modelValue"],
  setup(e5, { emit: o4 }) {
    const s12 = e5, l18 = o4, { modelValue: i2 } = toRefs(s12), { isFocusVisible: u45 } = g2({ isTextInput: true }), a2 = computed({
      get: () => i2 == null ? void 0 : i2.value,
      set: (r4) => l18("update:modelValue", r4)
    });
    return (r4, n6) => withDirectives((openBlock(), createElementBlock("textarea", {
      "onUpdate:modelValue": n6[0] || (n6[0] = (p14) => a2.value = p14),
      class: normalizeClass([
        "px-4 bg-white rounded-md text-neutral-900 ring-inset hover:ring-primary-800 focus:caret-primary-700 active:caret-primary-700 active:ring-primary-700 active:ring-2 focus:ring-primary-700 focus:ring-2 outline-none",
        {
          "ring-2 ring-negative-700": e5.invalid,
          "ring-1 ring-neutral-200": !e5.invalid,
          "focus:outline focus:outline-offset": unref(u45)
        },
        V3[e5.size]
      ]),
      "data-testid": "textarea"
    }, null, 2)), [
      [vModelText, a2.value]
    ]);
  }
});

// node_modules/@storefront-ui/vue/dist/composables/usePagination/usePagination.mjs
var import_jw_paginate = __toESM(require_jw_paginate(), 1);
function P4({ totalItems: n6, currentPage: r4 = 1, pageSize: u45 = 10, maxPages: g4 = 1 }) {
  const t4 = ref(r4), a2 = computed(() => (0, import_jw_paginate.default)(n6, t4.value, u45, g4));
  return {
    totalPages: computed(() => a2.value.totalPages),
    pages: computed(() => a2.value.pages),
    selectedPage: computed(() => a2.value.currentPage),
    endPage: computed(() => a2.value.endPage),
    startPage: computed(() => a2.value.startPage),
    maxVisiblePages: g4,
    next: () => t4.value += 1,
    prev: () => t4.value -= 1,
    setPage: (l18) => t4.value = l18
  };
}
export {
  t as ClassProp,
  O as InitialFocusType,
  Et as Scrollable,
  C as SfAccordionItem,
  g as SfBadge,
  Y as SfBadgePlacement,
  X as SfBadgeVariant,
  j2 as SfButton,
  j as SfButtonSize,
  G as SfButtonVariant,
  y as SfCheckbox,
  A2 as SfChip,
  C2 as SfChipSize,
  C3 as SfCounter,
  U as SfCounterSize,
  K2 as SfDrawer,
  A as SfDrawerPlacement,
  B2 as SfDropdown,
  u3 as SfIconAdd,
  m3 as SfIconAddShoppingCart,
  h2 as SfIconAlokai,
  _2 as SfIconAlokaiFull,
  u4 as SfIconArrowBack,
  u5 as SfIconArrowDownward,
  u6 as SfIconArrowForward,
  u7 as SfIconArrowUpward,
  m2 as SfIconBase,
  d3 as SfIconBlock,
  h3 as SfIconCalendarToday,
  q3 as SfIconCall,
  u8 as SfIconCancel,
  u9 as SfIconCheck,
  q4 as SfIconCheckBox,
  _3 as SfIconCheckBoxOutlineBlank,
  q5 as SfIconCheckCircle,
  u10 as SfIconChevronLeft,
  h4 as SfIconChevronRight,
  u11 as SfIconCircle,
  u12 as SfIconClose,
  u13 as SfIconCloseSm,
  d5 as SfIconCompareArrows,
  d6 as SfIconContactSupport,
  u14 as SfIconCreditCard,
  l2 as SfIconDelete,
  _4 as SfIconDownload,
  z2 as SfIconEmail,
  u15 as SfIconError,
  u16 as SfIconExpandLess,
  u17 as SfIconExpandMore,
  l4 as SfIconFacebook,
  u18 as SfIconFavorite,
  u19 as SfIconFavoriteFilled,
  d9 as SfIconGridView,
  d10 as SfIconHelp,
  d11 as SfIconHome,
  u20 as SfIconIndeterminateCheckBox,
  h6 as SfIconInfo,
  u21 as SfIconInstagram,
  u22 as SfIconLanguage,
  l5 as SfIconLocalShipping,
  d13 as SfIconLocal_fire_department,
  h8 as SfIconLocationOn,
  u23 as SfIconLocationOnFilled,
  l7 as SfIconLock,
  l8 as SfIconLockOpen,
  q6 as SfIconLogin,
  u24 as SfIconLogout,
  l11 as SfIconMenu,
  u25 as SfIconMoreHoriz,
  u26 as SfIconMoreVert,
  u27 as SfIconOpenInNew,
  d16 as SfIconOpenSource,
  l12 as SfIconPackage,
  d17 as SfIconPercent,
  l13 as SfIconPerson,
  u28 as SfIconPinterest,
  l14 as SfIconPublishedWithChanges,
  l15 as SfIconRadioButtonChecked,
  l16 as SfIconRadioButtonUnchecked,
  u29 as SfIconReact,
  u30 as SfIconRemove,
  h9 as SfIconRemoveShoppingCart,
  u31 as SfIconSafetyCheck,
  u32 as SfIconSchedule,
  u33 as SfIconSearch,
  u34 as SfIconSell,
  d20 as SfIconShare,
  h10 as SfIconShoppingCart,
  m11 as SfIconShoppingCartCheckout,
  q as SfIconSize,
  u35 as SfIconSort,
  u36 as SfIconStar,
  u37 as SfIconStarFilled,
  u38 as SfIconStarHalf,
  d21 as SfIconThumbDown,
  h11 as SfIconThumbUp,
  _6 as SfIconTune,
  u39 as SfIconTwitter,
  u40 as SfIconUndo,
  u41 as SfIconUnfoldMore,
  q9 as SfIconUpload,
  d22 as SfIconViewList,
  q10 as SfIconVisibility,
  d23 as SfIconVisibilityOff,
  u42 as SfIconVsfDiamond,
  d24 as SfIconVuejs,
  d25 as SfIconVuestorefront,
  u43 as SfIconWarehouse,
  u44 as SfIconWarning,
  l17 as SfIconYoutube,
  N as SfInput,
  $ as SfInputSize,
  v as SfLink,
  R as SfLinkVariant,
  g3 as SfListItem,
  J as SfListItemSize,
  k3 as SfLoaderCircular,
  p11 as SfLoaderLinear,
  e3 as SfLoaderLinearSize,
  Q as SfLoaderSize,
  F2 as SfModal,
  S as SfPopoverPlacement,
  tt as SfPopoverStrategy,
  v2 as SfProgressCircular,
  c19 as SfProgressLinear,
  e4 as SfProgressLinearSize,
  Z as SfProgressSize,
  y2 as SfRadio,
  q11 as SfRating,
  j4 as SfRatingButton,
  V as SfRatingButtonSize,
  _ as SfRatingSize,
  W2 as SfScrollable,
  T as SfScrollableButtonsPlacement,
  u2 as SfScrollableDirection,
  O2 as SfSelect,
  B as SfSelectSize,
  x2 as SfSwitch,
  B3 as SfTextarea,
  P as SfTextareaSize,
  p13 as SfThumbnail,
  z as SfThumbnailSize,
  P3 as SfTooltip,
  f as useAttrsRef,
  i as useDisclosure,
  C4 as useDropdown,
  g2 as useFocusVisible,
  e as useId,
  P4 as usePagination,
  b2 as usePopover,
  y3 as useScrollable,
  n as useSlotsRef,
  A3 as useTooltip,
  E3 as useTrapFocus,
  n4 as waitForElementInDOM,
  t3 as waitForNextRender
};
/*! Bundled license information:

tabbable/dist/index.esm.js:
  (*!
  * tabbable 6.2.0
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  *)
*/
//# sourceMappingURL=@storefront-ui_vue.js.map
