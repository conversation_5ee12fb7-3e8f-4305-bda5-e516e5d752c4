import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/ro.es.js
var weekDays = [
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>"
];
var months = [
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "August",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>mb<PERSON>",
  "Decembrie"
];
var years = "Ani";
var year = "An";
var month = "Lună";
var week = "Săptămână";
var day = "Zi";
var today = "Azi";
var noEvent = "Nici o interacțiune";
var allDay = "Toată ziua";
var deleteEvent = "Șterge";
var createEvent = "Adaugă un eveniment";
var dateFormat = "dddd D MMMM YYYY";
var ro = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  ro as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/ro.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=ro.es-ZGTSL7M6.js.map
