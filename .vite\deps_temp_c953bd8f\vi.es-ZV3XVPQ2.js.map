{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/vi.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"Thứ hai\",\n  \"<PERSON><PERSON><PERSON> ba\",\n  \"<PERSON><PERSON><PERSON> tư\",\n  \"<PERSON><PERSON><PERSON> năm\",\n  \"<PERSON><PERSON><PERSON> sá<PERSON>\",\n  \"<PERSON><PERSON><PERSON> bả<PERSON>\",\n  \"<PERSON><PERSON> nhật\"\n];\nconst weekDaysShort = [\n  \"T2\",\n  \"T3\",\n  \"T4\",\n  \"T5\",\n  \"T6\",\n  \"T7\",\n  \"CN\"\n];\nconst months = [\n  \"Tháng 1\",\n  \"Tháng 2\",\n  \"Tháng 3\",\n  \"Tháng 4\",\n  \"Tháng 5\",\n  \"Tháng 6\",\n  \"Tháng 7\",\n  \"Tháng 8\",\n  \"Tháng 9\",\n  \"Tháng 10\",\n  \"Tháng 11\",\n  \"Tháng 12\"\n];\nconst years = \"Năm\";\nconst year = \"Năm nay\";\nconst month = \"Tháng\";\nconst week = \"Tuần\";\nconst day = \"Ngày\";\nconst today = \"Hôm nay\";\nconst noEvent = \"NKhông có Event\";\nconst allDay = \"Cả ngày\";\nconst deleteEvent = \"Xóa\";\nconst createEvent = \"Tạo event\";\nconst dateFormat = \"dddd MMMM D YYYY\";\nconst vi = {\n  weekDays,\n  weekDaysShort,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  vi as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  weekDaysShort,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}