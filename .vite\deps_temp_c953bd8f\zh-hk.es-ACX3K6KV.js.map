{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/zh-hk.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"星期一\",\n  \"星期二\",\n  \"星期三\",\n  \"星期四\",\n  \"星期五\",\n  \"星期六\",\n  \"星期日\"\n];\nconst weekDaysShort = [\n  \"一\",\n  \"二\",\n  \"三\",\n  \"四\",\n  \"五\",\n  \"六\",\n  \"日\"\n];\nconst months = [\n  \"一月\",\n  \"二月\",\n  \"三月\",\n  \"四月\",\n  \"五月\",\n  \"六月\",\n  \"七月\",\n  \"八月\",\n  \"九月\",\n  \"十月\",\n  \"十一月\",\n  \"十二月\"\n];\nconst years = \"年\";\nconst year = \"本年\";\nconst month = \"月\";\nconst week = \"周\";\nconst day = \"日\";\nconst today = \"今日\";\nconst noEvent = \"暫無活動\";\nconst allDay = \"整天\";\nconst deleteEvent = \"刪除\";\nconst createEvent = \"新建活動\";\nconst dateFormat = \"YYYY MMMM D dddd\";\nconst zhHk = {\n  weekDays,\n  weekDaysShort,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  zhHk as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  weekDaysShort,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,OAAO;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}