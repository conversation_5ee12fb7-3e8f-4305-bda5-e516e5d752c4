{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/ar.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"الإثنين\",\n  \"الثلاثاء\",\n  \"الأربعاء\",\n  \"الخميس\",\n  \"الجمعة\",\n  \"السبت\",\n  \"الأحد\"\n];\nconst months = [\n  \"يناير\",\n  \"فبراير\",\n  \"مارس\",\n  \"أبريل\",\n  \"مايو\",\n  \"يونيو\",\n  \"يوليو\",\n  \"أغسطس\",\n  \"سبتمبر\",\n  \"أكتوبر\",\n  \"نوفمبر\",\n  \" ديسمبر\"\n];\nconst years = \"سنوات\";\nconst year = \"سنة\";\nconst month = \"شهر\";\nconst week = \"أسبوع\";\nconst day = \"يوم\";\nconst today = \"اليوم\";\nconst noEvent = \"لا حدث\";\nconst allDay = \"طوال اليوم\";\nconst deleteEvent = \"حذف\";\nconst createEvent = \"إنشاء حدث\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst ar = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  ar as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}