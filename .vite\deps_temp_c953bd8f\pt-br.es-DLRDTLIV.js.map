{"version": 3, "sources": ["../../node_modules/vue-cal/dist/i18n/pt-br.es.js"], "sourcesContent": ["/**\n  * vue-cal v4.10.2\n  * (c) 2025 <PERSON><PERSON> <<EMAIL>>\n  * @license MIT\n  */\nconst weekDays = [\n  \"Segunda-feira\",\n  \"Terça-feira\",\n  \"Quarta-feira\",\n  \"Q<PERSON><PERSON>-feira\",\n  \"Sex<PERSON><PERSON>feira\",\n  \"Sábado\",\n  \"Domingo\"\n];\nconst months = [\n  \"Janeiro\",\n  \"Fevereiro\",\n  \"Março\",\n  \"Abril\",\n  \"Maio\",\n  \"Junho\",\n  \"Julho\",\n  \"Agosto\",\n  \"Setembro\",\n  \"Outubro\",\n  \"Novembro\",\n  \"Dezembro\"\n];\nconst years = \"Anos\";\nconst year = \"Ano\";\nconst month = \"Mês\";\nconst week = \"Semana\";\nconst day = \"Dia\";\nconst today = \"Hoje\";\nconst noEvent = \"Sem eventos\";\nconst allDay = \"Dia inteiro\";\nconst deleteEvent = \"Remover\";\nconst createEvent = \"Criar um evento\";\nconst dateFormat = \"dddd D MMMM YYYY\";\nconst ptBr = {\n  weekDays,\n  months,\n  years,\n  year,\n  month,\n  week,\n  day,\n  today,\n  noEvent,\n  allDay,\n  deleteEvent,\n  createEvent,\n  dateFormat\n};\nexport {\n  allDay,\n  createEvent,\n  dateFormat,\n  day,\n  ptBr as default,\n  deleteEvent,\n  month,\n  months,\n  noEvent,\n  today,\n  week,\n  weekDays,\n  year,\n  years\n};\n"], "mappings": ";;;AAKA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,QAAQ;AACd,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,OAAO;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;", "names": []}