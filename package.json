{"name": "dashboard-api-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 4005 --host", "dev:prod": "vite --port 4005 --mode prod", "build": "vue-tsc -b && vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint --ext .js,.ts,.vue --ignore-path .gitignore src", "lint:fix": "eslint --ext .js,.ts,.vue --ignore-path .gitignore --fix src", "build:uat": "vue-tsc -b && vite build --mode uat", "build:prod": "vue-tsc -b && vite build --mode prod", "format": "prettier --write src/", "start": "serve -s dist -p 4005", "prepare": "husky"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.15", "@lumaai/luma-web": "^0.2.2", "@storefront-ui/vue": "^2.6.4", "@vuepic/vue-datepicker": "^7.4.1", "@vueuse/core": "^12.4.0", "@zag-js/slider": "^0.61.1", "@zag-js/vue": "^0.61.1", "aframe": "^1.5.0", "axios": "^1.3.5", "chart.js": "^4.4.3", "debug": "^4.3.7", "firebase": "^11.9.1", "moment-timezone": "^0.5.45", "openseadragon": "^4.1.1", "papaparse": "^5.4.1", "pinia": "^2.1.7", "propvr-svg-overlay": "^0.0.48", "serve": "^14.2.1", "three": "^0.157.0", "three-globe": "^2.30.0", "tweenjs": "^1.0.2", "typescript-eslint": "^8.18.0", "vee-validate": "^4.12.2", "vue": "^3.4.29", "vue-cal": "^4.10.2", "vue-dndrop": "^1.3.1", "vue-draggable-plus": "^0.6.0", "vue-google-charts": "^1.1.0", "vue-multiselect": "^3.0.0-beta.3", "vue-router": "^4.3.3", "vue3-timepicker": "^1.0.0-beta.2", "yup": "^1.3.2", "vite-plugin-html": "^3.2.2", "vite-plugin-vue-devtools": "^7.7.7"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@svgdotjs/svg.js": "^3.2.4", "@tailwindcss/aspect-ratio": "^0.4.2", "@tsconfig/node20": "^20.1.4", "@types/node": "^20.14.5", "@typescript-eslint/parser": "^8.8.1", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.13", "dotenv": "^16.0.3", "eslint": "^8.57.1", "eslint-config-prettier": "^8.10.0", "eslint-plugin-html": "^6.2.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.32.0", "husky": "^9.1.7", "npm-run-all2": "^6.2.0", "postcss": "^8.4.21", "prettier": "3.3.3", "tailwindcss": "^3.2.6", "typescript": "~5.4.0", "vite": "^5.3.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-vue-devtools": "^7.3.1", "vue-tsc": "^2.0.21"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5"}}