import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/hu.es.js
var weekDays = [
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>rtök",
  "Péntek",
  "<PERSON><PERSON><PERSON><PERSON>",
  "Vas<PERSON><PERSON><PERSON>"
];
var months = [
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>z<PERSON>",
  "Szeptember",
  "Október",
  "November",
  "December"
];
var years = "Évek";
var year = "Év";
var month = "Hónap";
var week = "Hét";
var day = "Nap";
var today = "Mai nap";
var noEvent = "Nincs esemény";
var allDay = "Egész nap";
var deleteEvent = "Esemény törlese";
var createEvent = "Esemény létrehozása";
var dateFormat = "dddd D MMMM YYYY";
var hu = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  hu as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/hu.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=hu.es-34F4O332.js.map
