import "./chunk-LK32TJAX.js";

// node_modules/vue-cal/dist/i18n/de.es.js
var weekDays = [
  "<PERSON>ag",
  "Dienstag",
  "<PERSON><PERSON><PERSON><PERSON>",
  "Donnerstag",
  "Freitag",
  "Samstag",
  "Sonntag"
];
var months = [
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "April",
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "August",
  "September",
  "Oktober",
  "November",
  "Dezember"
];
var years = "Jahre";
var year = "Jahr";
var month = "Monat";
var week = "Woche";
var day = "Tag";
var today = "Heute";
var noEvent = "Keine Events";
var allDay = "Ganztägig";
var deleteEvent = "Löschen";
var createEvent = "Event erstellen";
var dateFormat = "dddd D MMMM YYYY";
var de = {
  weekDays,
  months,
  years,
  year,
  month,
  week,
  day,
  today,
  noEvent,
  allDay,
  deleteEvent,
  createEvent,
  dateFormat
};
export {
  allDay,
  createEvent,
  dateFormat,
  day,
  de as default,
  deleteEvent,
  month,
  months,
  noEvent,
  today,
  week,
  weekDays,
  year,
  years
};
/*! Bundled license information:

vue-cal/dist/i18n/de.es.js:
  (**
    * vue-cal v4.10.2
    * (c) 2025 Antoni Andre <<EMAIL>>
    * @license MIT
    *)
*/
//# sourceMappingURL=de.es-EVCLPUNF.js.map
